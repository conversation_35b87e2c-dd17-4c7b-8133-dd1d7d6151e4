import { ArtifactV3 } from "@/types/open-canvas";
import { Annotation, MessagesAnnotation } from "@langchain/langgraph";

export const ReflectionGraphAnnotation = Annotation.Root({
  /**
   * The chat history to reflect on.
   */
  ...MessagesAnnotation.spec,
  /**
   * The artifact to reflect on.
   */
  artifact: Annotation<ArtifactV3 | undefined>,
});

export type ReflectionGraphReturnType = Partial<
  typeof ReflectionGraphAnnotation.State
>;

export const GraphAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  summary: Annotation<string | undefined>({
    reducer: (_, action) => action,
    default: () => undefined,
  }),
  user_memories: Annotation<string[] | undefined>,
});