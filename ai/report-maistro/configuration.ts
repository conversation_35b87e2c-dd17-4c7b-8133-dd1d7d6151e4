import { Annotation } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { v4 as uuidv4 } from 'uuid';
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { DEFAULT_REPORT_STRUCTURE } from "./prompts"

/**
 * Configuration annotation for the report generation agent
 */
export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>,
  assistant_id: Annotation<string>,
  thread_id: Annotation<string>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schemas: Annotation<Record<string, MemoryConfig>>,
  model: Annotation<string | undefined>,
  delay: Annotation<number | undefined>,

  /**
   * The structure template for the report
   */
  reportStructure: Annotation<string>,

  /**
   * Number of research queries to perform
   */
  numberOfQueries: Annotation<number>,

  /**
   * Topic for Tavily search
   */
  tavilyTopic: Annotation<string>,

  /**
   * Date range for Tavily search
   * Number of days to look back for news articles (only used when tavily_topic='news')
   */
  tavilyDays: Annotation<number | undefined>,
});

/**
 * Create a ConfigurationAnnotation.State instance from a RunnableConfig object
 * 
 * @param config - The configuration object to use
 * @returns An instance of ConfigurationAnnotation.State with the specified configuration
 */
export function ensureConfiguration(
  config: RunnableConfig
): typeof ConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof ConfigurationAnnotation.State>;

  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    schemas: configurable?.schemas || {},
    model: configurable?.model || "groq/llama-3.3-70b-versatile",
    delay: configurable?.delay || 3,
    reportStructure:
      configurable.reportStructure ||
      process.env.REPORT_STRUCTURE ||
      DEFAULT_REPORT_STRUCTURE,
    numberOfQueries:
      configurable.numberOfQueries ||
      Number(process.env.NUMBER_OF_QUERIES) ||
      2,
    tavilyTopic:
      configurable.tavilyTopic ||
      process.env.TAVILY_TOPIC ||
      "general",
    tavilyDays:
      configurable.tavilyDays !== undefined 
        ? Number(configurable.tavilyDays) 
        : (process.env.TAVILY_DAYS 
            ? Number(process.env.TAVILY_DAYS) 
            : undefined),
  };
}