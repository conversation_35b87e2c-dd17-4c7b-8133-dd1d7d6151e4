import {
  StateGraph,
  START,
  END,
  Send,
  LangGraphRunnableConfig
} from '@langchain/langgraph'
//import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from '@langchain/groq'
import { ChatGoogleGenerativeAI } from '@langchain/google-genai'
import { ChatGoogle } from '@langchain/google-gauth'
import {
  type BaseCheckpointSaver,
  type BaseStore
} from '@langchain/langgraph-checkpoint'
import { PostgresSaver } from '@langchain/langgraph-checkpoint-postgres'
import { SystemMessage, HumanMessage } from '@langchain/core/messages'

import {
  ReportStateInput,
  ReportStateOutput,
  ReportStateAnnotation,
  SectionStateAnnotation,
  Section,
  ReportState
} from './exp_state'
import { QueriesSchema, SectionsSchema } from './schemas'
import {
  reportPlannerInstructions,
  reportPlannerQueryWriterInstructions,
  finalSectionWriterInstructions
} from './prompts'
import {
  formatSections,
  extractMessageContent,
  tavilySearchAsync,
  deduplicateAndFormatSources
} from './utils'
import { ConfigurationAnnotation, ensureConfiguration } from './configuration'
import { sectionBuilder } from './section_graph/exp_graph'
import { deleteCompletedSections } from '@/ai/utils/delete_completed_sections'
import { v4 as uuidv4 } from 'uuid'
import { sections_test } from './constant'

// LLMs
//const planner_model_ = new ChatOpenAI({model: "o3-mini", temperature: 0, reasoningEffort: "medium"})
const groq_planner_model = new ChatGroq({
  model: 'deepseek-r1-distill-llama-70b',
  temperature: 0,
})
const groq_writer_model = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
  temperature: 0
})
const planner_model = new ChatGoogleGenerativeAI({
  model: 'gemini-2.0-flash-thinking-exp-01-21',
  temperature: 0,
})
const writer_model = new ChatGoogleGenerativeAI({
  model: 'gemini-2.0-pro-exp-02-05',
  temperature: 0
})

export async function generateReportPlan(
  state: typeof ReportStateAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<{ reports: (typeof ReportStateAnnotation.State)[] }> {
  // Create a new report.
  const currentReport = {
    id: uuidv4(),
    topic: state.topic,
    sections: [],
    //completed_sections: [],
    report_sections_from_research: '',
    final_report: ''
  }

  // Extract data from the current report
  const topic = state.topic
  if (!topic) {
    throw new Error('No topic provided in state')
  }
  const feedback = state.feedback_on_report_plan || null
  // Ensure configuration is valid
  const configurable = ensureConfiguration(config)
  const reportStructure = configurable.reportStructure
  const numberOfQueries = configurable.numberOfQueries
  const tavilyTopic = configurable.tavilyTopic
  const tavilyDays = configurable.tavilyDays

  const reportOrganization =
    typeof reportStructure === 'object'
      ? JSON.stringify(reportStructure)
      : reportStructure

  const fastLlm = new ChatGroq({
    model: 'llama-3.3-70b-versatile',
    temperature: 0
  })
  const llm = new ChatGroq({
    model: 'llama-3.3-70b-versatile',
    temperature: 0
  })
  const structuredLlm = llm.withStructuredOutput(QueriesSchema)

  // Format system instructions for generating search queries.
  const systemInstructionsQuery = reportPlannerQueryWriterInstructions
    .replace('{topic}', topic)
    .replace('{report_organization}', reportOrganization)
    .replace('{number_of_queries}', numberOfQueries.toString())

  // Generate search queries.
  const queryResults = await structuredLlm.invoke([
    new SystemMessage(systemInstructionsQuery),
    new HumanMessage(
      'Generate search queries that will help with planning the sections of the report.'
    )
  ])

  const queryList = queryResults.queries
    .map(item => item.search_query)
    .filter((query): query is string => query !== null)

  // Perform web search.
  const [deleteResult, searchDocs] = await Promise.all([
    deleteCompletedSections("completed_sections", "/research"),
    tavilySearchAsync(queryList, tavilyTopic, tavilyDays ?? undefined),
  ]);

  // Deduplicate and format sources.
  const sourceStr = deduplicateAndFormatSources(searchDocs, 1000, false)

  // Format system instructions for generating sections (including feedback).
  const systemInstructionsSections = reportPlannerInstructions
    .replace('{topic}', topic)
    .replace('{report_organization}', reportOrganization)
    .replace('{context}', sourceStr)
    .replace('{feedback}', feedback)

  console.log('systemInstructionsSections', systemInstructionsSections)

  // Generate report sections.
  const sectionsLlm = llm.withStructuredOutput(SectionsSchema)
  const reportSections = await sectionsLlm.invoke([
    new SystemMessage(systemInstructionsSections),
    new HumanMessage('Generate the sections of the report.')
  ])

  console.log('reportSections', reportSections)

  const sectionsWithDefaults = reportSections.sections.map(section => ({
    ...section,
    content: section.content ?? '',
    name: section.name ?? '',
    description: section.description ?? '',
    research: section.research ?? false
  }))
  console.log('sectionsWithDefaults', sectionsWithDefaults)

  // Create an updated report by replacing the sections.
  const updatedReport = {
    ...currentReport,
    sections: sectionsWithDefaults,
    //completed_sections: []
  }

  // Merge the updated report into the reports array.
  let updatedReports: (typeof ReportStateAnnotation.State)[]
  if (state.reports && state.reports.length > 0) {
    updatedReports = state.reports.concat(updatedReport)
  } else {
    updatedReports = [updatedReport]
  }

  // Return the update as a full state update for the reports array.
  return { reports: updatedReports, completed_sections: [] }
}

const humanFeedback = (state: typeof ReportStateAnnotation.State) => {
  console.log("--- humanFeedback ---");
  const feedback: string = interrupt("Please provide feedback");
  return {
    feedback_on_report_plan: feedback
  };
}

export function initiateSectionWriting(
  state: typeof ReportStateAnnotation.State
): Send[] | string {
  // Extract the latest report
  if (!state.reports || state.reports.length === 0) return []
  const currentReport = state.reports.at(-1)

  const feedback = state.feedback_on_report_plan || null

  /* Feedback is by default None and accept_report_plan is by default False
  # If a user hits "Continue" in Studio, we want to proceed with the report plan
  # If a user enters feedback_on_report_plan in Studio, we want to regenerate the report plan
  # Once a user enters feedback_on_report_plan, they need to flip accept_report_plan to True to proceed
  */
  // If feedback is provided and the plan hasn't been accepted, regenerate the plan.
  if (!state.accept_report_plan && feedback) {
      return 'generate_report_plan'
    }

  // Otherwise, kick off section writing for sections that require research.
  return currentReport.sections
    .filter(s => s.research)
    .map(s => new Send('build_section_with_web_research', { section: s }))
}

export async function writeFinalSections(
  state: typeof SectionStateAnnotation.State
): Promise<{ completed_sections: Section[] }> {
  console.log("Writing section state:", state);
  console.log("Writing section:", state.section.name);

  const section = state.section;
  const completedReportSections = state.report_sections_from_research;

  const google = new ChatGoogleGenerativeAI({
    temperature: 0,
    model: 'gemini-2.0-flash-exp',
  });

  const systemInstructions = finalSectionWriterInstructions
    .replace('{section_title}', section.name)
    .replace('{section_topic}', section.description)
    .replace('{context}', completedReportSections);

  try {
    const sectionContent = await planner_model.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage('Generate a report section based on the provided sources.')
    ]);

    const content = extractMessageContent(sectionContent);

    const updatedSection: Section = {
      ...section,
      content: content
    };

    // Ensure we have an array for completed_sections.
    const currentCompletedSections = Array.isArray(state.completed_sections)
      ? state.completed_sections
      : [];

    return { completed_sections: [updatedSection] };
  } catch (error) {
    console.error('Error generating section content:', error);

    const groq = new ChatGroq({
      model: 'llama-3.3-70b-versatile',
      temperature: 0,
    });

    const fallbackContent = await groq.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage('Generate a report section based on the provided sources.')
    ]);

    const fallbackSection: Section = {
      ...section,
      content: extractMessageContent(fallbackContent)
    };

    const currentCompletedSections = Array.isArray(state.completed_sections)
      ? state.completed_sections
      : [];

    return { completed_sections: [fallbackSection] };
  }
}


export function gatherCompletedSections(
  state: typeof ReportStateAnnotation.State
): ReportState {
  console.log('gatherCompletedSections===============>', state)
  if (!state.reports || state.reports.length === 0) return state

  const reports = [...state.reports]
  const currentReport = reports.at(-1)

  if (!currentReport) return state // Safety check

  // Merge new completed sections (ensure they persist in the report)
  const updatedCompletedSections = [
    //...currentReport.completed_sections,
    ...(state.completed_sections || []) // Fetch completed sections from section state
  ]

  // Format completed sections for report context
  const completedReportSections = formatSections(updatedCompletedSections)

  // Update the current report
  const updatedReport = {
    ...currentReport,
    //completed_sections: updatedCompletedSections, // Persisting completed sections
    report_sections_from_research: completedReportSections
  }

  // Update the reports array with the modified current report
  reports[reports.length - 1] = updatedReport

  // Return the updated state
  return {
    ...state,
    reports
  }
}

export function initiateFinalsectionWriting(
  state: typeof ReportStateAnnotation.State
): Send[] {
  //console.log('initiateFinalsectionWriting state===============>', state)
  if (!state.reports || state.reports.length === 0) return []
  const currentReport = state.reports[state.reports.length - 1]
  // Kick off section writing in parallel for sections not requiring research
  return currentReport.sections
    .filter(s => !s.research)
    .map(
      s =>
        new Send('write_final_sections', {
          section: s,
          report_sections_from_research:
            currentReport.report_sections_from_research
        })
    )
}

export function compileFinalReport(
  state: typeof ReportStateAnnotation.State,
): { final_report: string } {
  if (!state.reports || state.reports.length === 0) return { final_report: '' };
  //console.log('compileFinalReport state============>', state);
  
  // Copy the reports array and get the current (latest) report.
  const updatedReports = [...state.reports];
  const currentReportIndex = updatedReports.length - 1;
  const currentReport = updatedReports[currentReportIndex];
  const sections = currentReport.sections;
  
  // Build a lookup from completed_sections by section name.
  const completedSections = state.completed_sections.reduce(
    (acc, s) => {
      acc[s.name] = s.content;
      return acc;
    },
    {} as Record<string, string>
  );
  
  // Update each section with completed content if available.
  const updatedSections = sections.map(section => ({
    ...section,
    content: completedSections[section.name] || section.content
  }));
  
  // Compile the final report by joining section contents.
  const newFinalReport = updatedSections.map(s => s.content).join('\n\n');
  
  // Update the current report's final_report field.
  const updatedReport = {
    ...currentReport,
    final_report: newFinalReport,
  };

  //console.log("updatedReport888888888888888888888888888888888888888888888888888888888888888888888888===============>", updatedReport)
  
  // Replace the last report in the reports array.
  updatedReports[currentReportIndex] = updatedReport;
  
  // and return the new final report.
  return { reports: updatedReports, completed_sections: [] };
}

export async function createGraph(
  checkpointer?: PostgresSaver,
  store?: BaseStore
): Promise<any> {
  if (checkpointer) {
    await checkpointer.setup()
  }
  const builder = new StateGraph(
    {
      stateSchema: ReportStateAnnotation,
      input: ReportStateInput,
      output: ReportStateOutput
    },
    ConfigurationAnnotation
  )
    .addNode('generate_report_plan', generateReportPlan)
    //.addNode("human_feedback", humanFeedback)
    .addNode('build_section_with_web_research', sectionBuilder.compile())
    .addNode('gather_completed_sections', gatherCompletedSections)
    .addNode('write_final_sections', writeFinalSections)
    .addNode('compile_final_report', compileFinalReport)
    .addEdge(START, 'generate_report_plan')
    //.addEdge("generate_report_plan", "human_feedback")
    .addConditionalEdges('generate_report_plan', initiateSectionWriting, [
      'build_section_with_web_research'
    ])
    .addEdge('build_section_with_web_research', 'gather_completed_sections')
    .addConditionalEdges(
      'gather_completed_sections',
      initiateFinalsectionWriting,
      ['write_final_sections']
    )
    .addEdge('write_final_sections', 'compile_final_report')
    .addEdge('compile_final_report', END)

  return builder.compile({
    checkpointer
    //store,
    //interruptBefore: ["human_feedback"],
  })
}
