import { 
  StateGraph, 
  START, 
  END, 
  Send,
  LangGraphRunnableConfig,  
} from "@langchain/langgraph";
//import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { ChatGoogle } from "@langchain/google-gauth";
import {
  type BaseCheckpointSaver,
  type BaseStore,
} from "@langchain/langgraph-checkpoint";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";

import { 
  ReportStateInput,
  ReportStateOutput,
  ReportStateAnnotation, 
  SectionStateAnnotation, 
  Section 
} from './state';
import {
  QueriesSchema,
  SectionsSchema
} from './schemas'
import { 
  reportPlannerInstructions, 
  reportPlannerQueryWriterInstructions, 
  finalSectionWriterInstructions 
} from './prompts';
import { 
  formatSections,
  extractMessageContent,
  tavilySearchAsync,
  deduplicateAndFormatSources
} from './utils';
import { 
  ConfigurationAnnotation,
  ensureConfiguration 
} from './configuration';
import { 
  sectionBuilder 
} from './section_graph/graph';

// LLMs 
//const planner_model_ = new ChatOpenAI({model: "o3-mini", temperature: 0, reasoningEffort: "medium"}) 
const groq_planner_model = new ChatGroq({model: "deepseek-r1-distill-llama-70b", temperature: 0})
const planner_model = new ChatGoogleGenerativeAI({ model: "gemini-2.0-flash-thinking-exp-01-21", temperature: 0 })
const writer_model = new ChatGoogleGenerativeAI({ model: "gemini-2.0-pro-exp-02-05", temperature: 0 })

export async function generateReportPlan(
  state: typeof ReportStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ sections: Section[] }> {
  const topic = state.topic;
  const feedback = state?.feedback_on_report_plan ?? null;
  const configurable = ensureConfiguration(config);
  const reportStructure = configurable.reportStructure
  const numberOfQueries = configurable.numberOfQueries
  const tavilyTopic = configurable.tavilyTopic
  const tavilyDays = configurable.tavilyDays


  const reportOrganization = typeof reportStructure === 'object' 
    ? JSON.stringify(reportStructure) 
    : reportStructure;

  const fastLlm = new ChatGroq({ model: "llama-3.3-70b-versatile", temperature: 0 });
  const llm = new ChatGroq({ model: "llama-3.3-70b-versatile", temperature: 0 });
  const structuredLlm = llm.withStructuredOutput(QueriesSchema);

  // Format system instructions
  const systemInstructionsQuery = reportPlannerQueryWriterInstructions.replace(
    '{topic}', topic
  ).replace(
    '{report_organization}', reportOrganization
  ).replace(
    '{number_of_queries}', numberOfQueries.toString()
  );

  // Generate search queries
  const queryResults = await structuredLlm.invoke([
    new SystemMessage(systemInstructionsQuery),
    new HumanMessage("Generate search queries that will help with planning the sections of the report.")
  ]);

  // Web search
  const queryList = queryResults.queries
  .map(item => item.search_query)
  .filter((query): query is string => query !== null);
  
  // Search web
  const searchDocs = await tavilySearchAsync(
    queryList, 
    tavilyTopic, 
    tavilyDays ?? undefined
  );

  // Deduplicate and format sources
  const sourceStr = deduplicateAndFormatSources(searchDocs, 1000, false);

  // Format system instructions
  const systemInstructionsSections = reportPlannerInstructions.replace(
    '{topic}', topic
  ).replace(
    '{report_organization}', reportOrganization
  ).replace(
    '{context}', sourceStr
  ).replace(
    '{feedback}', feedback
  );

  console.log("systemInstructionsSections", systemInstructionsSections)
  // Generate report sections
  // In production: use planner_model: o3-mini
  const sectionsLlm = llm.withStructuredOutput(SectionsSchema);
  const reportSections = await sectionsLlm.invoke([
    new SystemMessage(systemInstructionsSections),
    new HumanMessage("Generate the sections of the report.")
  ]);

  console.log("reportSections", reportSections) 

  const sectionsWithDefaults = reportSections.sections.map((section) => ({
    ...section,
    content: section.content ?? "",
    name: section.name ?? "", 
    description: section.description ?? "",
    research: section.research ?? false
  }));
  console.log("sectionsWithDefaults", sectionsWithDefaults)
  
  return { sections: sectionsWithDefaults };

}
function humanFeedback(
  state: typeof ReportStateAnnotation.State, 
  config: LangGraphRunnableConfig
): void {
  /* No-op node that should be interrupted on */
}

export function initiateSectionWriting(
  state: typeof ReportStateAnnotation.State
): Send[] {
  // Get feedback
  const feedback = state.feedback_on_report_plan ?? null;

  // If user has provided feedback but has not accepted the report plan, regenerate the report plan
  if (!state.accept_report_plan && feedback) {
    return "generate_report_plan";
  }

  // Kick off section writing in parallel for sections requiring research
  return state.sections
    .filter(s => s.research)
    .map(s => new Send("build_section_with_web_research", { section: s }));
}

export async function writeFinalSections(
  state: typeof SectionStateAnnotation.State
): Promise<{ completed_sections: Section[] }> {
  const section = state.section;
  const completedReportSections = state.report_sections_from_research;

  // Using regular ChatGoogleGenerativeAI without structured output
  const google = new ChatGoogleGenerativeAI({
    temperature: 0,
    model: "models/gemini-2.0-flash",
    maxOutputTokens: 2048,
  });

  // Format system instructions
  const systemInstructions = finalSectionWriterInstructions
    .replace('{section_title}', section.name)
    .replace('{section_topic}', section.description)
    .replace('{context}', completedReportSections);

  try {
    // Generate section content without structured output
    const sectionContent = await google.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage("Generate a report section based on the provided sources.")
    ]);

    // Extract content safely
    const content = extractMessageContent(sectionContent);

    // Create updated section
    const updatedSection: Section = {
      ...section,
      content: content
    };

    // Return completed sections
    return { completed_sections: [updatedSection] };
  } catch (error) {
    console.error("Error generating section content:", error);
    
    // Fallback to Groq if Gemini fails
    const groq = new ChatGroq({ 
      model: "llama-3.3-70b-versatile", 
      temperature: 0,
      maxOutputTokens: 2048
    });

    const fallbackContent = await groq.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage("Generate a report section based on the provided sources.")
    ]);

    const fallbackSection: Section = {
      ...section,
      content: extractMessageContent(fallbackContent)
    };

    return { completed_sections: [fallbackSection] };
  }
}

export function gatherCompletedSections(
  state: typeof ReportStateAnnotation.State
): { report_sections_from_research: string } {
  // Format completed sections as context for final sections
  const completedReportSections = formatSections(state.completed_sections);
  return { report_sections_from_research: completedReportSections };
}

export function initiateFinalsectionWriting(
  state: typeof ReportStateAnnotation.State
): Send[] {
  // Kick off section writing in parallel for sections not requiring research
  return state.sections
    .filter(s => !s.research)
    .map(s => new Send("write_final_sections", { 
      section: s, 
      report_sections_from_research: state.report_sections_from_research 
    }));
}

export function compileFinalReport(
  state: typeof ReportStateAnnotation.State
): { final_report: string } {
  const sections = state.sections;
  const completedSections = state.completed_sections.reduce((acc, s) => {
    acc[s.name] = s.content;
    return acc;
  }, {} as Record<string, string>);

  // Update sections with completed content
  const updatedSections = sections.map(section => ({
    ...section,
    content: completedSections[section.name]
  }));

  // Compile final report
  const allSections = updatedSections.map(s => s.content).join("\n\n");

  return { final_report: allSections };
}

export async function createGraph(
  checkpointer?: BaseCheckpointSaver | PostgresSaver, 
  store?: BaseStore
): Promise<any> {
  if (checkpointer) {
    await checkpointer.setup();
  }
  const builder = new StateGraph(
    {
      stateSchema: ReportStateAnnotation,
      input: ReportStateInput,
      output: ReportStateOutput,
    },
      ConfigurationAnnotation
  )
    .addNode("generate_report_plan", generateReportPlan)
    //.addNode("human_feedback", humanFeedback)
    .addNode("build_section_with_web_research", sectionBuilder.compile())
    .addNode("gather_completed_sections", gatherCompletedSections)
    .addNode("write_final_sections", writeFinalSections)
    .addNode("compile_final_report", compileFinalReport)
    .addEdge(START, "generate_report_plan")
    //.addEdge("generate_report_plan", "human_feedback")
    .addConditionalEdges("generate_report_plan", initiateSectionWriting, ["build_section_with_web_research"])
    .addEdge("build_section_with_web_research", "gather_completed_sections")
    .addConditionalEdges("gather_completed_sections", initiateFinalsectionWriting, ["write_final_sections"])
    .addEdge("write_final_sections", "compile_final_report")
    .addEdge("compile_final_report", END);

  return builder.compile({
    checkpointer,
    //store,
    //interruptBefore: ["human_feedback"],
  });
}