import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import  { type TavilySearchOptions,type TavilySearchResponse, tavily } from "@tavily/core";
import { BaseMessage } from "@langchain/core/messages";
import { Section } from  "./state";

// Utility function for deduplicating and formatting sources
export function deduplicateAndFormatSources(
  searchResponse: any, 
  maxTokensPerSource: number, 
  includeRawContent: boolean = true
): string {
  // Determine the sources list
  let sourcesList: any[] = [];
  if (typeof searchResponse === 'object' && 'results' in searchResponse) {
    sourcesList = searchResponse.results;
  } else if (Array.isArray(searchResponse)) {
    sourcesList = searchResponse.flatMap(response => 
      response.results ? response.results : response
    );
  } else {
    throw new Error("Input must be either a dict with 'results' or a list of search results");
  }
  
  // Deduplicate by URL
  const uniqueSources: Record<string, any> = {};
  sourcesList.forEach(source => {
    if (!uniqueSources[source.url]) {
      uniqueSources[source.url] = source;
    }
  });
 
  // Format output
  let formattedText = "Sources:\n\n";
  Object.values(uniqueSources).forEach((source, index) => {
    formattedText += `Source ${source.title}:\n===\n`;
    formattedText += `URL: ${source.url}\n===\n`;
    formattedText += `Most relevant content from source: ${source.content}\n===\n`;
  
    if (includeRawContent) {
      const charLimit = maxTokensPerSource * 4;
      let rawContent = source.raw_content || '';
       
      if (rawContent.length > charLimit) {
        rawContent = rawContent.slice(0, charLimit) + "... [truncated]";
      }
       
      formattedText += `Full source content limited to ${maxTokensPerSource} tokens: ${rawContent}\n\n`;
    }
  });
  
  return formattedText.trim();
}
  
// Format sections into a string
export function formatSections(sections: Section[]): string {
    return sections.map((section, idx) => `
  ${'='.repeat(60)}
  Section ${idx + 1}: ${section.name}
  ${'='.repeat(60)}
  Description:
  ${section.description}
  Requires Research: 
  ${section.research}
  
  Content:
  ${section.content || '[Not yet written]'}
  `).join('\n');
}
  
// Async web search function
export async function tavilySearchAsync(
  searchQueries: string[],
  tavilyTopic: string = 'general',
  tavilyDays?: number
): Promise<any[]> {
  const tavilySearchTool = new TavilySearchResults({
    maxResults: 5
  });
  const tvly = tavily({ apiKey: process.env.TAVILY_API_KEY });

  // Ensure search queries are provided
  if (searchQueries.length === 0) {
    throw new Error("No valid search queries provided.");
  }

  const searchTasks = searchQueries.map(async (query) => {
    // Log the query being processed for debugging
    console.log("Processing query:", query);

    const searchOptions: TavilySearchOptions = {
      includeRawContent: true,
      maxResults: 5,
    };

    if (tavilyTopic === 'news' && tavilyDays) {
      searchOptions.topic = 'news';
      searchOptions.days = tavilyDays;
    } else {
      searchOptions.topic = "general";
    }


    try {
      // Directly pass the query string to the invoke method
      const searchDocs = await tvly.search(
        query, 
        searchOptions
      );
      //const result = await tavilySearchTool.invoke(query);
      //console.log("Search Result:", searchDocs);
      return searchDocs;
    } catch (error: any) {
      console.error("Search failed for query:", query, error);
      throw new Error(`Search failed for query "${query}": ${error.message}`);
    }
  });

  return Promise.all(searchTasks);
}


export function extractMessageContent(message: BaseMessage): string {
  if (typeof message.content === 'string') {
    return message.content;
  }
  
  if (Array.isArray(message.content)) {
    // If it's an array, try to find a string content
    const stringContent = message.content.find(item => typeof item === 'string');
    return stringContent ?? JSON.stringify(message.content);
  }
  
  // Fallback to stringifying the content
  return JSON.stringify(message.content);
}