import { 
  LangGraphRunnableConfig, 
  StateGraph, 
  END, 
  START 
} from "@langchain/langgraph";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { SystemMessage, HumanMessage } from "@langchain/core/messages";
import { tavilySearchAsync, deduplicateAndFormatSources, extractMessageContent } from "../utils"
import { 
  type Section, 
  type SearchQuery,
  SectionStateOutput,
  SectionStateAnnotation 
} from '../state';
import {
  QueriesSchema,
} from '../schemas'
import { 
  queryWriterInstructions, 
  sectionWriterInstructions 
} from '../prompts';
import { ensureConfiguration } from '../configuration';

// LLMs 
const queryModel = new ChatGroq({ model: "llama-3.3-70b-versatile", temperature: 0, streaming: true })
const writerModel = new ChatGoogleGenerativeAI({ model: "gemini-2.0-pro-exp-02-05", temperature: 0 })

// Node functions for graph workflow
export async function generateQueries(
  state: typeof SectionStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ search_queries: SearchQuery[] }> {
  const section = state.section;
  const configurable = ensureConfiguration(config);
  const numberOfQueries = configurable.numberOfQueries;

  const structuredLlm = queryModel.withStructuredOutput(QueriesSchema);

  // Format system instructions
  const systemInstructions = queryWriterInstructions
    .replace('{section_topic}', section.description)
    .replace('{number_of_queries}', numberOfQueries.toString());

  // Generate queries (await the promise)
  const queries = await structuredLlm.invoke([
    new SystemMessage(systemInstructions),
    new HumanMessage("Generate search queries on the provided topic.")
  ]);
  console.log("queries=========================>", queries)

  // Return the formatted queries
  return { search_queries: queries.queries as SearchQuery[] };
}

export async function searchWeb(
  state: typeof SectionStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ source_str: string }> {
  const searchQueries = state.search_queries;
  const configurable = ensureConfiguration(config);
  const tavilyTopic = configurable.tavilyTopic;
  const tavilyDays = configurable.tavilyDays;
  // Web search
  const queryList = searchQueries
  .map(query => query.search_query)
  .filter((query): query is string => query !== null);

  const searchDocs = await tavilySearchAsync(queryList, tavilyTopic, tavilyDays);

  // Deduplicate and format sources
  const sourceStr = deduplicateAndFormatSources(searchDocs, 500, true);

  return { source_str: sourceStr };
}

export async function writeSection(
  state: typeof SectionStateAnnotation.State
): Promise<{ completed_sections: Section[] }> {
  const section = state.section;
  const sourceStr = state.source_str;

  

  // Format system instructions
  const systemInstructions = sectionWriterInstructions
    .replace('{section_title}', section.name)
    .replace('{section_topic}', section.description)
    .replace('{context}', sourceStr);

  // Generate section content
  const sectionContent = await writerModel.invoke([
    new SystemMessage(systemInstructions),
    new HumanMessage("Generate a report section based on the provided sources.")
  ]);

  // Extract content safely
  const content = extractMessageContent(sectionContent);

  // Create updated section
  const updatedSection: Section = {
    ...section,
    content: content
  };

  // Return completed sections
  return { completed_sections: [updatedSection] };
}

export const sectionBuilder = new StateGraph({
  stateSchema: SectionStateAnnotation,
  output: SectionStateOutput
})
  .addNode("generate_queries", generateQueries)
  .addNode("search_web", searchWeb)
  .addNode("write_section", writeSection)
  .addEdge(START, "generate_queries")
  .addEdge("generate_queries", "search_web")
  .addEdge("search_web", "write_section")
  .addEdge("write_section", END);

export async function createSectionGraph() {
  const sectionGraph = sectionBuilder.compile();
  return sectionGraph;
}

  
