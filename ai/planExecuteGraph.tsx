import { Annotation } from "@langchain/langgraph";
import { StateGraphArgs } from "@langchain/langgraph";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { z } from 'zod/v3';
import { zodToJsonSchema } from "zod-to-json-schema";
import { JsonOutputToolsParser } from "@langchain/core/output_parsers/openai_tools";
import { ChatPromptTemplate } from "@langchain/core/prompts"
import { END, START, StateGraph } from "@langchain/langgraph";
import { type RunnableConfig } from "@langchain/core/runnables";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq"
import { ChatAnthropic } from "@langchain/anthropic";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { HumanMessage } from "@langchain/core/messages";


const PlanExecuteState = Annotation.Root({
  input: Annotation<string>({
    reducer: (x, y) => y ?? x ?? "",
  }),
  plan: Annotation<string[]>({
    reducer: (x, y) => y ?? x ?? [],
  }),
  pastSteps: Annotation<[string, string][]>({
    reducer: (x, y) => x.concat(y),
  }),
  response: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
})


const tools = [new TavilySearchResults({ maxResults: 3 })];

// Define our Execution Agent
const agentExecutor = createReactAgent({
  llm: new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
    streaming: true,
  }),
  tools: tools,
});


// Planning Step
const plan = zodToJsonSchema(
  z.object({
    steps: z
      .array(z.string())
      .describe("different steps to follow, should be in sorted order"),
  }),
);
const planFunction = {
  name: "plan",
  description: "This tool is used to plan the steps to follow",
  inputSchema: plan,
};

const planTool = {
  type: "function",
  function: planFunction,
};

const plannerPrompt = ChatPromptTemplate.fromTemplate(
  `For the given objective, come up with a simple step by step plan. \
This plan should involve individual tasks, that if executed correctly will yield the correct answer. Do not add any superfluous steps. \
The result of the final step should be the final answer. Make sure that each step has all the information needed - do not skip steps.

{objective}`,
);

const model = new ChatGroq({
  model: "llama-3.3-70b-versatile",
  temperature: 0,
  streaming: true,
}).withStructuredOutput(planFunction);

const planner = plannerPrompt.pipe(model);


// Re-Plan Step
const response = zodToJsonSchema(
  z.object({
    response: z.string().describe("Response to user."),
  }),
);

const responseTool = {
  type: "function",
  function: {
    name: "response",
    description: "Response to user.",
    inputSchema: response,
  },
};

const replannerPrompt = ChatPromptTemplate.fromTemplate(
  `For the given objective, come up with a simple step by step plan. 
This plan should involve individual tasks, that if executed correctly will yield the correct answer. Do not add any superfluous steps.
The result of the final step should be the final answer. Make sure that each step has all the information needed - do not skip steps.

Your objective was this:
{input}

Your original plan was this:
{plan}

You have currently done the follow steps:
{pastSteps}

Update your plan accordingly. If no more steps are needed and you can return to the user, then respond with that and use the 'response' function.
Otherwise, fill out the plan.  
Only add steps to the plan that still NEED to be done. Do not return previously done steps as part of the plan.`,
);

const parser = new JsonOutputToolsParser();
const replanner = replannerPrompt
  .pipe(
    new ChatOpenAI({ model: "gpt-4.1" }).bindTools([
      planTool,
      responseTool,
    ]),
  )
  .pipe(parser);


// Create the Graph
async function executeStep(
  state: typeof PlanExecuteState.State,
  config?: RunnableConfig,
): Promise<Partial<typeof PlanExecuteState.State>> {
  const task = state.plan[0];
  const input = {
    messages: [new HumanMessage(task)],
  };
  const { messages } = await agentExecutor.invoke(input, config);

  return {
    pastSteps: [[task, messages[messages.length - 1].content.toString()]],
    plan: state.plan.slice(1),
  };
}

async function planStep(
  state: typeof PlanExecuteState.State,
): Promise<Partial<typeof PlanExecuteState.State>> {
  const plan = await planner.invoke({ objective: state.input });
  return { plan: plan.steps };
}

async function replanStep(
  state: typeof PlanExecuteState.State,
): Promise<Partial<typeof PlanExecuteState.State>> {
  const output = await replanner.invoke({
    input: state.input,
    plan: state.plan.join("\n"),
    pastSteps: state.pastSteps
      .map(([step, result]) => `${step}: ${result}`)
      .join("\n"),
  });

  if (Array.isArray(output) && output.length > 0) {
    const toolCall = output[0];

    if (toolCall.type == "response") {
      return { response: toolCall.args?.response };
    }

    return { plan: toolCall.args?.steps };
  }
  return {}; // Return an empty object
}

function shouldEnd(state: typeof PlanExecuteState.State) {
  return state.response ? "true" : "false";
}
  
export function createPlanExecutor() {
  const workflow = new StateGraph(PlanExecuteState)
    .addNode("planner", planStep)
    .addNode("agent", executeStep)
    .addNode("replan", replanStep)
    .addEdge(START, "planner")
    .addEdge("planner", "agent")
    .addEdge("agent", "replan")
    .addConditionalEdges("replan", shouldEnd, {
      true: END,
      false: "agent",
    });
    
    // Finally, we compile it!
    // This compiles it into a LangChain Runnable,
    // meaning you can use it as you would any other runnable
    const app = workflow.compile();
  return app;
}

export async function PlanExecuteGraph(task: string, threadId: string = "123") {
  console.log("----------------------task-------------------", task);
  const config = { recursionLimit: 50, configurable: { thread_id: threadId } };
  const app = createPlanExecutor();
  let finalResult: any;
  const stream = await app.stream(
    { input: task },
    config,
  );

  for await (const event of stream) {
    console.log(event);
    console.log("-----");
    finalResult = event;
  }
  console.log("-----finalResult-----", finalResult);
  const solution = finalResult?.solve?.result || '' 
  return solution;
}


