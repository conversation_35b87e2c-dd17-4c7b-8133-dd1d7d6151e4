//import redis from "@/lib/redis";
import pdfParse from "pdf-parse";
import { GoogleGenerativeAI } from "@google/generative-ai";

const AI_MODEL = "gemini-exp-1206";
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);
const generationConfig = {
  model: AI_MODEL,
  responseMimeType: "application/json",
}
const aiModel = genAI.getGenerativeModel(generationConfig);

export const extractTextFromPDF = async (fileData: Buffer) => {
  try {
    const data = await pdfParse(fileData);
    return data.text; // Contains the extracted text from the PDF
  } catch (error: any) {
    console.error("Error extracting text from PDF:", error);
    throw new Error(`Failed to extract text from PDF. Error: ${error.message}`);
  }
};

export const detectContractType = async (
  contractText: string
): Promise<string> => {
  const prompt = `
    Analyze the following contract text and determine the type of contract it is.
    Provide only the contract type as a single string (e.g., "Employment", "Non-Disclosure Agreement", "Sales", "Lease", etc.).
    Do not include any additional explanation or text.

    Contract text:
    ${contractText.substring(0, 2000)}
  `;

  const results = await aiModel.generateContent(prompt);
  const response = results.response;
  return response.text().trim();
};

export const analyzeContractWithAI = async (
  contractText: string,
  tier: "free" | "premium",
  contractType: string
) => {
  let prompt;
  let prompt_zhTW
  if (tier === "premium") {
    prompt = `
    Analyze the following ${contractType} contract and provide:
    1. A list of at least 10 potential risks for the party receiving the contract, each with a brief explanation and severity level (low, medium, high).
    2. A list of at least 10 potential opportunities or benefits for the receiving party, each with a brief explanation and impact level (low, medium, high).
    3. A comprehensive summary of the contract, including key terms and conditions.
    4. Any recommendations for improving the contract from the receiving party's perspective.
    5. A list of key clauses in the contract.
    6. An assessment of the contract's legal compliance.
    7. A list of potential negotiation points.
    8. The contract duration or term, if applicable.
    9. A summary of termination conditions, if applicable.
    10. A breakdown of any financial terms or compensation structure, if applicable.
    11. Any performance metrics or KPIs mentioned, if applicable.
    12. A summary of any specific clauses relevant to this type of contract (e.g., intellectual property for employment contracts, warranties for sales contracts).
    13. An overall score from 1 to 100, with 100 being the highest. This score represents the overall favorability of the contract based on the identified risks and opportunities.

    Format your response as a JSON object with the following structure:
    {
      "risks": [{"risk": "Risk description", "explanation": "Brief explanation", "severity": "low|medium|high"}],
      "opportunities": [{"opportunity": "Opportunity description", "explanation": "Brief explanation", "impact": "low|medium|high"}],
      "summary": "Comprehensive summary of the contract",
      "recommendations": ["Recommendation 1", "Recommendation 2", ...],
      "keyClauses": ["Clause 1", "Clause 2", ...],
      "legalCompliance": "Assessment of legal compliance",
      "negotiationPoints": ["Point 1", "Point 2", ...],
      "contractDuration": "Duration of the contract, if applicable",
      "terminationConditions": "Summary of termination conditions, if applicable",
      "overallScore": "Overall score from 1 to 100",
      "financialTerms": {
        "description": "Overview of financial terms",
        "details": ["Detail 1", "Detail 2", ...]
      },
      "performanceMetrics": ["Metric 1", "Metric 2", ...],
      "specificClauses": "Summary of clauses specific to this contract type"
    }
      `;
      prompt_zhTW = `
      分析以下 ${contractType} 合約並提供：
      1. 至少10個可能對接收方不利的風險，每項風險包含簡短的解釋以及嚴重程度（低、中、高）。
      2. 至少10個可能對接收方有利的機會或優勢，每項機會包含簡短的解釋以及影響程度（低、中、高）。
      3. 合約的全面總結，包括關鍵條款和條件。
      4. 從接收方的角度提出改進合約的建議。
      5. 合約中的關鍵條款列表。
      6. 合約的法律合規性評估。
      7. 潛在的談判要點列表。
      8. 合約的期限或有效期（如果適用）。
      9. 終止條件的總結（如果適用）。
      10. 合約的財務條款或補償結構概述（如果適用）。
      11. 合約中提到的績效指標（KPI）（如果適用）。
      12. 與此類合約相關的具體條款總結（例如，對於就業合約的知識產權條款，對於銷售合約的保證條款）。
      13. 合約的總分（從1到100），其中100為最高分。此分數根據所識別的風險和機會，表示合約的整體有利程度。
  
      Format your response as a JSON object with the following structure:
      {
        "risks": [{"risk": "風險描述", "explanation": "簡要說明", "severity": "低|中|高"}],
        "opportunities": [{"opportunity": "機會描述", "explanation": "簡要說明", "impact": "低|中|高"}],
        "summary": "合約的全面總結",
        "recommendations": ["建議1", "建議2", ...],
        "keyClauses": ["條款1", "條款2", ...],
        "legalCompliance": "法律合規性的評估",
        "negotiationPoints": ["談判要點1", "談判要點2", ...],
        "contractDuration": "合約的期限（如果適用）",
        "terminationConditions": "終止條件的總結（如果適用）",
        "overallScore": "從1到100的總分",
        "financialTerms": {
          "description": "財務條款概述",
          "details": ["詳細說明1", "詳細說明2", ...]
        },
        "performanceMetrics": ["指標1", "指標2", ...],
        "specificClauses": "與此合約類型相關的具體條款總結"
      }
  `;
  
  } else {
    prompt = `
    Analyze the following ${contractType} contract and provide:
    1. A list of at least 5 potential risks for the party receiving the contract, each with a brief explanation and severity level (low, medium, high).
    2. A list of at least 5 potential opportunities or benefits for the receiving party, each with a brief explanation and impact level (low, medium, high).
    3. A brief summary of the contract
    4. An overall score from 1 to 100, with 100 being the highest. This score represents the overall favorability of the contract based on the identified risks and opportunities.

     {
      "risks": [{"risk": "Risk description", "explanation": "Brief explanation"}],
      "opportunities": [{"opportunity": "Opportunity description", "explanation": "Brief explanation"}],
      "summary": "Brief summary of the contract",
      "overallScore": "Overall score from 1 to 100"
    }
`;
    
  prompt_zhTW = `
  分析以下 ${contractType} 合約並提供：
  1. 至少10個可能對接收方不利的風險，每項風險包含簡短的解釋以及嚴重程度（低、中、高）。
  2. 至少10個可能對接收方有利的機會或優勢，每項機會包含簡短的解釋以及影響程度（低、中、高）。
  3. 合約的全面總結，包括關鍵條款和條件。
  4. 合約的總分（從1到100），其中100為最高分。此分數根據所識別的風險和機會，表示合約的整體有利程度。

  {
    "risks": [{"risk": "風險描述", "explanation": "簡要說明", "severity": "低|中|高"}],
    "opportunities": [{"opportunity": "機會描述", "explanation": "簡要說明", "impact": "低|中|高"}],
    "summary": "合約的全面總結",
    "overallScore": "從1到100的總分",
  }
`;
  }

  prompt += `
    Important: Provide only the JSON object in your response, without any additional text or formatting. 
    
    
    Contract text:
    ${contractText}
    `;
  prompt_zhTW += `
    Important: Provide only the JSON object in your response, without any additional text or formatting. 
    
    
    Contract text:
    ${contractText}
    `;

  const results = await aiModel.generateContent(prompt_zhTW);
  const response = await results.response;
  let text = response.text();

  // remove any markdown formatting
  text = text.replace(/```json\n?|\n?```/g, "").trim();

  try {
    // Attempt to fix common JSON errors
    text = text.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3'); // Ensure all keys are quoted
    text = text.replace(/:\s*"([^"]*)"([^,}\]])/g, ': "$1"$2'); // Ensure all string values are properly quoted
    text = text.replace(/,\s*}/g, "}"); // Remove trailing commas

    const analysis = JSON.parse(text);
    console.log("analysis:", analysis);
    return analysis;
  } catch (error) {
    console.log("Error parsing JSON:", error);
  }

  interface IRisk {
    risk: string;
    explanation: string;
  }

  interface IOpportunity {
    opportunity: string;
    explanation: string;
  }

  interface FallbackAnalysis {
    risks: IRisk[];
    opportunities: IOpportunity[];
    summary: string;
  }

  const fallbackAnalysis: FallbackAnalysis = {
    risks: [],
    opportunities: [],
    summary: "Error analyzing contract",
  };

  // Extract risks
  const risksMatch = text.match(/"risks"\s*:\s*\[([\s\S]*?)\]/);
  if (risksMatch) {
    fallbackAnalysis.risks = risksMatch[1].split("},").map((risk) => {
      const riskMatch = risk.match(/"risk"\s*:\s*"([^"]*)"/);
      const explanationMatch = risk.match(/"explanation"\s*:\s*"([^"]*)"/);
      return {
        risk: riskMatch ? riskMatch[1] : "Unknown",
        explanation: explanationMatch ? explanationMatch[1] : "Unknown",
      };
    });
  }

  //Extact opportunities
  const opportunitiesMatch = text.match(/"opportunities"\s*:\s*\[([\s\S]*?)\]/);
  if (opportunitiesMatch) {
    fallbackAnalysis.opportunities = opportunitiesMatch[1]
      .split("},")
      .map((opportunity) => {
        const opportunityMatch = opportunity.match(
          /"opportunity"\s*:\s*"([^"]*)"/
        );
        const explanationMatch = opportunity.match(
          /"explanation"\s*:\s*"([^"]*)"/
        );
        return {
          opportunity: opportunityMatch ? opportunityMatch[1] : "Unknown",
          explanation: explanationMatch ? explanationMatch[1] : "Unknown",
        };
      });
  }

  // Extract summary
  const summaryMatch = text.match(/"summary"\s*:\s*"([^"]*)"/);
  if (summaryMatch) {
    fallbackAnalysis.summary = summaryMatch[1];
  }

  return fallbackAnalysis;
};