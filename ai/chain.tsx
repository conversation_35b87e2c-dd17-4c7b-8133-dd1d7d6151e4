import {
    ChatPromptTemplate,
    MessagesPlaceholder,
  } from "@langchain/core/prompts"
  import { AgentExecutor, createToolCallingAgent } from "langchain/agents"
  import { ChatGroq } from "@langchain/groq"
  import { ChatOpenAI } from "@langchain/openai"
  import { ChatAnthropic } from "@langchain/anthropic";
  import { ChatGoogleGenerativeAI } from "@langchain/google-genai"

  //import { searchTool, imagesTool } from "@/app/(ichat)/(routes)/ichat/_ai/tools";
  import { weatherTool, retrieverTool, searchTool, imagesTool } from "./tools";

  //export const runtime = 'edge'
  export const preferredRegion = ['sfo1']
  export const maxDuration = 60;
  export const dynamic = 'force-dynamic';
  
  const prompt = ChatPromptTemplate.fromMessages([
    [
      "system",
      `You are a talking podcaster named <PERSON>. All final responses must be how a talking podcaster would respond!`,
    ],
    new MessagesPlaceholder("chat_history"),
    ["human", "{input}"],
    new MessagesPlaceholder("agent_scratchpad"),
  ]);
  
  const tools = [searchTool, imagesTool, weatherTool, retrieverTool];
  const openai = new ChatOpenAI({
    model: "gpt-4.1-mini",
    temperature: 0,
    streaming: true,
  });
  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
    temperature: 0,
    streaming: true,
  })
  
  const anthropic = new ChatAnthropic({
    model: "claude-3-haiku-20240307",
    temperature: 0,
    streaming: true,
  })
  
  export const chainExecutor = new AgentExecutor({
    agent: createToolCallingAgent({ 
      // @ts-ignore
      llm, 
      tools, 
      prompt 
    }),
    tools,
    verbose: false,
  });
  

  
