import { ChatGoogleGenerativeAI } from '@langchain/google-genai'
import { ChatGroq } from '@langchain/groq'
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { createSwarm, createHandoffTool } from "@langchain/langgraph-swarm";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from '@langchain/langgraph-checkpoint-postgres'
import { type BaseStore } from '@langchain/langgraph-checkpoint'
import { planner_prompt, researcher_prompt } from "./prompt";

// Initialize the LLM model
const model = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
})

// Define the fetch_doc tool
const fetch_doc = new TavilySearchResults({ maxResults: 1 });

// Define handoff tools
const transfer_to_planner_agent = createHandoffTool({
  agentName: "planner_agent",
  description:
  "Transfer the user to the planner_agent for clarifying questions related to the user's request.",
});

const transfer_to_researcher_agent = createHandoffTool({
  agentName: "researcher_agent",
  description:
    "Transfer the user to the researcher_agent to perform research and implement the solution to the user's request.",
});

// Define the planner prompt
const llmsTxt = "LangGraph:https://langchain-ai.github.io/langgraphjs/llms.txt";
const numUrls = 3;

const planner_prompt_formatted = planner_prompt
.replace('{llms_txt}', llmsTxt)
.replace('{num_urls}', String(numUrls))

const planner_model = new ChatGoogleGenerativeAI({
  model: "gemini-2.5-pro-exp-03-25",
  temperature: 1.0,
}).bindTools([fetch_doc, transfer_to_researcher_agent]);

// Create the planner agent
const planner_agent = createReactAgent({
  llm: model,
  tools: [fetch_doc, transfer_to_researcher_agent],
  name: "planner_agent",
  prompt: planner_prompt_formatted,
});

const researcher_model = new ChatGoogleGenerativeAI({
  model: "gemini-2.5-pro-exp-03-25",
  temperature: 1.0,
}).bindTools([fetch_doc, transfer_to_planner_agent]);

// Create the researcher agent
const researcher_agent = createReactAgent({
  llm: model,
  tools: [fetch_doc, transfer_to_planner_agent],
  name: "researcher_agent",
  prompt: researcher_prompt,
});

// Create the agent swarm
const agentSwarm = createSwarm({
  agents: [planner_agent, researcher_agent],
  defaultActiveAgent: "planner_agent",
});

// Compile the graph
export async function createGraph(
  checkpointer?: PostgresSaver,
  store?: BaseStore
): Promise<any> {
  if (checkpointer) {
    await checkpointer.setup()
  }
  return agentSwarm.compile({
    checkpointer
    //store,
  })
}

//export const app = agentSwarm.compile();
