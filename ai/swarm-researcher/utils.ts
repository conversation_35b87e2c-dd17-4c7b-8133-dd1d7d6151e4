import axios from "axios";
import TurndownService from "turndown";

const httpClient = axios.create({
  timeout: 10000,
  maxRedirects: 0,
});

export function printStream(stream: any): void {
  for (const [ns, update] of stream) {
    console.log(`Namespace '${ns}'`);

    for (const node in update) {
      const nodeUpdates = update[node];
      if (!nodeUpdates) continue;

      const nodeUpdatesList = Array.isArray(nodeUpdates) ? nodeUpdates : [nodeUpdates];

      for (const updates of nodeUpdatesList) {
        console.log(`Update from node '${node}'`);
        if (Array.isArray(updates)) {
          console.log(updates);
          continue;
        }

        const messagesKey = Object.keys(updates).find((key) => key.includes("messages"));
        if (messagesKey) {
          console.log(updates[messagesKey].slice(-1)[0]); // Assuming pretty_print equivalent
        } else {
          console.log(updates);
        }
      }
    }
    console.log("\n===\n");
  }
}

export async function fetchDoc(url: string): Promise<string> {
  /**
   * Fetch a document from a URL and return the markdownified text.
   * @param url - The URL of the document to fetch.
   * @returns A promise that resolves to the markdownified text of the document.
   */
  try {
    const response = await httpClient.get(url);
    const turndownService = new TurndownService();
    return turndownService.turndown(response.data);
  } catch (error) {
    return `Encountered an HTTP error: ${error}`;
  }
}
