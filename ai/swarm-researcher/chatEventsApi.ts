"use server"
import {  
  RemoveMessage, BaseMessage } from "@langchain/core/messages";
import { StreamMode } from "@/components/langgraph/Settings";
import { Command } from "@langchain/langgraph";
import { createGraph } from "./agent";
import { createEventProcessor } from "@/ai/utils/createEventProcessor";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools";
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts";
import { deleteCheckpointsByThreadId } from '@/ai/utils/delete_checkpoints';



const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function resumeGraph(
  threadId: string,
  fields: { newState: Record<string, any>, asNode?: string },
) {
  console.log("fields.newState", fields.newState);
  const graph = await createGraph(checkpointer);
  const eventProcessor = await createEventProcessor();
  const config = { 
    configurable: { thread_id: threadId },
    version: "v2" as "v2",
    encoding: undefined, 
  }

  return eventProcessor.processGraphEvents(
    graph,
    new Command({ resume: fields.newState }),
    config
  );
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph?.getState(config)).values?.reports

  await deleteCheckpointsByThreadId(threadId);

  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  targetUserId?: string;
  targetThreadId?: string;
  targetCompanionId?: string;
  systemInstructions: string;
  language: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      messages: [{ role: "user", content: params.message }],
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 8,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
    },
    version: "v2" as "v2",
    encoding: undefined,
  };

  const graph = await createGraph(checkpointer);
  const eventProcessor = await createEventProcessor();
  return eventProcessor.processGraphEvents(
    graph,
    input,
    config
  );
}