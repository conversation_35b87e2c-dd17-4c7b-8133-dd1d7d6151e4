import { Ratelimit } from '@upstash/ratelimit'
import { kv } from '@vercel/kv'
import { redirect } from 'next/navigation'
import { headers, type UnsafeUnwrappedHeaders } from 'next/headers';

const geminiRatelimit = new Ratelimit({
  redis: kv,
  limiter: Ratelimit.slidingWindow(5000, '1 m'),
  analytics: true,
  prefix: 'gemini_ratelimit'
})

async function getIP() {
  return (((await headers() as unknown as UnsafeUnwrappedHeaders) as unknown as UnsafeUnwrappedHeaders) as unknown as UnsafeUnwrappedHeaders).get('x-real-ip') ?? 'unknown';
}

export async function rateLimit() {
  const limit = await geminiRatelimit.limit(await getIP())
  if (!limit.success) {
    redirect('/waiting-room')
  }
}