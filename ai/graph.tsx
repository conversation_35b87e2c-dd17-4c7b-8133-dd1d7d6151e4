import { auth, currentUser } from "@clerk/nextjs/server";
import { AIMessage, BaseMessage } from "@langchain/core/messages";
import { type RunnableConfig } from "@langchain/core/runnables";
import { StateGraph, START, END } from "@langchain/langgraph";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { summarizerTool, githubTool, invoiceTool, weatherTool, websiteDataTool, retrieverTool, searchTool, imagesTool } from "./tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq"
import { ChatAnthropic } from "@langchain/anthropic";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"

import { type Language } from "@/components/ui/ai-language"
import { z } from 'zod/v3';
import { zodToJsonSchema } from "zod-to-json-schema";
import { StringOutputParser } from "@langchain/core/output_parsers";
//import zepClient, { zepMemory, formatChatSummaries } from "@/lib/zepClient"
import { Chat, Message } from '@/lib/itypes'
import { saveChat } from '@/app/actions/ichatActions'

//export const runtime = 'edge'
export const preferredRegion = ['sfo1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

interface AgentExecutorState {
  input: string;
  input_options: {
    companionId: string;
    threadId: string;
    aiLanguage: Language;
  };
  messages: BaseMessage[];
  chat_history: BaseMessage[];
  /**
   * The plain text result of the LLM if
   * no tool was used.
   */
  result?: string;
  /**
   * The parsed tool result that was called.
   */
  toolCall?: {
    id: string;
    name: string;
    parameters: Record<string, any>;
  };
  /**
   * The result of a tool.
   */
  toolResult?: Record<string, any>;
}


const formatChatHistoryAsString = (history: BaseMessage[]) => {
    return history
      .map((message) => `${message.getType()}: ${message.content}`)
      .join("\n");
  };

const invokeModel = async (
  state: AgentExecutorState,
  config?: RunnableConfig,
): Promise<Partial<AgentExecutorState>> => {
  console.log("Chain state: ", state)
  const language = state?.input_options?.aiLanguage?.label ?? 'zh-TW'
  const initialPrompt = ChatPromptTemplate.fromMessages([
    [
      "system",
      `You are a helpful assistant. You're provided a list of tools, and an input from the user.\n
Your job is to determine whether or not you have a tool which can handle the users input, or respond with plain text in ${language}.`,
    ],
    new MessagesPlaceholder({
      variableName: "chat_history",
      optional: true,
    }),
    ["human", "{input}"],
  ]);

  const tools = [retrieverTool];
  //const tools = [githubTool, invoiceTool, weatherTool, websiteDataTool, retrieverTool, searchTool, imagesTool];

  const openai = new ChatOpenAI({
    temperature: 0,
    model: "gpt-4.1",
    streaming: true,
  }).bindTools(tools);

  const anthropic = new ChatAnthropic({
    model: "claude-3-opus-20240229", //"claude-3-haiku-20240307", //"claude-3-sonnet-20240229",
    temperature: 0,
    streaming: true,
  }).bindTools(tools);

  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
    streaming: true,
  }).bindTools(tools);

  const google = new ChatGoogleGenerativeAI({
    model: "gemini-1.5-pro",
    temperature: 0,
    maxOutputTokens: 2048,
    streaming: true,
  }).bindTools(tools);

  const chain = initialPrompt.pipe(llm);
  const result = await chain.invoke(
    {
      input: state.input,
      chat_history: state.chat_history,
      //verbose: true
    },
    config,
  );
  console.log("Chain Result: ", result)

  if (result.tool_calls && result.tool_calls.length > 0) {
    return {
      toolCall: {
        id: result.tool_calls[0].id!,
        name: result.tool_calls[0].name,
        parameters: result.tool_calls[0].args,
      },
    };
  }
  return {
    result: result.content as string,
  };
};

const invokeToolsOrReturn = (state: AgentExecutorState) => {
  if (state.toolCall) {
    return "invokeTools";
  }
  if (state.result) {
    return END;
  }
  throw new Error("No tool call or result found.");
};

const invokeTools = async (
  state: AgentExecutorState,
  config?: RunnableConfig,
): Promise<Partial<AgentExecutorState>> => {
  if (!state.toolCall) {
    throw new Error("No tool call found.");
  }

  const toolMap = {
    //[githubTool.name]: githubTool,
    //[invoiceTool.name]: invoiceTool,
    //[weatherTool.name]: weatherTool,
    //[websiteDataTool.name]: websiteDataTool,
    [retrieverTool.name]: retrieverTool,
    //[summarizerTool.name]: summarizerTool,
    //[searchTool.name]: searchTool,
    //[imagesTool.name]: imagesTool,
  };

  const selectedTool = toolMap[state.toolCall.name];
  console.log("selectedTool: ", state.toolCall.name)
  console.log("Chat History: ", state.chat_history)
  
  if (!selectedTool) {
    throw new Error("No tool found in tool map.");
  }

  const toolResult = await selectedTool.invoke(
    state.toolCall.parameters as any,
    config,
  );
  //console.log("state: ", state)
  console.log("toolResult: ========================================>", toolResult)
  return {
    //messages: [new AIMessage(JSON.parse(toolResult))],
    toolResult: JSON.parse(toolResult),
  };
};

function shouldContinue(state: AgentExecutorState): string {
  if (state.toolCall && state.toolCall.name === 'search_my_personal_report') {
    console.log("---DECISION: GCONTINUE---");
    return "continue";
  } else {
    console.log("---DECISION: DO NOT CONTINUE / DONE---");
    return "end";
  }
}

const gradeDocuments = async (
  state: AgentExecutorState,
  config?: RunnableConfig,
): Promise<Partial<AgentExecutorState>> => {
  // Output
  const output = zodToJsonSchema(z.object({
    binaryScore: z.string().describe("Relevance score 'yes' or 'no'"),
  }));
  const tool = {
    type: "function" as const,
    function: {
      name: "give_relevance_score",
      description: "Give a relevance score to the retrieved documents.",
      inputSchema: output,
    }
  }

  const prompt = ChatPromptTemplate.fromTemplate(`You are a grader assessing relevance of retrieved docs to a user question.
  Here are the retrieved docs:
  \n ------- \n
  {context} 
  \n ------- \n
  Here is the user question: {question}
  If the content of the docs are relevant to the users question, score them as relevant.
  Give a binary score 'yes' or 'no' score to indicate whether the docs are relevant to the question.
  Yes: The docs are relevant to the question.
  No: The docs are not relevant to the question.`);

  const model = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
  }).bind({
    tools: [tool],
    tool_choice: tool,
  });

  const chain = prompt.pipe(model);
  //console.log("state: ", state)
  //const lastMessage = state.messages[state.messages.length - 1];
  //console.log("lastMessage: ", lastMessage)

  const score = await chain.invoke({
    question: state.input as string,
    context: JSON.stringify(state.toolResult),
    config,
  });
  console.log("score: ", score)
  // Ensure tool_calls[0].args is an object before JSON.stringify
  const args = score.tool_calls?.[0]?.args;
  if (!args) {
    throw new Error("No tool call found.");
  }
  const parsedArgs = typeof args === "object" ? JSON.stringify(args) : args;
  //console.log("parsedArgs: ", parsedArgs);
  return {
   toolResult: JSON.parse(parsedArgs)
  }
}

function checkRelevance(state: AgentExecutorState) {
  console.log("---CHECK RELEVANCE---");
  console.log("state: ", state)
  if (!state.toolCall) {
    throw new Error("No tool call found.");
  }
  
  if (state.toolResult && state.toolResult.binaryScore === "yes") {
    console.log("---DECISION: DOCS RELEVANT---");
    return "yes";
  }
  console.log("---DECISION: DOCS NOT RELEVANT---");
  return "no";
}

async function rewrite(state: AgentExecutorState) {
    console.log("---TRANSFORM QUERY---");
    const question = state.input as string;
    const prompt = ChatPromptTemplate.fromTemplate(`Look at the input and try to reason about the underlying semantic intent / meaning. \n 
    Here is the initial question:
    \n ------- \n
    {question} 
    \n ------- \n
    Formulate an improved question:`);

    // Grader
    //const model = new ChatOpenAI({
    //  modelName: "gpt-4.1",
    //  temperature: 0,
    //  streaming: true,
    //});
    const model = new ChatGroq({
      model: "llama-3.3-70b-versatile",
      temperature: 0,
    })
    const response = await prompt.pipe(model).invoke({ question });
    console.log("improved question response: ", response)
    return {
      result: response.content as string,
    };
  }

  async function generate(
    state: AgentExecutorState, 
    config?: RunnableConfig
  ): Promise<Partial<AgentExecutorState>> {
  console.log("---GENERATE---");
  const question = state.input as string;
  //console.log("---GENERATE---question---", question);
  //const sendLastMessage = state.messages[state.messages.length - 1];
  //console.log("---GENERATE---sendLastMessage---", sendLastMessage);
  const docs = JSON.stringify(state.toolResult)
  console.log("---GENERATE---docs---", docs);

  const prompt = ChatPromptTemplate.fromTemplate(`# Role
    Act as a seasoned Enneagram Coach, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and ensure the dialogue is insightful and supportive.

    # Goals 
    - Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
    - Utilize open-ended questions to help me explore my core needs and underlying fears or beliefs.
    - Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
    - Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.


    # Task
    Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding.
    Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection.
    Follow this step-by-step inquiry process:

    1. Invite me to share my Enneagram report and assess my initial understanding.
    2. Help me understand my report, highlighting areas for deeper exploration.
    3. Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
    4. Personalize our conversation based on the insights gained, focusing on understanding before advising.
    5. Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

    ## Specifics
    This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

    Specifically, I'm looking for:
    1. Comprehensive insights into my personality traits.
    2. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
    3. Assistance in recognizing and addressing behaviors that may be limiting my growth.
    4. Guidance on leveraging Enneagram insights for personal development
    5. Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

    # Tools
    Always use available tools to research relevant information when uncertain, particularly for questions about my personal details, report, and Enneagram profile.

    # Communication Style
    Adopt my discourse particles when responding. Engage warmly and supportively, emulating the qualities of a counselor.
    Keep responses concise, using up to three sentences unless more detail is requested. Ask simple, engaging questions like "Could you share more about that?". Focus on a single question or key message per reply. Maintain a supportive tone, particularly if I express confusion or frustration.

    Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate. Always speak in the first person as an Enneagram Coach. If unsure about the content's relevance or accuracy, express the need for more information instead of providing an uncertain answer.
    Responses in {language}.

    # Notes
    - Be concise and response in 100 words unless I ask for details.
    - Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
    - Please use relatable examples or analogies to help clarify more complex Enneagram concepts.

    Question: {question} \n
    My Report: {context} \n
    Chat History: {chat_history} \n
    Previous Chat Summary: {chat_summary} \n
  `);

  const google = new ChatGoogleGenerativeAI({
    model: "gemini-2.5-flash",
    maxOutputTokens: 2048,
    streaming: true,
  });
  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
    temperature: 0,
    maxOutputTokens: 4096,
    streaming: true,
  })

  /*
  const { userId } = auth(); 
  const { 
    prevMemoryMessages, 
    prevMemorySummaries 
  } = await zepMemory(
    state?.input_options?.companionId!,
    state?.input_options?.threadId!,
    userId!,
    question,
  ) ?? { prevMemoryMessages: [], prevMemorySummaries: [] };
  */
  const ragChain = prompt.pipe(llm).pipe(new StringOutputParser());
  const response = await ragChain.invoke(
    {
      context: docs,
      question,
      language: state?.input_options?.aiLanguage?.label ?? 'zh-TW',
      chat_history: formatChatHistoryAsString(state.chat_history.slice(-6)),
      chat_summary: "" //formatChatSummaries(prevMemorySummaries)
    },
      config,
  );

  console.log("ragChain response: ", response)
  return {
    result: response as string,
  };
}


async function normalChat(
  state: AgentExecutorState, 
  config?: RunnableConfig
): Promise<Partial<AgentExecutorState>> {
  console.log("---NORMAL CHAT---");
  const question = state.input as string;
  console.log("---NORMAL CHAT---question---", question);
    
  const prompt = ChatPromptTemplate.fromTemplate(`# Role
Act as a seasoned Enneagram Coach, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and ensure the dialogue is insightful and supportive.

# Goals 
- Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
- Utilize open-ended questions to help me explore my core needs and underlying fears or beliefs.
- Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
- Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.


# Task
Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding.
Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection.
Follow this step-by-step inquiry process:

1. Help me understand my report, highlighting areas for deeper exploration.
2. Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
3. Personalize our conversation based on the insights gained, focusing on understanding before advising.
4. Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

## Specifics
This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

Specifically, I'm looking for:
1. Comprehensive insights into my personality traits.
2. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
3. Assistance in recognizing and addressing behaviors that may be limiting my growth.
4. Guidance on leveraging Enneagram insights for personal development
5. Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

# Communication Style
Adopt my discourse particles when responding. Engage warmly and supportively, emulating the qualities of a counselor.
Keep responses in concise. Ask simple, engaging questions like "Could you share more about that?". Focus on a single question or key message per reply. Maintain a supportive tone, particularly if I express confusion or frustration.

Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate. Always speak in the first person as an Enneagram Coach. If unsure about the content's relevance or accuracy, express the need for more information instead of providing an uncertain answer.
Responses in {language}.

# Notes
- Be concise unless I ask for suggestions or details.
- Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
- Please use relatable examples or analogies to help clarify more complex Enneagram concepts.

Question: {question} \n
Chat History: {chat_history} \n
Previous Chat Summary: {chat_summary} \n
`);

  const google = new ChatGoogleGenerativeAI({
    model: "gemini-2.5-flash",
    maxOutputTokens: 2048,
    streaming: true,
    verbose: false,
  });
  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0.7,
    maxOutputTokens: 500,
    streaming: true,
  })
  /*
  const { userId } = auth(); 
  const { 
    prevMemoryMessages, 
    prevMemorySummaries 
  } = await zepMemory(
    state?.input_options?.companionId!,
    state?.input_options?.threadId!,
    userId!,
    question,
  ) ?? { prevMemoryMessages: [], prevMemorySummaries: [] }; 
  */
  const chatChain = prompt.pipe(llm).pipe(new StringOutputParser());
  const response = await chatChain.invoke(
    {
      question,
      language: state?.input_options?.aiLanguage?.label ?? 'zh-TW',
      chat_history: formatChatHistoryAsString(state?.chat_history!.slice(-6)),
      chat_summary: '' //formatChatSummaries(prevMemorySummaries),
    },
    config,
  );

  console.log("STATE from normalChat: ", state);

  return {
    result: response as string,
  };
}

const saveCompletion = async (state: AgentExecutorState): Promise<Partial<AgentExecutorState>> => {
//const saveCompletion = async (state: AgentExecutorState) => {
  const toolCallName = state?.toolCall?.name
  console.log("---SAVE COMPLETION---");
  if ((state?.result) || (toolCallName && toolCallName?.length > 0 )) {
    const createdAt = new Date()
    const user = await currentUser(); 
    let result: string = ''
    if (toolCallName === 'Summarizer') {
      result = state?.toolResult?.summarizeResult!
    } else {
      result = state?.result!
    }
  
    if (user && result) {
      const companionId = state?.input_options?.companionId!
      const threadId = state?.input_options?.threadId!
      const path = `/ichat/${companionId}/${threadId}`

      const firstMessageContent = state.input as string
      const title = firstMessageContent.substring(0, 50)

      const chat: Chat = {
        id: threadId,
        title,
        companionId,
        userId: user?.id!,
        createdAt,
        messages: [],
        path
      }
      //console.log("chat: ", chat)

      await saveChat(chat)
      /*await zepClient.memory.add(state?.input_options?.threadId!, {
        messages: [
          { role: "assistant", roleType: "assistant", content: result }
        ]
      });*/
    }
  }
  return {}
 };

export function agentExecutor() {
  const workflow = new StateGraph<AgentExecutorState>({
    channels: {
      input: null,
      input_options: null,
      messages: null,
      chat_history: null,
      result: null,
      toolCall: null,
      toolResult: null,
    },
  })
    .addNode("invokeModel", invokeModel)
    .addNode("invokeTools", invokeTools)
    //.addNode("gradeDocuments", gradeDocuments)
    .addNode("generate", generate)
    .addNode("normalChat", normalChat)
    //.addNode("saveCompletion", saveCompletion)
    .addConditionalEdges("invokeModel", invokeToolsOrReturn)
    .addConditionalEdges(
      // First, we define the start node. We use `agent`.
      // This means these are the edges taken after the `agent` node is called.
      "invokeTools",
      // Next, we pass in the function that will determine which node is called next.
      shouldContinue,
        {
        // If `tools`, then we call the tool node.
        continue: "generate", //"gradeDocuments",
        // Otherwise we finish.
        end: "normalChat" //END //"saveCompletion" 
      }
    )
    /*.addConditionalEdges(
      "gradeDocuments",
      // Assess agent decision
      checkRelevance,
      {
        // Call tool node
        yes: "generate",
        no: "normalChat"
      }
    )*/
    .addEdge(START, "invokeModel")
    //.addEdge("generate", "saveCompletion")
    //.addEdge("normalChat", "saveCompletion")
    //.addEdge("saveCompletion", END)
    .addEdge("generate", END)
    .addEdge("normalChat", END)

  const graph = workflow.compile();
  return graph;
}
