import {
  type LangGraphRunnableConfig,
} from "@langchain/langgraph";
import { SqlGraphAnnotation, EmbeddingConfig } from "./state";
import { END, Send, START, StateGraph } from "@langchain/langgraph";
import { InMemoryStore } from "@langchain/langgraph";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { currentState } from "./nodes/currentState"
import { genEmbeddings } from "./nodes/embeddings"
import { callModel } from "./nodes/generate"

const inMemoryStore = new InMemoryStore();

const routeNode = (
  state: typeof SqlGraphAnnotation.State,
  config: LangGraphRunnableConfig
): Send => {
  const { chatContents, observations }: EmbeddingConfig = state.embeddingState;
  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;
  const sends: Send[] = [];

  try {
    if (
      chatContents === "isNotReady" ||
      observations === "isNotReady"
    ) {
      sends.push(new Send("genEmbeddings", { ...state }));
    }

    if (sends.length === 0) {
      sends.push(new Send("generate", { ...state }));
    }

    // Ensure returns Send[]
    return sends[0];
  } catch (error) {
    console.error(`Error in routeNode: ${error}`);
    // Return empty array or a default send in case of error
    return new Send("generate", { ...state })
  }
};


const workflow = new StateGraph(SqlGraphAnnotation)
  // Start node & edge
  .addNode("currentState", currentState)
  .addNode("routeNode", routeNode)
  .addNode("genEmbeddings", genEmbeddings)
  .addNode("generate", callModel)
  .addEdge(START, "currentState")
  .addEdge("currentState", "routeNode")
  .addConditionalEdges("routeNode", routeNode, [
    "genEmbeddings",
  ])
  .addEdge("genEmbeddings", "generate")
  .addEdge("generate", END);

export async function createGraph() {
  //await checkpointer.setup()

  const graph = workflow.compile({
    //checkpointer,
    store: inMemoryStore
  });
  return graph;
}