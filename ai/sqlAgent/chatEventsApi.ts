"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import { type AIMessageChunk, 
  RemoveMessage, BaseMessage } from "@langchain/core/messages";
import { type ToolCallChunk } from "@langchain/core/messages/tool";
import { StreamMode } from "@/components/langgraph/Settings";
import { ThreadState } from "@/components/langgraph/Schema";
import { createGraph } from "./index";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts"
import { INTERVIEW_PROMPT_V2 } from "./prompts"

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph.getState(config)).values.messages;
  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  targetUserId?: string;
  targetThreadId?: string;
  targetCompanionId?: string;
  systemInstructions: string;
  language: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      messages: 
        {
          role: "user",
          content: params.message,
        },
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 8,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      target_user_id: params.targetUserId,
      target_thread_id: params.targetThreadId,
      target_companion_id: params.targetCompanionId,
      system_prompt: INTERVIEW_PROMPT_V2,
      language: params.language,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
    },
    version: "v2" as "v2",
    encoding: undefined,
  };

  const stream = createStreamableValue();
  let aiMessageId: string | null = null;

  (async () => {
    const graph = await createGraph()
    //console.log("graph: ", graph)
    //const state = await graph.getState(config);
    //console.log("state: ", state)
    const eventStream = await graph.streamEvents(input, config);

    for await (const { event, tags, data } of eventStream) {
      //console.log("================================================================= event: =================================================================", event)
      //console.log("================================================================= data: =================================================================", data)
      if (event === "on_chat_model_stream") {
        // AI message chunks - stream token by token
        if (!aiMessageId) {
          aiMessageId = crypto.randomUUID();
        }
        // Intermediate chat model generations will contain tool calls and no content
        const message = data.chunk as AIMessageChunk;
        if (typeof message.content === "string" ? message.content.trim() : "" !== "") { // Prevent update for empty content
          //console.log("aiMessage====================>", message);
          stream.update({
            messages: [{
              id: aiMessageId,
              sender: message.getType(), //'ai'
              text: message.content,
              isPartial: true  // Flag to indicate this is a partial message
            }]
          });
        }
      
      } else if (event === "on_chat_model_end") {
        const message = data?.output
        if (message?.tool_call_chunks?.length) {
          // Tool calls
          stream.update({
            messages: [{
              id: message.id,
              sender: message.getType(), //'ai'
              toolCalls: message.tool_call_chunks.map((call: ToolCallChunk) => ({
                id: call.id,
                name: call.name,
                args: call.args,
                result: undefined,
              }))
            }]
          })
        }
      } else if (event === "on_tool_end") {
        const message = data?.output
        if (message?.content?.length) {
          // ToolMessage  
          stream.update({
            messages: [{
              id: message?.id || crypto.randomUUID(),
              sender: message.getType(), //'tool',
              toolCalls: [{
                id: message.tool_call_id,
                name: message.name,
                args: "",
                result: message.content,
              }]
            }]
          });
        }
      } else {
        //console.log("Unknown event type:", event);
      }
    }
    
    stream.done();
  })();
  
  return { output: stream.value };
}