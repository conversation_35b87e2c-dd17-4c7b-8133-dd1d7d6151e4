import { Message, ChatSummary, Observations } from '@prisma/client';
import { DocumentInterface } from "@langchain/core/documents";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import { Document } from "@langchain/core/documents";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
  RunnablePassthrough,
  RunnableSequence,
} from "@langchain/core/runnables";
import { CohereEmbeddings } from "@langchain/cohere";
//import { HuggingFaceTransformersEmbeddings } from '@/lib/embeddings/huggingfaceTransformer';
import { codeBlock } from 'common-tags';
import { z } from 'zod/v3';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true,
  verbose: true
})

const llm__ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const datasource = new DataSource({
  type: "postgres",
  url: "postgresql://comfyminds_owner:<EMAIL>/comfyminds?sslmode=require&connect_timeout=15&connection_limit=20&pool_timeout=15"//process.env.NEON_DATABASE_URL,
});
//const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 4,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);

// Global cache for vector stores
const userVectorStores: { [target_thread_id: string]: MemoryVectorStore } = {};

// Create a function to get or create a vector store
async function getOrCreateVectorStore(target_thread_id: string): Promise<MemoryVectorStore> {
  if (!userVectorStores[target_thread_id]) {
    // Create a new vector store if it doesn't exist
    const embeddings = new CohereEmbeddings({ 
      model: "embed-multilingual-light-v3.0" 
    });
    //const embeddings = new HuggingFaceTransformersEmbeddings({
    //  model: 'Xenova/bert-base-multilingual-uncased'
    //});
    
    userVectorStores[target_thread_id] = new MemoryVectorStore(embeddings);
  }
  
  return userVectorStores[target_thread_id];
}

async function retrieveContextForQuestion(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  question: string,
  limit: number = 30,
  forceRefresh: boolean = false
) {
  const filterMetadata = { 
    target_user_id: target_user_id, 
    target_thread_id: target_thread_id 
  };

  // Get or create the vector store
  let vectorStore = await getOrCreateVectorStore(target_thread_id);

  // Check if vector store already has content and doesn't need refresh
  if (!forceRefresh && vectorStore.memoryVectors.length > 0) {
    const retriever = vectorStore.asRetriever({ 
      k: 5, 
      filter: (doc) => 
        doc.metadata.target_user_id === target_user_id && 
        doc.metadata.target_thread_id === target_thread_id 
    });
    
    const similarContents = await retriever.invoke(question);
    return similarContents.map(doc => doc.pageContent);
  }

  // If no existing vector store or force refresh, fetch from database
  const rawContentQuery = `
    SELECT m.content
    FROM "Thread" t
    JOIN "Message" m ON t.id = m."threadId"
    WHERE t.id = '${target_thread_id}'
    AND t."userId" = '${target_user_id}'
    ORDER BY m."updatedAt" DESC
    LIMIT ${limit}
  `;

  const rawSummaryQuery = `
  SELECT c.content
  FROM "Thread" t
  JOIN "ChatSummary" c ON t.id = c."threadId"
  WHERE t.id = '${target_thread_id}'
  AND t."userId" = '${target_user_id}'
  ORDER BY c."createdAt" DESC
  LIMIT ${limit}
`;

  const [rawContents, rawSummaries] = await Promise.all([
    db.run(rawContentQuery).then(JSON.parse),
    db.run(rawSummaryQuery).then(JSON.parse),
  ]);

  // Extract content strings in parallel
  const contentProcessing = Promise.resolve(
    rawContents.map((item: Message | string) =>
      typeof item === "object" ? item.content : item
    )
  );

  const summaryProcessing = Promise.resolve(
    rawSummaries.map((item: ChatSummary | string) =>
      typeof item === "object" ? item.content : item
    )
  );

  // Create documents for vector store
  const documentsProcessing = Promise.all([
    contentProcessing,
    summaryProcessing,
  ]).then(([contents, summaries]) =>
    [...contents, ...summaries].map(
      content =>
        new Document({
          pageContent: content,
          metadata: {
            ...filterMetadata,
            timestamp: new Date().toISOString(),
          },
        })
    )
  );

  // Add documents to vector store
  try {
    await documentsProcessing.then(documents =>
      vectorStore.addDocuments(documents)
    );
  } catch (error) {
    console.error("Error adding documents to vector store:", error);
  }

  // Cache the updated vector store
  userVectorStores[target_thread_id] = vectorStore;

  // Perform similarity search
  const retriever = vectorStore.asRetriever({ 
    k: 5, 
    filter: (doc) => 
      doc.metadata.target_user_id === target_user_id && 
      doc.metadata.target_thread_id === target_thread_id 
  });

  const similarContents = await retriever.invoke(question);
  return similarContents.map(doc => doc.pageContent);
}

async function updateUserVectorStore(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  newMessages: string[] = []
) {
  // If there are new messages, add them to the existing vector store
  if (newMessages.length > 0) {
    const vectorStore = await getOrCreateVectorStore(target_thread_id);
    
    // Use addDocuments instead of addTexts
    await vectorStore.addDocuments(
      newMessages.map(message => new Document({
        pageContent: message,
        metadata: {
          target_user_id: target_user_id,
          target_thread_id: target_thread_id,
          timestamp: new Date().toISOString()
        }
      }))
    );
  } else {
    // Force a complete refresh
    await retrieveContextForQuestion(
      db, 
      target_user_id, 
      target_thread_id, 
      '', 
      20, 
      true
    );
  }
}

// Function to execute user queries
async function executeUserQuery(
  question: string, 
  target_user_id: string, 
  target_thread_id: string,
  language: string,
) {
  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });

  const chain = RunnableSequence.from([
    async (input: { question: string }) => {
      // Retrieve similar context
      const similarContents = await retrieveContextForQuestion(
        db,
        target_user_id,
        target_thread_id,
        input.question
      );
      
      console.log("similarContents==============>", similarContents);

      // Create prompt template here
      const discussPrompt = ChatPromptTemplate.fromMessages([
        ["system", codeBlock`
          You are a helpful assistant. Use the following context to answer the user's question.
          If the context doesn't directly answer the question, response with "User did not mention about that" in ${language}
          and then provide a helpful response based on the available information in ${language}.
          
          Previous conversation context:
          ${similarContents.join('\n\n')}
        `],
        ["human", "{input}"]
      ]);

      return {
        context: similarContents,
        discussPrompt,
        input: input.question
      };
    },
    // Use LLM to generate response
    async (input) => {
      // Create the chain using the discussPrompt from the previous step
      const responseChain = input.discussPrompt.pipe(llm);
      
      // Invoke the chain with the input
      const response = await responseChain.invoke({
        input: input.input
      });

      console.log("response=>>>>>>>>>>>>>>>>>>>", response);
      
      // Return the content of the response
      return response.content;
    }
  ]);

  return await chain.invoke({ question });
}

const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id;
  const target_thread_id = configurable?.target_thread_id;
  const language = configurable?.language!;
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const info = memories.map((d) => d.value.data).join("\n");
  const systemMsg = `You are a helpful assistant talking to the user. User info: ${info}`;
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)

  /*const llmWithTools = llm.bindTools(tools);
  const result = await llmWithTools.invoke([
    { type: "system", content: systemMsg },
    ...messages,
  ]);*/

  if (target_user_id && target_thread_id) {
    try {
      const sqlResult = await executeUserQuery(question, target_user_id, target_thread_id, language)
      // To add new messages to existing vector store
      //await updateUserVectorStore(db, userId, ["New message about Hornevian groups"]);
        
      console.log(`Got sql output====================>>>> ${sqlResult}`);
      //return { messages: [sqlResult.content] };
      if (sqlResult) return { messages: [sqlResult] };
    } catch (error) {
      console.log("Errors: ", error)
    }    
  }

  return { messages: []}  
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END]);

export async function createGraph() {
  //await checkpointer.setup()

  const graph = workflow.compile({
    //checkpointer,
    store: inMemoryStore
  });
  return graph;
}
