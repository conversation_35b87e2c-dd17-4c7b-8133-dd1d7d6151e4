import { Annotation, MessagesAnnotation } from "@langchain/langgraph";

export type ReadyStatus = "isNotReady" | "isReady";

export type EmbeddingConfig = {
  chatContents?: ReadyStatus;
  observations?: ReadyStatus;
};

export const EmbeddingState = Annotation.Root({
  embeddingState: Annotation<Record<string, EmbeddingConfig>>({
    reducer: (_, action) => action,
    default: () => ({
      embeddingState: {
        chatContents: "isNotReady",
        observations: "isNotReady"
      }
    })
  })
})
export const SqlGraphAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  ...EmbeddingState.spec,
  targets: Annotation<Record<string, any>>({
    reducer: (_, action) => action,
    default: () => ({
      targetCompanion: "",
      targetThread: "",
      targetUser: ""
    })
  }),
  //next: Annotation<string | undefined>,
});

export type SqlGraphReturnType = Partial<
  typeof SqlGraphAnnotation.State
>;