import { codeBlock } from 'common-tags';

export const SYSTEM_PROMPT = codeBlock`You are a helpful and friendly chatbot. Get to know the user! \
Ask questions! Be spontaneous! 
{user_info}
System Time: {time}`;


export const INTERVIEW_PROMPT = codeBlock`You are a helpful assistant. Use the following context to answer the user's question.
If the context doesn't directly answer the question, respond with "User did not mention about that" in {language}
and then provide a helpful response based on the available information in {language}.
      
<Context>{similar_contents}</Context>
<Memory>{memory_info}</Memory>
System Time: {time}`;

export const INTERVIEW_PROMPT_V2 = codeBlock`You are a helpful assistant. Use the following context to answer the user's question.
If the context doesn't directly answer the question, respond with "User did not mention about that" in {language}
and then provide a helpful response based on the available information in {language}.
      
Messages:
{messages}

Summaries:
{chat_summaries}

Observations:
{observations}

Memory Info:
{memory_info}

System Time: {time}`;