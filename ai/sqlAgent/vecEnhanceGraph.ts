import { Message, ChatSummary, Observations } from '@prisma/client';
import { DocumentInterface } from "@langchain/core/documents";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, BaseMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import { Document } from "@langchain/core/documents";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
  RunnablePassthrough,
  RunnableSequence,
} from "@langchain/core/runnables";
import { CohereEmbeddings } from "@langchain/cohere";
import { getModelMessages } from "@/ai/utils/model_messages"
import { z } from 'zod/v3';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true,
  verbose: true
})

const llm__ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const datasource = new DataSource({
  type: "postgres",
  url: "postgresql://comfyminds_owner:<EMAIL>/comfyminds?sslmode=require&connect_timeout=15&connection_limit=20&pool_timeout=15"//process.env.NEON_DATABASE_URL,
});
const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 4,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);

// Global cache for vector stores
const userVectorStores: { [target_thread_id: string]: MemoryVectorStore } = {};

// Create a function to get or create a vector store
async function getOrCreateVectorStore(target_thread_id: string): Promise<MemoryVectorStore> {
  if (!userVectorStores[target_thread_id]) {
    // Create a new vector store if it doesn't exist
    const embeddings = new CohereEmbeddings({ 
      model: "embed-multilingual-light-v3.0" 
    });
    
    userVectorStores[target_thread_id] = new MemoryVectorStore(embeddings);
  }
  
  return userVectorStores[target_thread_id];
}

const fetchData = async (
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  target_companion_id: string,
  limit: number = 30,
) => {
  try {
    const fetchMessages = async () => {
      const rawMessageQuery = `
        SELECT 
          m.role,
          m.content
        FROM "Thread" t
        JOIN "Message" m ON t.id = m."threadId"
        WHERE t.id = '${target_thread_id}'
        AND t."userId" = '${target_user_id}'
        ORDER BY m."updatedAt" DESC
        LIMIT ${limit}
      `;
    
      const result = await db.run(rawMessageQuery);
      
      // Parse the JSON string to an array
      return JSON.parse(result);
    };

    const fetchSummaries = async () => {
      const rawSummaryQuery = `
        SELECT c.content
        FROM "Thread" t
        JOIN "ChatSummary" c ON t.id = c."threadId"
        WHERE t.id = '${target_thread_id}'
        AND t."userId" = '${target_user_id}'
        ORDER BY c."createdAt" DESC
        LIMIT ${limit}
      `;
      const result = await db.run(rawSummaryQuery);
      
      // Parse the JSON string to an array
      return JSON.parse(result);
    }

    const fetchObservations = async () => {
      const rawObservationQuery = `
        SELECT
          o.message,
          o."createdAt"
        FROM "Observations" o
        WHERE o."userId" = '${target_user_id}'
        AND o."companionId" = '${target_companion_id}'
        ORDER BY o."createdAt" DESC
        LIMIT ${limit};
      `;
      const result = await db.run(rawObservationQuery);
      
      // Parse the JSON string to an array
      return JSON.parse(result);
    };

    const [messageData, summaryData, observationData] = await Promise.all([
      fetchMessages(),
      fetchSummaries(),
      fetchObservations(),
    ]);
    console.log({"messageData": messageData,"summaryData": summaryData, "observationData": observationData})

    // Process messages and summaries
    const messages = messageData
    .map((row: any) => {
      const role = row.role || "unknown";
      const content = row.content;
      return { role, content };
    });

    const summaries = summaryData
    .map((row: any) => {
      const content = row.content;
      return { content };
    });

    const observations = observationData
    .map((row: any) => {
      const message = row.message;
      const createdAt = row.createdAt;
      return { message, createdAt };
    });

    console.log({"messages": messages,  "summaries": summaries, "observations": observations})
    return { messages, summaries, observations };
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
};

async function retrieveContextForQuestion(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  target_companion_id: string,
  question: string,
  limit: number = 50,
  forceRefresh: boolean = false
) {
  const filterMetadata = { 
    target_user_id: target_user_id, 
    target_thread_id: target_thread_id 
  };

  // Get or create the vector store
  let vectorStore = await getOrCreateVectorStore(target_thread_id);

  // Check if vector store already has content and doesn't need refresh
  if (!forceRefresh && vectorStore.memoryVectors.length > 0) {
    const retriever = vectorStore.asRetriever({ 
      k: 3, 
      filter: (doc) => 
        doc.metadata.target_user_id === target_user_id && 
        doc.metadata.target_thread_id === target_thread_id 
    });
    
    const similarContents = await retriever.invoke(question);

    // Map retrieved documents to include both pageContent and context
    const processedContents = similarContents.map((doc) => {
      const { pageContent, metadata } = doc;
      if (metadata.context) {
        return {
          type: "message",
          content: pageContent,
          metadata,
        };
      }
      // For summaries and observations: Return pageContent only
      return {
        type: metadata.type || "unknown", // Fallback type
        content: pageContent,
        metadata,
      };
    });
      
    return processedContents;
    //return similarContents.map(doc => doc.pageContent);
  }

  // If no existing vector store or force refresh, fetch from database
  const { messages, summaries, observations } = await fetchData(
    db,
    target_user_id,
    target_thread_id,
    target_companion_id,
  );

  // Extract content strings in parallel
  const chunkMessages = (messages: Partial<Message>[], contextSize: number = 2) => {
    const chunks = [];
    for (let i = 0; i < messages.length; i++) {
        // Ensure role and content are defined
        const currentMessage = messages[i];
        const pageContent = `${currentMessage.role}: ${currentMessage.content}`;
    
        // Build the context string
        const context = messages
          .slice(Math.max(0, i - contextSize), i) // Include previous `contextSize` messages
          .map((item) => `${item.role}: ${item.content}`)
          .join("\n");
    
        // Add the chunk
        chunks.push({
          pageContent,
          metadata: {
            context,
            messageIndex: i,
            timestamp: new Date().toISOString(),
          },
        });
      }
      return chunks;
    };
 
  
  // Process messages into chunks with context
  const contentProcessing = Promise.resolve(chunkMessages(messages));

  const summaryProcessing = Promise.resolve(
    summaries.map((item: ChatSummary | string) =>
      typeof item === "object" ? item.content : item
    )
  );

  const observationProcessing = Promise.resolve(
    observations.map((item: Observations | string) =>
      typeof item === "object" ? item.message : item
    )
  );

  // Create documents for vector store
  const documentsProcessing = Promise.all([
    contentProcessing,
    summaryProcessing,
    observationProcessing,
  ]).then(([messageChunks, summaries, observations]) => {
    const messageDocuments = messageChunks.map((chunk) =>
      new Document({
        pageContent: chunk.pageContent,
        metadata: {
          ...chunk.metadata,
          type: "message",
          target_companion_id: target_companion_id,
          target_thread_id: target_thread_id,
          target_user_id: target_user_id
        },
      })
    );
  
    const summaryDocuments = summaries.map((content: string) =>
      new Document({
        pageContent: content,
        metadata: {
          type: "summary",
          ...filterMetadata,
          timestamp: new Date().toISOString(),
        },
      })
    );
  
    const observationDocuments = observations.map((content: string) =>
      new Document({
        pageContent: content,
        metadata: {
          type: "observation",
          ...filterMetadata,
          timestamp: new Date().toISOString(),
        },
      })
    );
    console.log({"messageDocuments": JSON.stringify(messageDocuments, null, 2),  "summaryDocuments": summaryDocuments, "observationDocuments": observationDocuments})
  
    return [...messageDocuments, ...summaryDocuments, ...observationDocuments];
  });


  // Add documents to vector store
  try {
    await documentsProcessing.then(documents =>
      vectorStore.addDocuments(documents)
    );
  } catch (error) {
    console.error("Error adding documents to vector store:", error);
  }

  // Cache the updated vector store
  userVectorStores[target_thread_id] = vectorStore;

  // Perform similarity search with retriever
  const retriever = vectorStore.asRetriever({
    k: 3,
    filter: (doc) =>
      doc.metadata.target_user_id === target_user_id &&
      doc.metadata.target_thread_id === target_thread_id,
  });

  const similarContents = await retriever.invoke(question);

  // Map retrieved documents to include both pageContent and context
  const processedContents = similarContents.map((doc) => {
    const { pageContent, metadata } = doc;
    console.log("doc========================>", doc)

    if (metadata.context) {
      return {
        type: "message",
        content: pageContent,
        metadata,
      };
    }
    // For summaries and observations: Return pageContent only
    return {
      type: metadata.type || "unknown", // Fallback type
      content: pageContent,
      metadata,
    };
  });
  console.log({"similarContents": similarContents , "processedContents": processedContents})

  return processedContents;

}

async function updateUserVectorStore(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  target_companion_id: string,
  newMessages: string[] = []
) {
  // If there are new messages, add them to the existing vector store
  if (newMessages.length > 0) {
    const vectorStore = await getOrCreateVectorStore(target_thread_id);
    
    // Use addDocuments instead of addTexts
    await vectorStore.addDocuments(
      newMessages.map(message => new Document({
        pageContent: message,
        metadata: {
          target_user_id: target_user_id,
          target_thread_id: target_thread_id,
          timestamp: new Date().toISOString()
        }
      }))
    );
  } else {
    // Force a complete refresh
    await retrieveContextForQuestion(
      db, 
      target_user_id, 
      target_thread_id, 
      target_companion_id,
      '', 
      50, 
      true
    );
  }
}

// Function to execute user queries
async function executeUserQuery(
  question: string, 
  target_user_id: string, 
  target_thread_id: string,
  target_companion_id: string,
) {
  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });
  
  // Retrieve similar context
  const similarContents = await retrieveContextForQuestion(
    db,
    target_user_id,
    target_thread_id,
    target_companion_id,
    question
  );
  
  console.log("similarContents==============>", similarContents);
  return similarContents

}

const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;
  const language = configurable?.language!;
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)
 
  const prepareMessages = async () => {
    try {
      // Parallel fetching of promises
      const [similarContents, memoryInfo, modelMessages] = await Promise.all([
        // Fetch similar contents if target_user_id or target_thread_id exist
        target_user_id && target_thread_id
          ? executeUserQuery(question, target_user_id, target_thread_id, target_companion_id)
              .then((result) => {
                // Convert array of similar contents to a string
                const formattedContents = result.map((item) => {
                  if (typeof item === "object" && item !== null) {
                    // Access metadata and content if item is an object
                    return item.metadata?.context || item.content || "";
                  }
                  return item || ""; // Fallback for strings
                }).join('\n\n');
                
                console.log(`Got SQL output====================>>>> `, formattedContents);
                return formattedContents;
              })
              .catch((error) => {
                console.error("Errors fetching similar contents:", error);
                return ""; // Fallback to empty string on error
              })
          : "",
        
        // Prepare memory info
        memories.map((d) => d.value.data).join("\n"),
        
        // Fetch model messages
        getModelMessages(state.messages),
      ]);
  
      const systemMsg = configurable.system_prompt
        .replace("{language}", language)
        .replace("{language}", language)
        .replace("{similar_contents}", similarContents)
        .replace("{memory_info}", memoryInfo ? memoryInfo : "")
        .replace("{time}", new Date().toISOString());
  
      return { systemMsg, modelMessages };
    } catch (error) {
      console.error("Error preparing messages:", error);
      throw error; // Rethrow the error to let the caller handle it
    }
  };
  
  try {
    const { systemMsg, modelMessages } = await prepareMessages();
  
    const result = await llm.invoke([
      { type: "system", content: systemMsg },
      ...modelMessages,
    ]);
  
    return { messages: [result] };
  } catch (error) {
    console.error("Error invoking LLM:", error);
    return { messages: [] }; // Return a fallback or empty result
  }
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END])

export async function createGraph() {
  await checkpointer.setup()

  const graph = workflow.compile({
    checkpointer,
    store: inMemoryStore
  });
  return graph;
}
