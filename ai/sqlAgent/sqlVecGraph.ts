import { Message, ChatSummary, Observations } from '@prisma/client';
import { DocumentInterface } from "@langchain/core/documents";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
//import { getVectorStore } from "@/lib/vectorstores/astradb";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import {
  AstraDBVectorStore,
  AstraLibArgs,
} from "@langchain/community/vectorstores/astradb";
import { Document } from "@langchain/core/documents";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
  RunnablePassthrough,
  RunnableSequence,
} from "@langchain/core/runnables";
import { CohereEmbeddings } from "@langchain/cohere";
import { getModelMessages } from "@/ai/utils/model_messages"
import { z } from 'zod/v3';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true,
  verbose: true
})

const llm__ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const datasource = new DataSource({
  type: "postgres",
  url: "postgresql://comfyminds_owner:<EMAIL>/comfyminds?sslmode=require&connect_timeout=15&connection_limit=20&pool_timeout=15"//process.env.NEON_DATABASE_URL,
});
const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 4,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);

// Global cache for vector stores
const userVectorStores: { [target_thread_id: string]: MemoryVectorStore } = {};

// Create a function to get or create a vector store
async function getOrCreateVectorStore(target_thread_id: string): Promise<MemoryVectorStore> {
  if (!userVectorStores[target_thread_id]) {
    // Create a new vector store if it doesn't exist
    const embeddings = new CohereEmbeddings({ 
      model: "embed-multilingual-light-v3.0" 
    });
    //const astraVectorStore = await getVectorStore({embeddings, dimension: 384});
    userVectorStores[target_thread_id] = new MemoryVectorStore(embeddings);
  }
  
  return userVectorStores[target_thread_id];
}

async function retrieveContextForQuestion(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  target_companion_id: string,
  question: string,
  limit: number = 50,
  forceRefresh: boolean = false
) {
  const filterMetadata = { 
    target_user_id: target_user_id, 
    target_thread_id: target_thread_id 
  };

  // Get or create the vector store
  let vectorStore = await getOrCreateVectorStore(target_thread_id);

  // Check if vector store already has content and doesn't need refresh
  if (!forceRefresh && vectorStore.memoryVectors.length > 0) {
    console.log("========================> forceRefresh")
    const retriever = vectorStore.asRetriever({ 
      k: 3, 
      filter: (doc) => 
        doc.metadata.target_user_id === target_user_id && 
        doc.metadata.target_thread_id === target_thread_id 
    });
    
    const similarContents = await retriever.invoke(question);
    return similarContents.map(doc => doc.pageContent);
  }

  // If no existing vector store or force refresh, fetch from database
  const rawContentQuery = `
    SELECT 
      m.role,
      m.content
    FROM "Thread" t
    JOIN "Message" m ON t.id = m."threadId"
    WHERE t.id = '${target_thread_id}'
    AND t."userId" = '${target_user_id}'
    ORDER BY m."updatedAt" DESC
    LIMIT ${limit}
  `;

  const rawSummaryQuery = `
    SELECT c.content
    FROM "Thread" t
    JOIN "ChatSummary" c ON t.id = c."threadId"
    WHERE t.id = '${target_thread_id}'
    AND t."userId" = '${target_user_id}'
    ORDER BY c."createdAt" DESC
    LIMIT ${limit}
  `;

  const rawObservationQuery = `
    SELECT
      o.message,
      o."createdAt"
    FROM "Observations" o
    WHERE o."userId" = '${target_user_id}'
    AND o."companionId" = '${target_companion_id}'
    ORDER BY o."createdAt" DESC
    LIMIT ${limit};
  `;

  const [rawContents, rawSummaries, rawObservations] = await Promise.all([
    db.run(rawContentQuery).then(JSON.parse),
    db.run(rawSummaryQuery).then(JSON.parse),
    db.run(rawObservationQuery).then(JSON.parse),
  ]);

  // Extract content strings with role
  const contentProcessing = Promise.resolve(
    rawContents.map((item: Message | string) => {
      if (typeof item === "object") {
        // Combine role and content
        return item.role ? `${item.role}: ${item.content}` : item.content;
      }
      return item;
    })
  );

  const summaryProcessing = Promise.resolve(
    rawSummaries.map((item: ChatSummary | string) =>
      typeof item === "object" ? item.content : item
    )
  );

  const observationProcessing = Promise.resolve(
    rawObservations.map((item: Observations | string) =>
      typeof item === "object" ? item.message : item
    )
  );

  console.log({"contentProcessing": contentProcessing, "summaryProcessing": summaryProcessing, "observationProcessing": observationProcessing})
  // Create documents for vector store
  const documentsProcessing = Promise.all([
    contentProcessing,
    summaryProcessing,
    observationProcessing,
  ]).then(([contents, summaries, observations]) =>
    [...contents, ...summaries, ...observations].map(
      content =>
        new Document({
          pageContent: content,
          metadata: {
            ...filterMetadata,
            timestamp: new Date().toISOString(),
          },
        })
    )
  );

  // Add documents to vector store
  try {
    await documentsProcessing.then(documents =>
      vectorStore.addDocuments(documents)
    );
  } catch (error) {
    console.error("Error adding documents to vector store:", error);
  }

  // Cache the updated vector store
  userVectorStores[target_thread_id] = vectorStore;

  // Perform similarity search
  const retriever = vectorStore.asRetriever({ 
    k: 3, 
    filter: (doc) => 
      doc.metadata.target_user_id === target_user_id && 
      doc.metadata.target_thread_id === target_thread_id 
  });

  const similarContents = await retriever.invoke(question);
  return similarContents.map(doc => doc.pageContent);
}

async function updateUserVectorStore(
  db: SqlDatabase,
  target_user_id: string,
  target_thread_id: string,
  target_companion_id: string,
  newMessages: string[] = []
) {
  // If there are new messages, add them to the existing vector store
  if (newMessages.length > 0) {
    const vectorStore = await getOrCreateVectorStore(target_thread_id);
    
    // Use addDocuments instead of addTexts
    await vectorStore.addDocuments(
      newMessages.map(message => new Document({
        pageContent: message,
        metadata: {
          target_user_id: target_user_id,
          target_thread_id: target_thread_id,
          target_companion_id: target_companion_id,
          timestamp: new Date().toISOString()
        }
      }))
    );
  } else {
    // Force a complete refresh
    await retrieveContextForQuestion(
      db, 
      target_user_id, 
      target_thread_id, 
      target_companion_id,
      '', 
      50, 
      true
    );
  }
}

// Function to execute user queries
async function executeUserQuery(
  question: string, 
  target_user_id: string, 
  target_thread_id: string,
  target_companion_id: string,
) {
  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });
  
  // Retrieve similar context
  const similarContents = await retrieveContextForQuestion(
    db,
    target_user_id,
    target_thread_id,
    target_companion_id,
    question
  );
  
  console.log("similarContents==============>", similarContents);
  return similarContents

}

const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;
  const language = configurable?.language!;
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)
 
  const prepareMessages = async () => {
    try {
      // Parallel fetching of promises
      const [similarContents, memoryInfo, modelMessages] = await Promise.all([
        // Fetch similar contents if target_user_id or target_thread_id exist
        target_user_id && target_thread_id
          ? executeUserQuery(question, target_user_id, target_thread_id, target_companion_id)
              .then((result) => {
                console.log(`Got SQL output====================>>>> ${result}`);
                return `${result}` 
              })
              .catch((error) => {
                console.error("Errors fetching similar contents:", error);
                return ""; // Fallback to empty string on error
              })
          : "",
        // Prepare memory info
        memories.map((d) => d.value.data).join("\n"),
        // Fetch model messages
        getModelMessages(state.messages),
      ]);
  
      const systemMsg = configurable.system_prompt
        .replace("{language}", language)
        .replace("{language}", language)
        .replace("{similar_contents}", similarContents)
        .replace("{memory_info}", memoryInfo ? memoryInfo : "")
        .replace("{time}", new Date().toISOString())  
      //console.log(`ModelMessages====================>>>>`,JSON.stringify(modelMessages, null, 2));
  
      return { systemMsg, modelMessages };
    } catch (error) {
      console.error("Error preparing messages:", error);
      throw error; // Rethrow the error to let the caller handle it
    }
  };
  
  try {
    const { systemMsg, modelMessages } = await prepareMessages();
    console.log(`systemMsg====================>>>>`,JSON.stringify(systemMsg, null, 2));
    const result = await llm.invoke([
      { type: "system", content: systemMsg },
      ...modelMessages,
    ]);
  
    return { messages: [result] };
  } catch (error) {
    console.error("Error invoking LLM:", error);
    return { messages: [] }; // Return a fallback or empty result
  }
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END])

export async function createGraph() {
  await checkpointer.setup()

  const graph = workflow.compile({
    checkpointer,
    store: inMemoryStore
  });
  return graph;
}
