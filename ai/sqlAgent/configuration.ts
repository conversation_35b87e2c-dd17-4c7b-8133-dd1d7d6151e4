// Define the configurable parameters for the agent

import { Annotation, LangGraphRunnableConfig } from "@langchain/langgraph";
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { INTERVIEW_PROMPT_V2 } from "./prompts";
import { v4 as uuidv4 } from 'uuid';

export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>(),
  assistant_id: Annotation<string>(),
  thread_id: Annotation<string>(),
  target_user_id: Annotation<string | undefined>(),
  target_thread_id: Annotation<string | undefined>(),
  target_companion_id: Annotation<string | undefined>(),
  system_prompt: Annotation<string>(),
  language: Annotation<string>(),
  schemas: Annotation<Record<string, MemoryConfig>>(),
  model: Annotation<string | undefined>(),
  delay: Annotation<number | undefined>(),
});

export type Configuration = typeof ConfigurationAnnotation.State;


export function ensureConfiguration(config?: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    system_prompt: configurable?.system_prompt || INTERVIEW_PROMPT_V2,
    language: configurable?.language || "English", 
    target_user_id: configurable?.target_user_id!,
    target_thread_id: configurable?.target_thread_id!,
    target_companion_id: configurable?.target_companion_id!,
    schemas: configurable?.schemas || {},
    model: configurable?.model || "default-model",
    delay: configurable?.delay || 5,
  };
}