import {
  type LangGraphRunnableConfig,
} from "@langchain/langgraph";
import { SqlGraphAnnotation, EmbeddingConfig } from "../state";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "../configuration";
import { neon } from '@neondatabase/serverless';
import { getEmbedding } from "@/lib/cohere";

const queryFunction = neon(`${process.env.NEON_DATABASE_URL!!}`);

const cleanContent = (input: string): string => {
  return input
    .trim() // Remove leading/trailing spaces
    .replace(/\s+/g, ' '); // Replace multiple spaces/newlines with a single space
};

type UpdateEmbeddingsParams = {
  tableName: string;
  contentColumn?: string;
  embeddingColumn?: string;
  dimension?: number
  model?: string;
  batchSize?: number;
  filter?: Record<string, string | number>;
}
const updateEmbeddings = async ({
  tableName,
  contentColumn = "content",
  embeddingColumn = "embedding",
  dimension = 384,
  model = "embed-multilingual-light-v3.0",
  batchSize = 36,
  filter = {},
}: UpdateEmbeddingsParams): Promise<void> => {
  try {
    // Generate WHERE conditions from filter
    const filterConditions = Object.entries(filter)
      .map(([key, value]) => (typeof value === "string" ? `"${key}" = '${value}'` : `"${key}" = ${value}`))
      .join(" AND ");

    // Construct WHERE clause
    const whereClause = `${embeddingColumn} IS NULL${filterConditions ? ` AND ${filterConditions}` : ""}`;
    const quotedTableName = `"${tableName}"`;

    // Fetch records with missing embeddings
    const missingEmbeddingsQuery = `
      SELECT id, ${contentColumn}
      FROM ${quotedTableName}
      WHERE ${whereClause};
    `;

    //console.log("missingEmbeddingsQuery", missingEmbeddingsQuery);
    const missingEmbeddings = await queryFunction(missingEmbeddingsQuery);

    if (!missingEmbeddings.length) {
      console.log(`No records to process in table: ${tableName}`);
      return;
    }

    console.log(`Found ${missingEmbeddings.length} records to process in ${tableName}`);

    // Process records in batches
    for (let i = 0; i < missingEmbeddings.length; i += batchSize) {
      const batchRecords = missingEmbeddings.slice(i, i + batchSize);
      
      // Generate embeddings for the batch
      const batchContents = batchRecords.map(record => record[contentColumn]);
      const batchEmbeddings = await getEmbedding(batchContents, model) as number[][];

      // Prepare batch update
      const updatePromises = batchRecords.map(async (record, index) => {
        const { id } = record;
        const embeddingVector = batchEmbeddings[index];

        const updateQuery = `
          UPDATE ${quotedTableName}
          SET ${embeddingColumn} = '[${embeddingVector}]'::vector(${dimension})
          WHERE id = '${id}';
        `;

        try {
          await queryFunction(updateQuery);
          console.log(`Updated embedding for record ID: ${id}`);
        } catch (error) {
          console.error(`Failed to update embedding for record ID: ${id}`, error);
        }
      });

      // Wait for batch updates to complete
      await Promise.all(updatePromises);
    }
  } catch (error) {
    console.error(`Error updating embeddings in table: ${tableName}`, error);
  }
};


export const genEmbeddings = async (
  state: typeof SqlGraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  console.log("======updateEmbeddings state: =======", JSON.stringify(state), null, 2)
  const { chatContents, observations }: EmbeddingConfig = state.embeddingState;

  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;

  if (
    chatContents === "isNotReady" ||
    observations === "isNotReady"
  ) {
    await Promise.all([
      updateEmbeddings({
        tableName: "Message",
        contentColumn: "content",
        embeddingColumn: "embedding",
        filter: {
          userId: target_user_id,
          threadId: target_thread_id,
        },
      }),
      updateEmbeddings({
        tableName: "ChatSummary",
        contentColumn: "content",
        embeddingColumn: "embedding",
        filter: {
          userId: target_user_id,
          threadId: target_thread_id,
        },
      }),
      updateEmbeddings({
        tableName: "Observations",
        contentColumn: "message",
        embeddingColumn: "embedding",
        filter: {
          userId: target_user_id,
          companionId: target_companion_id,
        },
      }),
    ]);

    return {
      embeddingState: {
        chatContents: "isReady",
        observations: "isReady",
      },
    };
  }
};

