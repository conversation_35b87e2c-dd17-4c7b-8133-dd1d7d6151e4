import { LangGraphRunnableConfig } from "@langchain/langgraph";
import { SqlGraphAnnotation } from "../state";
import {
    ConfigurationAnnotation,
    ensureConfiguration,
  } from "../configuration";

  
export const currentState = async (
  state: typeof SqlGraphAnnotation.State,
  config: LangGraphRunnableConfig
  ) => {
  const { targetCompanion, targetThread, targetUser } = state.targets;
  const { chatContents, observations } = state.embeddingState;
  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;
  
  // Common checks for targetThreadId and targetUserId
  const hasCompanionMismatch = targetCompanion !== target_companion_id;
  const hasThreadMismatch = targetThread !== target_thread_id;
  const hasUserMismatch = targetUser !== target_user_id;
  
  if (
    hasCompanionMismatch ||
    hasThreadMismatch ||
    hasUserMismatch
  ) {
    return {
      embeddingState: {
        chatContents: hasThreadMismatch ? "isNotReady" : "isReady",
        observations: hasUserMismatch || hasCompanionMismatch ? "isNotReady" : "isReady"
      },
      targets: {
        targetCompanion: target_companion_id,
        targetThread: target_thread_id,
        targetUser: target_user_id
      }
    };
  }
  
  return null;
};