import { Message, ChatSummary, Observations } from '@prisma/client';
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { SqlGraphAnnotation, EmbeddingConfig } from "../state";
import { AIMessage, BaseMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "../configuration";
import { neon } from '@neondatabase/serverless';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { getEmbedding } from "@/lib/cohere";
import { getModelMessages } from "@/ai/utils/model_messages"
import { z } from 'zod/v3';
import { v4 as uuidv4 } from 'uuid';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true,
  verbose: true
})

const llm__ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const queryFunction = neon(`${process.env.NEON_DATABASE_URL!!}`);

const findSimilar = async (
  tableName: string,
  query: number[],
  columns: string[],
  k: number = 10,
  filter: Record<string, string | number> = {}
) => {
  try {
    const quotedTableName = `"${tableName}"`;

    // Convert filter to SQL conditions
    const filterConditions = Object.entries(filter)
      .map(([key, value]) => (typeof value === "string" ? `"${key}" = '${value}'` : `"${key}" = ${value}`))
      .join(" AND ");

    const whereClause = filterConditions ? `${filterConditions}` : "TRUE";

    // Prepare the vector as a properly formatted PostgreSQL array
    const formattedVector = `[${query.join(",")}]`;

    const columnList = columns.join(", ");
    const queryString = `
      SELECT id, ${columnList}
      FROM ${quotedTableName}
      WHERE ${whereClause}
      ORDER BY embedding <#> $1::vector(${query.length})
      LIMIT $2;
    `;

    const results = await queryFunction(queryString, [formattedVector, k]);

    if (!results || results.length === 0) {
      console.warn(
        `No similar records found in table ${tableName} with the given filter.`
      );
      return [];
    }

    return results;
  } catch (error: any) {
    console.error(`Error fetching similar records: ${error.message}`);
    throw error;
  }
};


const updateMemory = async (
  state: typeof SqlGraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

export const callModel = async (
  state: typeof SqlGraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { chatContents, observations }: EmbeddingConfig = state.embeddingState;
  console.log("======generate state: =======", JSON.stringify(state), null, 2)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const target_user_id = configurable?.target_user_id!;
  const target_thread_id = configurable?.target_thread_id!;
  const target_companion_id = configurable?.target_companion_id!;
  const language = configurable?.language!;
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  const queryVector = await getEmbedding(
    question,
    'embed-multilingual-light-v3.0',
  );

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================queryVector: ===============", queryVector)
 
  const prepareMessages = async () => {
    try {
      const contentsFilter = { userId: target_user_id, threadId: target_thread_id }
      const observationsFilter = { userId: target_user_id, companionId: target_companion_id }
      const [chatMessages, chatSummaries, chatObservations, memoryInfo, modelMessages] = await Promise.all([
        chatContents === "isReady" && target_user_id && target_thread_id
          ? findSimilar("Message", queryVector as number[], ["content"], 3, contentsFilter).catch((error) => {
              console.error("Error fetching similar messages:", error);
              return [];
            })
          : [],
        chatContents === "isReady" && target_user_id && target_thread_id
          ? findSimilar("ChatSummary", queryVector as number[], ["content"], 3, contentsFilter).catch((error) => {
              console.error("Error fetching similar chat summaries:", error);
              return [];
            })
          : [],
        observations === "isReady" && target_user_id && target_thread_id
          ? findSimilar("Observations", queryVector as number[], ["message"], 3, observationsFilter).catch((error) => {
              console.error("Error fetching similar observations:", error);
              return [];
            })
          : [],
        // Prepare memory info
        Promise.resolve(memories.map((d) => d.value.data).join("\n")),
        // Fetch model messages
        getModelMessages(state.messages),
      ]);
  
      // Format each content type into strings
      const formattedMessages =
        chatMessages.length > 0
          ? chatMessages
            .map((item) => (typeof item === "object" ? item.content || "" : item))
            .join("\n\n")
          : "";
        
      const formattedChatSummaries =
        chatSummaries.length > 0
          ? chatSummaries
            .map((item) => (typeof item === "object" ? item.content || "" : item))
            .join("\n\n")
          : "";
        
      const formattedObservations =
        chatObservations.length > 0
          ? chatObservations
            .map((item) => (typeof item === "object" ? item.message || "" : item))
            .join("\n\n")
          : "";
  
      // Replace placeholders in system_prompt
      const systemMsg = configurable.system_prompt
        .replace("{language}", language)
        .replace("{language}", language)
        .replace("{messages}", formattedMessages)
        .replace("{chat_summaries}", formattedChatSummaries)
        .replace("{observations}", formattedObservations)
        .replace("{memory_info}", memoryInfo || "")
        .replace("{time}", new Date().toISOString());
  
      return { systemMsg, modelMessages };
    } catch (error: any) {
      console.error("Error preparing messages:", error);
      throw error; // Rethrow the error to let the caller handle it
    }
  };
  
  try {
    const { systemMsg, modelMessages } = await prepareMessages();
  
    const result = await llm.invoke([
      { type: "system", content: systemMsg },
      ...modelMessages,
    ]);
  
    return { messages: [result] };
  } catch (error: any) {
    console.error("Error invoking LLM:", error);
    return { messages: [] };
  }
};


