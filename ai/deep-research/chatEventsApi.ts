"use server"
import { 
  RemoveMessage, BaseMessage } from "@langchain/core/messages";
import { StreamMode } from "@/components/langgraph/Settings";
import { Command } from "@langchain/langgraph";
import { createGraph } from "./graph";
import { createEventProcessor } from "@/ai/utils/createEventProcessor";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools";
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts";
import { DEFAULT_REPORT_STRUCTURE } from "./prompts";
import { deleteCheckpointsByThreadId } from '@/ai/utils/delete_checkpoints';



const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function resumeGraph(
  threadId: string,
  fields: { newState: Record<string, any>, asNode?: string },
) {
  console.log("fields.newState", fields.newState);
  const graph = await createGraph(checkpointer);
  const eventProcessor = await createEventProcessor();
  const config = { 
    configurable: { thread_id: threadId },
    version: "v2" as "v2",
    encoding: undefined, 
  }

  return eventProcessor.processGraphEvents(
    graph,
    new Command({ resume: fields.newState }),
    config
  );
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(checkpointer);
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph?.getState(config)).values?.reports

  await deleteCheckpointsByThreadId(threadId);

  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  targetUserId?: string;
  targetThreadId?: string;
  targetCompanionId?: string;
  systemInstructions: string;
  language: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      topic: params.message,
      reports: []
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 16,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
      reportStructure: params.systemInstructions ?? DEFAULT_REPORT_STRUCTURE,
    },
    version: "v2" as "v2",
    encoding: undefined,
  };

  const graph = await createGraph(checkpointer);
  const eventProcessor = await createEventProcessor();
  return eventProcessor.processGraphEvents(
    graph,
    input,
    config
  );

  /*
  const stream = createStreamableValue();
  let aiMessageId: string | null = null;

  (async () => {
    const graph = await createGraph(checkpointer);
    //console.log("graph: ", graph)
    const state = await graph.getState(config);
    //console.log("state: ", state)
    

    const eventStream = await graph.streamEvents(input, config);

    for await (const { event, tags, data, name } of eventStream) {
      console.log("================================================================= event: =================================================================", {event, name})
      console.log("================================================================= data: =================================================================", data)
      if (event === "on_chat_model_stream") {
        // AI message chunks - stream token by token
        if (!aiMessageId) {
          aiMessageId = crypto.randomUUID();
        }
        // Intermediate chat model generations will contain tool calls and no content
        const message = data.chunk as AIMessageChunk;
        if (typeof message.content === "string" ? message.content.trim() : "" !== "") { // Prevent update for empty content
          //console.log("aiMessage====================>", message);
          stream.update({
            messages: [{
              id: aiMessageId,
              sender: message.getType(), //'ai'
              text: message.content,
              isPartial: true  // Flag to indicate this is a partial message
            }]
          });
        }
      
      } else if (event === "on_chat_model_end") {
        const message = data?.output
        if (message?.tool_call_chunks?.length) {
          // Tool calls
          stream.update({
            messages: [{
              id: message.id,
              sender: message.getType(), //'ai'
              toolCalls: message.tool_call_chunks.map((call: ToolCallChunk) => ({
                id: call.id,
                name: call.name,
                args: call.args,
                result: undefined,
              }))
            }]
          })
        }
      } else if (event === "on_tool_end") {
        const message = data?.output
        if (message?.content?.length) {
          // ToolMessage  
          stream.update({
            messages: [{
              id: message?.id || crypto.randomUUID(),
              sender: message.getType(), //'tool',
              toolCalls: [{
                id: message.tool_call_id,
                name: message.name,
                args: "",
                result: message.content,
              }]
            }]
          });
        }
      } else {
        //console.log("Unknown event type:", event);
      }
    }
    
    stream.done();
  })();
  
  return { output: stream.value };
  */
}