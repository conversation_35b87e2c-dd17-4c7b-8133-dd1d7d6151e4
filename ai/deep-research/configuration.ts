import { Annotation } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { DEFAULT_REPORT_STRUCTURE } from "./prompts";
import { v4 as uuidv4 } from 'uuid';
import { Record } from "@phosphor-icons/react";

export enum SearchAPI {
  PERPLEXITY = "perplexity",
  TAVILY = "tavily",
  EXA = "exa",
  ARXIV = "arxiv",
  PUBMED = "pubmed",
  LINKUP = "linkup",
}

export enum PlannerProvider {
  ANTHROPIC = "anthropic",
  OPENAI = "openai",
  GROQ = "groq",
  OLLAMA = "ollama",
}

export enum WriterProvider {
  ANTHROPIC = "anthropic",
  OPENAI = "openai",
  GROQ = "groq",
  OLLAMA = "ollama",
}

export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>,
  assistant_id: Annotation<string>,
  thread_id: Annotation<string>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schemas: Annotation<Record<string, MemoryConfig>>,
  delay: Annotation<number | undefined>,

  report_structure: Annotation<string>({
    value: (_state, action) => action,
    default: () => DEFAULT_REPORT_STRUCTURE,
  }),

  /**
   * Number of search queries to generate per iteration
   */
  number_of_queries: Annotation<number>({
    value: (_state, action) => action,
    default: () => 2,
  }),

  /**
   * Maximum number of reflection + search iterations
   */
  max_search_depth: Annotation<number>({
    value: (_state, action) => action,
    default: () => 2,
  }),

  /**
   * The Writer LLM model to use
   */
  planner_provider: Annotation<PlannerProvider>({
    value: (_state, action) => action,
    default: () => PlannerProvider.GROQ,
  }),
  planner_model: Annotation<string>({
    value: (_state, action) => action,
    default: () => "llama3.1:latest",
  }),

  /**
   * The Writer LLM model to use
   */
  writer_provider: Annotation<WriterProvider>({
    value: (_state, action) => action,
    default: () => WriterProvider.GROQ,
  }),
  writer_model: Annotation<string>({
    value: (_state, action) => action,
    default: () => "llama3.1:latest",
  }),

  /**
   * The search API to use (Tavily or Perplexity)
   */
  search_api: Annotation<SearchAPI>({
    value: (_state, action) => action,
    default: () => SearchAPI.TAVILY,
  }),
  search_api_config: Annotation<Record<string, any> | null | undefined>({
    value: (_state, action) => action,
    default: () => null,
  })
});

export function ensureConfiguration(
  config: RunnableConfig
): typeof ConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof ConfigurationAnnotation.State>;

  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    schemas: configurable?.schemas || {},
    delay: configurable?.delay || 3,
    report_structure: configurable?.report_structure || DEFAULT_REPORT_STRUCTURE,
    number_of_queries:
      configurable.number_of_queries !== undefined
        ? configurable.number_of_queries
        : Number(process.env.NUMBER_OF_QUERIES) || 3,

    max_search_depth:
      configurable.max_search_depth !== undefined
        ? configurable.max_search_depth
        : Number(process.env.MAX_SEARCH_DEPTH) || 3,

    planner_provider:
      configurable.planner_provider ||
      (process.env.PLANNER_PROVIDER === "ollama"
        ? PlannerProvider.OLLAMA
        : PlannerProvider.GROQ),
    planner_model:
      configurable.planner_model ||
      process.env.LOCAL_LLM ||
      "llama3.1:latest",

    writer_provider:
      configurable.writer_provider ||
      (process.env.WRITER_PROVIDER === "ollama"
        ? WriterProvider.OLLAMA
        : WriterProvider.GROQ),
    writer_model:
      configurable.writer_model ||
      process.env.LOCAL_LLM ||
      "llama3.1:latest",

    search_api:
      configurable.search_api ||
      (process.env.SEARCH_API === "perplexity"
        ? SearchAPI.PERPLEXITY
        : SearchAPI.TAVILY),
    
    search_api_config:
      configurable.search_api_config || null,
  };
}
