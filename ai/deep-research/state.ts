import { Annotation } from "@langchain/langgraph";
import { FeedbackSchema, type Feedback } from "./schemas"; 
import { v4 as uuidv4 } from "uuid";

// Define types for the models
export type Section = {
  name: string;
  description: string;
  research: boolean;
  content: string;
};

export type ReportData = {
  id: string;
  topic: string;
  sections: Section[];
  report_sections_from_research: string;
  final_report: string;
};

export type SearchQuery = {
  search_query: string | null;
};

export type ReadStatus = "isNotReady" | "isReady";

// Report State Annotations
export const ReportAnnotation = Annotation.Root({
  /**
   * The topic of the report
   */
  id: Annotation<string>({
    reducer: (_, action) => action,
    default: () => uuidv4()
  }),
  topic: Annotation<string>(),

  /**
   * List of report sections
   */
  sections: Annotation<Section[]>({
    reducer: (state, action) => action,
    default: () => []
  }),

  /**
   * Completed sections of the report
  completed_sections: Annotation<Section[]>({
    reducer: (state, action) => [...state, ...action],
    default: () => []
  }),
  */

  /**
   * Sections from research to write final report
   */
  report_sections_from_research: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  }),

  /**
   * Final report content
   */
  final_report: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  })
});

export const ReportStateAnnotation = Annotation.Root({
  topic: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  }),
  feedback_on_report_plan: Annotation<string>(),
  completed_sections: Annotation<Section[]>({
    reducer: (state, action) => [...state, ...action],
    default: () => []
  }),
  reports: Annotation<(typeof ReportAnnotation.State)[]>({
    reducer: (state, action) => action,
    default: () => []
  })
});

// Section State Annotations
export const SectionStateAnnotation = Annotation.Root({
  topic: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  }),
  /**
   * Current section being processed
   */
  section: Annotation<Section>(),

  search_iterations: Annotation<number>({
    reducer: (_, action) => action
  }),

  /**
   * Search queries for the section
   */
  search_queries: Annotation<SearchQuery[]>({
    reducer: (_, action) => action,
    default: () => []
  }),

  /**
   * Formatted source content from web search
   */
  source_str: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  }),

  /**
   * Sections from research to write final sections
   */
  report_sections_from_research: Annotation<string>({
    reducer: (_, action) => action,
    default: () => ""
  }),

  /**
   * Completed sections
   */
  completed_sections: Annotation<Section[]>({
    reducer: (state, action) => action,
    default: () => []
  })
});

// You can also use these with LangChain's structured output
export const SearchQueryAnnotation = Annotation.Root({
  search_query: Annotation<string | null>()
});

export const QueriesAnnotation = Annotation.Root({
  search_queries: Annotation<SearchQuery[]>({
    reducer: (state, action) => action,
    default: () => []
  })
});

export const SectionStateOutput = Annotation.Root({
  completed_sections: Annotation<Section[]>(),
})

// Types for input and output states
export const ReportStateInput = Annotation.Root({
  topic: Annotation<string>,
  //feedback_on_report_plan: Annotation<string>(),
});

export const ReportStateOutput = Annotation.Root({
  final_report: Annotation<string>,
});

// Type exports for convenience
export type ReportState = typeof ReportStateAnnotation.State;
export type SectionState = typeof SectionStateAnnotation.State;

export class Sections {
  sections: Section[];
  
  constructor(sections: Section[]) {
    this.sections = sections;
  }
  
  static modelValidate(data: any): Sections {
    // Simple validation logic, adjust based on your needs
    if (!Array.isArray(data.sections)) {
      throw new Error("Invalid data: sections must be an array");
    }
    return new Sections(data.sections);
  }
}

export class FeedbackHelper {
  // Static method for validation and creation from raw data
  static modelValidate(data: any): Feedback {
    // Use Zod to validate the input data
    return FeedbackSchema.parse(data);
  }
}