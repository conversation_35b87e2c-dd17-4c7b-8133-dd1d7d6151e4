import  { type TavilySearchOptions, type TavilySearchResponse, tavily } from "@tavily/core";
import { BaseMessage } from "@langchain/core/messages";
import { ArxivRetriever } from '@langchain/community/retrievers/arxiv';
import { Section } from  "./state"
import { LinkupClient } from 'linkup-sdk';
import { PubMedAPIWrapper } from '@/lib/pubMedAPIWrapper';
import Exa from 'exa-js';

type SearchAPI = 'exa' | 'tavily' | 'perplexity' | 'arxiv' | 'pubmed' | 'linkup';

type SearchResult = {
 title: string;
 url: string;
 content: string;
 raw_content: string | null;
 score: number;
}

type SearchResponse = {
 query: string;
 follow_up_questions: null;
 answer: null;
 images: any[];
 results: SearchResult[];
 error?: string;
}

export interface ExaSearchParams {
  max_characters?: number;
  num_results?: number;
  include_domains?: string[];
  exclude_domains?: string[];
  subpages?: boolean;
}


export interface ArxivSearchParams {
  load_max_docs?: number;
  get_full_documents?: boolean;
  load_all_available_meta?: boolean;
}

export interface PubMedSearchParams {
  top_k_results?: number;
  email?: string;
  api_key?: string;
  doc_content_chars_max?: number;
}

// Linkup Search
export interface LinkupSearchParams {
  depth?: "standard" | "deep";
}

interface LinkupSearchResult {
  title: string;
  url: string;
  content: string;
}

interface LinkupSearchResponse {
  results: LinkupSearchResult[];
}

// Define a type map to ensure type safety for each API
export type SearchParamsMap = {
  exa: ExaSearchParams;
  tavily: Record<string, never>; // Empty object for APIs with no params
  perplexity: Record<string, never>;
  arxiv: ArxivSearchParams;
  pubmed: PubMedSearchParams;
  linkup: LinkupSearchParams;
};

/**
 * Filters the searchApiConfig to include only parameters accepted by the specified search API.
 * 
 * @param searchApi The search API identifier
 * @param searchApiConfig Optional configuration dictionary for the search API
 * @returns A dictionary of parameters to pass to the search function
 */
export function getSearchParams<T extends SearchAPI>(
  searchApi: T, 
  searchApiConfig?: Partial<SearchParamsMap[T]> | null
): SearchParamsMap[T] {
  // Define accepted parameters for each search API
  const SEARCH_API_PARAMS: Record<SearchAPI, string[]> = {
    exa: ["max_characters", "num_results", "include_domains", "exclude_domains", "subpages"],
    tavily: [], // Tavily currently accepts no additional parameters
    perplexity: [], // Perplexity accepts no additional parameters
    arxiv: ["load_max_docs", "get_full_documents", "load_all_available_meta"],
    pubmed: ["top_k_results", "email", "api_key", "doc_content_chars_max"],
    linkup: ["depth"],
  };

  // Get the list of accepted parameters for the given search API
  const acceptedParams = SEARCH_API_PARAMS[searchApi] || [];
  
  // If no config provided, return an empty object
  if (!searchApiConfig) {
    return {} as SearchParamsMap[T]; 
  }
  
  // Filter the config to only include accepted parameters
  const filteredParams = Object.fromEntries(
    Object.entries(searchApiConfig)
      .filter(([key]) => acceptedParams.includes(key))
  );
  
  return filteredParams as SearchParamsMap[T];
}

// Utility function for deduplicating and formatting sources
export function deduplicateAndFormatSources({
 searchResults,
 maxTokensPerSource,
 includeRawContent
} : {
  searchResults: any; 
  maxTokensPerSource: number; 
  includeRawContent: boolean;
}

): string {
  // Determine the sources list
  let sourcesList: any[] = [];
  if (typeof searchResults === 'object' && 'results' in searchResults) {
    sourcesList = searchResults.results;
  } else if (Array.isArray(searchResults)) {
    sourcesList = searchResults.flatMap(response => 
      response.results ? response.results : response
    );
  } else {
    throw new Error("Input must be either a dict with 'results' or a list of search results");
  }
  
  // Deduplicate by URL
  const uniqueSources: Record<string, any> = {};
  sourcesList.forEach(source => {
    if (!uniqueSources[source.url]) {
      uniqueSources[source.url] = source;
    }
  });
 
  // Format output
  let formattedText = "Sources:\n\n";
  Object.values(uniqueSources).forEach((source, index) => {
    formattedText += `Source ${source.title}:\n===\n`;
    formattedText += `URL: ${source.url}\n===\n`;
    formattedText += `Most relevant content from source: ${source.content}\n===\n`;
  
    if (includeRawContent) {
      const charLimit = maxTokensPerSource * 4;
      let rawContent = source.raw_content || '';
       
      if (rawContent.length > charLimit) {
        rawContent = rawContent.slice(0, charLimit) + "... [truncated]";
      }
       
      formattedText += `Full source content limited to ${maxTokensPerSource} tokens: ${rawContent}\n\n`;
    }
  });
  
  return formattedText.trim();
}
  
// Format sections into a string
export function formatSections(sections: Section[]): string {
    return sections.map((section, idx) => `
  ${'='.repeat(60)}
  Section ${idx + 1}: ${section.name}
  ${'='.repeat(60)}
  Description:
  ${section.description}
  Requires Research: 
  ${section.research}
  
  Content:
  ${section.content || '[Not yet written]'}
  `).join('\n');
}
  
// Tavily search function
export async function tavilySearchAsync(
  searchQueries: string[]
): Promise<TavilySearchResponse[]> {
  console.log("searchQueries", searchQueries)
  const tvly = tavily({ apiKey: process.env.TAVILY_API_KEY });

    // Ensure search queries are provided
    if (searchQueries.length === 0) {
      throw new Error("No valid search queries provided.");
    }
  
    const searchTasks = searchQueries.map(async (query) => {
      // Log the query being processed for debugging
      console.log("Processing query:", query);
  
      const searchOptions: TavilySearchOptions = {
        includeRawContent: true,
        maxResults: 5,
        topic: "general",
      };
  
      try {
        // Directly pass the query string to the invoke method
        const searchDocs = await tvly.search(
          query, 
          searchOptions
        );
        //const result = await tavilySearchTool.invoke(query);
        //console.log("Search Result:", searchDocs);
        return searchDocs;
      } catch (error: any) {
        console.error("Search failed for query:", query, error);
        throw new Error(`Search failed for query "${query}": ${error.message}`);
      }
    });
  
    return Promise.all(searchTasks);
}

// Perplexity search function
export async function perplexitySearchAsync(searchQueries: string[]): Promise<SearchResponse[]> {
 /**
  * Search the web using the Perplexity API.
  * 
  * @param searchQueries - List of search queries to process
  * @returns List of search responses from Perplexity API, one per query
  */
 
 const headers = {
   "accept": "application/json",
   "content-type": "application/json",
   "Authorization": `Bearer ${process.env.PERPLEXITY_API_KEY}`
 };
 
 const searchDocs: SearchResponse[] = [];
 
 for (const query of searchQueries) {
   const payload = {
     "model": "sonar-pro",
     "messages": [
       {
         "role": "system",
         "content": "Search the web and provide factual information with sources."
       },
       {
         "role": "user",
         "content": query
       }
     ]
   };
   
   const response = await fetch(
     "https://api.perplexity.ai/chat/completions",
     {
       method: "POST",
       headers: headers,
       body: JSON.stringify(payload)
     }
   );
   
   if (!response.ok) {
     throw new Error(`Perplexity API error: ${response.status} ${response.statusText}`);
   }
   
   // Parse the response
   const data = await response.json();
   const content = data.choices[0].message.content;
   const citations = data.citations || ["https://perplexity.ai"];
   
   // Create results list for this query
   const results: SearchResult[] = [];
   
   // First citation gets the full content
   results.push({
     title: "Perplexity Search, Source 1",
     url: citations[0],
     content: content,
     raw_content: content,
     score: 1.0  // Adding score to match Tavily format
   });
   
   // Add additional citations without duplicating content
   for (let i = 1; i < citations.length; i++) {
     results.push({
       title: `Perplexity Search, Source ${i + 1}`,
       url: citations[i],
       content: "See primary source for full content",
       raw_content: null,
       score: 0.5  // Lower score for secondary sources
     });
   }
   
   // Format response to match Tavily structure
   searchDocs.push({
     query: query,
     follow_up_questions: null,
     answer: null,
     images: [],
     results: results
   });
 }
 
 return searchDocs;
}

export async function exaSearch(
  searchQueries: string[],
  params: ExaSearchParams = {}
): Promise<SearchResponse[]> {
  const { 
    max_characters, 
    num_results = 5, 
    include_domains, 
    exclude_domains, 
    subpages 
  } = params;

  // Check that include_domains and exclude_domains are not both specified
  if (include_domains && exclude_domains) {
    throw new Error("Cannot specify both include_domains and exclude_domains");
  }

  // Initialize Exa client (API key should be configured in your environment)
  const exa = new Exa(process.env.EXA_API_KEY);

  // Function to process a single query
  async function processQuery(query: string): Promise<SearchResponse> {
    try {
      // Build parameters object
      const kwargs: Record<string, any> = {
        text: max_characters === undefined ? true : { max_characters },
        summary: true,
        num_results: num_results
      };

      // Add optional parameters only if they are provided
      if (subpages !== undefined) {
        kwargs.subpages = subpages;
      }

      if (include_domains) {
        kwargs.include_domains = include_domains;
      } else if (exclude_domains) {
        kwargs.exclude_domains = exclude_domains;
      }

      const response = await exa.searchAndContents(query, kwargs);

      // Format the response
      const formattedResults: SearchResult[] = [];
      const seenUrls = new Set<string>();

      // Helper function to safely get value
      function getValue(item: any, key: string, defaultValue: any = null) {
        if (item === null || item === undefined) return defaultValue;
        return typeof item === 'object' ? (item[key] ?? defaultValue) : defaultValue;
      }

      // Access the results from the SearchResponse object
      const resultsList = getValue(response, 'results', []);

      // Process all main results
      for (const result of resultsList) {
        const score = getValue(result, 'score', 0.0);
        const textContent = getValue(result, 'text', '');
        const summaryContent = getValue(result, 'summary', '');

        let content = textContent;
        if (summaryContent) {
          content = content ? `${summaryContent}\n\n${content}` : summaryContent;
        }

        const title = getValue(result, 'title', '');
        const url = getValue(result, 'url', '');

        // Skip if we've seen this URL before
        if (seenUrls.has(url)) continue;
        seenUrls.add(url);

        // Main result entry
        const resultEntry: SearchResult = {
          title,
          url,
          content,
          score,
          raw_content: textContent
        };

        formattedResults.push(resultEntry);
      }

      // Process subpages if the subpages parameter was provided
      if (subpages !== undefined) {
        for (const result of resultsList) {
          const subpagesList = getValue(result, 'subpages', []);
          
          for (const subpage of subpagesList) {
            const subpageScore = getValue(subpage, 'score', 0.0);
            const subpageText = getValue(subpage, 'text', '');
            const subpageSummary = getValue(subpage, 'summary', '');

            let subpageContent = subpageText;
            if (subpageSummary) {
              subpageContent = subpageContent 
                ? `${subpageSummary}\n\n${subpageContent}` 
                : subpageSummary;
            }

            const subpageUrl = getValue(subpage, 'url', '');

            // Skip if we've seen this URL before
            if (seenUrls.has(subpageUrl)) continue;
            seenUrls.add(subpageUrl);

            formattedResults.push({
              title: getValue(subpage, 'title', ''),
              url: subpageUrl,
              content: subpageContent,
              score: subpageScore,
              raw_content: subpageText
            });
          }
        }
      }

      // Collect images if available
      const images: any[] = [];
      for (const result of resultsList) {
        const image = getValue(result, 'image');
        if (image && !images.includes(image)) {
          images.push(image);
        }
      }

      return {
        query,
        follow_up_questions: null,
        answer: null,
        images,
        results: formattedResults
      };
    } catch (e) {
      console.error(`Error processing query '${query}': ${e}`);
      return {
        query,
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      };
    }
  }

  // Process all queries sequentially with delay
  const searchDocs: SearchResponse[] = [];
  for (let i = 0; i < searchQueries.length; i++) {
    try {
      // Add delay between requests
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 250)); // 0.25s delay
      }

      const result = await processQuery(searchQueries[i]);
      searchDocs.push(result);
    } catch (e) {
      console.error(`Error processing query '${searchQueries[i]}': ${e}`);
      searchDocs.push({
        query: searchQueries[i],
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      });

      // Add additional delay if we hit a rate limit error
      if (String(e).includes("429")) {
        console.log("Rate limit exceeded. Adding additional delay...");
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1s delay
      }
    }
  }

  return searchDocs;
}

export async function arxivSearchAsync(
  searchQueries: string[],
  params: ArxivSearchParams = {}
): Promise<SearchResponse[]> {
  const {
    load_max_docs = 5,
    get_full_documents = true,
    load_all_available_meta = true
  } = params;

  async function processSingleQuery(query: string): Promise<SearchResponse> {
    try {
      // Create retriever for each query
      const retriever = new ArxivRetriever({
        getFullDocuments: get_full_documents,
        maxSearchResults: load_max_docs,
      });

      // Run the synchronous retriever in a thread pool or similar
      const docs = await retriever.invoke(query);

      const results: SearchResult[] = [];
      // Assign decreasing scores based on the order
      const baseScore = 1.0;
      const scoreDecrement = docs.length ? 1.0 / (docs.length + 1) : 0;

      for (let i = 0; i < docs.length; i++) {
        const doc = docs[i];
        const metadata = doc.metadata;

        // Use entry_id as the URL
        const url = metadata.entry_id || '';

        // Format content with all useful metadata
        const contentParts: string[] = [];

        // Primary information
        if (metadata.Summary) {
          contentParts.push(`Summary: ${metadata.Summary}`);
        }

        if (metadata.Authors) {
          contentParts.push(`Authors: <AUTHORS>
        }

        // Add publication information
        let publishedStr = '';
        if (metadata.Published) {
          const published = metadata.Published;
          publishedStr = typeof published.toISOString === 'function' 
            ? published.toISOString() 
            : String(published);
          contentParts.push(`Published: ${publishedStr}`);
        }

        // Add additional metadata if available
        if (metadata.primary_category) {
          contentParts.push(`Primary Category: ${metadata.primary_category}`);
        }

        if (metadata.categories && metadata.categories.length) {
          contentParts.push(`Categories: ${metadata.categories.join(', ')}`);
        }

        if (metadata.comment) {
          contentParts.push(`Comment: ${metadata.comment}`);
        }

        if (metadata.journal_ref) {
          contentParts.push(`Journal Reference: ${metadata.journal_ref}`);
        }

        if (metadata.doi) {
          contentParts.push(`DOI: ${metadata.doi}`);
        }

        // Get PDF link if available in the links
        let pdfLink = "";
        if (metadata.links && metadata.links.length) {
          for (const link of metadata.links) {
            if (link.includes('pdf')) {
              pdfLink = link;
              contentParts.push(`PDF: ${pdfLink}`);
              break;
            }
          }
        }

        // Join all content parts with newlines
        const content = contentParts.join("\n");

        const result: SearchResult = {
          title: metadata.Title || '',
          url: url,
          content: content,
          score: baseScore - (i * scoreDecrement),
          raw_content: get_full_documents ? doc.pageContent : null
        };
        results.push(result);
      }

      return {
        query: query,
        follow_up_questions: null,
        answer: null,
        images: [],
        results: results
      };
    } catch (e) {
      console.error(`Error processing arXiv query '${query}': ${e}`);
      return {
        query: query,
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      };
    }
  }

  // Process queries sequentially with delay to respect arXiv rate limit
  const searchDocs: SearchResponse[] = [];
  for (let i = 0; i < searchQueries.length; i++) {
    try {
      // Add delay between requests (3 seconds per ArXiv's rate limit)
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3s delay
      }

      const result = await processSingleQuery(searchQueries[i]);
      searchDocs.push(result);
    } catch (e) {
      console.error(`Error processing arXiv query '${searchQueries[i]}': ${e}`);
      searchDocs.push({
        query: searchQueries[i],
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      });

      // Add additional delay if we hit a rate limit error
      if (String(e).includes("429") || String(e).includes("Too Many Requests")) {
        console.log("ArXiv rate limit exceeded. Adding additional delay...");
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5s delay
      }
    }
  }

  return searchDocs;
}

export async function pubmedSearchAsync(
  searchQueries: string[],
  params: PubMedSearchParams = {}
): Promise<SearchResponse[]> {
  const {
    top_k_results = 5,
    email = null,
    api_key = null,
    doc_content_chars_max = 4000
  } = params;

  async function processSingleQuery(query: string): Promise<SearchResponse> {
    try {
      // Create PubMed wrapper for the query
      const wrapper = new PubMedAPIWrapper({
        top_k_results,
        doc_content_chars_max,
        email: email || "<EMAIL>",
        api_key: api_key || ""
      });

      // Use wrapper.lazy_load to get better visibility
      const docs = Array.from(await wrapper.lazyLoad(query));

      console.log(`Query '${query}' returned ${docs.length} results`);

      const results: SearchResult[] = [];
      // Assign decreasing scores based on the order
      const baseScore = 1.0;
      const scoreDecrement = docs.length ? 1.0 / (docs.length + 1) : 0;

      for (let i = 0; i < docs.length; i++) {
        const doc = docs[i];
        // Format content with metadata
        const contentParts: string[] = [];

        if (doc.Published) {
          contentParts.push(`Published: ${doc.Published}`);
        }

        if (doc['Copyright Information']) {
          contentParts.push(`Copyright Information: ${doc['Copyright Information']}`);
        }

        if (doc.Summary) {
          contentParts.push(`Summary: ${doc.Summary}`);
        }

        // Generate PubMed URL from the article UID
        const uid = doc.uid || '';
        const url = uid ? `https://pubmed.ncbi.nlm.nih.gov/${uid}/` : "";

        // Join all content parts with newlines
        const content = contentParts.join("\n");

        const result: SearchResult = {
          title: doc.Title || '',
          url: url,
          content: content,
          score: baseScore - (i * scoreDecrement),
          raw_content: doc.Summary || ''
        };
        results.push(result);
      }

      return {
        query: query,
        follow_up_questions: null,
        answer: null,
        images: [],
        results: results
      };
    } catch (e) {
      const errorMsg = `Error processing PubMed query '${query}': ${e}`;
      console.error(errorMsg);
      console.error((e as Error).stack); // Print full stack trace for debugging

      return {
        query: query,
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      };
    }
  }

  // Process all queries with a reasonable delay between them
  const searchDocs: SearchResponse[] = [];

  // Start with a small delay that increases if we encounter rate limiting
  let delay = 1.0; // Start with a more conservative delay

  for (let i = 0; i < searchQueries.length; i++) {
    try {
      // Add delay between requests
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, delay * 1000));
      }

      const result = await processSingleQuery(searchQueries[i]);
      searchDocs.push(result);

      // If query was successful with results, we can slightly reduce delay (but not below minimum)
      if (result.results && result.results.length > 0) {
        delay = Math.max(0.5, delay * 0.9); // Don't go below 0.5 seconds
      }
    } catch (e) {
      // Handle exceptions gracefully
      const errorMsg = `Error in main loop processing PubMed query '${searchQueries[i]}': ${e}`;
      console.error(errorMsg);

      searchDocs.push({
        query: searchQueries[i],
        follow_up_questions: null,
        answer: null,
        images: [],
        results: [],
        error: String(e)
      });

      // If we hit an exception, increase delay for next query
      delay = Math.min(5.0, delay * 1.5); // Don't exceed 5 seconds
    }
  }

  return searchDocs;
}


export async function linkupSearchAsync(
  searchQueries: string[],
  params: LinkupSearchParams = {}
): Promise<LinkupSearchResponse[]> {
  const { depth = "standard" } = params;
  
  const client = new LinkupClient({
    apiKey: process.env.LINKUP_API_KEY
  });
  const searchTasks: Promise<any>[] = [];
  
  for (const query of searchQueries) {
    searchTasks.push(
      client.search({
        query, 
        depth, 
        outputType: 'sourcedAnswer'
      })
    );
  }

  const searchResults: LinkupSearchResponse[] = [];
  const responses = await Promise.all(searchTasks);
  
  for (const response of responses) {
    searchResults.push({
      results: response.results.map((result: any) => ({
        title: result.name,
        url: result.url,
        content: result.content
      }))
    });
  }

  return searchResults;
}

export function extractMessageContent(message: BaseMessage): string {
  if (typeof message.content === 'string') {
    return message.content;
  }
  
  if (Array.isArray(message.content)) {
    // If it's an array, try to find a string content
    const stringContent = message.content.find(item => typeof item === 'string');
    return stringContent ?? JSON.stringify(message.content);
  }
  
  // Fallback to stringifying the content
  return JSON.stringify(message.content);
}
