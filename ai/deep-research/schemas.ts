import { z } from 'zod/v3';

// Define the SearchQuery type using Zod for structured output
export const SearchQuerySchema = z.object({
  search_query: z.string().nullable().describe("Query for web search.")
});

// Define the Queries type for multiple search queries
export const QueriesSchema = z.object({
  queries: z.array(SearchQuerySchema).describe("List of search queries.")
});

export const FeedbackSchema = z.object({
  grade: z.enum(["pass", "fail"]),
  follow_up_queries: z.array(SearchQuerySchema).describe("List of follow-up search queries.")
});

export const SectionSchema = z.object({
  name: z.string().describe("Name for this section of the report."),
  description: z.string().describe("Brief overview of the main topics and concepts to be covered in this section."),
  research: z.boolean().describe("Whether to perform web research for this section of the report."),
  content: z.string().describe("The content of the section.")
});

// Define the Sections type for multiple sections
export const SectionsSchema = z.object({
  sections: z.array(SectionSchema).describe("Sections of the report.")
});

// TypeScript type definitions
export type SearchQuery = z.infer<typeof SearchQuerySchema>;
export type Queries = z.infer<typeof QueriesSchema>;
// TypeScript type definitions
export type Section = z.infer<typeof SectionSchema>;
export type Sections = z.infer<typeof SectionsSchema>;
export type Feedback = z.infer<typeof FeedbackSchema>;