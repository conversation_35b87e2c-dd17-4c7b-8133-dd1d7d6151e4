export const sections_test = [
  {
    name: 'Introduction',
    description: 'Brief overview of the topic area',
    research: false,
    content: ''
  },
  {
    name: 'Company Overview',
    description: 'Overview of ThreatSTOP, its history, mission, and products',
    research: true,
    content: ''
  },
  {
    name: 'Threat Intelligence and Automation',
    description: 'How ThreatSTOP uses threat intelligence and automation to protect networks',
    research: true,
    content: ''
  },
  {
    name: 'Protective DNS and IP Defense',
    description: "How ThreatSTOP's Protective DNS and IP Defense work to block malicious traffic",
    research: true,
    content: ''
  },
  {
    name: 'Case Studies and Success Stories',
    description: "Examples of ThreatSTOP's success in protecting networks and preventing cyberattacks",
    research: true,
    content: ''
  },
  {
    name: 'Conclusion',
    description: 'Summary of the report and key findings',
    research: false,
    content: ''
  }
]

export const queries_test = {
  queries: [
    {
      search_query:
        'Q-Learning vs Deep Q-Networks in value-based reinforcement learning 2024'
    },
    {
      search_query:
        'Technical architecture and implementation of value-based methods in RL, including SARSA and TD-Gammon, with examples in Python 2024'
    }
  ]
}

export const completed_sections_test = [
  {
    name: 'Value-Based Methods',
    description: 'Overview of value-based methods in reinforcement learning',
    research: true,
    content:
      '### Overview of Value-Based Methods in Reinforcement Learning\n' +
      '\n' +
      '**Value-based methods in reinforcement learning (RL) directly estimate the optimal value function, which represents the expected cumulative reward achievable from a given state or state-action pair.** These methods iteratively refine this estimate, enabling agents to select actions that maximize long-term returns. A core algorithm is Q-learning, as described in NeurIPS 2024 proceedings, which uses temporal difference (TD) learning to minimize a squared loss driven by the TD target, thereby updating the joint action-value network.\n' +
      '\n' +
      'A key advantage of value-based methods is their sample efficiency, particularly in environments with discrete action spaces. For example, Deep Q-Networks (DQNs) successfully applied Q-learning with deep neural networks to play Atari games at a superhuman level, demonstrating the power of combining value estimation with function approximation.\n' +
      '\n' +
      'However, value-based methods can struggle with continuous action spaces or environments where precise value estimation is difficult. They may also exhibit instability during training, especially when using non-linear function approximators like neural networks. Version 1 of arXiv:2412.05265 highlights this challenge. More stable methods are being developed, as discussed in the 2024 MIT 1.041/1.200 course materials.\n' +
      '\n' +
      '- Value-based RL focuses on *learning* the value function.\n' +
      '- Agents *use* the learned values to inform action selection.\n' +
      '- The value function *predicts* future rewards.\n' +
      '\n' +
      '#### Sources\n' +
      '\n' +
      '*   Value-Based Deep Multi-Agent Reinforcement ... : https://proceedings.neurips.cc/paper_files/paper/2024/file/********************************-Paper-Conference.pdf\n' +
      '*   Reinforcement Learning: An Overview : https://arxiv.org/abs/2412.05265\n' +
      '*   1.041/1.200 Transporta0on: Founda0ons and Methods Spring 2024.: https://web.mit.edu/1.041/www/lectures/L18-deep-reinforcement-learning-2024sp.pdf\n' +
      '*   Value-based methods are an approach to reinforcement learning...: http://incompleteideas.net/papers/Kris_PhD_Thesis.pdf\n'
  },
  {
    name: 'Policy-Based Methods',
    description: 'Overview of policy-based methods in reinforcement learning',
    research: true,
    content:
      '### Overview of Policy-Based Methods in Reinforcement Learning\n' +
      '\n' +
      "**Policy-based methods directly optimize the agent's policy, mapping states to actions, often achieving faster convergence than value-based approaches.** These methods, such as REINFORCE (policy gradients), directly adjust the policy's parameters to maximize expected rewards. A core challenge is navigating the trade-off between exploration and exploitation, which impacts sample efficiency.\n" +
      '\n' +
      "Policy gradient (PG) methods, a key type of policy-based approach, are frequently used, with version numbers varying across implementations (e.g., different versions within OpenAI's baselines or Stable Baselines3). Recent prominent applications include Reinforcement Learning from Human Feedback (RLHF) in large language models, such as Google's Gemma (Team & DeepMind, 2024) and OpenAI's InstructGPT (Ouyang et al., 2022).\n" +
      '\n' +
      'Proximal Policy Optimization (PPO), is a more advanced policy gradient method that improves stability. PPO addresses slow convergence or numerical instability, common drawbacks of basic gradient descent used in standard PG. For example, PPO with a clipping objective function, limits policy updates, preventing drastic changes that can destabilize training.\n' +
      '\n' +
      '-   PPO variations exist, such as those using adaptive KL penalties.\n' +
      '-   Implementations are available in libraries like Stable-Baselines3.\n' +
      '-   Performance is often measured by average episode reward or success rate on benchmark environments (e.g., MuJoCo).\n' +
      '\n' +
      '#### Sources\n' +
      '\n' +
      '*   Asynchronous Federated Reinforcement Learning with Policy Gradient ... : [https://arxiv.org/html/2404.08003v3](https://arxiv.org/html/2404.08003v3)\n' +
      '*   Adaptive reinforcement learning-based control using proximal policy ... : [https://www.sciencedirect.com/science/article/pii/S1568494624004617](https://www.sciencedirect.com/science/article/pii/S1568494624004617)\n' +
      '*   [2412.05265] Reinforcement Learning: An Overview - arXiv.org : [https://arxiv.org/abs/2412.05265](https://arxiv.org/abs/2412.05265)\n'
  },
  {
    name: 'Introduction',
    description: 'Brief overview of the topic area',
    research: false,
    content:
      '```markdown\n' +
      '## Reinforcement Learning: An Overview\n' +
      '\n' +
      'Reinforcement learning (RL) is a powerful branch of machine learning where agents learn to make decisions by interacting with an environment.  The agent learns to maximize a cumulative reward, adapting its actions based on feedback received. This approach has shown remarkable success in areas ranging from game playing to resource management. This report provides a concise overview of key reinforcement learning methods, including value-based, policy-based, actor-critic, and model-based approaches, along with their applications. Deep reinforcement learning, combining RL with deep neural networks, is also covered, highlighting its ability to tackle complex, high-dimensional problems.\n' +
      '```\n'
  },
  {
    name: 'Conclusion',
    description: 'Summary of the report and key findings',
    research: false,
    content:
      '### Summary of Key Findings\n' +
      '\n' +
      "This report provides a comprehensive overview of reinforcement learning (RL) methods, highlighting key advancements and applications. We examined value-based methods, such as Q-learning and Deep Q-Networks (DQN), which excel in discrete action spaces but can struggle with continuous ones. Policy-based methods, including REINFORCE and Proximal Policy Optimization (PPO), directly optimize the agent's policy, often achieving faster convergence. Actor-critic methods, like DDPG, SAC, and TD3, combine the strengths of both, improving stability and efficiency, especially in continuous control tasks. Model-based methods offer another approach by learning a model of the environment. Deep reinforcement learning (DRL) integrates these RL techniques with deep neural networks, enabling the handling of complex, high-dimensional state spaces.\n" +
      '\n' +
      'Key findings indicate that DRL is rapidly evolving, with distributed learning frameworks accelerating training and applications expanding beyond traditional benchmarks. A prime example is cloud computing resource scheduling, where DRL has demonstrated significant performance improvements. The choice of method depends on the specific application, with trade-offs between sample efficiency, stability, and the ability to handle different action spaces. Future development will likely focus on improving the robustness and generalizability of DRL algorithms, and expanding their use in real-world applications.\n'
  }
]

export const sourceStr_test = `Sources:

Source Q-Learning vs. Deep Q-Learning vs. Deep Q-Network - Baeldung:
===
URL: https://www.baeldung.com/cs/q-learning-vs-deep-q-learning-vs-deep-q-network
===
Most relevant content from source: As a value-based algorithm, Q-learning trains the value function to learn which actions are more valuable in each state and select the optimal action accordingly. By updating the Q-values based on the highest Q-value action in each state, Q-learning can converge to the optimal policy even if it is different from the policy used to take actions during training. Deep Neural Network: DQN uses a deep neural network to estimate the Q-values for each (state, action) pair. In summary, DQN learns the optimal policy by using a deep neural network to estimate the Q-values, a replay memory buffer to store past experiences, and a target network to prevent overestimating Q-values.
===
Full source content limited to 500 tokens:

Source What is the difference between Q-learning, Deep Q-learning and Deep Q ...:
===
URL: https://ai.stackexchange.com/questions/25913/what-is-the-difference-between-q-learning-deep-q-learning-and-deep-q-network
===
Most relevant content from source: Stack Exchange network consists of 183 Q&A communities including Stack Overflow, the largest, most trusted online community for developers to learn, share their knowledge, and build their careers. Q-learning is a model-free RL algorithm, so how could there be the one called Deep Q-learning, as deep means using DNN; or maybe the state-action table (Q-table) is still there but the DNN is only for input reception (e.g. turning images into vectors)? Now, it seems in your question that you're confused as to why you use a model (the neural network) when Q-learning is, as you rightly say, model-free. Deep Q-network (DQN) Deep Q-network (DQN) refers to the specific neural network architecture used in Deep Q-learning to approximate the Q-values. 0Negative action-state values found during deep Q-learning
===
Full source content limited to 500 tokens:

Source Reinforcement Learning: From Q-Learning to Deep Q-Networks - Codalien Blogs:
===
URL: https://codalien.com/blog/comprehensive-guide-reinforcement-learning-q-learning-deep-q-networks/
===
Most relevant content from source: In the ever-evolving field of artificial intelligence (AI), Reinforcement Learning (RL) stands as a pioneering technique enabling agents (entities or software algorithms) to learn from interactions with an environment. Reinforcement Learning (RL) is a subset of machine learning where an agent learns to make decisions by interacting with an environment. Q-Learning is a fundamental model-free RL algorithm designed to learn the optimal action-value function (Q-function). Meta-Reinforcement Learning (Meta-RL) focuses on enabling agents to not only learn from direct interactions with an environment but also to learn how to learn efficiently across different tasks or environments. Reinforcement Learning, from Q-Learning to Deep Q-Networks, represents a powerful paradigm for developing intelligent agents capable of learning optimal behaviors through interactions with their environments.
===
Full source content limited to 500 tokens:

Source Reinforcement Learning Vs Q-Learning | Restackio:
===
URL: https://www.restack.io/p/reinforcement-learning-answer-differences-q-learning-cat-ai
===
Most relevant content from source: Q-learning is a specific model-free reinforcement learning algorithm that aims to learn the value of actions in particular states. Q-learning is a powerful off-policy reinforcement learning algorithm that is designed to learn the value of actions in a given state, ultimately guiding an agent towards optimal decision-making. Reinforcement learning encompasses a broader range of algorithms, including policy-based methods, while Q-learning is specifically a value-based method that focuses on estimating the value of actions. The neural network takes the state as input and outputs the corresponding Q-values, enabling the agent to make informed decisions based on learned experiences. Q-Learning: A value-based method that learns the value of actions in states, allowing the agent to derive an optimal policy.
===
Full source content limited to 500 tokens:

Source Reinforcement Learning with Deep Q-Networks - Western Kentucky University:
===
URL: https://digitalcommons.wku.edu/cgi/viewcontent.cgi?article=4558&context=theses
===
Most relevant content from source: 2.3 Deep Q-Learning. Deep Q-Learning refers specifically to the application of deep neural networks (DNNs) to the Q-Learning algorithm. DNNs have a powerful ability to learn multiple levels of abstraction from data [7,4] and are thus a suitable choice for making generalizations about the Q-function based on these learned ab-stractions.
===
Full source content limited to 500 tokens:

Source PDF:
===
URL: https://web.mit.edu/6.7950/www/lectures/L10-2022fa.pdf
===
Most relevant content from source: Wu Value-based RL methods 4 Example: § Q-learning § SARSA § Approximate value iteration § Fitted Q-iteration § DQN § Double DQN § … Environment Action-value function 𝑄(𝑠,⋅) Action max w.p. 1 −𝜖 random w.p. 𝜖 state, reward update Bootstrap target e.g. 𝑟+ 𝛾max !" 𝑄(𝑠", 𝑎′) Wu Outline 5 1. 𝑠= + "#$ % 𝜃 "𝜑" 𝑠, 𝜃∈ℝ% With features 𝜑": 𝑆→0, 𝐿 𝜙𝑠= 𝜑$ 𝑠… 𝜑% 𝑠 & Wu Approximate Monte-Carlo as Supervised Learning 40 § Distribution over the state space 𝒟 § Function approximation 𝑉 !: 𝑆→ℝ, 𝜃∈ℝ% [e.g. linear, deepNet] § Build training set of 𝑛samples 𝑠'~ 𝒟 𝑅' = + (#) * 𝑟 (,' = 𝑉, 𝑠' + 𝜖' 𝔼𝜖' = 0 § Training (batch) ?
===
Full source content limited to 500 tokens:

Source PDF:
===
URL: https://deep-reinforcement-learning.net/wp-content/uploads/2021/12/drl_lecture3.pdf
===
Most relevant content from source: Reinforcement Learning 2022 Lecture 3: Deep Value Based Methods ... • In RL, state of the art in NN is still TD-Gammon on a small network (40 hidden units), from 1992 ... • Convergence proof of Value Iteration, Q-learning and SARSA depend on covering the entire state space, in the
===
Full source content limited to 500 tokens:

Source PDF:
===
URL: https://icaps18.icaps-conference.org/fileadmin/alg/conferences/icaps18/summerschool/lectures/Lecture5-rl-intro.pdf
===
Most relevant content from source: Model-free methods Value-based methods: based upon temporal difference learning (TD, SARSA, Q-learning), learn value function /0or /⋆(or a slight generalization called the Q-function, that we will discuss shortly) Policy-based method: directly learn optimal policy .⋆(or try to approximate optimal policy, if true optimal policy is not
===
Full source content limited to 500 tokens:

Source PDF:
===
URL: https://people.csail.mit.edu/agf/Files/13JMLR-RLPy.pdf
===
Most relevant content from source: RLPy is an object-oriented reinforcement learning framework with focus on value-function-based methods using linear function-approximation and discrete actions. The framework was designed for both, education and research purposes. It provides a rich li-brary of ne-grained, easily exchangeable components for learning agents (e.g., policies or
===
Full source content limited to 500 tokens:

Source Reinforcement Learning Algorithms and Applications in Healthcare and ...:
===
URL: https://pmc.ncbi.nlm.nih.gov/articles/PMC11053800/
===
Most relevant content from source: Value functions are pivotal in all reinforcement learning algorithms as they estimate the future reward that can be expected from a given state and action [36,37]. Additionally, there are two primary approaches in reinforcement learning for problem-solving, which are value-based and policy-based, both of which can be categorized under model-free methods [43]. In other words, model-based reinforcement learning methods encompass the computation of action values through the simulation of action outcomes using a mental map or model of the environment that includes the environment’s various states, transition probabilities, and rewards [44,45]. In [77], the authors introduce a novel pipeline that combines traditional control and reinforcement learning (RL) techniques for both simulated and real-world environments to validate RL methods across various scenarios, including reach, grasp, and pick-and-place tasks.
===
`
