import { 
  LangGraphRunnableConfig, 
  StateGraph, 
  END, 
  START,
  Command
} from "@langchain/langgraph";
import { initChatModel } from 'langchain/chat_models/universal';
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";
import { 
  tavilySearchAsync, 
  perplexitySearchAsync, 
  deduplicateAndFormatSources, 
  extractMessageContent 
} from "../utils"
import { 
  type SearchQuery,
  FeedbackHelper,
  SectionStateOutput,
  SectionStateAnnotation 
} from '../state';
import {
  QueriesSchema,
  FeedbackSchema,
  type Feedback,
} from '../schemas'
import { 
  queryWriterInstructions, 
  sectionWriterInstructions,
  sectionGraderInstructions
} from '../prompts';
import { ensureConfiguration } from '../configuration';
import { queries_test, completed_sections_test, sourceStr_test } from '../constant';

// LLMs 
const queryModel = new ChatGroq({ model: "llama-3.3-70b-versatile", temperature: 0, streaming: true })
const writerModel_ = new ChatGoogleGenerativeAI({ model: "gemini-2.0-pro-exp-02-05", temperature: 0 })
const thinkingModel = new ChatGoogleGenerativeAI({
  model: 'gemini-2.0-flash-thinking-exp-01-21',
  temperature: 0,
})

// Node functions for graph workflow
export async function generateQueries(
  state: typeof SectionStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ search_queries: SearchQuery[] }> {
  const topic = state.topic;
  const section = state.section;
  const configurable = ensureConfiguration(config);
  const numberOfQueries = configurable.number_of_queries;

  const structuredLlm = queryModel.withStructuredOutput(QueriesSchema);

  // Format system instructions
  const systemInstructions = queryWriterInstructions
    .replace('{topic}', topic)
    .replace('{section_topic}', section.description)
    .replace('{number_of_queries}', numberOfQueries.toString());

  // Generate queries (await the promise)
  const queries = await structuredLlm.invoke([
    new SystemMessage(systemInstructions),
    new HumanMessage("Generate search queries on the provided topic.")
  ]);
  //const queries = queries_test;
  console.log("queries=========================>", queries)

  // Return the formatted queries
  return { search_queries: queries.queries as SearchQuery[] };
}

export async function searchWeb(
  state: typeof SectionStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ source_str: string, search_iterations: number }> {
  const searchQueries = state.search_queries;
  const configurable = ensureConfiguration(config);
  const searchApi = configurable.search_api;

  // Web search
  const queryList = searchQueries
  .map(query => query.search_query)
  .filter((query): query is string => query !== null);
  
  let searchResults, sourceStr;

  if (searchApi === "tavily") {
    searchResults = await tavilySearchAsync(queryList);
    //console.log("searchResults", JSON.stringify(searchResults))
    sourceStr = deduplicateAndFormatSources({searchResults, maxTokensPerSource: 1000, includeRawContent: true});
  } else if (searchApi === "perplexity") {
    searchResults = await perplexitySearchAsync(queryList);
    sourceStr = deduplicateAndFormatSources({searchResults, maxTokensPerSource: 1000, includeRawContent: false});
  } else {
    throw new Error(`Unsupported search API: ${configurable.search_api}`);
  }

  //console.log("sourceStr==============>", sourceStr)
  return { source_str: sourceStr, search_iterations: state.search_iterations + 1 };
}

export async function writeSection(
  state: typeof SectionStateAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<Command<"END" | "search_web">> {
  // Get state
  const topic = state.topic;
  const section = state.section;
  const sourceStr = state.source_str;
  
  const configurable = ensureConfiguration(config)
  
  // Format system instructions
  const systemInstructions = sectionWriterInstructions
    .replace('{topic}', topic)
    .replace('{section_title}', section.name)
    .replace('{section_topic}', section.description)
    .replace('{context}', sourceStr)
    .replace('{section_content}', section.content);
  
  // Generate section
  const sectionContent = await thinkingModel.invoke([
    new SystemMessage(systemInstructions),
    new HumanMessage("Generate a report section based on the provided sources.")
  ]);
  
  // Write content to the section object
  section.content = extractMessageContent(sectionContent);
  
  // Feedback
  const sectionGraderMessage = `Grade the report and consider follow-up questions for missing information.
  If the grade is 'pass', return empty strings for all follow-up queries.
  If the grade is 'fail', provide specific search queries to gather missing information.`
    
  // Grade prompt
  const sectionGraderInstructionsFormatted = sectionGraderInstructions
    .replace('{topic}', topic)
    .replace('{section_topic}', section.description)
    .replace('{section}', section.content);
  
  const plannerProvider = configurable.planner_provider;
  const plannerModel = configurable.planner_model;

  let feedback: Feedback;

  if (plannerModel === "claude-3-7-sonnet-latest") {
    const reflectionModel = initChatModel(plannerModel, {
      modelProvider: plannerProvider,
      maxOutputTokens: 20000,
      thinking: {"type": "enabled", "budget": 16000}
    })
    const reflection_result = (await reflectionModel).bindTools([FeedbackSchema]).invoke([
      new SystemMessage(sectionGraderInstructionsFormatted),
      new HumanMessage(sectionGraderMessage)
    ])
    const toolCall = (await reflection_result).tool_calls[0]['args']
    feedback = FeedbackHelper.modelValidate(toolCall);
  } else {
    const structuredLlm = queryModel.withStructuredOutput(FeedbackSchema);
    feedback = await structuredLlm.invoke([
      new SystemMessage(sectionGraderInstructionsFormatted),
      new HumanMessage(sectionGraderMessage)
    ]);
  }
  

  console.log("feedback", feedback)
  
  if (feedback.grade === "pass" || state.search_iterations >= configurable.max_search_depth) {
    // Publish the section to completed sections
    return new Command({
      goto: "END",
      update: { completed_sections: [section] }
    });
    // Update the existing section with new content and update search queries
  } else {
    return new Command({
      goto: "search_web",
      update: { search_queries: feedback.follow_up_queries, section: section }
    });
  }
}

export const sectionBuilder = new StateGraph({
  stateSchema: SectionStateAnnotation,
  output: SectionStateOutput
})
  .addNode("generate_queries", generateQueries)
  .addNode("search_web", searchWeb)
  .addNode("write_section", writeSection)
  .addEdge(START, "generate_queries")
  .addEdge("generate_queries", "search_web")
  .addEdge("search_web", "write_section")
  .addEdge("write_section", END);

export async function createSectionGraph() {
  const sectionGraph = sectionBuilder.compile();
  return sectionGraph;
}