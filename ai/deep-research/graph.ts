import {
  interrupt,
  StateGraph,
  START,
  END,
  Send,
  LangGraphRunnableConfig,
  Command
} from '@langchain/langgraph'
import { initChatModel } from 'langchain/chat_models/universal';
//import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from '@langchain/groq'
import { ChatGoogleGenerativeAI } from '@langchain/google-genai'
import { ChatGoogle } from '@langchain/google-gauth'
import {
  type BaseCheckpointSaver,
  type BaseStore
} from '@langchain/langgraph-checkpoint'
import { PostgresSaver } from '@langchain/langgraph-checkpoint-postgres'
import { SystemMessage, HumanMessage } from '@langchain/core/messages'

import {
  ReportStateInput,
  ReportStateOutput,
  ReportStateAnnotation,
  SectionStateAnnotation,
  Section,
  Sections,
  ReportData,
  ReportState
} from './state'
import { QueriesSchema, SectionsSchema } from './schemas'
import {
  reportPlannerInstructions,
  reportPlannerQueryWriterInstructions,
  finalSectionWriterInstructions
} from './prompts'
import {
  SearchParamsMap,
  ArxivSearchParams,
  LinkupSearchParams,
  PubMedSearchParams,
  ExaSearchParams,
  formatSections,
  extractMessageContent,
  getSearchParams,
  exaSearch,
  arxivSearchAsync,
  pubmedSearchAsync,
  linkupSearchAsync,
  tavilySearchAsync,
  perplexitySearchAsync,
  deduplicateAndFormatSources
} from './utils'
import { ConfigurationAnnotation, ensureConfiguration } from './configuration'
import { sectionBuilder } from './section_graph/graph'
import { deleteCompletedSections } from '@/ai/utils/delete_completed_sections'
import { v4 as uuidv4 } from 'uuid'
import { sections_test } from './constant'

// LLMs
//const planner_model_ = new ChatOpenAI({model: "o3-mini", temperature: 0, reasoningEffort: "medium"})
const groq_planner_model = new ChatGroq({
  model: 'deepseek-r1-distill-llama-70b',
  temperature: 0.6,
})
const groq_writer_model = new ChatGroq({
  model: 'llama-3.3-70b-versatile',
  temperature: 0
})
const planner_model = new ChatGoogleGenerativeAI({
  model: 'gemini-2.0-flash-thinking-exp-01-21',
  temperature: 0,
})
const writer_model = new ChatGoogleGenerativeAI({
  model: 'gemini-2.0-pro-exp-02-05',
  temperature: 1.0
})

export async function generateReportPlan(
  state: typeof ReportStateAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<{ reports: (typeof ReportStateAnnotation.State)[] }> {
  // Create a new report.
  const currentReport = {
    id: uuidv4(),
    topic: state.topic,
    sections: [] as Section[],
    report_sections_from_research: '',
    final_report: ''
  };

  // Extract data from the current report
  const topic = state.topic
  if (!topic) {
    throw new Error('No topic provided in state')
  }
  const feedback = state.feedback_on_report_plan || null
  // Ensure configuration is valid
  const configurable = ensureConfiguration(config)
  const report_structure = configurable.report_structure
  const number_of_queries = configurable.number_of_queries
  const searchApi = configurable.search_api;
  const searchApiConfig = configurable.search_api_config
  const params_to_pass = getSearchParams(searchApi, searchApiConfig) as SearchParamsMap[typeof searchApi];
  console.log("params_to_pass", params_to_pass)

  const report_organization =
    typeof report_structure === 'object'
      ? JSON.stringify(report_structure)
      : report_structure

  const llm = new ChatGroq({
    model: 'llama-3.3-70b-versatile',
    temperature: 0
  })
  const structuredLlm = llm.withStructuredOutput(QueriesSchema)

  // Format system instructions for generating search queries.
  const systemInstructionsQuery = reportPlannerQueryWriterInstructions
    .replace('{topic}', topic)
    .replace('{report_organization}', report_organization)
    .replace('{number_of_queries}', number_of_queries.toString())

  // Generate search queries.
  const queryResults = await structuredLlm.invoke([
    new SystemMessage(systemInstructionsQuery),
    new HumanMessage(
      'Generate search queries that will help with planning the sections of the report.'
    )
  ])
  console.log("queryResults", queryResults)

  const queryList = queryResults.queries
    .map(item => item.search_query)
    .filter((query): query is string => query !== null)

  // Perform web search.
  const getSearchPromise = () => {
    if (searchApi === "tavily") {
      return tavilySearchAsync(queryList).then(results => {
        console.log("searchResults", results);
        return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
      });
    } else if (searchApi === "perplexity") {
      return perplexitySearchAsync(queryList).then(results => {
        return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
      });
    } else if (searchApi === "exa") {
      return exaSearch(queryList, params_to_pass as ExaSearchParams).then(results => {
        return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
      });
    } else if (searchApi === "pubmed") {
      return pubmedSearchAsync(queryList, params_to_pass as PubMedSearchParams).then(results => {
        return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
      })
    } else if (searchApi === "arxiv") {
      return arxivSearchAsync(queryList, params_to_pass as ArxivSearchParams).then(results => {
        return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
      })
    } else if (searchApi === "linkup") {
        return linkupSearchAsync(queryList, params_to_pass as LinkupSearchParams).then(results => {
          return deduplicateAndFormatSources({searchResults: results, maxTokensPerSource: 1000, includeRawContent: false});
        })
    } else {
      throw new Error(`Unsupported search API: ${configurable.search_api}`);
    }
  };

  // Then run both operations in parallel
  const [sourceStr, deleteResult] = await Promise.all([
    getSearchPromise(),
    deleteCompletedSections("completed_sections", "/research")
  ]);

  //console.log("sourceStr", JSON.stringify(sourceStr, null, 2));

  // Format system instructions for generating sections (including feedback).
  const systemInstructionsSections = reportPlannerInstructions
    .replace('{topic}', topic)
    .replace('{report_organization}', report_organization)
    .replace('{context}', sourceStr)
    .replace('{feedback}', feedback)

  console.log('systemInstructionsSections', systemInstructionsSections)

  const plannerMessage = `Generate the sections of the report. Your response must include a 'sections' field containing a list of sections. 
Each section must have: name, description, plan, research, and content fields.`

  const plannerProvider = configurable.planner_provider;
  const plannerModel = configurable.planner_model;

  let sections: Section[];

  if (plannerModel === "claude-3-7-sonnet-latest") {
    const planner_llm = initChatModel(plannerModel, {
      modelProvider: plannerProvider,
      maxOutputTokens: 20000,
      thinking: {"type": "enabled", "budget": 16000}
    })
    const report_sections = (await planner_llm).bindTools([SectionsSchema]).invoke([
      new SystemMessage(systemInstructionsSections),
      new HumanMessage(plannerMessage),
    ])

    const toolCall = (await report_sections).tool_calls[0]['args']
    const validated_report_sectionss = Sections.modelValidate(toolCall)
    sections = validated_report_sectionss.sections
  } else {
    const planner_llm = initChatModel(plannerModel, {
      modelProvider: plannerProvider,
    })

    const structured_llm = groq_planner_model.withStructuredOutput(SectionsSchema)
    const report_sections = await structured_llm.invoke([
      new SystemMessage(systemInstructionsSections),
      new HumanMessage(plannerMessage)
    ])
    const sectionsWithDefaults = report_sections.sections.map((section: Section) => ({
      ...section,
      content: section.content ?? '',
      name: section.name ?? '',
      description: section.description ?? '',
      research: section.research ?? false
    }))
    sections = sectionsWithDefaults
  }

  // Generate report sections.
  /*const sectionsLlm = llm.withStructuredOutput(SectionsSchema)
  const reportSections = await sectionsLlm.invoke([
    new SystemMessage(systemInstructionsSections),
    new HumanMessage(plannerMessage)
  ])

  console.log('reportSections', reportSections)

  const sectionsWithDefaults = reportSections.sections.map((section: Section) => ({
    ...section,
    content: section.content ?? '',
    name: section.name ?? '',
    description: section.description ?? '',
    research: section.research ?? false
  }))
  */
  //const sectionsWithDefaults = sections_test;
  //console.log('sectionsWithDefaults', sectionsWithDefaults)

  // Create an updated report by replacing the sections.
  const updatedReport = {
    ...currentReport,
    sections: sections,
  }
  console.log("updatedReport", updatedReport)
  // Merge the updated report into the reports array.
  let updatedReports: any[];
  if (state.reports && state.reports.length > 0) {
    updatedReports = state.reports.concat(updatedReport);
  } else {
    updatedReports = [updatedReport];
  }
  
  return { reports: updatedReports };
}


export async function humanFeedback(
  state: typeof ReportStateAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<
  Command<'generate_report_plan' | 'build_section_with_web_research'>
> {
  // Get sections
  //console.log('humanFeedback state======>', JSON.stringify(state, null, 2))
  const currentReport = state.reports.at(-1)
  const topic = state.topic
  const sections = currentReport.sections

  // Format sections for display
  const sectionsStr = sections
    .map(
      section =>
        `Section: ${section.name}\n` +
        `Description: ${section.description}\n` +
        `Research needed: ${section.research ? 'Yes' : 'No'}`
    )
    .join('\n\n')

  // Get feedback on the report plan from interrupt
  const feedback = interrupt(
    `Please provide feedback on the following report plan. \n\n${sectionsStr}\n\n` +
      `Does the report plan meet your needs? Pass 'true' to approve the report plan or provide feedback to regenerate the report plan:`
  )

  //const feedback = true;

  console.log("feedback", feedback)
  // If the user approves the report plan, kick off section writing
  if (typeof feedback === 'boolean' && feedback === true) {
    // Treat this as approve and kick off section writing
    return new Command({
      goto: sections
        .filter(s => s.research)
        .map(
          s =>
            new Send('build_section_with_web_research', {
              topic,
              section: s,
              search_iteration: 0
            })
        )
    })
  }

  // If the user provides feedback, regenerate the report plan
  else if (typeof feedback === 'string') {
    // treat this as feedback
    return new Command({
      goto: 'generate_report_plan',
      update: { feedback_on_report_plan: feedback }
    })
  } else {
    throw new TypeError(
      `Interrupt value of type ${typeof feedback} is not supported.`
    )
  }
}

export async function writeFinalSections(
  state: typeof SectionStateAnnotation.State
): Promise<{ completed_sections: Section[] }> {
  console.log("Writing section state:", state);
  console.log("Writing section:", state.section.name);

  const section = state.section;
  const completedReportSections = state.report_sections_from_research;

  const google = new ChatGoogleGenerativeAI({
    temperature: 0,
    model: 'gemini-2.0-flash-exp',
  });

  const systemInstructions = finalSectionWriterInstructions
    .replace('{topic}', state.topic)
    .replace('{section_title}', section.name)
    .replace('{section_topic}', section.description)
    .replace('{context}', completedReportSections);

  try {
    const sectionContent = await planner_model.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage('Generate a report section based on the provided sources.')
    ]);

    const content = extractMessageContent(sectionContent);

    const updatedSection: Section = {
      ...section,
      content: content
    };

    // Ensure we have an array for completed_sections.
    const currentCompletedSections = Array.isArray(state.completed_sections)
      ? state.completed_sections
      : [];

    return { completed_sections: [updatedSection] };
  } catch (error) {
    console.error('Error generating section content:', error);

    const groq = new ChatGroq({
      model: 'llama-3.3-70b-versatile',
      temperature: 0,
    });

    const fallbackContent = await groq.invoke([
      new SystemMessage(systemInstructions),
      new HumanMessage('Generate a report section based on the provided sources.')
    ]);

    const fallbackSection: Section = {
      ...section,
      content: extractMessageContent(fallbackContent)
    };

    const currentCompletedSections = Array.isArray(state.completed_sections)
      ? state.completed_sections
      : [];

    return { completed_sections: [fallbackSection] };
  }
}


export function gatherCompletedSections(
  state: typeof ReportStateAnnotation.State
): ReportState {
  console.log('gatherCompletedSections===============>', state)
  if (!state.reports || state.reports.length === 0) return state

  const reports = [...state.reports]
  const currentReport = reports.at(-1)

  if (!currentReport) return state // Safety check

  // Merge new completed sections (ensure they persist in the report)
  const updatedCompletedSections = [
    //...currentReport.completed_sections,
    ...(state.completed_sections || []) // Fetch completed sections from section state
  ]

  // Format completed sections for report context
  const completedReportSections = formatSections(updatedCompletedSections)

  // Update the current report
  const updatedReport = {
    ...currentReport,
    //completed_sections: updatedCompletedSections, // Persisting completed sections
    report_sections_from_research: completedReportSections
  }

  // Update the reports array with the modified current report
  reports[reports.length - 1] = updatedReport

  // Return the updated state
  return {
    ...state,
    reports
  }
}

export function initiateFinalsectionWriting(
  state: typeof ReportStateAnnotation.State
): Send[] {
  //console.log('initiateFinalsectionWriting state===============>', state)
  if (!state.reports || state.reports.length === 0) return []
  const currentReport = state.reports[state.reports.length - 1]
  // Kick off section writing in parallel for sections not requiring research
  return currentReport.sections
    .filter(s => !s.research)
    .map(
      s =>
        new Send('write_final_sections', {
          topic: state.topic,
          section: s,
          report_sections_from_research:
            currentReport.report_sections_from_research
        })
    )
}

export function compileFinalReport(
  state: typeof ReportStateAnnotation.State,
): { final_report: string, reports: ReportData[] } {
  if (!state.reports || state.reports.length === 0) return { final_report: '', reports: [] };
  //console.log('compileFinalReport state============>', state);
  
  // Copy the reports array and get the current (latest) report.
  const updatedReports = [...state.reports];
  const currentReportIndex = updatedReports.length - 1;
  const currentReport = updatedReports[currentReportIndex];
  const sections = currentReport.sections;
  
  // Build a lookup from completed_sections by section name.
  const completedSections = state.completed_sections.reduce(
    (acc, s) => {
      acc[s.name] = s.content;
      return acc;
    },
    {} as Record<string, string>
  );
  
  // Update each section with completed content if available.
  const updatedSections = sections.map(section => ({
    ...section,
    content: completedSections[section.name] || section.content
  }));
  
  // Compile the final report by joining section contents.
  const newFinalReport = updatedSections.map(s => s.content).join('\n\n');
  
  // Update the current report's final_report field.
  const updatedReport = {
    ...currentReport,
    final_report: newFinalReport,
  };

  //console.log("updatedReport888888888888888888888888888888888888888888888888888888888888888888888888===============>", updatedReport)
  
  // Replace the last report in the reports array.
  updatedReports[currentReportIndex] = updatedReport;
  
  // and return the new final report.
  return { final_report: newFinalReport, reports: updatedReports };
}

export async function createGraph(
  checkpointer?: PostgresSaver,
  store?: BaseStore
): Promise<any> {
  if (checkpointer) {
    await checkpointer.setup()
  }
  const builder = new StateGraph(
    {
      stateSchema: ReportStateAnnotation,
      input: ReportStateInput,
      output: ReportStateOutput
    },
    ConfigurationAnnotation
  )
    .addNode('generate_report_plan', generateReportPlan)
    .addNode("human_feedback", humanFeedback)
    .addNode('build_section_with_web_research', sectionBuilder.compile(), {
      ends: ["build_section_with_web_research"]
    })
    .addNode('gather_completed_sections', gatherCompletedSections)
    .addNode('write_final_sections', writeFinalSections)
    .addNode('compile_final_report', compileFinalReport)
    .addEdge(START, 'generate_report_plan')
    .addEdge("generate_report_plan", "human_feedback")
    .addEdge('build_section_with_web_research', 'gather_completed_sections')
    .addConditionalEdges(
      'gather_completed_sections',
      initiateFinalsectionWriting,
      ['write_final_sections']
    )
    .addEdge('write_final_sections', 'compile_final_report')
    .addEdge('compile_final_report', END)

  return builder.compile({
    checkpointer
    //store,
    //interruptBefore: ["human_feedback"],
  }).withConfig({ runName: "deep_research" })
}
