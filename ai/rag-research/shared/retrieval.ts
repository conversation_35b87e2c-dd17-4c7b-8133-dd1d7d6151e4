import { RunnableConfig } from "@langchain/core/runnables";
import { Document } from "@langchain/core/documents";
import { VectorStoreRetriever } from "@langchain/core/vectorstores";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import { Embeddings } from "@langchain/core/embeddings";
import { CohereEmbeddings } from "@langchain/cohere";
import { OpenAIEmbeddings } from "@langchain/openai";
import { ensureBaseConfiguration } from "./configuration";

async function makeMemoryRetriever(
  configuration: ReturnType<typeof ensureBaseConfiguration>,
  embeddingModel: Embeddings,
): Promise<VectorStoreRetriever> {

  let auth: { username: string; password: string } | { apiKey: string };
  
  const vectorStore = new MemoryVectorStore(embeddingModel)
  const filters = configuration?.searchKwargs || null;

  return vectorStore.asRetriever({
    filter: filters
      ? (doc: Document) => Object.entries(filters).every(([key, value]) => doc.metadata[key] === value)
      : undefined, // No filter applied
  });

}

function makeTextEmbeddings(modelName: string): Embeddings {
  /**
   * Connect to the configured text encoder.
   */
  const index = modelName.indexOf("/");
  let provider, model;
  if (index === -1) {
    model = modelName;
    provider = "openai"; // Assume openai if no provider included
  } else {
    provider = modelName.slice(0, index);
    model = modelName.slice(index + 1);
  }
  switch (provider) {
    case "openai":
      return new OpenAIEmbeddings({ model });
    case "cohere":
      return new CohereEmbeddings({ model });
    default:
      throw new Error(`Unsupported embedding provider: ${provider}`);
  }
}

export async function makeRetriever(
  config: RunnableConfig,
): Promise<VectorStoreRetriever> {
  const configuration = ensureBaseConfiguration(config);
  const embeddingModel = makeTextEmbeddings(configuration.embeddingModel);

  switch (configuration.retrieverProvider) {
    case "memory":
      return makeMemoryRetriever(configuration, embeddingModel)
    default:
      throw new Error(
        `Unrecognized retrieverProvider in configuration: ${configuration.retrieverProvider}`,
      );
  }
}