/**
 * Define the configurable parameters for the agent.
 */

import { Annotation } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { v4 as uuidv4 } from 'uuid';
/**
 * typeof ConfigurationAnnotation.State class for indexing and retrieval operations.
 *
 * This annotation defines the parameters needed for configuring the indexing and
 * retrieval processes, including user identification, embedding model selection,
 * retriever provider choice, and search parameters.
 */
export const BaseConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>(),
  assistant_id: Annotation<string>(),
  thread_id: Annotation<string>(),
  schemas: Annotation<Record<string, MemoryConfig>>(),
  delay: Annotation<number | undefined>(),
  /**
   * Name of the embedding model to use. Must be a valid embedding model name.
   */
  embeddingModel: Annotation<string>,

  /**
   * The vector store provider to use for retrieval.
   * Options are 'elastic', 'elastic-local', 'pinecone', or 'mongodb'.
   */
  retrieverProvider: Annotation<
    "memory"
  >,

  /**
   * Additional keyword arguments to pass to the search function of the retriever.
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  searchKwargs: Annotation<Record<string, any>>,
});

/**
 * Create an typeof BaseConfigurationAnnotation.State instance from a RunnableConfig object.
 *
 * @param config - The configuration object to use.
 * @returns An instance of typeof BaseConfigurationAnnotation.State with the specified configuration.
 */
export function ensureBaseConfiguration(
  config: RunnableConfig,
): typeof BaseConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<
    typeof BaseConfigurationAnnotation.State
  >;
  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    schemas: configurable?.schemas || {},
    delay: configurable?.delay || 5,
    embeddingModel:
      configurable.embeddingModel || "cohere/embed-multilingual-light-v3.0",
    retrieverProvider: configurable.retrieverProvider || "memory",
    searchKwargs: configurable.searchKwargs || {},
  };
}