import {
  LangGraphRunnableConfig,
  START,
  StateGraph,
  END,
} from "@langchain/langgraph";
import { BaseMessage, SystemMessage, ToolMessage,
  AIMessage, HumanMessage, RemoveMessage } from "@langchain/core/messages";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { initChatModel } from "langchain/chat_models/universal";
import { SqlDatabase } from "langchain/sql_db";
import { createSqlAgent, SqlToolkit } from "langchain/agents/toolkits/sql";
import { DataSource } from "typeorm";
import { 
  convertToOpenAITool } from "@langchain/core/utils/function_calling";
import { initializeTools } from "./tools";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { GraphAnnotation } from "./state";
import { getStoreFromConfigOrThrow, splitModelAndProvider } from "./utils";
import { v4 as uuidv4 } from 'uuid';

const datasource = new DataSource({
  type: "postgres",
  url: "postgresql://comfyminds_owner:<EMAIL>/comfyminds?sslmode=require&connect_timeout=15&connection_limit=20&pool_timeout=15",
});

//const llm = await initChatModel();
const llm_ = new ChatGroq({
  model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
  temperature: 0,
  streaming: true
})

const llm = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true
})

const filterMessages = (messages: BaseMessage[]) => {
  return messages.slice(-8)
}
async function callModel(
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig,
): Promise<{ messages: BaseMessage[] }> {
  const { messages, summary } = state
  const question = messages[0].content as string;
  const store = getStoreFromConfigOrThrow(config);
  const configurable = ensureConfiguration(config);
  const memories = await store.search(["memories", configurable.user_id], {
    limit: 10,
  });
  console.log("====================memories====================", memories)

  let formatted =
    memories
      ?.map((mem) => `[${mem.key}]: ${JSON.stringify(mem.value)}`)
      ?.join("\n") || "";
  if (formatted) {
    formatted = `\n<memories>\n${formatted}\n</memories>`;
  }

  console.log("====================summary====================", summary)
  const sys = configurable.system_prompt
    .replace("{user_info}", formatted)
    .replace("{time}", new Date().toISOString()) +
    (summary ? `\nSummary of conversation earlier: ${summary}` : "");

  const search = new TavilySearchResults({ maxResults: 1 });
  const tools = initializeTools(config);
  const oaiTools = tools.map((tool) => convertToOpenAITool(tool));

  const boundLLM = llm.bind({
    tools: tools,
    tool_choice: "auto",
  });

  console.log("=================sys============", sys)

  let modelMessages = [];
  for (let i = state.messages.length - 1; i >= 0; i--) {
    modelMessages.push(state.messages[i]);
    if (modelMessages.length >= 5) {
      if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
        break;
      }
    }
  }
  modelMessages.reverse();
  
  /*const result = await boundLLM.invoke(
    [{ role: "system", content: sys }, ...modelMessages],
    {
      configurable: splitModelAndProvider(configurable?.model!),
    },
  );*/

  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });
  
  const toolkit = new SqlToolkit(db, llm);
  const executor = createSqlAgent(llm, toolkit);
  const input = `List the content to answer question. 
<SQL_query_example>
SELECT content FROM Message WHERE userId = 'user_2mZV57t33M5DFaeAPx9iYYjYUZP' LIMIT 10
</SQL_query_example>
 
<data_schema>
"Message" (
  id UUID PRIMARY KEY,
  role Role,
  content TEXT,
  updatedAt TIMESTAMP(3) NOT NULL,
  userId TEXT NOT NULL,
)
TYPE "Role" AS ('user', 'assistant', 'system', 'function', 'tool', 'data')

</data_schema>

Only retrieval queries are allowed.

Question: ${question}`;

  const sqlResult = await executor.invoke({ input });
  console.log(`Got sql output====================>>>> ${sqlResult.output}`);

  return { messages: [sqlResult.output] };
}

const summarizeConversation = async (messages: any[], summary: string | undefined) => {
  // Filtering only for Human and AI messages
  const relevantMessages = messages.filter(msg => msg instanceof HumanMessage || msg instanceof AIMessage);
  let summaryMessage: string;
  if (summary) {
    // If a summary already exists, we use a different system prompt
    // to summarize it than if one didn't
    summaryMessage = `This is summary of the conversation to date: ${summary}\n\n` +
      "Extend the summary by taking into account the new messages above:";
  } else {
    summaryMessage = "Create a summary of the conversation above:";
  }

  const allMessages = [...relevantMessages, new HumanMessage({
    id: uuidv4(),
    content: summaryMessage,
  })];
  console.log("//////////////allMessages in summarizeConversation/////////////", allMessages)
  // We now need to delete messages that we no longer want to show up
  // I will delete all but the last two messages, but you can change this
  const deleteMessages = relevantMessages.slice(0, -2).map(
    (m) => new RemoveMessage({ id: m.id as string })
  );
  const response = await llm.invoke(allMessages);

  if (typeof response.content !== "string") {
    throw new Error("Expected a string response from the model");
  }
  console.log("//////////////deleteMessages in summarizeConversation/////////////", deleteMessages)
  
  return { summary: response.content, messages: deleteMessages };
}

async function storeMemory(
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig,
): Promise<{ messages: BaseMessage[]; summary: string }> {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  const toolCalls = lastMessage.tool_calls || [];

  const tools = initializeTools(config);
  const upsertMemoryTool = tools[0];

    // Initialize summarizePromise with a resolved default if there are fewer than six messages
  let summarizePromise: Promise<{ summary: string; messages: RemoveMessage[] }> =
  state.messages.length > 6
    ? summarizeConversation(state.messages, state.summary)
    : Promise.resolve({ summary: state.summary ?? "", messages: [] });

  // Call upsertMemoryTool for each tool call in parallel
  const savedMemoriesPromise = Promise.all(
    toolCalls.map((tc) => upsertMemoryTool.invoke(tc)),
  );

  // Wait for both operations to complete
  const [summarizeResult, savedMemories] = await Promise.all([
    summarizePromise,
    savedMemoriesPromise,
  ]);

  console.log("summarizedResult", summarizeResult);
  console.log("savedMemories", savedMemories);

  // Construct the result based on the summarizeConversation and savedMemories
  return {
    summary: summarizeResult.summary,
    messages: savedMemories,
  };
}

function routeMessage(
  state: typeof GraphAnnotation.State,
): "store_memory" | typeof END {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  if (lastMessage && lastMessage.tool_calls?.length) {
    return "store_memory";
  }
  return END;
}

export const builder = new StateGraph(
  {
    stateSchema: GraphAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("call_model", callModel)
  .addNode("store_memory", storeMemory)
  .addEdge(START, "call_model")
  .addConditionalEdges("call_model", routeMessage, {
    store_memory: "store_memory",
    [END]: END,
  })
  .addEdge("store_memory", "call_model");

