


import { LangGraphRunnableConfig } from "@langchain/langgraph";
import { ToolMessage } from "@langchain/core/messages";
import { SYSTEM_PROMPT } from "@/ai/memory_service//prompts"
import { builder } from "@/ai/memory_service/graph";
import { GraphAnnotation } from "@/ai/memory/state"
import { createGraph } from "@/ai/utils"
import { ensureConfiguration } from "../configuration"

export const memoryNode = async (
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const configurable = ensureConfiguration(config);
  //console.log("memoryConfigurable: ", JSON.stringify(configurable, null, 2))
  const memoryConfig = {
    recursionLimit: 6,
    configurable: {
      delay: 0,
      user_id: configurable.user_id,
      assistant_id: configurable.assistant_id,
      thread_id: configurable.thread_id,
      model: "groq/llama-3.3-70b-versatile",
      schemas: configurable.schemas,
    }
  };
  //console.log("memoryConfig: ", JSON.stringify(memoryConfig, null, 2))

  let modelMessages = [];
  for (let i = state.messages.length - 1; i >= 0; i--) {
    modelMessages.push(state.messages[i]);
    if (modelMessages.length >= 5) {
      if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
        break;
      }
    }
  }
  modelMessages.reverse();
  const graph = await createGraph(builder)
  const memoryInput = { 
    messages: modelMessages, 
    summary: state?.summary, 
  };
  //console.log("====================================memoryInput===============================: ", memoryInput)
  const memoryGraphOutput = await graph.invoke(memoryInput, memoryConfig);
    // We enqueue the memory formation process on the same thread.
    // This means that IF this thread doesn't receive more messages before `afterSeconds`,
    // it will read from the shared state and extract memories for us.
    // If a new request comes in for this thread before the scheduled run is executed,
    // that run will be canceled, and a **new** one will be scheduled once
    // this node is executed again.
  return {};
};