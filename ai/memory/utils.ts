import { BaseStore, LangGraphRunnableConfig } from "@langchain/langgraph";
/**
 * Get the store from the configuration or throw an error.
 */
export function getStoreFromConfigOrThrow(
  config: LangGraphRunnableConfig,
): BaseStore {
  if (!config.store) {
    throw new Error("Store not found in configuration");
  }

  return config.store;
}

/**
 * Split the fully specified model name into model and provider.
 */
export function splitModelAndProvider(fullySpecifiedName: string): {
  model: string;
  provider?: string;
} {
  let provider: string | undefined;
  let model: string;

  if (fullySpecifiedName.includes("/")) {
    [provider, model] = fullySpecifiedName.split("/", 2);
  } else {
    model = fullySpecifiedName;
  }

  return { model, provider };
}


/**
 * Replaces placeholders in a path template with actual values.
 * @param template The path template string with placeholders like `{user_id}`
 * @param values An object containing the keys and values to replace in the template
 * @returns The formatted path with placeholders replaced by actual values
 */
export function formatPath(template: string, values: Record<string, string>): string {
  return template.replace(/\{(\w+)\}/g, (_, key) => {
    if (key in values) {
      return values[key];
    }
    throw new Error(`Missing value for placeholder: ${key}`);
  });
}

/**
// Format PATCH_PATH
const patchPath = formatPath(PATCH_PATH, { user_id: assistant_id, function_name: functionName });
console.log("Formatted PATCH_PATH:", patchPath);
// Output: "user/12345/patches/reflectionMemory"

// Format INSERT_PATH
const insertPath = formatPath(INSERT_PATH, { user_id: assistant_id, function_name: functionName, event_id: eventId });
console.log("Formatted INSERT_PATH:", insertPath);
// Output: "user/12345/inserts/reflectionMemory/67890"
*/
