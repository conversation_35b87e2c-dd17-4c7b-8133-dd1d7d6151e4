import {
  LangGraphRunnableConfig,
  START,
  StateGraph,
  END,
} from "@langchain/langgraph";
import { BaseMessage, AIMessage, HumanMessage, RemoveMessage, filterMessages } from "@langchain/core/messages";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { CohereEmbeddings } from "@langchain/cohere";
import { initChatModel } from "langchain/chat_models/universal";
import { 
  convertToOpenAITool } from "@langchain/core/utils/function_calling";
import { initializeTools, createUpsertReflectionTool, createRecallReflectionTool } from "./tools";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { GraphAnnotation } from "./state";
import { getStoreFromConfigOrThrow, splitModelAndProvider } from "./utils";
import { createVectorStore } from "@/ai/memory_service/settings"
import { type FormatOptions, formatMemories } from "@/ai/memory_service/utils"
import { memoryNode } from "./nodes";
import { v4 as uuidv4 } from 'uuid';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
  temperature: 0,
  streaming: true
})

const llm_ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
})

async function callModel(
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig,
): Promise<{ messages: BaseMessage[] }> {
  const { messages, summary, user_memories } = state
  const store = getStoreFromConfigOrThrow(config);
  const configurable = ensureConfiguration(config);
  const memories = await store.search(["memories", configurable.user_id], {
    limit: 10,
  });

  let formattedMemories: string[] = []
  if (user_memories) {
    formattedMemories = formatMemories(user_memories);
  }

  //console.log("configurable: ", configurable)
  //console.log("====================memories====================", user_memories)
  console.log("====================formattedMemories====================", formattedMemories)
  let formatted =
    memories
      ?.map((mem) => `[${mem.key}]: ${JSON.stringify(mem.value)}`)
      ?.join("\n") || "";
  if (formatted) {
    formatted = `\n<memories>\n${formatted}\n</memories>`;
  }

  //console.log("====================summary====================", summary)
  const sys = configurable.system_prompt
    .replace("{user_info}", formattedMemories.join(", "))
    .replace("{time}", new Date().toISOString()) +
    (summary ? `\nSummary of conversation earlier: ${summary}` : "");

  const tools = initializeTools(config);
  const oaiTools = tools.map((tool) => convertToOpenAITool(tool));

  const boundLLM = llm.bind({
    tools: oaiTools,
    tool_choice: "auto",
  });

  console.log("=================sys============", sys)

  const result = await llm.invoke(
    [{ role: "system", content: sys }, ...messages],
    {
      configurable: splitModelAndProvider(configurable?.model!),
    },
  );
  return { messages: [result] };
}

function deleteMessages(state: typeof GraphAnnotation.State) {
  const messages = state.messages;
  if (messages.length > 5) {
    return { messages: messages.slice(0, -5).map(m => new RemoveMessage({ id: m?.id! })) };
  }
  return {};
}

async function summarizeConversation(
  state: typeof GraphAnnotation.State
): Promise<Partial<typeof GraphAnnotation.State>> {
  const { summary, messages } = state;
  // Filtering only for Human and AI messages
  const relevantMessages = messages.filter(msg => msg instanceof HumanMessage || msg instanceof AIMessage);
  let summaryMessage: string;
  if (summary) {
    // If a summary already exists, we use a different system prompt
    // to summarize it than if one didn't
    summaryMessage = `This is summary of the conversation to date: ${summary}\n\n` +
      "Extend the summary by taking into account the new messages above:";
  } else {
    summaryMessage = "Create a summary of the conversation above:";
  }

  const allMessages = [...relevantMessages, new HumanMessage({
    id: uuidv4(),
    content: summaryMessage,
  })];
  //console.log("//////////////allMessages in summarizeConversation/////////////", allMessages)
  // We now need to delete messages that we no longer want to show up
  // I will delete all but the last two messages, but you can change this
  const deleteMessages = messages.slice(0, -2).map(
    (m) => new RemoveMessage({ id: m?.id! })
  );
  const response = await llm.invoke(allMessages);

  if (typeof response.content !== "string") {
    throw new Error("Expected a string response from the model");
  }
  //console.log("//////////////deleteMessages in summarizeConversation/////////////", deleteMessages)
  
  return { summary: response.content };
}

async function queryMemories(
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig,
): Promise<Partial<typeof GraphAnnotation.State>> {
  const configurable = ensureConfiguration(config);
  const user_id = configurable.user_id;
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];

  let queryMessage: string = "";
  if (lastUserMessage?.content) {
    if (typeof lastUserMessage.content === "string") {
      queryMessage = lastUserMessage.content.trim(); // Use content as-is if it's a string
    } else if (Array.isArray(lastUserMessage.content)) {
      // Handle array case if content is a complex type
      queryMessage = lastUserMessage.content.join(" ").trim();
    } else {
      console.warn("Unexpected content format:", lastUserMessage.content);
    }
  }

  if (!queryMessage) {
    console.warn("No valid last user message found; skipping vector search.");
    return { user_memories: [] };
  }
  
  const filterMetadata = { 
    user_id: user_id, 
    assistant_id: configurable.assistant_id
  }

  const vectorStore = await createVectorStore(filterMetadata);
  const results = await vectorStore.similaritySearch(queryMessage, 3);
  //console.log("results=======>: ", results)
  const memories: string[] = [];
  if (results.length > 0) {
    memories.push(...results.map(doc => {
      try {
        return doc.pageContent
      } catch (error) {
        console.error('Error parsing pageContent:', error);
        return null;
      }
    }).filter((content): content is string => content !== null));
  }
  
  //console.log("memories=======>: ", memories);

  return {
    user_memories: memories,
  };
}

/*async function storeMemory(
  state: typeof GraphAnnotation.State,
  config: LangGraphRunnableConfig,
): Promise<{ messages: BaseMessage[]; summary: string }> {
  const { summary, messages } = state;
  const lastMessage = messages[messages.length - 1] as AIMessage;
  const toolCalls = lastMessage.tool_calls || [];

  const tools = initializeTools(config);
  const upsertMemoryTool = tools[0];

  // Initialize the async operations
  //let summarizePromise: Promise<{ summary: string; messages: any[] }> | null = null;
  let summarizedResult: any
  // Only call summarizeConversation if there are more than 6 messages
  //if (messages.length > 2) {
  //  summarizedResult = await summarizeConversation();
  //}

  // Call upsertMemoryTool for each tool call in parallel
  const savedMemoriesPromise = Promise.all(
    toolCalls.map((tc) => upsertMemoryTool.invoke(tc)),
  );

  // Wait for both operations to complete
  const [savedMemories] = await Promise.all([
    savedMemoriesPromise,
  ]);
  //console.log("summarizedResult", summarizedResult)
  //console.log("savedMemories", savedMemories)

  // Construct the result based on the summarizeConversation and savedMemories
  return {
    //summary: summarizedResult?.summary || summary,
    //messages: summarizedResult?.deleteMessages || savedMemories,
  };
}*/

function routeMessage(
  state: typeof GraphAnnotation.State,
): "store_memory" | typeof END {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  //if (lastMessage.tool_calls?.length) {
  if (state.messages.length > 6) {
    return "store_memory";
  }
  return END;
}

export const builder = new StateGraph(
  {
    stateSchema: GraphAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("query_memories", queryMemories)
  .addNode("call_model", callModel)
  .addNode("store_memory", memoryNode)
  .addNode("delete_messages", deleteMessages)
  .addEdge(START, "query_memories")
  .addEdge("query_memories", "call_model")
  .addConditionalEdges("call_model", routeMessage, {
    store_memory: "store_memory",
    [END]: END,
  })
  .addEdge("store_memory", "delete_messages")
  .addEdge("delete_messages", END)

