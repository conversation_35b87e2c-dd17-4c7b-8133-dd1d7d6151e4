import { BaseMessage } from "@langchain/core/messages";
import { Annotation, messagesStateReducer } from "@langchain/langgraph";

export const GraphAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),
  summary: Annotation<string | undefined>({
    reducer: (_, action) => action,
    default: () => undefined,
  }),
  user_memories: Annotation<string[] | undefined>,
});