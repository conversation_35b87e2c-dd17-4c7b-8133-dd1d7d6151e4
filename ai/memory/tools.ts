import { LangGraphRunnableConfig } from "@langchain/langgraph";
import type { Document } from "@langchain/core/documents";
import type { VectorStoreInterface } from "@langchain/core/vectorstores";
import { ensureConfiguration } from "./configuration";
import { v4 as uuidv4 } from "uuid";
import { tool } from "@langchain/core/tools";
import { z } from 'zod/v3';
import { getStoreFromConfigOrThrow } from "./utils";
import { PATCH_PATH } from "@/ai/memory_service/constants"

/**
 * Initialize tools within a function so that they have access to the current
 * state and config at runtime.
 */
export function initializeTools(config?: LangGraphRunnableConfig) {
  /**
   * Upsert a memory in the database.
   * @param content The main content of the memory.
   * @param context Additional context for the memory.
   * @param memoryId Optional ID to overwrite an existing memory.
   * @returns A string confirming the memory storage.
   */
  async function upsertMemory(opts: {
    content: string;
    context: string;
    memoryId?: string;
  }): Promise<string> {
    const { content, context, memoryId } = opts;
    if (!config || !config.store) {
      throw new Error("Config or store not provided");
    }

    const configurable = ensureConfiguration(config);
    const namespace = ["memories", configurable.user_id]
    const memId = memoryId || uuidv4();
    const store = getStoreFromConfigOrThrow(config);

    await store.put(namespace, memId, {
      content,
      context,
    });

    return `Stored memory ${memId}`;
  }

  const upsertMemoryTool = tool(upsertMemory, {
    name: "upsertMemory",
    description:
      "Upsert a memory in the database. If a memory conflicts with an existing one, \
      update the existing one by passing in the memory_id instead of creating a duplicate. \
      If the user corrects a memory, update it. Can call multiple times in parallel \
      if you need to store or update multiple memories.",
    schema: z.object({
      content: z.string().describe(
        "The main content of the memory. For example: \
          'User expressed interest in learning about French.'",
      ),
      context: z.string().describe(
        "Additional context for the memory. For example: \
          'This was mentioned while discussing career options in Europe.'",
      ),
      memoryId: z
        .string()
        .optional()
        .describe(
          "The memory ID to overwrite. Only provide if updating an existing memory.",
        ),
    }),
  });

  return [upsertMemoryTool];
}


export function createUpsertReflectionTool(
  vectorStore: VectorStoreInterface,
  config?: LangGraphRunnableConfig
) {
  async function upsertReflection(opts: {
    content: string;
    context: string;
    memoryId?: string;
  }): Promise<string> {
    const { content, context, memoryId } = opts;

    if (!config || !vectorStore) {
      throw new Error("Config or vectorStore not provided");
    }

    const configurable = ensureConfiguration(config);
    const namespace = PATCH_PATH.replace(
      "{user_id}", configurable.user_id
    ).replace(
      "{function_name}", "reflection_memory"
    );
    const fullContent = content + context;

    // If memoryId is provided, assume an update; otherwise, create new.
    if (memoryId) {
      const existingDoc = await vectorStore.similaritySearch(
        fullContent, 1, { user_id: configurable.user_id, namespace }
      );
      
      // To be complete
      //if (existingDoc.length) {
        // Replace content of the closest matching document
      //  existingDoc[0].pageContent = fullContent;
      //  await store.updateDocuments([existingDoc[0]]);
      //  return `Updated reflection memory ${memoryId}`;
      //}
    }

    // Insert a new document if no memoryId match or if no update needed
    const newDocument: Document = {
      id: uuidv4(),
      pageContent: fullContent,
      metadata: {
        user_id: configurable.user_id,
        assistant_id: configurable.assistant_id,
        namespace,
      }
    };

    await vectorStore.addDocuments([newDocument]);
    return `Stored new reflection in namespace ${namespace}`;
  }

  const upsertReflectionTool = tool(upsertReflection, {
    name: "upsertReflection",
    description: "Upsert a memory to the vector store for semantic retrieval. \
    If a memory conflicts with an existing one, update it by passing in the memoryId. \
    Use semantic matching to determine the closest match.",
    schema: z.object({
      content: z.string().describe("Content of the memory to store."),
      context: z.string().describe("Context for the memory."),
      memoryId: z
        .string()
        .optional()
        .describe("The memory ID to overwrite if updating an existing memory."),
    }),
  });

  return [upsertReflectionTool];
}


// Tool to search memories in the vector store based on a query
export function createRecallReflectionTool(
  vectorStore: VectorStoreInterface,
  config?: LangGraphRunnableConfig
) {
  if (!config || !vectorStore) {
    throw new Error("Config or vectorStore not provided");
  }
  const configurable = ensureConfiguration(config);
  const recallReflectionTool = tool(
    async (input, config) => {
      const configurable = ensureConfiguration(config);
      const namespace = PATCH_PATH.replace(
        "{user_id}", configurable.user_id
      ).replace(
        "{function_name}", "reflection_memory"
      );
      const filterMetadata = { user_id: configurable.user_id, namespace: namespace }

      const documents = await vectorStore.similaritySearch(input.query, 1, filterMetadata);

      return documents.map(doc => doc.pageContent);
    },
    {
      name: "recallReflection",
      description: "Searches saved memories in the vector store based on semantic similarity to a query",
      schema: z.object({
        query: z.string().describe("query to look up in retriever"),
      })
    }
  );

  return [recallReflectionTool];
}

