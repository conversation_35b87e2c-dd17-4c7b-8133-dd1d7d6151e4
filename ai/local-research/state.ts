import { Annotation } from "@langchain/langgraph";

// Define the structure of the summary state
export const SummaryStateAnnotation = Annotation.Root({
  research_topic: Annotation<string | null>({
    default: () => null,
    value: null
  }),

  search_query: Annotation<string | null>({
    default: () => null,
    value: null
  }),

  web_research_results: Annotation<string[]>({
    reducer: (state: string[] = [], action: string[]): string[] => [...state, ...action],
    default: () => [],
  }),

  sources_gathered: Annotation<string[]>({
    reducer: (state: string[] = [], action: string[]): string[] => [...state, ...action],
    default: () => [],
  }),

  research_loop_count: Annotation<number>({
    value: (_state, action) => action,
    default: () => 0
  }),
  
  running_summary: Annotation<string | null>({
    default: () => null,
    value: null
  })
});

// Define the input structure
export const SummaryStateInputAnnotation = Annotation.Root({
  /**
   * The research topic provided as input
   */
  research_topic: Annotation<string | null>({
    default: () => null,
    value: null
  })
});

// Define the output structure
export const SummaryStateOutputAnnotation = Annotation.Root({
  /**
   * The final summary report
   */
  running_summary: Annotation<string | null>({
    default: () => null,
    value: null
  })
});

// Export types for usage
export type SummaryState = typeof SummaryStateAnnotation.State;
export type SummaryStateInput = typeof SummaryStateInputAnnotation.State;
export type SummaryStateOutput = typeof SummaryStateOutputAnnotation.State;
