"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import { type AIMessageChunk } from "@langchain/core/messages";
import { type ToolCallChunk } from "@langchain/core/messages/tool";
import { StreamMode } from "@/components/langgraph/Settings";
import { builder } from"./graph";
import { createGraph } from "@/ai/utils";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts"
import { SearchAPI } from "./configuration";
import { deleteCheckpointsByThreadId } from "@/ai/utils/delete_checkpoints";

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  const config = { configurable: { thread_id: threadId } }
  const state = await graph.getState(config);

  await deleteCheckpointsByThreadId(threadId);

  // Update the state with the cleared values
  await graph.updateState(config, {});
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  targetUserId?: string;
  targetThreadId?: string;
  targetCompanionId?: string;
  systemInstructions: string;
  language: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      research_topic: params.message
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 30,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      schemas: memorySchemas,
      delay: 3,
      max_web_research_loops: 16,
      local_llm: "qwen3:32b",
      search_api: SearchAPI.TAVILY,
    },
    version: "v2" as "v2",
    encoding: undefined,
  };

  const stream = createStreamableValue();
  let aiMessageId: string | null = null;

  (async () => {
    const graph = await createGraph(builder);
    //console.log("graph: ", graph)
    //const state = await graph.getState(config);
    //console.log("state: ", state)
    const eventStream = graph.streamEvents(input, config);

    for await (const { event, tags, data } of eventStream) {
      //console.log("================================================================= event: =================================================================", event)
      //console.log("================================================================= data: =================================================================", data)
      if (event === "on_chat_model_stream") {
        // AI message chunks - stream token by token
        if (!aiMessageId) {
          aiMessageId = crypto.randomUUID();
        }
        // Intermediate chat model generations will contain tool calls and no content
        const message = data.chunk as AIMessageChunk;
        if (typeof message.content === "string" ? message.content.trim() : "" !== "") { // Prevent update for empty content
          //console.log("aiMessage====================>", message);
          stream.update({
            messages: [{
              id: aiMessageId,
              sender: message.getType(), //'ai'
              text: message.content,
              isPartial: true  // Flag to indicate this is a partial message
            }]
          });
        }
      
      } else if (event === "on_chat_model_end") {
        const message = data?.output
        if (message?.tool_call_chunks?.length) {
          // Tool calls
          stream.update({
            messages: [{
              id: message.id,
              sender: message.getType(), //'ai'
              toolCalls: message.tool_call_chunks.map((call: ToolCallChunk) => ({
                id: call.id,
                name: call.name,
                args: call.args,
                result: undefined,
              }))
            }]
          })
        }
      } else if (event === "on_tool_end") {
        const message = data?.output
        if (message?.content?.length) {
          // ToolMessage  
          stream.update({
            messages: [{
              id: message?.id || crypto.randomUUID(),
              sender: message.getType(), //'tool',
              toolCalls: [{
                id: message.tool_call_id,
                name: message.name,
                args: "",
                result: message.content,
              }]
            }]
          });
        }
      } else {
        //console.log("Unknown event type:", event);
      }
    }
    
    stream.done();
  })();
  
  return { output: stream.value };
}
