import axios from "axios";
import  { type TavilySearchResponse, tavily } from "@tavily/core";

// Utility function for deduplicating and formatting sources
export function deduplicateAndFormatSources(
  searchResponse: any, 
  maxTokensPerSource: number, 
  includeRawContent: boolean = true
): string {
  // Determine the sources list
  let sourcesList: any[] = [];
  if (typeof searchResponse === 'object' && 'results' in searchResponse) {
    sourcesList = searchResponse.results;
  } else if (Array.isArray(searchResponse)) {
    sourcesList = searchResponse.flatMap(response => 
      response.results ? response.results : response
    );
  } else {
    throw new Error("Input must be either a dict with 'results' or a list of search results");
  }
  
  // Deduplicate by URL
  const uniqueSources: Record<string, any> = {};
  sourcesList.forEach(source => {
    if (!uniqueSources[source.url]) {
      uniqueSources[source.url] = source;
    }
  });
 
  // Format output
  let formattedText = "Sources:\n\n";
  Object.values(uniqueSources).forEach((source, index) => {
    formattedText += `Source ${source.title}:\n===\n`;
    formattedText += `URL: ${source.url}\n===\n`;
    formattedText += `Most relevant content from source: ${source.content}\n===\n`;
  
    if (includeRawContent) {
      const charLimit = maxTokensPerSource * 4;
      let rawContent = source.raw_content || '';
       
      if (rawContent.length > charLimit) {
        rawContent = rawContent.slice(0, charLimit) + "... [truncated]";
      }
       
      formattedText += `Full source content limited to ${maxTokensPerSource} tokens: ${rawContent}\n\n`;
    }
  });
  
  return formattedText.trim();
}
  
type SearchResult = {
  title: string;
  url: string;
  content: string;
  raw_content?: string | null;
}

type SearchResponse = {
  results: SearchResult[];
}

// Format search results into a bullet-point list of sources
export function formatSources(searchResults: SearchResponse): string {
  return searchResults.results
    .map((source) => `* ${source.title} : ${source.url}`)
    .join("\n");
}

// Tavily search function
export async function tavilySearchAsync(
  query: string,
  includeRawContent = true,
  maxResults = 3
): Promise<TavilySearchResponse> {
  const tvly = tavily({ apiKey: process.env.TAVILY_API_KEY });
  return await tvly.search(
    query, {
    includeRawContent,
    maxResults,
  });
}

// Perplexity search function
export async function perplexitySearchAsync(
  query: string,
  perplexitySearchLoopCount: number
): Promise<SearchResponse> {
  const API_KEY = process.env.PERPLEXITY_API_KEY;
  if (!API_KEY) throw new Error("PERPLEXITY_API_KEY is not set");

  const headers = {
    accept: "application/json",
    "content-type": "application/json",
    Authorization: `Bearer ${API_KEY}`,
  };

  const payload = {
    model: "sonar-pro",
    messages: [
      {
        role: "system",
        content: "Search the web and provide factual information with sources.",
      },
      {
        role: "user",
        content: query,
      },
    ],
  };

  const response = await axios.post(
    "https://api.perplexity.ai/chat/completions",
    payload,
    { headers }
  );

  const data = response.data;
  const content = data.choices[0].message.content;
  const citations = data.citations || ["https://perplexity.ai"];

  const results: SearchResult[] = [
    {
      title: `Perplexity Search ${perplexitySearchLoopCount + 1}, Source 1`,
      url: citations[0],
      content,
      raw_content: content,
    },
    ...citations.slice(1).map((citation, i) => ({
      title: `Perplexity Search ${perplexitySearchLoopCount + 1}, Source ${i + 2}`,
      url: citation,
      content: "See above for full content",
      raw_content: null,
    })),
  ];

  return { results };
}
