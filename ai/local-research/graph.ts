import { 
  StateGraph, 
  START, 
  END, 
  LangGraphRunnableConfig
} from "@langchain/langgraph";
import { ChatGroq } from "@langchain/groq";
import { ChatOllama } from "@langchain/ollama";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";
import { ConfigurationAnnotation, ensureConfiguration } from "./configuration";
import { 
  SummaryStateInputAnnotation, 
  SummaryStateOutputAnnotation, 
  SummaryStateAnnotation 
} from "./state";
import { 
  query_writer_instructions, 
  summarizer_instructions, 
  reflection_instructions 
} from "./prompts";
import { 
  deduplicateAndFormatSources, 
  tavilySearchAsync, 
  formatSources, 
  perplexitySearchAsync 
} from "./utils";
import { extractThinkingAndResponses } from "@/ai/utils/thinking";

const ollamaBaseUrl = "http://192.168.0.100:11434";

async function generateQuery(
  state: typeof SummaryStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ search_query: string }> {
  const configurable = ensureConfiguration(config);
  const llmJsonMode = new ChatOllama({ baseUrl: ollamaBaseUrl, model: configurable.local_llm, temperature: 0, format: "json" });

  const queryPrompt = query_writer_instructions.replace("{research_topic}", state.research_topic);

  const result = await llmJsonMode.invoke([
    new SystemMessage(queryPrompt),
    new HumanMessage("Generate a query for web search:")
  ]);

  const { query } = JSON.parse(result.content as string);
  return { search_query: query };
}

async function webResearch(
  state: typeof SummaryStateAnnotation.State, 
  config: LangGraphRunnableConfig
) {
  const configurable = ensureConfiguration(config);
  const searchApi = configurable.search_api;
  
  let searchResults, searchStr;

  if (searchApi === "tavily") {
    searchResults = await tavilySearchAsync(state.search_query, true, 1);
    console.log("searchResults", searchResults)
    searchStr = deduplicateAndFormatSources(searchResults, 2000, true);
    console.log("searchStr", searchStr)
  } else if (searchApi === "perplexity") {
    searchResults = await perplexitySearchAsync(state.search_query, state.research_loop_count);
    searchStr = deduplicateAndFormatSources(searchResults, 2000, false);
  } else {
    throw new Error(`Unsupported search API: ${configurable.search_api}`);
  }

  return {
    sources_gathered: [formatSources(searchResults)],
    research_loop_count: state.research_loop_count + 1,
    web_research_results: [searchStr]
  };
}

async function summarizeSources(
  state: typeof SummaryStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ running_summary: string }> {
  const configurable = ensureConfiguration(config);
  const llm = new ChatOllama({ baseUrl: ollamaBaseUrl, model: "qwen3:32b", temperature: 0 });

  const mostRecentResearch = state.web_research_results.at(-1);
  const humanMessageContent = state.running_summary
    ? `<Existing Summary> ${state.running_summary}\n<New Search Results> ${mostRecentResearch}`
    : `<Search Results> ${mostRecentResearch}`;

  const response = await llm.invoke([
    new SystemMessage(summarizer_instructions),
    new HumanMessage(humanMessageContent)
  ]);
  console.log("response: ", response)

  const result = extractThinkingAndResponses(response.content as string );
  console.log('Extracted Thinking: ', result.thinking);
  console.log('Extracted Response: ', result.response);

  let runningSummary = result.thinking + result.response;
  console.log("runningSummary: ", runningSummary)
  return { running_summary: runningSummary };
}

async function reflectOnSummary(
  state: typeof SummaryStateAnnotation.State, 
  config: LangGraphRunnableConfig
): Promise<{ search_query: string }> {
  const configurable = ensureConfiguration(config);
  const llmJsonMode = new ChatOllama({ 
    baseUrl: ollamaBaseUrl, 
    model: configurable.local_llm, 
    temperature: 0, 
    format: "json" 
  });

  const reflectionPrompt = reflection_instructions.replace("{research_topic}", state.research_topic);
  
  let result;
  try {
    result = await llmJsonMode.invoke([
      new SystemMessage(reflectionPrompt),
      new HumanMessage(`Identify a knowledge gap: ${state.running_summary}`)
    ]);
  } catch (error) {
    console.error("LLM invocation failed:", error);
    return { search_query: `Tell me more about ${state.research_topic}` };
  }

  console.log("reflectionPrompt:", reflectionPrompt);
  console.log("reflectOnSummary result:", result);

  let followUpQuery: string;

  try {
    // Ensure result.content is a string
    const content = typeof result.content === "string" 
      ? result.content 
      : JSON.stringify(result.content);

    const parsedContent = JSON.parse(content);

    followUpQuery = parsedContent.follow_up_query 
      ?? `Tell me more about ${state.research_topic}`;
  } catch (error) {
    console.error("JSON parsing failed:", error);
    followUpQuery = `Tell me more about ${state.research_topic}`;
  }

  return { search_query: followUpQuery };
}


async function finalizeSummary(
  state: typeof SummaryStateAnnotation.State
): Promise<{ running_summary: string }> {
  const allSources = state.sources_gathered.join("\n");
  return { running_summary: `## Summary\n\n${state.running_summary}\n\n### Sources:\n${allSources}` };
}

function routeResearch(
  state: typeof SummaryStateAnnotation.State, 
  config: LangGraphRunnableConfig
): "finalize_summary" | "web_research" {
  const configurable = ensureConfiguration(config);
  return state.research_loop_count <= configurable.max_web_research_loops
    ? "web_research"
    : "finalize_summary";
}

export const builder = new StateGraph(
  {
    stateSchema: SummaryStateAnnotation,
    input: SummaryStateInputAnnotation,
    output: SummaryStateOutputAnnotation,
  },
  ConfigurationAnnotation
)
  .addNode("generate_query", generateQuery)
  .addNode("web_research", webResearch)
  .addNode("summarize_sources", summarizeSources)
  .addNode("reflect_on_summary", reflectOnSummary)
  .addNode("finalize_summary", finalizeSummary)
  .addEdge(START, "generate_query")
  .addEdge("generate_query", "web_research")
  .addEdge("web_research", "summarize_sources")
  .addEdge("summarize_sources", "reflect_on_summary")
  .addConditionalEdges("reflect_on_summary", routeResearch, ["web_research", "finalize_summary"])
  .addEdge("finalize_summary", END);

