import { Annotation } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { v4 as uuidv4 } from 'uuid';

export enum SearchAPI {
  PERPLEXITY = "perplexity",
  TAVILY = "tavily",
}

export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>,
  assistant_id: Annotation<string>,
  thread_id: Annotation<string>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schemas: Annotation<Record<string, MemoryConfig>>,
  delay: Annotation<number | undefined>,

  /**
   * Maximum number of web research loops
   */
  max_web_research_loops: Annotation<number>({
    value: (_state, action) => action,
    default: () => 3,
  }),

  /**
   * The local LLM model to use
   */
  local_llm: Annotation<string>({
    value: (_state, action) => action,
    default: () => "qwen3:32b",
  }),

  /**
   * The search API to use (Tavily or Perplexity)
   */
  search_api: Annotation<SearchAPI>({
    value: (_state, action) => action,
    default: () => SearchAPI.TAVILY,
  }),
});

export function ensureConfiguration(
  config: RunnableConfig
): typeof ConfigurationAnnotation.State {
  const configurable = (config?.configurable || {}) as Partial<typeof ConfigurationAnnotation.State>;

  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    schemas: configurable?.schemas || {},
    delay: configurable?.delay || 3,
    max_web_research_loops:
      configurable.max_web_research_loops !== undefined
        ? configurable.max_web_research_loops
        : Number(process.env.MAX_WEB_RESEARCH_LOOPS) || 3,

    local_llm:
      configurable.local_llm ||
      process.env.LOCAL_LLM ||
      "qwen3:32b",

    search_api:
      configurable.search_api ||
      (process.env.SEARCH_API === "perplexity"
        ? SearchAPI.PERPLEXITY
        : SearchAPI.TAVILY),
  };
}
