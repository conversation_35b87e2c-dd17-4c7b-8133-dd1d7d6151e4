import { BaseMessage } from "@langchain/core/messages";
import { Annotation } from "@langchain/langgraph";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { type RunnableConfig } from "@langchain/core/runnables";
import { ChatOpenAI } from "@langchain/openai";
import { tavilyTool, scrapeWebpage } from "@/ai/tools/research/search"
import { 
    agentMessageModifier,
    createTeamSupervisor, 
    runAgentNode 
} from "./utilities"

// This defines the agent state for the research team
export const ResearchTeamState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  team_members: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
  }),
  next: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "supervisor",
  }),
  instructions: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "Solve the human's question.",
  }),
})

const llm = new ChatOpenAI({ modelName: "gpt-4.1" });

export const searchNode = (state: typeof ResearchTeamState.State, config?: RunnableConfig) => {
  const messageModifier = agentMessageModifier(
    "You are a research assistant who can search for up-to-date info using the tavily search engine.",
    [tavilyTool],
    state.team_members ?? ["Search"],
  )
  const searchAgent = createReactAgent({
    llm,
    tools: [tavilyTool],
    messageModifier,
  })
  return runAgentNode({ state, agent: searchAgent, name: "Search", config });
};

export const researchNode = (state: typeof ResearchTeamState.State, config?: RunnableConfig) => {
  const messageModifier = agentMessageModifier(
    "You are a research assistant who can scrape specified urls for more detailed information using the scrapeWebpage function.",
    [scrapeWebpage],
    state.team_members ?? ["WebScraper"],
  )
  const researchAgent = createReactAgent({
    llm,
    tools: [scrapeWebpage],
    messageModifier,
  })
  return runAgentNode({ state, agent: researchAgent, name: "WebScraper", config });
}

export const supervisorAgent = await createTeamSupervisor(
  llm,
  "You are a supervisor tasked with managing a conversation between the" +
    " following workers:  {team_members}. Given the following user request," +
    " respond with the worker to act next. Each worker will perform a" +
    " task and respond with their results and status. When finished," +
    " respond with FINISH.\n\n" +
    " Select strategically to minimize the number of steps taken.",
  ["Search", "WebScraper"],
);