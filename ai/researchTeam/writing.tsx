import * as fs from "fs/promises";
import { Annotation } from "@langchain/langgraph";
import { BaseMessage } from "@langchain/core/messages";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { RunnableLambda, RunnableConfig } from "@langchain/core/runnables";
import {
  WORKING_DIRECTORY,
  createOutlineTool,
  readDocumentTool,
  writeDocumentTool,
  editDocumentTool,
  chartTool,

} from "@/ai/tools/research/writing"
import { ChatOpenAI } from "@langchain/openai";
import { 
  agentMessageModifier,
  createTeamSupervisor, 
  runAgentNode 
} from "./utilities"

export const prelude = new RunnableLambda({
  func: async (state: {
    messages: BaseMessage[];
    next: string;
    instructions: string;
  }) => {
    let writtenFiles: string[] = [];
    if (
      !(await fs
        .stat(WORKING_DIRECTORY)
        .then(() => true)
        .catch(() => false))
    ) {
      await fs.mkdir(WORKING_DIRECTORY, { recursive: true });
    }
    try {
      const files = await fs.readdir(WORKING_DIRECTORY);
      for (const file of files) {
        writtenFiles.push(file);
      }
    } catch (error) {
      console.error(error);
    }
    const filesList = writtenFiles.length > 0
      ? "\nBelow are files your team has written to the directory:\n" +
        writtenFiles.map((f) => ` - ${f}`).join("\n")
      : "No files written.";
    return { ...state, current_files: filesList };
  },
});

export const DocWritingState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  team_members: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
  }),
  next: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "supervisor",
  }),
  current_files: Annotation<string>({
    reducer: (x, y) => (y ? `${x}\n${y}` : x),
    default: () => "No files written.",
  }),
  instructions: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "Solve the human's question.",
  }),
})

const docWritingLlm = new ChatOpenAI({ modelName: "gpt-4.1" });

export const docWritingNode = (state: typeof DocWritingState.State, config?: RunnableConfig) => {
  const messageModifier = agentMessageModifier(
    `You are an expert writing a research document.\nBelow are files currently in your directory:\n${state.current_files}`,
    [writeDocumentTool, editDocumentTool, readDocumentTool],
    state.team_members ?? [],
  )
  const docWriterAgent = createReactAgent({
    llm: docWritingLlm,
    tools: [writeDocumentTool, editDocumentTool, readDocumentTool],
    messageModifier,
  })
  const contextAwareDocWriterAgent = prelude.pipe(docWriterAgent);
  return runAgentNode({ state, agent: contextAwareDocWriterAgent, name: "DocWriter", config });
}

export const noteTakingNode = (state: typeof DocWritingState.State, config?: RunnableConfig) => {
  const messageModifier = agentMessageModifier(
    "You are an expert senior researcher tasked with writing a paper outline and" +
    ` taking notes to craft a perfect paper. ${state.current_files}`,
    [createOutlineTool, readDocumentTool],
    state.team_members ?? [],
  )
  const noteTakingAgent = createReactAgent({
    llm: docWritingLlm,
    tools: [createOutlineTool, readDocumentTool],
    messageModifier,
  })
  const contextAwareNoteTakingAgent = prelude.pipe(noteTakingAgent);
  return runAgentNode({ state, agent: contextAwareNoteTakingAgent, name: "NoteTaker", config });
}

export const chartGeneratingNode = async (
  state: typeof DocWritingState.State,
  config?: RunnableConfig,
) => {
  const messageModifier = agentMessageModifier(
    "You are a data viz expert tasked with generating charts for a research project." +
    `${state.current_files}`,
    [readDocumentTool, chartTool],
    state.team_members ?? [],
  )
  const chartGeneratingAgent = createReactAgent({
    llm: docWritingLlm,
    tools: [readDocumentTool, chartTool],
    messageModifier,
  })
  const contextAwareChartGeneratingAgent = prelude.pipe(chartGeneratingAgent);
  return runAgentNode({ state, agent: contextAwareChartGeneratingAgent, name: "ChartGenerator", config });
}

const docTeamMembers = ["DocWriter", "NoteTaker", "ChartGenerator"];
export const docWritingSupervisor = await createTeamSupervisor(
  docWritingLlm,
  "You are a supervisor tasked with managing a conversation between the" +
    " following workers:  {team_members}. Given the following user request," +
    " respond with the worker to act next. Each worker will perform a" +
    " task and respond with their results and status. When finished," +
    " respond with FINISH.\n\n" +
    " Select strategically to minimize the number of steps taken.",
  docTeamMembers,
);