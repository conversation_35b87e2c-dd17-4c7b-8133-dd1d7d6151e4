import { Annotation } from "@langchain/langgraph";
import { BaseMessage } from "@langchain/core/messages";
import { RunnableLambda } from "@langchain/core/runnables";
import { 
    searchNode, 
    supervisorAgent, 
    researchNode,
    ResearchTeamState
  } from "@/ai/researchTeam/research"
import { 
  DocWritingState,
  docWritingNode, 
  noteTakingNode, 
  chartGeneratingNode, 
  docWritingSupervisor 
} from "@/ai/researchTeam/writing"
import { END, START, StateGraph } from "@langchain/langgraph";
import { createTeamSupervisor } from "@/ai/researchTeam/utilities"
import { ChatOpenAI } from "@langchain/openai";

export const maxDuration = 30;

const llm = new ChatOpenAI({ modelName: "gpt-4.1" });
const researchGraph = new StateGraph(ResearchTeamState)
  .addNode("Search", searchNode)
  .addNode("supervisor", supervisorAgent)
  .addNode("WebScraper", researchNode)
  // Define the control flow
  .addEdge("Search", "supervisor")
  .addEdge("WebScraper", "supervisor")
  .addConditionalEdges("supervisor", (x) => x.next, {
    Search: "Search",
    WebScraper: "WebScraper",
    FINISH: END,
  })
  .addEdge(START, "supervisor");

const researchChain = researchGraph.compile();

// Create the graph here:
// Note that we have unrolled the loop for the sake of this doc
const authoringGraph = new StateGraph(DocWritingState)
  .addNode("DocWriter", docWritingNode)
  .addNode("NoteTaker", noteTakingNode)
  .addNode("ChartGenerator", chartGeneratingNode)
  .addNode("supervisor", docWritingSupervisor)
  // Add the edges that always occur
  .addEdge("DocWriter", "supervisor")
  .addEdge("NoteTaker", "supervisor")
  .addEdge("ChartGenerator", "supervisor")
  // Add the edges where routing applies
  .addConditionalEdges("supervisor", (x) => x.next, {
    DocWriter: "DocWriter",
    NoteTaker: "NoteTaker",
    ChartGenerator: "ChartGenerator",
    FINISH: END,
  })
  .addEdge(START, "supervisor");

const enterAuthoringChain = RunnableLambda.from(
  ({ messages }: { messages: BaseMessage[] }) => {
    return {
      messages: messages,
      team_members: ["Doc Writer", "Note Taker", "Chart Generator"],
    };
  },
);
const authoringChain = enterAuthoringChain.pipe(authoringGraph.compile());

// Define the top-level State interface
const State = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  next: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "ResearchTeam",
  }),
  instructions: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "Resolve the user's request.",
  }),
});

const supervisorNode = await createTeamSupervisor(
  llm,
  "You are a supervisor tasked with managing a conversation between the" +
    " following teams: {team_members}. Given the following user request," +
    " respond with the worker to act next. Each worker will perform a" +
    " task and respond with their results and status. When finished," +
    " respond with FINISH.\n\n" +
    " Select strategically to minimize the number of steps taken.",
  ["ResearchTeam", "PaperWritingTeam"],
);

const getMessages = RunnableLambda.from((state: typeof State.State) => {
  return { messages: state.messages };
});

const joinGraph = RunnableLambda.from((response: any) => {
  return {
    messages: [response.messages[response.messages.length - 1]],
  };
});

const superGraph = new StateGraph(State)
  .addNode("ResearchTeam", getMessages.pipe(researchChain).pipe(joinGraph))
  .addNode("PaperWritingTeam", getMessages.pipe(authoringChain).pipe(joinGraph))
  .addNode("supervisor", supervisorNode)
  .addEdge("ResearchTeam", "supervisor")
  .addEdge("PaperWritingTeam", "supervisor")
  .addConditionalEdges("supervisor", (x) => x.next, {
    PaperWritingTeam: "PaperWritingTeam",
    ResearchTeam: "ResearchTeam",
    FINISH: END,
  })
  .addEdge(START, "supervisor");

const compiledSuperGraph = superGraph.compile();

/*resultStream = compiledSuperGraph.stream(
  {
    messages: [
      new HumanMessage(
        "Look up a current event, write a poem about it, then plot a bar chart of the distribution of words therein.",
      ),
    ],
  },
  { recursionLimit: 150 },
);

for await (const step of await resultStream) {
  if (!step.__end__) {
    console.log(step);
    console.log("---");
  }
}*/