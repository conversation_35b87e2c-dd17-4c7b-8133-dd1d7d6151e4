"use server"
import { createClient } from "@supabase/supabase-js";
import { memoryTableName } from "./settings";
import { PATCH_PATH } from "./constants";
import { type UserMemory } from '@/components/langgraph/UserMemories';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_PRIVATE_KEY
const supabaseClient = createClient(supabaseUrl!!, supabaseKey!!);

export async function getUserMemories(
  userId: string,
  assistantId: string,
  functionNames: string[], // Accept an array of function names
  jsonFormat?: boolean
): Promise<UserMemory[]> {
  if (!userId || !assistantId || !functionNames.length) return [];

  // Generate paths for each function name
  const paths = functionNames.map((functionName) =>
    PATCH_PATH.replace("{user_id}", userId).replace("{function_name}", functionName)
  );

  const { data, error } = await supabaseClient
    .from(memoryTableName)
    .select("id, content, metadata")
    .in("id", paths); // Query for all paths at once

  if (error) {
    console.log("Error fetching data:", error);
    return []; // Return an empty array on error
  }

  const userMemories = data
    ?.map((doc) => {
      try {
        return {
          id: doc.id,
          content: jsonFormat ? JSON.parse(doc.content) : doc.content, // Parse JSON content if requested
          metadata: doc.metadata,
        };
      } catch (error) {
        console.error("Error parsing content:", error);
        return null; // Exclude entries with parsing errors
      }
    })
    .filter(
      (memory): memory is UserMemory => 
        memory !== null &&
        memory.metadata.user_id === userId &&
        memory.metadata.assistant_id === assistantId
    );

  return userMemories || [];
}



export async function updateUserMemory(
    id: string,
    newContent: Record<string, any>
  ): Promise<UserMemory | null> {
  if (!id || !newContent) {
    console.error("Invalid parameters for updateUserMemory");
    return null;
  }

  const { data, error } = await supabaseClient
    .from(memoryTableName) // Explicitly define the table's return type
    .update({ content: JSON.stringify(newContent) }) // Convert content to JSON string
    .eq('id', id)
    .select("id, content, metadata")

  if (error) {
    console.error('Error updating memory:', error);
    return null; // Return null on error
  }

  if (!data || data.length === 0) {
    console.warn("No data returned after update");
    return null; // Explicitly handle null or empty data
  }

  return data[0]; // Return the first (and likely only) updated record
}

export async function deleteUserMemory(id: string) {
  if (!id) {
    console.error("Invalid parameter for deleteUserMemory");
    return false;
  }

  const { data, error } = await supabaseClient
    .from(memoryTableName)
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting memory:', error);
    return false; // Return false on error
  }

  return true; // Return true on successful deletion
}