"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import {
  type BaseCheckpointSaver,
  type BaseStore,
} from "@langchain/langgraph-checkpoint";
import { RemoveMessage, BaseMessage,
 } from "@langchain/core/messages";
import { StreamMode } from "@/components/langgraph/Settings";
import { ThreadState } from "@/components/langgraph/Schema";
import { createMemorableEventConfig } from "./utils";
import { memorableEventSchema } from "./schemas"
import { userProfileTool, threadSummaryTool } from "./tools"
import { SYSTEM_PROMPT } from "./prompts"
import { builder } from "./graph";
import { handleServerStreamEvent } from '@/utils/streamHandler'


interface MemorableEvent {
  description: string;
  participants: string[];
}

export async function createGraph(
  builder: any, 
  store?: BaseStore
) {
  if (!builder) return
  const graph = builder.compile({
    store,
  });

  return graph;
}

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph(builder);
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph.getState(config)).values.messages;
  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  systemInstructions: string;
  streamMode: StreamMode;
}) => {
  
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      messages: 
        [{
          role: "user",
          content: params.message,
        }],
      //function_name: "memorable_event",
    };
  }
  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    SYSTEM_PROMPT
  );

  const config = {
    recursionLimit: 30,
    configurable: {
      delay: 0,
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      model: "groq/llama-3.3-70b-versatile",
      schemas: memorySchemas,
      system_prompt: SYSTEM_PROMPT,
    },
    streamMode: "updates" as const,
  };
  //console.log("config: ", JSON.stringify(config, null, 2))
  const graph = await createGraph(builder)
  //console.log("graph: ", graph)
  //const state = await graph.getState(config);
  //console.log("response_state: ", state)

  const stream = createStreamableValue();
  const textStream = await graph.stream(input, config, { subgraphs: true });

  for await (const event of textStream) {
    //console.log("event:", event); 
    const { messages, summary, memories } = await handleServerStreamEvent(
      event,
      ["AIMessage", "ToolMessage"],
    );    

    //console.log("response_messages:", JSON.stringify(messages, null, 2));
    stream.update({ summary });
    stream.update({ memories }); 
    stream.update({ messages }); 

  }

  stream.done();
  
  return { output: stream.value };
}

/*const createMemorableEventConfig = (): MemoryConfig => ({
  function: {
    name: "memorable_event",
    description: "Any event, observation, or insight that might be useful for future interactions.",
    parameters: {
      type: "object",
      properties: {
        description: { type: "string" },
        participants: {
          type: "array",
          items: { type: "string" },
          description: "Names of participants in the event."
        },
        preferences: {
          type: "array",
          items: { type: "string" },
          description: "User's preferences related to the event."
        },
        emotions: {
          type: "array",
          items: { type: "string" },
          description: "Emotions experienced during the event."
        },
        events: {
          type: "array",
          items: { type: "string" },
          description: "Events that occurred during the memorable event."
        }
      },
      required: ["description"]
    }
  },
  system_prompt: SYSTEM_PROMPT,
  //"Extract memorable events from the user's messages. If nothing noteworthy, reply 'None'.",
  update_mode: "semantic", // "patch" | "semantic"
});*/
