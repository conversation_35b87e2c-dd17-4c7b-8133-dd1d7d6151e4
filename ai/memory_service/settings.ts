
import { createClient } from "@supabase/supabase-js";
import { CohereEmbeddings } from "@langchain/cohere";
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { Document } from "@langchain/core/documents";

export const memoryTableName = "memory_cohere"
export interface Settings {
  supabaseUrl: string;
  supabaseKey: string;
  supabaseTableName: string;
  supabaseQueryName: string;
  cohereApiKey: string;
  model: string;
  namespace: string;
}

// Default settings
export const SETTINGS: Settings = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!!,
  supabaseKey: process.env.SUPABASE_PRIVATE_KEY!!,
  supabaseTableName: "memory_cohere",
  supabaseQueryName: "match_memory_cohere",
  cohereApiKey: process.env.COHERE_API_KEY!!,
  model: "accounts/groq/models/llama-3.3-70b-versatile",
  namespace: "ns1",
};

// Supabase client initialization
export const supabaseClient = createClient(
  SETTINGS.supabaseUrl!!, 
  SETTINGS.supabaseKey!!
);

// Initialize Cohere embeddings
const embeddings = new CohereEmbeddings({
  apiKey: SETTINGS.cohereApiKey,
  model: "embed-multilingual-light-v3.0", // embedding vector (384)
});


// Initialize vector store
export const createVectorStore = async (
  filterMetadata?: Record<string, any>
) => {
  return new SupabaseVectorStore(embeddings, {
    client: supabaseClient,
    tableName: SETTINGS.supabaseTableName,
    queryName: SETTINGS.supabaseQueryName,
    filter: filterMetadata,
  });
};

// Export initialized services
export { supabaseClient as client, embeddings };