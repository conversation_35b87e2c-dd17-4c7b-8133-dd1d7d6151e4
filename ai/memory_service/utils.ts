import { Annotation, LangGraphRunnableConfig } from "@langchain/langgraph";
import { 
  HumanMessage,
  SystemMessage,
  BaseMessage ,
  mergeMessageRuns 
} from "@langchain/core/messages";
import { SYSTEM_PROMPT } from "./prompts";
import { createVectorStore } from "./settings";
import { MemoryConfig, GraphConfig, Configuration } from "./schemas";
import { v4 as uuidv4 } from 'uuid';

const DEFAULT_DELAY = 60; // seconds

export type FormatOptions = {
  includeMetadata?: boolean;
  separator?: string;
  indent?: number;
  includeTimestamp?: boolean;
}

// Merge message runs helper function
function mergeMessage(messages: BaseMessage[]): BaseMessage[] {
  // Implementation of message run merging logic
  // This is a simplified version - you may need to adapt based on your specific needs
  return messages;
}

export const createMemorableEventConfig = (
  tools: {
    type: string;
    function: {
      name: string;
      description: string;
      parameters: Record<string, any>;
    };
    update_mode?: "patch" | "insert";
    system_prompt?: string;
  }[],
  globalSystemPrompt: string
): GraphConfig["schemas"] => {
  if (!tools.length) return {};

  return tools.reduce((acc, tool) => {
    const { name, description, parameters } = tool.function;

    const memoryConfig: MemoryConfig = {
      function: {
        name,
        description,
        inputSchema,
      },
      update_mode: tool.update_mode || "patch", // Default mode
      system_prompt: tool.system_prompt || globalSystemPrompt, // Use specific system prompt or fallback
    };

    return {
      ...acc,
      [name]: memoryConfig,
    };
  }, {});
};


function isMemoryConfig(schema: unknown): schema is MemoryConfig {
  return schema !== null && typeof schema === 'object';
}

export function ensureMemoryConfig(config: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    user_id: configurable.user_id || '',
    assistant_id: configurable.assistant_id || '',
    system_prompt: configurable.system_prompt || '',
    model: configurable.model || '',
    delay: configurable.delay || 0,
    thread_id: configurable.thread_id || '',
    schemas: Object.fromEntries(
      Object.entries(configurable.schemas || {}).map(([key, schema]) => [
        key,
        {
          ...((schema as MemoryConfig) || {}),  // Add type assertion here
          function: (schema as MemoryConfig)?.function ?? {},
          system_prompt: (schema as MemoryConfig)?.system_prompt ?? null,
          update_mode: (schema as MemoryConfig)?.update_mode ?? "patch"
        } as MemoryConfig
      ])
    )
  };
}

export function ensureConfiguration(config?: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    system_prompt: configurable?.system_prompt || SYSTEM_PROMPT,
    schemas: configurable?.schemas || {},
    model: configurable?.model || "default-model",
    delay: configurable?.delay || 60,
    thread_id: configurable?.thread_id || "default-thread",
  };
}


/**
 * Merge message runs and add instructions before and after to stay on task
 */
export function prepareMessages(
  messages: BaseMessage[],
  system_prompt: string
): BaseMessage[] {
  const sys = new SystemMessage(
    `${system_prompt}
<memory-system>Reflect on following interaction. Use the provided tools to \
retain any necessary memories about the user.</memory-system>
`
  );

  const finalMessage = new HumanMessage(
    "## End of conversation\n\n" +
      "<memory-system>Reflect on the interaction above. " +
      "What memories ought to be retained or updated?</memory-system>"
  );

  //return mergeMessageRuns([sys, ...messages, finalMessage]);
  return mergeMessage([sys, ...messages, finalMessage]);
}

// Memoized vector store instance
let vectorStoreInstance: Awaited<ReturnType<typeof createVectorStore>> | null = null;

/**
 * Get or create vector store instance
 */
export async function getVectorStore() {
  if (!vectorStoreInstance) {
    vectorStoreInstance = await createVectorStore();
  }
  return vectorStoreInstance;
}

/**
 * Reset vector store instance (useful for testing or when config changes)
 */
export function resetVectorStore() {
  vectorStoreInstance = null;
}


export function formatMemories(jsonStrings: string[]): string[] {
  return jsonStrings.map(jsonString => {
    const parsedObject = JSON.parse(jsonString);

    const formatObject = (obj: Record<string, any>, prefix = ""): string => {
      let result = "";

      for (const [key, value] of Object.entries(obj)) {
        const formattedKey = `${prefix}${key}`; // Add prefix if needed for nested objects.

        if (Array.isArray(value)) {
          // Handle arrays
          result += `${formattedKey}: ${value.map(item => 
            typeof item === "object" ? `\n  - ${formatObject(item, "")}` : item
          ).join(", ")}\n`;
        } else if (typeof value === "object" && value !== null) {
          // Handle nested objects
          result += `${formattedKey}:\n${formatObject(value, "  ")}\n`;
        } else {
          // Handle primitive values
          result += `${formattedKey}: ${value}\n`;
        }
      }

      return result;
    };

    return formatObject(parsedObject);
  });
}


export {
  DEFAULT_DELAY,
  mergeMessageRuns
};