import {
  Annotation,
  MessagesAnnotation,
} from "@langchain/langgraph";
import { BaseMessage } from "@langchain/core/messages";
import { z } from 'zod/v3';

export const memorableEventSchema = z.object({
  description: z.string().describe('A description of the memorable event'),
  participants: z.array(z.string()).describe('Names of participants in the event'),
  preferences: z.array(z.string()).describe('User\'s preferences related to the event'),
  emotions: z.array(z.string()).describe('Emotions experienced during the event'),
  events: z.array(z.string()).describe('Events that occurred during the memorable event'),
});

export type FunctionSchema = {
  name: string;
  /** Name of the function */
  description: string;
  /** A description of the function */
  parameters: Record<string, any>;
  /** The JSON Schema for the memory */
};

export type MemoryConfig = {
  function: FunctionSchema;
  /** The function to use for the memory assistant */
  system_prompt?: string;
  /** The system prompt to use for the memory assistant */
  update_mode: "patch" | "insert";
  /** Whether to continuously patch the memory, or treat each new
   * generation as a new memory.
   * Patching is useful for maintaining a structured profile or core list
   * of memories. Inserting is useful for maintaining all interactions and
   * not losing any information.
   * For patched memories, you can GET the current state at any given time.
   * For inserted memories, you can query the full history of interactions.
   */
};

export type GraphConfig = {
  delay?: number;
  /** The delay in seconds to wait before considering a conversation complete.
   * Default is 60 seconds.
   */
  model?: string;
  /** The model to use for generating memories.
   * Defaults to Fireworks's "accounts/fireworks/models/firefunction-v2"
   */
  schemas: Record<string, MemoryConfig>;
  /** The schemas for the memory assistant */
  thread_id: string;
  /** The thread ID of the conversation */
  user_id: string;
  /** The ID of the user to remember in the conversation */
  assistant_id: string;
};

export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>(),
  assistant_id: Annotation<string>(),
  system_prompt: Annotation<string>(),
  schemas: Annotation<Record<string, MemoryConfig>>(),
  model: Annotation<string | undefined>(),
  delay: Annotation<number | undefined>(),
  thread_id: Annotation<string>(),
});
  
export type Configuration = typeof ConfigurationAnnotation.State;
 
export const MemGraphStateAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  eager: Annotation<boolean>(),
  summary: Annotation<string | undefined>({
    reducer: (_, action) => action,
    default: () => undefined
  }),
});

// Define `SingleExtractorState` annotation by merging `StateAnnotation` and adding unique fields
export const SingleExtractorStateAnnotation = Annotation.Root({
  ...MemGraphStateAnnotation.spec,
  function_name: Annotation<string | undefined>(),
  //responses: Annotation<BaseMessage[]>(), //if responses from subGraph
  responses: Annotation<Record<string, any>[] | null>(),
  user_state: Annotation<Record<string, any> | null>(),
});

// Types for both annotations
export type MemGraphState = typeof MemGraphStateAnnotation.State;
export type SingleExtractorState = typeof SingleExtractorStateAnnotation.State;