import { 
  END, 
  Send,
  START,
  StateGraph, 
  LangGraphRunnableConfig 
} from "@langchain/langgraph";
import { AIMessage, HumanMessage, BaseMessage, filterMessages } from "@langchain/core/messages";
import { ensureMemoryConfig, ensureConfiguration, 
  prepareMessages } from "./utils";
import { getUserMemories } from "./actions";
import * as constants from "./constants";
import * as settings from "./settings";
import { SYSTEM_PROMPT } from "./prompts"
import { createVectorStore, supabaseClient, memoryTableName } from "./settings"
import { userReflectionAnalysisTools } from "./tools"
import { getEmbedding } from "@/lib/cohere"
import { 
  type MemoryConfig,
  type MemGraphState, 
  type Configuration,
  type SingleExtractorState,
  MemGraphStateAnnotation, 
  SingleExtractorStateAnnotation, 
  ConfigurationAnnotation 
} from "./schemas"
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { v4 as uuidv4 } from 'uuid';

const model = new ChatGroq({
  model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
  temperature: 0,
})

function googleProModel() {
  return new ChatGoogleGenerativeAI({
    temperature: 0,
    model: "gemini-1.5-pro-002",
  })
}

function groqModel() {
  return new ChatGroq({
    temperature: 0,
    model: "llama-3.3-70b-versatile",
  })
}

// Fetch patched state
async function fetchPatchedState(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
) {
  if (!state.function_name) return { user_state: null };
  const configurable = ensureConfiguration(config);
  const userMemories = await getUserMemories(
    configurable.user_id, 
    configurable.assistant_id, 
    [state.function_name]
  );
  console.log("userMemories=======>: ", userMemories)
  if (userMemories && userMemories.length > 0) {
    const memories = userMemories[0]?.content ?? null;
    return { user_state: memories };
  }
  return { user_state: null };
}

async function extractPatchMemories(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
) {
  const configurable = ensureConfiguration(config);
  const memoryConfig = configurable.schemas[state?.function_name!];
  //const memoryConfig = configurable.schemas["memorable_event"];
  //console.log("schemas: ", JSON.stringify(configurable.schemas, null, 2))
  //console.log("state: ", JSON.stringify(state, null, 2))
  
  if (!memoryConfig) {
    console.error("Memory config not found for function:", state.function_name);
    throw new Error(`Memory config not found for function: ${state.function_name}`);
  }

  const inputs: any = {};
  let systemPrompt: string = memoryConfig.system_prompt || SYSTEM_PROMPT;

  /**
   * If state.user_state exists, it is added to the inputs. 
   * This is important because the LLM might need to know the current state of the user's data 
   * (such as previous preferences or configurations) to correctly generate a response.
   */
  if (state.user_state) {
    inputs.existing = {
      [memoryConfig.function.name]: state.user_state
    };
  }

  if (inputs.existing) {
    const existingMemory = inputs.existing[memoryConfig?.function.name];
    systemPrompt = systemPrompt
    .replace("{recall_memories}", typeof state.user_state === "string" 
      ? existingMemory : JSON.stringify(existingMemory || ""));
  }

  const lastUserMessage = state.user_state ? 
    state.messages.slice(-2)
    : state.messages.slice(-1)

  // Prepare the LLM input messages
  const messages = prepareMessages(
    lastUserMessage,
    systemPrompt,
  );

  // Construct the input object for LLM (you can adapt this as necessary)
  inputs.messages = prepareMessages(state.messages, systemPrompt);

  console.log("inputs: ", JSON.stringify(inputs, null, 2))
  //console.log("messages: ", JSON.stringify(state.messages, null, 2))
  //console.log("memoryConfig?.function.parameters", memoryConfig?.function.parameters)

  // Generate the structured response using the LLM (this could be a schema patch)
  
  const tool = { type: 'function', 
    function: { 
      name: memoryConfig?.function.name, 
      description: memoryConfig?.function.description, 
      inputSchema: memoryConfig?.function.parameters
    }
  }
  const llmWithResponseFormat = groqModel().bindTools([tool], {
    //strict: false, 
    tool_choice: "auto", 
    //response_format: memoryConfig.function.parameters
  })
  
  const llm = groqModel().withStructuredOutput(memoryConfig?.function.parameters, {
    name: "extract_patch_memories",
    includeRaw: false,
    //strict: true,
  });

  const extractor = llm.withConfig({
    runName: "ExtractPatchMemories",
  });

  //console.log("inputs.messages: ", JSON.stringify(inputs.messages, null, 2))
  const result = await extractor.invoke(inputs.messages);
  console.log("result: ", JSON.stringify(result, null, 2))
  // Assuming the LLM returns patch operations or structured data
  const extractedData = result; // Assuming a schema or patch operation is returned

  return { responses: [extractedData] };
}


// Upsert patched state
async function upsertPatchedState(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
) {
  if (!state.responses) return { user_state: {} }
  const configurable = ensureConfiguration(config);
  const path = constants.PATCH_PATH.replace(
    "{user_id}", configurable.user_id
  ).replace(
    "{function_name}", state?.function_name!
  );

  const serialized = JSON.stringify(state.responses[0]);
  const contentEmbeddings = await getEmbedding(
    serialized, "embed-multilingual-light-v3.0"
  )
  //console.log("contentEmbeddings================>", contentEmbeddings)
  console.log("path================>", path)

  const { data, error } = await supabaseClient
    .from(memoryTableName)
    .upsert({
        id: path,
        content: serialized,
        embedding: contentEmbeddings,
        metadata: {
        user_id: configurable.user_id,
        assistant_id: configurable.assistant_id,
        [constants.PAYLOAD_KEY]: state.responses[0],
        [constants.PATH_KEY]: path,
        [constants.TIMESTAMP_KEY]: new Date().toISOString(),
        }
  }, { onConflict: 'id' })
  console.log("==================Insert contentEmbeddings================>", error)

  return { user_state: {} };
}

// Should continue check
function shouldContinue(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
): "upsert_patched_state" | typeof END {
  //console.log("shouldContinue=======================>", state)
  return (state?.responses?.length ?? 0) > 0 ? "upsert_patched_state" : END;
}

// Build the patch graph
const patchBuilder = new StateGraph(
  { stateSchema: SingleExtractorStateAnnotation, },
  ConfigurationAnnotation,
)
  .addNode("fetch_patched_state", fetchPatchedState)
  .addNode("extract_patch_memories", extractPatchMemories)
  .addNode("upsert_patched_state", upsertPatchedState)
  .addEdge(START, "fetch_patched_state")
  .addEdge("fetch_patched_state", "extract_patch_memories")
  .addConditionalEdges(
    "extract_patch_memories",
    shouldContinue,
    {
      upsert_patched_state: "upsert_patched_state",
      [END]: END
    }
  )

  const patchGraph = patchBuilder.compile();

// Conditional Edge Functions
function shouldCommitPatch(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
): "upsert_patched_state" | typeof END {
  return (state?.responses?.length ?? 0) > 0 ? "upsert_patched_state" : END;
}

// Extract Semantic Memories
async function extractSemanticMemories(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
): Promise<{ responses: any[] }> {
  const configurable = ensureConfiguration(config);
  //const llm = initChatModel(configurable.model);
  const memoryConfig = configurable.schemas[state?.function_name!];
  //const memoryConfig = configurable.schemas["memorable_event"];
  //console.log("schemas: ", JSON.stringify(configurable.schemas, null, 2))
  //console.log("memoryConfig: ", JSON.stringify(memoryConfig, null, 2))

  
  if (!memoryConfig) {
    console.error("Memory config not found for function:", state.function_name);
    throw new Error(`Memory config not found for function: ${state.function_name}`);
  }

  let systemPrompt: string = memoryConfig.system_prompt || SYSTEM_PROMPT;
  if (state.user_state || state.summary) {
    systemPrompt = systemPrompt
      .replace("{summary}", String(state.summary ?? ""))
      .replace("{recall_memories}", typeof state.user_state === "string" ? state.user_state : JSON.stringify(state.user_state || ""));
  }

  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  const lastUserMessage = state.summary 
    ? humanMessages.slice(-2)
    : state.user_state 
    ? humanMessages.slice(-1)
    : humanMessages.slice(-2)
   
  const messages = prepareMessages(
    humanMessages.slice(-2), //state.summary ? state.messages.slice(-4) : state.messages.slice(-8),
    systemPrompt,
  );
  //console.log("==========================messages======================", messages)
  console.log("==========================lastUserMessage======================", lastUserMessage)
  const llm = groqModel().withStructuredOutput(memoryConfig.function.parameters, {
    name: "extract_semantic_memories",
    includeRaw: false,
    //strict: true,
  });

  const extractor = llm.withConfig({
    runName: "ExtractSemanticMemories",
  });

  const result = await extractor.invoke(messages);
  console.log("==========================result======================", result)
  return { responses: [result] };
}

// Insert Memories into Supabase VectorStore
async function insertMemories(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
): Promise<{ user_state: Record<string, any> }> {
  if (!state.responses) return { user_state: {} }
  const configurable = ensureConfiguration(config);
  //console.log("state: ", JSON.stringify(state, null, 2))
  // Initialize the vector store using Supabase and Cohere embeddings
  const vectorStore = await createVectorStore();

  const currentTime = new Date().toISOString();

  // Loop through responses and insert each one into the vector store
  for (const response of state.responses) {
    // Create a path for each document (unique for each response)
    const path = constants.INSERT_PATH.replace("{user_id}", configurable.user_id)
      .replace("{function_name}", state?.function_name!)
      .replace("{event_id}", uuidv4());
      
    const serialized = JSON.stringify(response);
    const contentEmbeddings = await getEmbedding(
      serialized, "embed-multilingual-light-v3.0"
    )
    // Prepare metadata
    const metadata = {
      user_id: configurable.user_id,
      assistant_id: configurable.assistant_id,
      [constants.PAYLOAD_KEY]: response,
      [constants.PATH_KEY]: path,
      [constants.TIMESTAMP_KEY]: currentTime,
    };
    /*const memories = {
      id: path,
      pageContent: serialized, // The response as content
      metadata: metadata, // The metadata for the document
    }
    console.log("semantic memories================>", memories)*/
    // Insert the response into the vector store
    const { data, error } = await supabaseClient
    .from(memoryTableName)
    .upsert({
        id: path,
        content: serialized,
        embedding: contentEmbeddings,
        metadata
  }, { onConflict: 'id' })
    //await vectorStore.addDocuments([memories]);
  }

  return { user_state: {} };
}


// Should Insert Edge Check
function shouldInsert(
  state: SingleExtractorState,
  config: LangGraphRunnableConfig
): "insert_memories" | typeof END {
  return (state?.responses?.length ?? 0) > 0 ? "insert_memories" : END;
}

// Schedule Processing Function
async function schedule(
  state: MemGraphState,
  config: LangGraphRunnableConfig
): Promise<{ messages: BaseMessage[] }> {
  if (state.eager) return { messages: state.messages };

  const configurable = ensureConfiguration(config);
  if (configurable.delay) {
    await new Promise((resolve) => setTimeout(resolve, configurable.delay));
  }
  return { messages: [] };
}

function isMemoryConfig(schema: unknown): schema is MemoryConfig {
  return schema !== null 
    && typeof schema === 'object' 
    && 'update_mode' in schema;
}
// Scatter Schemas
function scatterSchemas(
  state: MemGraphState,
  config: LangGraphRunnableConfig
): Send[] {
  const memoryConfig = ensureMemoryConfig(config);
  const sends = [];

  //console.log("memoryConfig: ", JSON.stringify(memoryConfig, null, 2))
  // function_name (the key) and schema (the value)
  for (const [function_name, schema] of Object.entries(memoryConfig.schemas)) {
    console.log('Processing schema for:', function_name);  // Debug log

    if (!isMemoryConfig(schema)) {
      throw new Error(`Invalid schema configuration for ${function_name}`);
    }
    
    const target =
      schema.update_mode === "patch"
        ? "handle_patch_memory"
      : schema.update_mode === "insert"
        ? "handle_semantic_memory"
      : (() => {
          throw new Error(`Unknown update mode: ${schema.update_mode}`);
        })();

    sends.push(new Send(target, { ...state, function_name }))
  }
  console.log("sends: ", JSON.stringify(sends, null, 2))
  return (sends);
}

// Semantic Memory Graph
const semanticBuilder = new StateGraph(
  { stateSchema: SingleExtractorStateAnnotation, },
  ConfigurationAnnotation,
)
  .addNode("extract_semantic_memories", extractSemanticMemories)
  .addNode("insert_memories", insertMemories)
  .addEdge(START, "extract_semantic_memories")
  .addConditionalEdges("extract_semantic_memories", shouldInsert)

const semanticGraph = semanticBuilder.compile();


// Main Graph
export const builder = new StateGraph(
  { stateSchema: MemGraphStateAnnotation, },
  ConfigurationAnnotation,
)
  .addNode("schedule", schedule)
  .addNode("handle_patch_memory", patchGraph)
  .addNode("handle_semantic_memory", semanticGraph)
  .addEdge(START, "schedule")
  .addConditionalEdges("schedule", scatterSchemas)

// Compile Main Graph
//export const memgraph = builder.compile();



/**
 * const callPatchGraph = async (
  state: typeof MemGraphStateAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const configurable = ensureMemoryConfig(config);
  const function_name = configurable.function?.name;
  const patchGraphInput = { 
    messages: state.messages, 
    function_name: function_name,
    summary: state.summary
 };
  //console.log("patchGraphInput", patchGraphInput)
  const patchGraphOutput = await patchGraph.invoke(patchGraphInput, config);
  return {};
};
const callSemanticGraph = async (
  state: typeof MemGraphStateAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const semanticGraphInput = { 
    messages: state.messages, 
    function_name: state.function_name,
    summary: state.summary
  };
  console.log("semanticGraphInput", semanticGraphInput)
  await semanticGraph.invoke(semanticGraphInput);
  return {};
};

function scatterSchemas(
  state: MemGraphState,
  config: LangGraphRunnableConfig
): { target: string; payload: SingleExtractorState }[] {
  const memoryConfig = ensureMemoryConfig(config);
  const sends = [];

  for (const [function_name, schema] of Object.entries(memoryConfig.schemas)) {
    const target =
      schema.update_mode === "patch"
        ? "handle_patch_memory"
        : schema.update_mode === "insert"
        ? "handle_semantic_memory"
        : (() => {
            throw new Error(`Unknown update mode: ${schema.update_mode}`);
          })();

    sends.push(new Send(target, { ...state, function_name }))
  }
  console.log("sends: ", JSON.stringify(sends, null, 2))
  return (sends);
}

function routeMemoryUpdates(
  state: typeof MemGraphStateAnnotation.State,
  config: LangGraphRunnableConfig
): string {
  //console.log("Routing state:", state);
  
  // Get the schema for the current function
  const configurable = ensureConfiguration(config);
  const schema = configurable.schemas;
  
  if (!schema) {
    console.log("No schema found, routing to patch");
    return "patch";
  }

  if (schema.update_mode === "patch") {
    return "patch"
  } else {
    return "semantic"
  }
}
 */