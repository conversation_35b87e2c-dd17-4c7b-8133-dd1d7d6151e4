export const SYSTEM_PROMPT = `You are a helpful assistant with advanced long-term memory
capabilities. Powered by a stateless LLM, you must rely on
external memory to store information between conversations.
Utilize the available memory tools to store and retrieve
important details that will help you better attend to the user's
needs and understand their context.
Memory Usage Guidelines:
1. Make informed suppositions and extrapolations based on stored memories.
2. Regularly reflect on past interactions to identify patterns and preferences.
3. Update your mental model of the user with each new piece of information.
4. Cross-reference new information with existing memories for consistency.
5. Prioritize storing emotional context and personal values alongside facts.
6. Use memory to anticipate needs and tailor responses to the user's style.
7. Recognize and acknowledge changes in the user's situation or perspectives over time.
8. Leverage memories to provide personalized examples and analogies.
9. Recall past challenges or successes to inform current problem-solving.

{summary}
## Recall Memories
Recall memories are contextually retrieved based on the previous conversation:
{recall_memories}

## Instructions
Engage with the user naturally, as a trusted colleague or friend.
There's no need to explicitly mention your memory capabilities.
Instead, seamlessly incorporate your understanding of the user into your responses. 
Be attentive to subtle cues and underlying emotions. 
Adapt your communication style to match the user's preferences and current emotional state.`

type MemorableTool = {
  type: "function";
  function: {
    name: string;
    description: string;
    parameters: Record<string, any>;
  };
  update_mode?: "patch" | "insert";
  system_prompt?: string;
};

export const threadSummaryTool: MemorableTool[] = [
  {
    type: 'function',
    function: {
      name: 'thread_summary',
      description: 'Prints a summary of the conversation.',
      inputSchema: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: 'Distinct for the conversation.'
          },
          topics: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of tags for topics discussed in this conversation, e.g. ["tech", "politics"]. Should be as specific as possible, and can overlap.'
          },
          summary: {
            type: 'string',
            description: 'High level summary of the interactions. One or two paragraphs max.'
          },
          coherence: {
            type: 'integer',
            description: 'Coherence of the conversation, 0-100 (inclusive)'
          },
          persuasion: {
            type: 'number',
            description: "Conversation's persuasion score, 0.0-1.0 (inclusive)"
          }
        },
        required: ['title', 'topics', 'summary', 'coherence', 'persuasion', 'counterpoint']
      }
    },
    update_mode: "insert",
    system_prompt: SYSTEM_PROMPT
  }
];

export const userProfileTool: MemorableTool[] = [
  {
    type: 'function',
    function: {
      name: 'user_state',
      description: 'Prints extract named entities.',
      inputSchema: {
        type: 'object',
        properties: {
          preferred_name: {
            type: 'string',
            description: "The user's name."
          },
          summary: {
            type: 'string',
            description: 'A quick summary of how the user would describe themselves.'
          },
          interests: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of short (two to three word) descriptions of areas of particular interest for the user. This can be a concept, activity, or idea. Favor broad interests over specific ones.'
          },
          emotions: {
            type: "array",
            items: { type: "string" },
            description: "Emotions experienced during the event."
          },
          other_info: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: ''
          },
          relationships: {
            type: 'array',
            description: 'A list of friends, family members, colleagues, and other relationships.',
            items: {
              type: 'object',
              description: "A person's biographical details.",
              properties: {
                name: {
                  type: 'string',
                  description: 'The name of the person.'
                },
                relation: {
                  type: 'string',
                  description: 'The relation of the person to the user.'
                },
                context: {
                  type: 'string',
                  description: 'A detailed yet concise history of things the person has done with the user.'
                }
              },
              required: ['name', 'relation', 'context']
            }
          }
        },
        required: ['summary']
      }
    },
    update_mode: "patch",
    system_prompt: SYSTEM_PROMPT
  }
];

export const coreBeliefAnalysisTool: MemorableTool[] = [
  {
    type: 'function',
    function: {
      name: 'core_belief_analysis',
      description: 'Analyzes and records the core beliefs of the user as extracted from conversations.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                belief: {
                  type: 'string',
                  description: 'The belief the user has about the world, themselves, or anything else.'
                },
                why: {
                  type: 'string',
                  description: 'Why the user believes this.'
                },
                context: {
                  type: 'string',
                  description: 'The raw context from the conversation that leads you to conclude that the user believes this.'
                }
              },
              required: ['belief', 'why', 'context']
            }
          }
        },
        required: ['entries']
      }
    },
  },
  {
    type: 'function',
    function: {
      name: 'formative_event_logging',
      description: 'Logs a significant, formative event in the user\'s life and its impact on them. This tool appends the recorded event to the user\'s state, contributing to a comprehensive understanding of the user\'s experiences.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                event: {
                  type: 'string',
                  description: 'The event that occurred. Must be important enough to be formative for the user.'
                },
                impact: {
                  type: 'string',
                  description: 'How this event influenced the user.'
                }
              },
              required: ['event', 'impact']
            }
          }
        },
        required: ['entries']
      }
    }
  }
];

export const userReflectionAnalysisTools: MemorableTool[] = [
  {
    type: 'function',
    function: {
      name: 'user_reflection_analysis',
      description: 'Analyzes reflections on specific activities or experiences (like stickball) and their significance to the user\'s life, along with scoring based on recency, importance, and relevance.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                content: {
                  type: 'object',
                  properties: {
                    subject: {
                      type: 'string',
                      description: 'The subject of the reflection.'
                    },
                    predicate: {
                      type: 'string',
                      description: 'The user\'s reflection on the activity or experience.'
                    },
                    object: {
                      type: 'string',
                      description: 'The impact of the activity or experience on the user\'s life.'
                    }
                  },
                  required: ['subject', 'predicate', 'object']
                },
                scores: {
                  type: 'object',
                  properties: {
                    recency: {
                      type: 'number',
                      description: 'The recency score of the reflection.'
                    },
                    importance: {
                      type: 'number',
                      description: 'The importance score of the reflection.'
                    },
                    relevance: {
                      type: 'number',
                      description: 'The relevance score of the reflection.'
                    }
                  },
                  required: ['recency', 'importance', 'relevance']
                }
              },
              required: ['content', 'scores']
            }
          }
        },
        required: ['entries']
      }
    },
    update_mode: "patch"
  }
];

export const userMemorableTools: MemorableTool[] = [
  {
    type: 'function',
    function: {
      name: "memorable_event",
      description: "Any event, observation, or insight that might be useful for future interactions.",
      inputSchema: {
        type: "object",
        properties: {
          description: { type: "string" },
          participants: {
            type: "array",
            items: { type: "string" },
            description: "Names of participants in the event."
          },
          preferences: {
            type: "array",
            items: { type: "string" },
            description: "User's preferences related to the event."
          },
          emotions: {
            type: "array",
            items: { type: "string" },
            description: "Emotions experienced during the event."
          },
          events: {
            type: "array",
            items: { type: "string" },
            description: "Events that occurred during the memorable event."
          }
        },
        required: ["description"]
      }
    }
  }
]
