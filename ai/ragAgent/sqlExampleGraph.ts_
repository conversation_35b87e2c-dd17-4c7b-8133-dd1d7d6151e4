import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, BaseMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
import { SqlToolkit } from "langchain/agents/toolkits/sql";
// Could not import hnswlib-node. Please install hnswlib-node as a dependency with, e.g. `npm install -S hnswlib-node`.
import { HNSWLib } from "@langchain/community/vectorstores/hnswlib";
import { SemanticSimilarityExampleSelector } from "@langchain/core/example_selectors";
import {
  FewShotPromptTemplate,
  PromptTemplate,
  ChatPromptTemplate,
  SystemMessagePromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { CohereEmbeddings } from "@langchain/cohere";
import { AgentExecutor, createToolCallingAgent } from "langchain/agents";
import { examples } from "./examples";

const datasource = new DataSource({
  type: "postgres",
  url: process.env.NEON_DATABASE_URL,
});

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true,
  verbose: true
})

const google = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 4,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);

const exampleSelector = await SemanticSimilarityExampleSelector.fromExamples(
  examples,
  new CohereEmbeddings({model: "embed-multilingual-light-v3.0"}),
  HNSWLib,
  {
    k: 5,
    inputKeys: ["input"],
  }
);

// https://github.com/langchain-ai/langchainjs/tree/main/examples/src/use_cases/sql
const SYSTEM_PREFIX = `You are an agent designed to interact with a SQL database.
Given an input question, create a syntactically correct {dialect} query to run, then look at the results of the query and return the answer.
Unless the user specifies a specific number of examples they wish to obtain, always limit your query to at most {top_k} results.
You can order the results by a relevant column to return the most interesting examples in the database.
Never query for all the columns from a specific table, only ask for the relevant columns given the question.
You have access to tools for interacting with the database.
Only use the given tools. Only use the information returned by the tools to construct your final answer.
You MUST double check your query before executing it. If you get an error while executing a query, rewrite the query and try again.

DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the database.

If the question does not seem related to the database, just return "I don't know" as the answer.

Here are some examples of user inputs and their corresponding SQL queries:`;



// Function to execute user queries
async function executeUserQuery(question: string, userId: string) {
  console.log("question====================================================", question)

  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });

  //const context = await db.getTableInfo();
  //console.log("Table context======================>", context);

  const fewShotPrompt = new FewShotPromptTemplate({
    exampleSelector,
    examplePrompt: PromptTemplate.fromTemplate(
      "User input: {input}\nSQL query: {query}"
    ),
    inputVariables: ["input", "dialect", "top_k"],
    prefix: SYSTEM_PREFIX,
    suffix: "",
  });
  const fullPrompt = ChatPromptTemplate.fromMessages([
    new SystemMessagePromptTemplate(fewShotPrompt),
    ["human", "{input}"],
    new MessagesPlaceholder("agent_scratchpad"),
  ]);

  const sqlToolKit = new SqlToolkit(db, llm);
  const tools = sqlToolKit.getTools();
  const newPrompt = await fullPrompt.partial({
    dialect: sqlToolKit.dialect,
    top_k: "10",
  });

  const runnableAgent = await createToolCallingAgent({
    llm: google,
    tools,
    prompt: newPrompt,
  });
  const agentExecutor = new AgentExecutor({
    agent: runnableAgent,
    tools,
  });
  const sqlResponse = await agentExecutor.invoke({ input: question })
  console.log("sqlResponse: ", sqlResponse)

return sqlResponse
}


const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const info = memories.map((d) => d.value.data).join("\n");
  const systemMsg = `You are a helpful assistant talking to the user. User info: ${info}`;
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)

  try {
    const sqlResult = await executeUserQuery(question, "user_2mZV57t33M5DFaeAPx9iYYjYUZP")
    console.log(`Got sql output====================>>>> ${sqlResult}`);
     return { messages: [sqlResult] };
  } catch (error) {
    console.log("Errors: Require OpenAI model ", error)
  }
  return { messages: []}  
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

/**
 * MessagesAnnotation is a pre-built state annotation imported from @langchain/langgraph.
 * It is the same as the following annotation:
 *
 * ```typescript
 * const MessagesAnnotation = Annotation.Root({
 *   messages: Annotation<BaseMessage[]>({
 *     reducer: messagesStateReducer,
 *     default: () => [systemMessage],
 *   }),
 * });
 * ```
 */
const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END]);


export async function createGraph() {
  await checkpointer.setup()

  const graph = workflow.compile({
    checkpointer,
    store: inMemoryStore
  });
  return graph;
}
