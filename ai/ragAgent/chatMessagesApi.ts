"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import { isAIMessageChunk, isToolMessage, 
  RemoveMessage, BaseMessage } from "@langchain/core/messages";
import { type ToolCallChunk } from "@langchain/core/messages/tool";
import { StreamMode } from "@/components/langgraph/Settings";
import { createGraph } from "./ragGraph";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts"
import { SYSTEM_PROMPT } from "./prompts"

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph?.getState(config)).values.messages;
  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  systemInstructions: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      messages: 
        {
          role: "user",
          content: params.message,
        },
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 8,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      system_prompt: SYSTEM_PROMPT,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
    },
    streamMode: "messages" as const,
  };

  const stream = createStreamableValue();
  let aiMessageId: string | null = null;

  (async () => {
    const graph = await createGraph()
    //console.log("graph: ", graph)
    //const state = await graph.getState(config);
    //console.log("state: ", state)
    const agentStream = await graph.stream(input, config);

    for await (const [message, _metadata] of agentStream) {
      //console.log("message================>:", JSON.stringify(message, null, 2));
      if (!message) return
      if (isAIMessageChunk(message)) {
        if (message.tool_call_chunks?.length) {
          // Tool calls
          stream.update({
            messages: [{
              id: message.id,
              sender: message.getType(), //'ai'
              toolCalls: message.tool_call_chunks.map((call: ToolCallChunk) => ({
                id: call.index,
                name: call.name,
                args: call.args,
                result: undefined,
              }))
            }]
          });
          console.log(`${message.getType()} MESSAGE TOOL CALL CHUNK: ${message.tool_call_chunks[0].args}`);
          console.log("aiMessage toolCalls====================>", message)
        } else {
          // AI message chunks - stream token by token
          if (!aiMessageId) {
            aiMessageId = crypto.randomUUID();
          }
          if (typeof message.content === "string" ? message.content.trim() : "" !== "") { // Prevent update for empty content
            console.log("aiMessage====================>", message);
            console.log("aiMessage type=====================>", message.getType())

            // Check if this is a final message (contains the complete content)
            const isFinalMessage = message.response_metadata?.object === "chat.completion.chunk";
            if (!isFinalMessage) {
              // Only stream partial chunks
              stream.update({
                messages: [{
                  id: aiMessageId,
                  sender: message.getType(),
                  text: message.content,
                  isPartial: true
                }]
              });
            }
          }
        }
      // ToolMessage  
      } else if (isToolMessage(message)) {
        stream.update({
          messages: [{
            id: message.id,
            sender: message.getType(), //'tool',
            toolCalls: [{
              id: message.tool_call_id,
              name: message.name,
              args: "",
              result: message.content,
            }]
          }]
        });
        console.log(`${message.getType()} MESSAGE TOOL CALL CHUNK: ${message}`);
        console.log("toolMessage====================>", message)
      }
    }

    stream.done();
  })();
  
  return { output: stream.value };
}