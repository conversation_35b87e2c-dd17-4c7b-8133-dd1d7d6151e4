// Define the configurable parameters for the agent

import { Annotation, LangGraphRunnableConfig } from "@langchain/langgraph";
import { MemoryConfig } from "@/ai/memory_service/schemas";
import { SYSTEM_PROMPT } from "./prompts";
import { v4 as uuidv4 } from 'uuid';

export const ConfigurationAnnotation = Annotation.Root({
  user_id: Annotation<string>(),
  assistant_id: Annotation<string>(),
  thread_id: Annotation<string>(),
  system_prompt: Annotation<string>(),
  schemas: Annotation<Record<string, MemoryConfig>>(),
  model: Annotation<string | undefined>(),
  delay: Annotation<number | undefined>(),
});

export type Configuration = typeof ConfigurationAnnotation.State;


export function ensureConfiguration(config?: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    user_id: configurable?.user_id || uuidv4(),
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || "default-thread",
    system_prompt: configurable?.system_prompt || SYSTEM_PROMPT,
    schemas: configurable?.schemas || {},
    model: configurable?.model || "default-model",
    delay: configurable?.delay || 5,
  };
}