import { UIMessage as VercelChatMessage } from 'ai';
import { auth, currentUser } from "@clerk/nextjs/server";
import { type DocumentInterface, Document } from "@langchain/core/documents";
//import { checkCreditBalance } from "@/lib/credit-balance";
//import { newThread } from '@/app/actions/threadActions'
//import { addTransaction } from '@/app/actions/creditsActions'
//import { saveCompletionToDatabase } from "@/lib/databaseUtils";

import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { CohereEmbeddings } from "@langchain/cohere";
import { CacheBackedEmbeddings } from "langchain/embeddings/cache_backed";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/core/stores";
import { z } from 'zod/v3';
import { zodToJsonSchema } from "zod-to-json-schema";
import { createRetrieverTool } from "langchain/tools/retriever";
import { tool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { ChatGroq } from "@langchain/groq";
import { ChatOpenAI } from "@langchain/openai";
import { ChatOllama } from "@langchain/ollama";
import { StringOutputParser } from "@langchain/core/output_parsers";

import { END, MemorySaver, START, StateGraph, LangGraphRunnableConfig } from "@langchain/langgraph";
import {
  ToolMessage, AIMessage,
  HumanMessage } from "@langchain/core/messages";
import { GraphState } from "./state";
//import zepClient, { zepMemory, formatChatSummaries, formatChatFacts } from "@/lib/zepClient"
import { TEMPLATE, SYSTEM_TEMPLATE_ENN, SYSTEM_TEMPLATE_CHAT } from "./prompts"
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";

export const preferredRegion = ['sfo1']

const llm_ = new ChatOllama({
  model: "llama3.1:latest",
  temperature: 0,
  maxRetries: 2,
  baseUrl: "http://192.168.0.100:11434"
});

//const retriever = vectorStore.asRetriever();
const inMemoryStore = new InMemoryStore();
const underlyingEmbeddings = new CohereEmbeddings({
  apiKey: process.env.COHERE_API_KEY,
  model: "embed-multilingual-light-v3.0", //dimension: 384
})
const cacheBackedEmbeddings = CacheBackedEmbeddings.fromBytesStore(
  underlyingEmbeddings,
  inMemoryStore,
  {
    namespace: underlyingEmbeddings.model,
  }
);

//const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);

type ClientData = {
  //companion: { id: string; name: string; instructions: string; seed: string };
  //persona: { name: string; nickName: string; age: number; traits: string[]; status: string };
  //messages: VercelChatMessage[];
  //aiLanguage: Language;
};

export async function createGraph() {
  const { userId, getToken } = await auth();  
  const user = await currentUser();
  const token = await getToken({ template: "supabase" });
  //console.log("getToken=========================>", token)
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  if (!userId || !authToken) {
    throw new Error("Unauthorized");
  }

  /*const creditAccess = await checkCreditBalance();
  if (!creditAccess) {
    return {
      error: 'Your credit is empty. Please purchase more credits.',
      status: 403,
    };
  }*/

  //const language = aiLanguage?.value! ?? 'English'
  const language = 'zh-TW'

  // Many keys logged with hashed values
  const keys = [];
  for await (const key of inMemoryStore.yieldKeys()) {
    keys.push(key);
  }

  console.log("====================================InMemoryStore====================================", keys.slice(0, 5));

  const client = await createClerkSupabaseServerClient(authToken);
  const hybridSearch = new SupabaseHybridSearch(
    new CohereEmbeddings({
      apiKey: process.env.COHERE_API_KEY,
      model: "embed-multilingual-v3.0", //dimension: 1024
    }),
    {
      client,
      similarityK: 1,
      keywordK: 0,
      tableName: "document_sections_1024",
      similarityQueryName: "match_document_sections_1024",
      keywordQueryName: "kw_match_document_sections",
    });
  const RetrieverSchema = z.object({
    query: z.string().describe("query to look up in retriever"),
  });
  
  const retrieverTool = tool(
    async (input, config) => {
      const parsedInput = RetrieverSchema.safeParse(input);
      if (!parsedInput.success) {
        throw new Error("Invalid input format");
      }
      const { query } = parsedInput.data;
      const docs = await hybridSearch.invoke(query)
      //const results = formatDocumentsAsString(docs)
      const concatenatedContent = docs
      .filter(doc => doc.metadata?.user_id === userId)
      .map(doc => doc.pageContent)
      .join('\n\n'); // Adds spacing between contents if needed

      //console.log("====================>concatenatedContent", concatenatedContent)

    return concatenatedContent;
    },
    {
      name: "search_my_personal_report",
      description: "Search and return information about me or my report",
      schema: RetrieverSchema,
    }
  )

  const vectorStore = new SupabaseVectorStore(
    cacheBackedEmbeddings,
    {
      client,
      tableName: 'document_sections_384_hf',
      queryName: 'match_document_sections_384_hf',
    }
  )

  /*const replyTool = tool(
    async (input, config) => {
      const docs = await vectorStore.similaritySearch(input.query, 1, { source: "Comfyminds AI_Traits.md" })
      //const results = formatDocumentsAsString(docs)
      const concatenatedContent = docs
      .filter(doc => doc.metadata?.user_id === userId)
      .map(doc => doc.pageContent)
      .join('\n\n'); // Adds spacing between contents if needed

      console.log("====================>concatenatedContent", concatenatedContent)

    return concatenatedContent;
    },
    {
      name: "search_my_enneagram_strength",
      description: "Searches and returns my enneagram strength",
      schema: RetrieverSchema,
    }
  )*/
  const reply_retriever = vectorStore.asRetriever({ k: 1 });
  const replyTool = createRetrieverTool(reply_retriever, {
    name: "search_enneagram_strength",
    description: "Searches and returns related enneagram strength for better reply user's question."
  });
  

  const retrieverTools = [retrieverTool, replyTool];
  //console.log("retrieverTools: ", retrieverTools)

  const toolNode = new ToolNode<typeof GraphState.State>(retrieverTools);
  function shouldRetrieve(state: typeof GraphState.State): string {
    const { messages } = state;
    console.log("---DECIDE TO RETRIEVE---");
    //console.log("state", state);
    const lastMessage = messages[messages.length - 1];
    //console.log("---DECIDE TO RETRIEVE---", lastMessage);

    if (lastMessage && "tool_calls" in lastMessage && Array.isArray(lastMessage.tool_calls) && lastMessage.tool_calls.length) {
      console.log("---DECISION: RETRIEVE---");
      return "continue";
    }
    // If there are no tool calls then we finish.
    return "end";
  };

  async function gradeDocuments(
    state: typeof GraphState.State
  ): Promise<Partial<typeof GraphState.State>> {
    console.log("---GET RELEVANCE---");
    const { messages } = state;
    const tool = {
      name: "give_relevance_score",
      description: "Give a relevance score to the retrieved documents.",
      schema: z.object({
        binaryScore: z.string().describe("Relevance score 'yes' or 'no'"),
      })
    }

    const prompt = ChatPromptTemplate.fromTemplate(
      `You are a grader assessing relevance of retrieved docs to a user question.
    Here are the retrieved docs:
    \n ------- \n
    {context} 
    \n ------- \n
    Here is the user question: {question}
    If the content of the docs are relevant to the users question, score them as relevant.
    Give a binary score 'yes' or 'no' score to indicate whether the docs are relevant to the question.
    Yes: The docs are relevant to the question.
    No: The docs are not relevant to the question.`,
    );

    const model = new ChatGroq({
      model: "llama3-groq-70b-8192-tool-use-preview",
      temperature: 0,
    }).bindTools([tool], {
      tool_choice: "auto",
    });

    const chain = prompt.pipe(model);
    //console.log("state: ", state)
    const lastMessage = messages[messages.length - 1];

    const score = await chain.invoke({
      question: messages[0].content as string,
      context: lastMessage.content as string,
    });
    //console.log("score: ", score)
    return {
      messages: [score]
    };
  }

  /**
    * Check the relevance of the previous LLM tool call.
    * 
    * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
    * @returns {string} - A directive to either "yes" or "no" based on the relevance of the documents.
    */
  function checkRelevance(
    state: typeof GraphState.State,
    config: LangGraphRunnableConfig
  ): string {
    console.log("---CHECK RELEVANCE---");
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];
    if (!("tool_calls" in lastMessage)) {
      throw new Error("The 'checkRelevance' node requires the most recent message to contain tool calls.")
    }
    //console.log("state: ", state)
    //console.log("lastMessage: ", lastMessage)
    const toolCalls = (lastMessage as AIMessage).tool_calls;
    if (!toolCalls || !toolCalls.length) {
      throw new Error("Last message was not a function message");
    }

    if (toolCalls[0].args.binaryScore === "yes") {
      console.log("---DECISION: DOCS RELEVANT---");
      return "yes";
    }
    console.log("---DECISION: DOCS NOT RELEVANT---");
    return "no";
  }

  async function agent(
    state: typeof GraphState.State, 
    config: LangGraphRunnableConfig
  )
    : Promise<Partial<typeof GraphState.State>> {
    console.log("---CALL AGENT---");
    const { messages } = state;
    // Find the AIMessage which contains the `give_relevance_score` tool call,
    // and remove it if it exists. This is because the agent does not need to know
    // the relevance score.
    const filteredMessages = messages.filter((message) => {
      if ("tool_calls" in message && Array.isArray(message.tool_calls) && message.tool_calls.length > 0) {
        return message.tool_calls[0].name !== "give_relevance_score";
      }
      return true;
    });
    console.log("---filteredMessages---", filteredMessages);
    
    const model = new ChatGroq({
      model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
      temperature: 0,
    })
    //.bind({
    //  systemMessage: `You are a helpful assistant that processes information and call the "retrieverTools" only when you do not have user's related information.
    //  Do NOT call the tool everytime if you have user's information`
    //})
    .bindTools(retrieverTools, {
      tool_choice: "auto"
    });

    const response = await model.invoke(filteredMessages);
    // We can return just the response because it will be appended to the state.
    //console.log("---AGENT RESPONSE---", response);
    return { messages: [response] };
  };

  /**
    * Transform the query to produce a better question.
    * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
    * @param {RunnableConfig | undefined} config - The configuration for the runnable.
    * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
    */
  async function rewrite(state: typeof GraphState.State): Promise<Partial<typeof GraphState.State>> {
    console.log("---TRANSFORM QUERY---");
    const { messages } = state;
    const question = messages[0].content as string;
    const prompt = ChatPromptTemplate.fromTemplate(`Look at the input and try to reason about the underlying semantic intent / meaning. \n 
    Here is the initial question:
    \n ------- \n
    {question} 
    \n ------- \n
    Formulate an improved question:`);

    // Grader
    //const model = new ChatOpenAI({
    //  modelName: "gpt-4.1",
    //  temperature: 0,
    //  streaming: true,
    //});
    const model = new ChatGroq({
      modelName: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
      temperature: 0,
    })
    const response = await prompt.pipe(model).invoke({ question });
    //console.log("improved question response: ", response)
    return { messages: [response] }
  }

  async function generate(
    state: typeof GraphState.State,
    config: LangGraphRunnableConfig
  ): Promise<Partial<typeof GraphState.State>> {
    console.log("---GENERATE---");
    const { messages } = state;
    console.log("---GENERATE---messages---", messages);
    const question = messages[0].content as string;
    //console.log("---GENERATE---question---", question);
    const configurable = ensureConfiguration(config);
    // Extract the most recent ToolMessage
    const lastToolMessage = messages.slice().reverse().find((msg) => msg.getType() === "tool");
    if (!lastToolMessage) {
      throw new Error("No tool message found in the conversation history");
    }
    //console.log("---GENERATE---lastToolMessage---", lastToolMessage);
    const docs = lastToolMessage.content as string;
    //console.log("---GENERATE---docs---", docs);
    
    const formatVercelMessages = (chatHistory: undefined[]) => {
      const formattedDialogueTurns = chatHistory.map((message) => {
        if (message.role === "user") {
          return `Human: ${message.content}`;
        } else if (message.role === "assistant") {
          return `Assistant: ${message.content}`;
        } else {
          return `${message.role}: ${message.content}`;
        }
      });
      return formattedDialogueTurns.join("\n");
    };

    let prompt: ChatPromptTemplate;
    if (lastToolMessage?.name === "tavily_search_results_json") {
      prompt = ChatPromptTemplate.fromTemplate(TEMPLATE);
    } else {
      prompt = ChatPromptTemplate.fromTemplate(SYSTEM_TEMPLATE_ENN);
    }

    //console.log("@@@@@@@@@@@@@@@@@@@@prompt@@@@@@@@@@@@@@@@@@@@@@@@@@@@: ", prompt)

    /*const llm = new ChatOpenAI({
      model: "gpt-4.1-mini",
      temperature: 0,
      streaming: true,
    });*/
    const sambanova = new ChatOpenAI({
      configuration: {
        baseURL: "https://api.sambanova.ai/v1/",
        apiKey: process.env.SAMBANOVA_API_KEY,
      },
      model: "Meta-Llama-3.1-70B-Instruct",
    })
    const model = new ChatGroq({
      model: "llama-3.3-70b-versatile", //"deepseek-r1-distill-llama-70b", //"llama-3.3-70b-versatile", //"llama-3.3-70b-versatile",
      temperature: 0,
      maxOutputTokens: 1024,
      streaming: true
    })

    /*const { 
      prevMemoryFacts, 
      prevMemorySummaries 
    } = (await zepMemory(
      configurable.assistant_id!,
      configurable.thread_id!,
      configurable.user_id!,
      question,
    )) ?? { prevMemoryFacts: [], prevMemorySummaries: [] }; */
    //console.log("ChatHistory", formatVercelMessages(previousMessages))
        
    const ragChain = prompt.pipe(model)//.pipe(new StringOutputParser());
    const response = await ragChain.invoke({
      context: docs,
      question,
      language,
      chat_history: "", //formatVercelMessages(previousMessages),
      chat_fact: "", //formatChatFacts(prevMemoryFacts),
      chat_summary: "", //formatChatSummaries(prevMemorySummaries)
    });
    console.log("response: ==========================>", response)
    /*if (response && response?.content) {
        await zepClient.memory.add(configurable.thread_id, {
        messages: [
          //{ role: "user", roleType: "user", content: question },
          { role: "assistant", roleType: "assistant", content: response.content }
        ]   
      });    
    }*/
    
    return { messages: [response] }
  }

  async function normalChat(
    state: typeof GraphState.State,
    config: LangGraphRunnableConfig
  ): Promise<Partial<typeof GraphState.State>> {
    console.log("---CHAT---");
    const { messages } = state;
    console.log("---CHAT---messages---", messages);
    const question = messages[0].content as string;
    console.log("---CHAT---question---", question);
    const configurable = ensureConfiguration(config);
    
    const formatVercelMessages = (chatHistory: undefined[]) => {
      const formattedDialogueTurns = chatHistory.map((message) => {
        if (message.role === "user") {
          return `Human: ${message.content}`;
        } else if (message.role === "assistant") {
          return `Assistant: ${message.content}`;
        } else {
          return `${message.role}: ${message.content}`;
        }
      });
      return formattedDialogueTurns.join("\n");
    };
    const prompt = ChatPromptTemplate.fromTemplate(SYSTEM_TEMPLATE_CHAT)

    const model = new ChatGroq({
      model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
      temperature: 0.7,
      maxOutputTokens: 1024,
      streaming: true
    })

    /*const { 
      prevMemoryFacts, 
      prevMemorySummaries 
    } = (await zepMemory(
      configurable.assistant_id!,
      configurable.thread_id!,
      configurable.user_id!,
      question,
    )) ?? { prevMemoryFacts: [], prevMemorySummaries: [] }; */

    const chatChain = prompt.pipe(model)//.pipe(new StringOutputParser());
    const response = await chatChain.invoke({
      question,
      language,
      chat_history: "", //formatVercelMessages(previousMessages),,
      chat_fact: "", //formatChatFacts(prevMemoryFacts),
      chat_summary: "", //formatChatSummaries(prevMemorySummaries),
    });
    /*if (response && response?.content) {
        await zepClient.memory.add(configurable.thread_id, {
        messages: [
          //{ role: "user", roleType: "user", content: question },
          { role: "assistant", roleType: "assistant", content: response.content }
        ]   
      });    
    }*/

    //console.log(`STATE from normalChat: '${state}'`);

    return { messages: [response] }
  }

  const workflow = new StateGraph(
    { stateSchema: GraphState, },
    ConfigurationAnnotation,
  )
    .addNode("agent", agent)
    .addNode("retrieve", toolNode)
    //.addNode("gradeDocuments", gradeDocuments)
    //.addNode("rewrite", rewrite)
    .addNode("generate", generate)
    .addNode("normalChat", normalChat)

  // Call agent node to decide to retrieve or not
  .addEdge(START, "agent")
  // Decide whether to retrieve
  .addConditionalEdges(
    "agent",
    // Assess agent decision
    shouldRetrieve,
    {
      // Call tool node
      continue: "retrieve",
      end: "normalChat",
    }
  )

  //.addEdge("retrieve", "gradeDocuments")
  .addEdge("retrieve", "generate")

  // Edges taken after the `action` node is called.
  /*.addConditionalEdges(
    "gradeDocuments",
    // Assess agent decision
    checkRelevance,
    {
      // Call tool node
      yes: "generate",
      no: "rewrite" , // placeholder
    }
  )*/

  .addEdge("generate", END)
  .addEdge("normalChat", END)
  //.addEdge("rewrite", "normalChat")


  // You must call .setup() the first time you use the checkpointer:
  //await checkpointer.setup();

  //const app = workflow.compile({ checkpointer: new MemorySaver(), interruptBefore: ["retrieve"] });
  const graph = workflow.compile({  });

  return graph
}
