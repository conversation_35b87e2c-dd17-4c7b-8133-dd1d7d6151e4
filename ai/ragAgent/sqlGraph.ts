import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, BaseMessage, filterMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';
import { SqlDatabase } from "langchain/sql_db";
import { DataSource } from "typeorm";
//import { createSqlAgent, SqlToolkit } from "langchain/agents/toolkits/sql";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import {
  RunnablePassthrough,
  RunnableSequence,
} from "@langchain/core/runnables";
//import { createSqlQueryChain } from "langchain/chains/sql_db";
import { generateObject } from "ai";
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod/v3';

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});


const datasource = new DataSource({
  type: "postgres",
  url: process.env.NEON_DATABASE_URL,
});

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //"llama-3.3-70b-versatile", 
  temperature: 0,
  streaming: true
})

const llm__ = new ChatGoogleGenerativeAI({
  temperature: 0,
  model: "gemini-1.5-pro",
  streaming: true,
  verbose: true
})

const sambanova = new ChatOpenAI({
  configuration: {
    baseURL: "https://api.sambanova.ai/v1/",
    apiKey: process.env.SAMBANOVA_API_KEY,
  },
  model: "Meta-Llama-3.1-70B-Instruct",
  streaming: true
})

const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 4,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);



// Define the system prompt for the SQL generation agent
const systemPrompt = `
You are an SQL expert helping retrieve messages from a database. 
Based on the user's input, generate a syntactically correct SQL query for the "Message" table.
Only include necessary filters based on the input.
Unless otherwise specified, do not return more than {top_k} rows.

Here is the relevant table info: {table_info}

If the user specifies a userId (e.g., "user_2mZV57t33M5DFaeAPx9iYYjYUZP") 
or time (e.g., "recently", "today", or "2024-11-01"), 
apply the appropriate SQL filter (e.g., WHERE "userId" = "user_2mZV57t33M5DFaeAPx9iYYjYUZP").

Always respond with just the SQL query without explanation.`;

// Function to execute user queries
async function executeUserQuery(question: string, userId: string) {
  console.log("question====================================================", question)

  const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });
  // Prompt setup for LangChain
  /*const prompt = ChatPromptTemplate.fromMessages([
    ["system", systemPrompt],
    ["human", "{input}"],
  ]);
  const queryChain = await createSqlQueryChain({
    llm,
    db,
    prompt,
    dialect: "postgres",
  });*/


  // Unified chain for content retrieval
  const chain = RunnableSequence.from([
    async (input: { question: string }) => {
      //const sqlQueryResult = await queryChain.invoke(input);
      //const sqlQueryResult = await generateQuery(input.question, userId)
      //console.log("Generated SQL Query:========>", sqlQueryResult);
   
      const keywords = question.split(/\s+/).filter(k => k.length > 2); // Split by spaces and filter out short words
      const userIdCondition = `"userId" = '${userId}'`;
      
      // Create ILIKE conditions for each keyword
      const keywordConditions = keywords.map(keyword => 
        `content ILIKE '%${keyword}%'`
      ).join(' OR ');
      
      // Combine all conditions
      const conditions = [
        userIdCondition,
        `(${keywordConditions})`  // Wrap keyword conditions in parentheses
      ].join(" AND ");
      
      return {
        question,
        sqlQuery: `
          SELECT content
          FROM "Message"
          WHERE ${conditions}
          ORDER BY "updatedAt" DESC
          LIMIT 10;
        `
      };
    },
    async (queryInput: { sqlQuery: string }) => {
      console.log("Generated SQL Query:", queryInput.sqlQuery);
      return await db.run(queryInput.sqlQuery);
    },
  ]);

  const sqlExecutionResult = await chain.invoke({ question });

  
  if (!sqlExecutionResult || sqlExecutionResult.length === 0) {
    return "No results found for your query.";
  }

  // Step 3: Use SQL results as context for LLM discussion
  const context = `SQL Results:\n${JSON.stringify(sqlExecutionResult, null, 2)}\n\n`;
  const discussionPrompt = `
    You are a helpful assistant. Based on the following data retrieved from the database, answer the user's question:
    ${context}

    User's Question: ${question}
  `;
  console.log("discussPrompt", discussionPrompt)

  const discussionChain = await llm.invoke(discussionPrompt);

  return discussionChain;
}

export const generateQuery = async (input: string, userId: string) => {
  "use server";
  try {
    const result = await generateObject({
      model: groq("llama-3.3-70b-versatile"),
      system: `You are a SQL (postgres) expert. Your job is to help the user write a SQL query from the content with WHERE userId = ${userId} to retrieve the data they need.
      User specified userId: '${userId}'
      
      If user specifies a userId (e.g., '${userId}') 
      Apply the appropriate SQL filter (e.g., WHERE userId = '${userId}').

      The table schema is as follows:
      Message (
      id UUID PRIMARY KEY,
      content Text,
      userId Text NOT NULL,
      updatedAt DATETIME NOT NULL
    )

    Always respond with just the SQL query without explanation.
    `,
      prompt: `Generate the query with conditions necessary to retrieve the data the user wants: ${input}`,
      schema: z.object({
        query: z.string(),
      }),
    });
    console.log("userId: ", userId)
    console.log("result.object.query", result.object.query)
    return result.object.query;
  } catch (e) {
    console.error(e);
    throw new Error("Failed to generate query");
  }
};

const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const info = memories.map((d) => d.value.data).join("\n");
  const systemMsg = `You are a helpful assistant talking to the user. User info: ${info}`;
  const humanMessages = filterMessages(state.messages, { includeTypes: ["human"] });
  
  // Now perform slicing with the filtered array
  const lastUserMessage = humanMessages.slice(-1)[0];
  const question = lastUserMessage.content as string

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)

  /*const llmWithTools = llm.bindTools(tools);
  const result = await llmWithTools.invoke([
    { type: "system", content: systemMsg },
    ...messages,
  ]);*/

  /*const db = await SqlDatabase.fromDataSourceParams({
    appDataSource: datasource,
  });
  
  const toolkit = new SqlToolkit(db, llm);  const executor = createSqlAgent(llm, toolkit);
  
  const input = `SELECT content FROM "Message" WHERE userId = 'user_2mZV57t33M5DFaeAPx9iYYjYUZP' LIMIT 10. 
  Prisma schema for Message table from supabase database (partial):
  id             String            @id @default(uuid())
  content        String
  userId         String
  
  Question: ${question} 
  Rsponse in zh-TW`;
  console.log(`input====================>>>> ${input}`);
  */

  try {
    //const sqlResult = await executor.invoke({ input });
    const sqlResult = await executeUserQuery(question, "user_2mZV57t33M5DFaeAPx9iYYjYUZP")
      
    console.log(`Got sql output====================>>>> ${sqlResult}`);
    //return { messages: [sqlResult.content] };
    return { messages: [sqlResult] };
  } catch (error) {
    console.log("Errors: Require OpenAI model ", error)
  }
  return { messages: []}  
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

/**
 * MessagesAnnotation is a pre-built state annotation imported from @langchain/langgraph.
 * It is the same as the following annotation:
 *
 * ```typescript
 * const MessagesAnnotation = Annotation.Root({
 *   messages: Annotation<BaseMessage[]>({
 *     reducer: messagesStateReducer,
 *     default: () => [systemMessage],
 *   }),
 * });
 * ```
 */
const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END]);


export async function createGraph() {
  await checkpointer.setup()

  const graph = workflow.compile({
    checkpointer,
    store: inMemoryStore
  });
  return graph;
}
