import { ToolNode } from "@langchain/langgraph/prebuilt";
import {
  type LangGraphRunnableConfig,
  END,
  MemorySaver,
  MessagesAnnotation,
  START,
  StateGraph,
} from "@langchain/langgraph";
import { AIMessage, ToolMessage, BaseMessage, isToolMessage } from "@langchain/core/messages";
import { ChatGroq } from "@langchain/groq";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { InMemoryStore } from "@langchain/langgraph";
import {
  ConfigurationAnnotation,
  ensureConfiguration,
} from "./configuration";
import { v4 as uuidv4 } from 'uuid';

const llm = new ChatGroq({
  model: "llama-3.3-70b-versatile", //llama-3.3-70b-versatile",
  temperature: 0,
  streaming: true
})

const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);
const inMemoryStore = new InMemoryStore();

const updateMemory = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  // Get the store instance from the config
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  // Get the user id from the config
  const configurable = ensureConfiguration(config);

  // Namespace the memory
  const namespace = ["memories", configurable.user_id];

  // Create a new memory ID
  const memoryId = uuidv4();

  // Store new memories if the user asks the model to remember
  const lastMessage = state.messages[state.messages.length - 1];
  if (
    typeof lastMessage.content === "string" &&
    lastMessage.content.toLowerCase().includes("remember")
  ) {
    await store.put(namespace, memoryId, { data: lastMessage.content });
  }
};

const webSearchTool = new TavilySearchResults({
  maxResults: 1,
});
const tools = [webSearchTool];

const toolNode = new ToolNode(tools);

const callModel = async (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;
  //console.log("======messages: =======", messages)
  const store = config.store;
  if (!store) {
    throw new Error("store is required when compiling the graph");
  }

  const configurable = ensureConfiguration(config);
  const namespace = ["memories", configurable?.user_id];
  const memories = await store.search(namespace);
  const info = memories.map((d) => d.value.data).join("\n");
  const systemMsg = `You are a helpful assistant talking to the user. User info: ${info}`;

  // Store new memories if the user asks the model to remember
  await updateMemory(state, config)
  //console.log("=================info: ===============", info)

  let modelMessages = [];
  for (let i = state.messages.length - 1; i >= 0; i--) {
    modelMessages.push(state.messages[i]);
    if (modelMessages.length >= 5) {
      if (!isToolMessage(modelMessages[modelMessages.length - 1])) {
        break;
      }
    }
  }
  modelMessages.reverse();

  const llmWithTools = llm.bindTools(tools);
  const result = await llmWithTools.invoke([
    { type: "system", content: systemMsg },
    ...modelMessages,
  ]);
  return { messages: [result] };
};

const shouldContinue = (
  state: typeof MessagesAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  const { messages } = state;

  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage?.getType() !== "ai" ||
    !(lastMessage as AIMessage).tool_calls?.length
  ) {
    // LLM did not call any tools, or it's not an AI message, so we should end.
    return END;
  }
  return "tools";
};

/**
 * MessagesAnnotation is a pre-built state annotation imported from @langchain/langgraph.
 * It is the same as the following annotation:
 *
 * ```typescript
 * const MessagesAnnotation = Annotation.Root({
 *   messages: Annotation<BaseMessage[]>({
 *     reducer: messagesStateReducer,
 *     default: () => [systemMessage],
 *   }),
 * });
 * ```
 */
const workflow = new StateGraph(
  {
    stateSchema: MessagesAnnotation,
  },
  ConfigurationAnnotation,
)
  .addNode("agent", callModel)
  .addEdge(START, "agent")
  .addNode("tools", toolNode)
  .addEdge("tools", "agent")
  .addConditionalEdges("agent", shouldContinue, ["tools", END]);


export async function createGraph() {
  await checkpointer.setup()

  const graph = workflow.compile({
    checkpointer,
    store: inMemoryStore
  });
  return graph;
}