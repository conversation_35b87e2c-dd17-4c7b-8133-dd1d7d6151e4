"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import { RemoveMessage, BaseMessage } from "@langchain/core/messages";
import { StreamMode } from "@/components/langgraph/Settings";
import { ThreadState } from "@/components/langgraph/Schema";
import { createGraph } from "./graph";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/prompts"
import { SYSTEM_PROMPT } from "./prompts"
import { 
  type StreamEvent,
  handleServerStreamEvent
 } from '@/utils/streamHandler'

export async function getThreadState(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.getState({ configurable: { thread_id: threadId } });
}

export async function updateState(
  threadId: string, 
  fields: { newState: Record<string, any>, asNode?: string }, 
) {
  if (!threadId) return
  const graph = await createGraph();
  return await graph.updateState({ configurable: { thread_id: threadId } }, {
    values: fields.newState,
    asNode: fields.asNode,
  });
}

export async function deleteThread(
  threadId: string, 
) {
  if (!threadId) return
  const graph = await createGraph();
  const config = { configurable: { thread_id: threadId } }
  const updatedMessages = (await graph.getState(config)).values.messages;
  await graph.updateState(
    config, 
    { messages: await updatedMessages.map(
      (m: BaseMessage) => new RemoveMessage({ id: m?.id! })) }
  );
}

export const sendMessage = async (params: {
  threadId: string;
  assistantId: string;
  messageId: string;
  message: string | null;
  userId: string;
  systemInstructions: string;
  streamMode: StreamMode;
}) => {
  let input: Record<string, any> | null = null;
  if (params.message !== null) {
    input = {
      messages: 
        {
          role: "user",
          content: params.message,
        },
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    MEMORY_PROMPT
  );
  const config = {
    recursionLimit: 8,
    configurable: {
      user_id: params.userId,
      assistant_id: params.assistantId,
      thread_id: params.threadId,
      system_prompt: SYSTEM_PROMPT,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
    },
    streamMode: "updates" as const,
    subgraphs: false 
  };

  const stream = createStreamableValue();
  
  (async () => {
    const graph = await createGraph()
    console.log("graph: ", graph)
    //const state = await graph.getState(config);
    //console.log("state: ", state)
    const textStream = await graph.stream(input, config);

    for await (const event of textStream) {
      //console.log("event:", event); 
      for (const [node, values] of Object.entries(event)) {
        console.log(`Receiving update from node: ${node}`);
        console.log(values);
        console.log("\n====\n");
        const { messages, summary, memories } = await handleServerStreamEvent(
          values as StreamEvent,
          ["AnyMessage"],
          "updates"
        );    
        stream.update({ messages });
        stream.update({ summary });
        stream.update({ memories });
      } 
    }

    stream.done();
  })();
  
  return { output: stream.value };
}