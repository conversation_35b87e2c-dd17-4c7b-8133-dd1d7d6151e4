import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { CheerioWebBaseLoader } from "@langchain/community/document_loaders/web/cheerio";
import { tool } from "@langchain/core/tools";
import { z } from 'zod/v3';

export const tavilyTool = new TavilySearchResults();
const ScrapeWebpageSchema = z.object({
  url: z.string().describe("The URL of the webpage to scrape"),
});
export const scrapeWebpage = tool(async (input) => {
  const parsedInput = ScrapeWebpageSchema.safeParse(input);
  if (!parsedInput.success) {
    throw new Error("Invalid input format");
  }
  const { url } = parsedInput.data;
  const loader = new CheerioWebBaseLoader(url);
  const docs = await loader.load();
  const formattedDocs = docs.map(
    (doc) =>
      `<Document name="${doc.metadata?.title}">\n${doc.pageContent}\n</Document>`,
  );
  return formattedDocs.join("\n\n");
},
{
  name: "scrape_webpage",
  description: "Scrape the contents of a webpage.",
  schema: ScrapeWebpageSchema,
}
)