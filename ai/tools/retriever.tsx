import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { createRetrieverTool } from "langchain/tools/retriever";
import { createClient } from "@supabase/supabase-js";
import { z } from 'zod/v3';
import { RetrieverLoading, RetrieverResults } from "@/components/uiCard/retriever";
import { createRunnableUI } from "@/utils/graph-server";
import { tool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";


const client = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_PRIVATE_KEY!,
);
const retriever = new SupabaseHybridSearch(
  new CohereEmbeddings({
    apiKey: process.env.COHERE_API_KEY,
    // @ts-ignore
    model: "embed-multilingual-v3.0", //dimension: 1024
  }),
  {
    client,
    similarityK: 4,
    keywordK: 0,
    tableName: "document_sections_1024",
    similarityQueryName: "match_document_sections_1024",
    keywordQueryName: "kw_match_document_sections",
});

export const RetrieverSchema = z.object({
  query: z.string().describe("query to look up in retriever"),
});

export const retrieverTool = tool(
  async (input, config) => {
    const parsedInput = RetrieverSchema.safeParse(input);
    if (!parsedInput.success) {
      throw new Error("Invalid input format");
    }
    const { query } = parsedInput.data;
    const stream = await createRunnableUI(config, <RetrieverLoading />)
    const docs = await retriever.invoke(query)
    const results = formatDocumentsAsString(docs)
    
    stream.done(<RetrieverResults input={query} results={results} tool="search_my_personal_report" />)

    return JSON.stringify(results, null)
  },
  {
    name: "search_my_personal_report",
    description: "Search and return information about me or my report",
    schema: RetrieverSchema,
  }
)