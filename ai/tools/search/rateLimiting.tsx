"use server";
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";
import { headers } from 'next/headers';
import { config } from '@/components/search/config';

let ratelimit: Ratelimit | undefined;

if (config.useRateLimiting) {
    ratelimit = new Ratelimit({
        redis: Redis.fromEnv(),
        limiter: Ratelimit.slidingWindow(10, "10 m") // 10 requests per 10 minutes
    });
}

export async function checkRateLimit(streamable: any) {
    if (config.useRateLimiting && ratelimit) {
        const identifier = (await headers()).get('x-forwarded-for') || (await headers()).get('x-real-ip') || (await headers()).get('cf-connecting-ip') || (await headers()).get('client-ip') || "";
        const { success } = await ratelimit.limit(identifier);
        streamable.done({ 'status': 'rateLimitReached' });
        return success;
    }
    return true;
}