import { tool } from "@langchain/core/tools";
import { z } from 'zod/v3';

import { createRunnableUI } from "@/utils/graph-server";
import { SearchLoading } from "@/components/uiCard/image";
import { SearchResultsImageSection } from '@/components/uiCard/search-results-image'


export async function images(input: { query?: string }) {
  if (!input.query) {
    throw new Error("Query is required.");
  }
  type UrlParameters = Record<
    string,
    string | number | boolean | undefined | null
  >;

  function buildUrl<P extends UrlParameters>(
    path: string,
    parameters: P,
    baseUrl: string,
  ): string {
    const nonUndefinedParams: [string, string][] = Object.entries(parameters)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => [key, `${value}`]);
    const searchParams = new URLSearchParams(nonUndefinedParams);
    return `${baseUrl}/${path}?${searchParams}`;
  }

  const baseUrl = "https://serpapi.com";
  const response = await fetch(
    buildUrl(
      "search",
      {
        api_key: process.env.SERPAPI_API_KEY,
        q: input.query,
        engine: "google_images",
      },
      baseUrl,
    ),
  );

  const res = await response.json();

  if (res.error) {
    throw new Error(`Got error from serpAPI: ${res.error}`);
  }

  return z
    .object({
      images_results: z.array(
        z.object({
          original: z.string(),
          thumbnail: z.string(),
        }),
      ),
    })
    .parse(res);
}
  
const searchImageSchema = z.object({
  query: z.string().describe("The search query used to search for cats"),
  limit: z.number().describe("The number of pictures shown to the user"),
})
export const imagesTool = tool(
  async (input, config) => {
    const stream = await createRunnableUI(config, <SearchLoading />);
    const parsedInput = searchImageSchema.safeParse(input);
    if (!parsedInput.success) {
      throw new Error("Invalid input format");
    }
    const { query, limit } = parsedInput.data;

    const result = await images({query});
    stream.done(
      <SearchResultsImageSection
        images={result.images_results
          .map((image) => image.thumbnail)
          .slice(0, limit)}
        query={query}
      />,
    );
    const numOfImages = `Returned ${result.images_results.length} images`
    return JSON.stringify(numOfImages, null);    
  },  
  {
    name: "get_search_images",
    description: "A tool to search for images. input should be a search query.",
    schema: searchImageSchema,
  }
);