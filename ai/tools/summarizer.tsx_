import { DynamicStructuredTool } from "langchain/tools";
import { z } from "zod";

import { createRunnableUI } from "@/utils/graph-server";
import { Summarizer, SummarizeLoading } from "@/components/uiCard/summarizer";

  
export const summarizerTool = new DynamicStructuredTool({
  name: "Summarizer",
  description: "A tool to summarize my Enneagram profile. This should only be called if I request for summarize the enneagram report.",
  schema: z.object({
    title: z.string().describe("The title of the input context"),
    summarizeResult: z.string().describe("The summazrized result of the context"),
  }),
  func: async (input, config) => {
    const stream = await createRunnableUI(config, <SummarizeLoading />);
    stream.done(<Summarizer {...input} />)
  
    return JSON.stringify(input, null);
  },
});