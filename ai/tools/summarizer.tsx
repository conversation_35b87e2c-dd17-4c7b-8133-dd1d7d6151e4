import { tool } from "@langchain/core/tools";
import { z } from 'zod/v3';

import { createRunnableUI } from "@/utils/graph-server";
import { Summarizer, SummarizeLoading, SummarizeProps } from "@/components/uiCard/summarizer";

  
export const summarizerTool = tool(
  async (input, config) => {
    const stream = await createRunnableUI(config, <SummarizeLoading />);
    stream.done(<Summarizer {...(input as unknown as SummarizeProps)} />)
    return JSON.stringify(input, null);
  },
  {
    name: "Summarizer",
    description: "A tool to summarize my Enneagram profile. This should only be called if I request for summarize the enneagram report.",
    schema: z.object({
      title: z.string().describe("The title of the input context"),
      summarizeResult: z.string().describe("The summazrized result of the context"),
    }),
  }
);