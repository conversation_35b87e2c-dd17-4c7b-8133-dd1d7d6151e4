import { tool } from "@langchain/core/tools";
import { z } from 'zod/v3';
import { WikipediaClient } from '@agentic/stdlib'

import { createRunnableUI } from "@/utils/graph-server";
import { WikipediaSummary, WikipediaSummaryLoading } from "@/components/uiCard/wikipedia-summary";

const WikipediaSummarySchema = z.object({
  title: z.string().describe("The title of the Wikipedia page"),
  summary: z.string().describe("The summary of the Wikipedia page"),
})
export const wikipediaSummaryTool = tool(
  async (input, config) => {
    const parsedInput = WikipediaSummarySchema.safeParse(input);
    if (!parsedInput.success) {
      throw new Error("Invalid input format");
    }
    const { title, summary } = parsedInput.data;
    const stream = await createRunnableUI(config, <WikipediaSummaryLoading />);
    const wikipediaClient = new WikipediaClient();
    const wikiSummary = await wikipediaClient.getPageSummary({ title });
    const result = {
      title: wikiSummary.displaytitle,
      summary: wikiSummary.extract,
    };
    stream.done(<WikipediaSummary {...result} />);
    return JSON.stringify(result, null);
  },  
  {
    name: "WikipediaSummary",
    description: "A tool to fetch and display a summary from Wikipedia for a given topic.",
    schema: WikipediaSummarySchema,
  }
);
