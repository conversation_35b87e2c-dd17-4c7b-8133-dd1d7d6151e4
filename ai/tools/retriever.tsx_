import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { createRetrieverTool } from "langchain/tools/retriever";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";
import { RetrieverLoading, RetrieverResults } from "@/components/uiCard/retriever";
import { createRunnableUI } from "@/utils/graph-server";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";


const client = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_PRIVATE_KEY!,
  );
  const retriever = new SupabaseHybridSearch(
    new CohereEmbeddings({
      apiKey: process.env.COHERE_API_KEY,
      // @ts-ignore
      model: "embed-multilingual-v3.0", //dimension: 1024
    }),
    {
      client,
      similarityK: 4,
      keywordK: 0,
      tableName: "document_sections_1024",
      similarityQueryName: "match_document_sections_1024",
      keywordQueryName: "kw_match_document_sections",
  });
  /*export const retrieverTool = createRetrieverTool(retriever, {
    name: "search_my_personal_report",
    description: "Search and return information about me or my report"
  });*/

  export const RetrieverSchema = z.object({
    query: z.string().describe("query to look up in retriever"),
  });
  
  export const retrieverTool = new DynamicStructuredTool({
    name: "search_my_personal_report",
    description: "Search and return information about me or my report",
    schema: RetrieverSchema,
    func: async (input, config) => {
      const stream = await createRunnableUI(config, <RetrieverLoading />)
      const docs = await retriever.getRelevantDocuments(input.query)
      const results = formatDocumentsAsString(docs)
      stream.done(<RetrieverResults input={input.query} results={results} tool="search_my_personal_report" />)

      return JSON.stringify(results, null)
    },
  })