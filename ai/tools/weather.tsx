import {
    CurrentWeatherLoading,
    CurrentWeather,
    CurrentWeatherProps
  } from "@/components/uiCard/weather";
  import { createRunnableUI } from "@/utils/graph-server";
  import { tool } from "@langchain/core/tools";
  import { z } from 'zod/v3';
  
  export const weatherSchema = z.object({
    city: z.string().describe("The city name to get weather for"),
    state: z
      .string()
      .describe("The two letter state abbreviation to get weather for"),
    country: z
      .string()
      .optional()
      .default("usa")
      .describe("The two letter country abbreviation to get weather for"),
  });

  // Helper function to validate input
  function validateWeatherInput(input: typeof weatherSchema) {
    const parsedInput = weatherSchema.safeParse(input);
    if (!parsedInput.success) {
      throw new Error(`Invalid input: ${parsedInput.error}`);
    }
    return parsedInput.data; // Return validated and parsed data
  }
  
  export async function weatherData(input: z.infer<typeof weatherSchema>) {
    const geoCodeApiKey = process.env.GEOCODE_API_KEY;
    if (!geoCodeApiKey) {
      throw new Error("Missing GEOCODE_API_KEY secret.");
    }
  
    const geoCodeResponse = await fetch(
      `https://geocode.xyz/${input.city.toLowerCase()},${input.state.toLowerCase()},${input.country.toLowerCase()}?json=1&auth=${geoCodeApiKey}`,
    );
    if (!geoCodeResponse.ok) {
      console.error("No geocode data found.");
      throw new Error("Failed to get geocode data.");
    }
    const geoCodeData = await geoCodeResponse.json();
    const { latt, longt } = geoCodeData;
  
    const openWeatherApiKey = process.env.OPENWEATHER_API_KEY
    if (!openWeatherApiKey) {
      throw new Error("Missing OPENWEATHER_API_KEY secret.");
    }
    const openWeatherResponse = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?lat=${latt}&lon=${longt}&units=metric&appid=${openWeatherApiKey}`,
    );
    if (!openWeatherResponse.ok) {
      console.error("No weather data found.");
      throw new Error("Failed to get weather data.");
    }
    const openWeatherData = await openWeatherResponse.json();
    //console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@openWeatherData@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", openWeatherData)
  
    return {
      ...input,
      ...{
        temperature: openWeatherData.main.temp,
        feels_like: openWeatherData.main.feels_like,
        temp_min: openWeatherData.main.temp_min,
        temp_max: openWeatherData.main.temp_max,
        pressure: openWeatherData.main.pressure,
        humidity: openWeatherData.main.humidity,
        description: openWeatherData.weather[0]?.description,
        timezone: openWeatherData.timezone,
        icon: openWeatherData.weather[0]?.icon,
      },
    };
  }
  
  export const weatherTool = tool(
    async (input, config) => {
      const stream = await createRunnableUI(config, <CurrentWeatherLoading />);
      const validatedInput = validateWeatherInput(input);
      const data = await weatherData(validatedInput);
      stream.done(<CurrentWeather {...(data as CurrentWeatherProps)} />);
      return JSON.stringify(data, null);
    },
    {
      name: "get_weather",
      description:
      "A tool to fetch the current weather, given a city and state. If the city/state is not provided, ask the user for both the city and state.",
      schema: weatherSchema,
    }
  );