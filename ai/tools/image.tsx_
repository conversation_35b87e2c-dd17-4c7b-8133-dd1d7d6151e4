import { DynamicStructuredTool } from "langchain/tools";
import { z } from "zod";

import { createRunnableUI } from "@/utils/graph-server";
import { Images, SearchLoading } from "@/components/uiCard/image";
import { SearchResultsImageSection } from '@/components/uiCard/search-results-image'


export async function images(input: { query: string }) {
    type UrlParameters = Record<
      string,
      string | number | boolean | undefined | null
    >;
  
    function buildUrl<P extends UrlParameters>(
      path: string,
      parameters: P,
      baseUrl: string,
    ): string {
      const nonUndefinedParams: [string, string][] = Object.entries(parameters)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => [key, `${value}`]);
      const searchParams = new URLSearchParams(nonUndefinedParams);
      return `${baseUrl}/${path}?${searchParams}`;
    }
  
    const baseUrl = "https://serpapi.com";
    const response = await fetch(
      buildUrl(
        "search",
        {
          api_key: process.env.SERPAPI_API_KEY,
          q: input.query,
          engine: "google_images",
        },
        baseUrl,
      ),
    );
  
    const res = await response.json();
  
    if (res.error) {
      throw new Error(`Got error from serpAPI: ${res.error}`);
    }
  
    return z
      .object({
        images_results: z.array(
          z.object({
            original: z.string(),
            thumbnail: z.string(),
          }),
        ),
      })
      .parse(res);
  }
  
export const imagesTool = new DynamicStructuredTool({
    name: "get_search_images",
    description: "A tool to search for images. input should be a search query.",
    schema: z.object({
      query: z.string().describe("The search query used to search for cats"),
      limit: z.number().describe("The number of pictures shown to the user"),
    }),
    func: async (input, config) => {
      const stream = await createRunnableUI(config, <SearchLoading />);
  
      const result = await images(input);
      stream.done(
        /*<Images
          images={result.images_results
            .map((image) => image.thumbnail)
            .slice(0, input.limit)}
        />,*/
        <SearchResultsImageSection
          images={result.images_results
            .map((image) => image.thumbnail)
            .slice(0, input.limit)}
          query={input?.query}
        />,
      );
  
      const numOfImages = `Returned ${result.images_results.length} images`
      return JSON.stringify(numOfImages, null);
    },
  });