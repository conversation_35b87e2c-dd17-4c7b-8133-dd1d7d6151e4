"use client";

import { AIMessageText } from "@/components/prebuilt/message";
import { StreamableValue, useStreamableValue } from '@ai-sdk/rsc';

export function AIMessage(props: { value: StreamableValue<string> }) {
  const [data, error, pending] = useStreamableValue(props.value);

  //if (pending) return <div>Thinking...</div>;
  //if (error) return <div>Something went wrong</div>;

  //console.log("AIMessage data: ", data);

  return data ? <AIMessageText content={data}/> : null;
}