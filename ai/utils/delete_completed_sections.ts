"use server"
import prismadb from "@/lib/prismadb";
import { revalidatePath } from "next/cache"

export async function deleteCompletedSections(channel: string, path: string) {
  try {
    await prismadb.$transaction([
      // Delete from CheckpointBlobs table
      prismadb.checkpointBlobs.deleteMany({
        where: {
          channel: channel
        }
      }),
      
      // Delete from CheckpointWrites table
      prismadb.checkpointWrites.deleteMany({
        where: {
          channel: channel
        }
      })
    ]);

    //revalidatePath(path)
    return { success: true };
  } catch (error) {
    console.error('Error deleting checkpoints:', error);
    return { success: false, error };
  }
}