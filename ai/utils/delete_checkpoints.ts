"use server"
import prismadb from "@/lib/prismadb";

export async function deleteCheckpointsByThreadId(threadId: string) {
  try {
    await prismadb.$transaction([
      // Delete from CheckpointWrites table
      prismadb.checkpointWrites.deleteMany({
        where: {
          thread_id: threadId
        }
      }),
      // Delete from CheckpointBlobs table
      prismadb.checkpointBlobs.deleteMany({
        where: {
          thread_id: threadId
        }
      }),
      // Delete from Checkpoints table
      prismadb.checkpoints.deleteMany({
        where: {
          thread_id: threadId
        }
      })
    ]);

    return { success: true };
  } catch (error) {
    console.error('Error deleting checkpoints:', error);
    return { success: false, error };
  }
}