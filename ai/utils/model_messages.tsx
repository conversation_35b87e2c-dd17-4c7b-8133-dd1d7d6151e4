import { BaseMessage, isToolMessage } from "@langchain/core/messages";
export async function getModelMessages(messages: BaseMessage[]) {
  let modelMessages = [];
  for (let i = messages.length - 1; i >= 0; i--) {
    modelMessages.push(messages[i]);
    if (modelMessages.length >= 5) {
      if (!isToolMessage(modelMessages[modelMessages.length - 1])) {
        break;
      }
    }
  }
  return modelMessages.reverse();
}