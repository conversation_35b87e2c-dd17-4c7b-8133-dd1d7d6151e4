"use server"
import { createStreamableValue } from '@ai-sdk/rsc';
import type { ToolCall } from "@/types/langgraph";

type Metadata = {
  langgraph_node?: string;
  [key: string]: any;
}; 
interface EventData {
  run_id: string;
  event: string;
  tags: any[];
  data: any;
  name: string;
  metadata?: Metadata; 
}

interface Message {
  id: string;
  sender: string;
  text?: string;
  isPartial?: boolean;
  toolCalls?: ToolCall[];
}

interface Graph {
  getState: (config: any) => Promise<any>;
  streamEvents: (input: any, config: any) => AsyncIterable<EventData>;
}

interface StreamUpdate {
  messages: Message[];
}

interface StreamableValue<T> {
  update: (value: T) => void;
  done: () => void;
  error: (error: Error) => void;
}


export async function createEventProcessor() {
  // Event handlers
  const eventHandlers: Record<string, (name: string, metadata: Metadata, data: any, stream: StreamableValue<StreamUpdate>, context: { aiMessageId: string | null }) => void> = {
    on_chat_model_stream: (name, metadata, data, stream, context) => {
      if (!context.aiMessageId) {
        context.aiMessageId = crypto.randomUUID();
      }
      
      const message = data.chunk;
      const content = typeof message.content === "string" ? message.content.trim() : "";
      console.log("================================================================= on_chat_model_stream message.content: =================================================================", content);
      // Prevent update for empty content
      if (content !== "") {
        stream.update({
          messages: [{
            id: context.aiMessageId,
            sender: message.getType(),
            text: message.content,
            isPartial: true
          }]
        });
      }
    },
    
    on_chat_model_end: (name, metadata, data, stream) => {
      const message = data?.output;
      if (message?.tool_call_chunks?.length) {
        stream.update({
          messages: [{
            id: message.id,
            sender: message.getType(),
            toolCalls: message.tool_call_chunks.map((call: any) => ({
              id: call.id,
              name: call.name,
              args: call.args,
              result: undefined,
            }))
          }]
        })
      }
    },

    on_chain_end: (name, metadata, data, stream) => {
      const message = data?.output;
      const langgraphNode = metadata?.langgraph_node;
      console.log("================================================================= on_chain_end message: ================================================================", message);
      if (langgraphNode === "write_final_sections") {
        console.log("================================================================= on_chain_end write_final_sections message: ================================================================", message);
      }
      if (message?.reports && message?.reports.length > 0 && langgraphNode === "compile_final_report") {
        const lastReport = message.reports?.at(-1);
        if (lastReport?.final_report) {
          stream.update({
            messages: [{
              id: lastReport.id || crypto.randomUUID(),
              sender: "ai",
              text: lastReport.final_report,
              isPartial: false
            }]
          });          
        }
      }
    },
    
    on_tool_end: (name, metadata, data, stream) => {
      const message = data?.output;
      if (message?.content?.length) {
        stream.update({
          messages: [{
            id: message?.id || crypto.randomUUID(),
            sender: message.getType(),
            toolCalls: [{
              id: message.tool_call_id,
              name: message.name,
              args: "",
              result: message.content,
            }]
          }]
        });
      }
    }
  };

  // Process event stream from a graph and return the stream
  function processGraphEvents(graph: Graph, input: any, config: any): { output: any } {
    const stream = createStreamableValue<StreamUpdate>();
    const context = {
      aiMessageId: null as string | null
    };
    
    // Start processing in background
    (async () => {
      try {
        const state = await graph.getState(config);
        const eventStream = graph.streamEvents(input, config);
        
        for await (const { event, tags, data, name, metadata } of eventStream) {
          console.log("================================================================= event: =================================================================", {event, name, metadata});
          //console.log("================================================================= data: =================================================================", data);
          
          if (eventHandlers[event]) {
            eventHandlers[event](name, metadata, data, stream, context);
          }
        }
        
        stream.done();
      } catch (error) {
        console.error("Error processing event stream:", error);
        stream.error(error instanceof Error ? error : new Error(String(error)));
      }
    })();
    
    return { output: stream.value };
  }

  return {
    processGraphEvents
  };
}