"use server"
import { StreamMode } from "@/components/langgraph/Settings";
import { type CompiledGraph } from "@langchain/langgraph"
import type { Message } from "@/types/langgraph";
import type { ReportData } from "@/ai/deep-research/state";
import {
  type BaseCheckpointSaver,
  type BaseStore,
} from "@langchain/langgraph-checkpoint";
import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";
import { handleServerStreamEvent } from '@/utils/streamHandler';

type ThreadResponse = {
  messages: Message[];
  summary?: string;
  memories?: Record<string, any>[];
  deep_research?: ReportData[] | undefined;
  research_topic?: string | undefined;
  running_summary?: string | undefined;
}
export interface GraphBuilder {
  compile(options: { checkpointer: BaseCheckpointSaver | PostgresSaver; store?: BaseStore }): any;
}

const checkpointer = PostgresSaver.fromConnString(process.env.NEON_DATABASE_URL!);

export async function createGraph(
  builder: any, 
  graphName?: string,
  store?: BaseStore
) {
  if (!builder) return
  await checkpointer.setup();
  const graph = builder.compile({
    checkpointer,
    store,
  });
  if (graphName) {
    graph.name = graphName
    return graph;
  }

  return graph;
}

async function getCheckpointerMessages(threadId: string) {
  if (!threadId) return
  await checkpointer.setup()
  const config = { configurable: { thread_id: threadId } };
  const thread = await checkpointer.get(config);
  //console.log("thread:", JSON.stringify(thread, null, 2));

  return thread;
}

export const getThread = async (
  threadId: string
): Promise<ThreadResponse | null> => {
  const thread = await getCheckpointerMessages(threadId)
  if (thread) {
    const { 
      messages, 
      summary, 
      memories, 
      deep_research,
      research_topic,
      running_summary,
     } = await handleServerStreamEvent(
      thread?.channel_values,
      ["AnyMessage"],
    );
    if (messages) {
      return { messages, summary, memories, deep_research, research_topic, running_summary };
    }    
  }
  return null
};
