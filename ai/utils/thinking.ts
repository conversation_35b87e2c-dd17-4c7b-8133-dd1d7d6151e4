import { AIMessage, type BaseMessage } from "@langchain/core/messages";
import { THINKING_MODELS } from "./models";

type ThinkingAndResponseTokens = {
  thinking: string;
  response: string;
};

// Function to check if a model is a thinking model
export function isThinkingModel(model: string): boolean {
  return THINKING_MODELS.some((m) => m === model);
}

// Function to extract thinking and response content
export function extractThinkingAndResponseTokens(text: string): ThinkingAndResponseTokens {
  const thinkStartTag = "<think>";
  const thinkEndTag = "</think>";
  const startIndex = text.indexOf(thinkStartTag);

  if (startIndex === -1) {
    return { thinking: "", response: text.trim() };
  }

  const afterStartTag = text.substring(startIndex + thinkStartTag.length);
  const endIndex = afterStartTag.indexOf(thinkEndTag);

  if (endIndex === -1) {
    return {
      thinking: afterStartTag.trim(),
      response: text.substring(0, startIndex).trim(),
    };
  }

  const thinking = afterStartTag.substring(0, endIndex).trim();
  const response = (
    text.substring(0, startIndex) +
    afterStartTag.substring(endIndex + thinkEndTag.length)
  ).trim();

  return { thinking, response };
}

// Function to handle model output and update messages
export function handleRewriteArtifactThinkingModel({
  newArtifactContent,
  setMessages,
  thinkingMessageId,
}: {
  newArtifactContent: string;
  setMessages: (fn: (messages: BaseMessage[]) => BaseMessage[]) => void;
  thinkingMessageId: string;
}): string {
  const { thinking, response } = extractThinkingAndResponseTokens(newArtifactContent);

  if (thinking.length > 0) {
    setMessages((prevMessages) => {
      const thinkingMessage = new AIMessage({
        id: thinkingMessageId,
        content: thinking,
      });

      const prevHasThinkingMsg = prevMessages.some(
        (msg) => msg.id === thinkingMessageId
      );

      if (prevHasThinkingMsg) {
        return prevMessages.map((msg) => 
          msg.id === thinkingMessageId ? thinkingMessage : msg
        );
      }

      return [...prevMessages, thinkingMessage];
    });
  }

  return response;
}

export function extractThinkingAndResponses(text: string): ThinkingAndResponseTokens {
  // Handle null or undefined input
  if (!text) {
    return { thinking: "", response: "" };
  }

  const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const thinkMatches = Array.from(text.matchAll(thinkRegex));
  
  // If no thinking tags found, return original text as response
  if (thinkMatches.length === 0) {
    return { thinking: "", response: text.trim() };
  }

  // Collect all thinking content
  const thinkingParts = thinkMatches.map(match => match[1].trim());
  const thinking = thinkingParts.join('\n').trim();

  // Remove all thinking tags and their content to get the response
  const response = text.replace(thinkRegex, '').trim();

  return { thinking, response };
}