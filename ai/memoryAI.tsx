"use server"
import { createA<PERSON> } from '@ai-sdk/rsc';
//import { getThreadState, deleteThread, sendMessage } from '@/ai/memory/chatUpdatesApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/memory/chatMessagesApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/ragAgent/chatUpdatesApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/ragAgent/chatMessagesApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/ragAgent/chatEventsApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/sqlAgent/chatEventsApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/report-maistro/chatEventsApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/enrichment/chatMessagesApi'
//import { getThreadState, deleteThread, sendMessage } from  "@/ai/memory_service/chatUpdatesApi"
import { getThreadState, deleteThread, sendMessage } from '@/ai/deep-research/chatEventsApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/local-research/chatEventsApi'
//import { getThreadState, deleteThread, sendMessage } from '@/ai/swarm-researcher/chatEventsApi'

export const MemoryAI = createAI({
  actions: { 
    getThreadState,
    deleteThread,
    sendMessage
  },
  initialUIState: [],
  initialAIState: { messages: [] },
});