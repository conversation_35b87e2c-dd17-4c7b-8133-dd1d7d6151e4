import 'server-only';

import { createAI, createStreamableValue } from '@ai-sdk/rsc';
import { config } from '@/components/search/config';
import { functionCalling } from '@/components/search/function-calling';
import { getSearchResults, getImages, getVideos } from '@/ai/tools/search/searchProviders';
import { get10BlueLinksContents, processAndVectorizeContent } from '@/ai/tools/search/contentProcessing';
import { setInSemanticCache, clearSemanticCache, initializeSemanticCache, getFromSemanticCache } from '@/ai/tools/search/semanticCache';
import { relevantQuestions } from '@/ai/tools/search/generateRelevantQuestions';
import { streamingChatCompletion } from '@/ai/tools/search/streamingChatCompletion';
import { checkRateLimit } from '@/ai/tools/search/rateLimiting';
import { lookupTool } from '@/ai/tools/search/mentionTools';

export const maxDuration = 30;

async function myAction(userMessage: string, mentionTool: string | null, logo: string | null, file: string): Promise<any> {
  "use server";
  const streamable = createStreamableValue({});

  (async () => {
    await checkRateLimit(streamable);
    await initializeSemanticCache();

    const cachedData = await getFromSemanticCache(userMessage);
    if (cachedData) {
      streamable.update({ cachedData });
      return;
    }

    if (mentionTool) {
      await lookupTool(mentionTool, userMessage, streamable, file);
    }

    // Separate query due to 1 query/s limitation by Brave Search 
    /*const [sources, images] = await Promise.all([
    
      getSearchResults(userMessage), // Brave Search
      getImages(userMessage),
    ]);
    const [videos, conditionalFunctionCallUI] = await Promise.all([
      getVideos(userMessage), // Brave Search
      functionCalling(userMessage),
    ]);*/

    const [images, sources, videos, conditionalFunctionCallUI] = await Promise.all([
      getImages(userMessage),
      getSearchResults(userMessage),
      getVideos(userMessage),
      functionCalling(userMessage),
    ]);

    streamable.update({ searchResults: sources, images, videos });

    if (config.useFunctionCalling) {
      streamable.update({ conditionalFunctionCallUI });
    }

    const html = await get10BlueLinksContents(sources);
    const vectorResults = await processAndVectorizeContent(html, userMessage);
    const accumulatedLLMResponse = await streamingChatCompletion(userMessage, vectorResults, streamable);
    const followUp = await relevantQuestions(sources, userMessage);

    streamable.update({ followUp });

    setInSemanticCache(userMessage, {
      searchResults: sources,
      images,
      videos,
      conditionalFunctionCallUI: config.useFunctionCalling ? conditionalFunctionCallUI : undefined,
      llmResponse: accumulatedLLMResponse,
      followUp,
      semanticCacheKey: userMessage
    });

    streamable.done({ status: 'done' });
  })();

  return streamable.value;
}

const initialAIState: {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  id?: string;
  name?: string;
}[] = [];

const initialUIState: {
  id: number;
  display: React.ReactNode;
}[] = [];

export const SearchAI = createAI({
  actions: {
    myAction,
    clearSemanticCache
  },
  initialUIState,
  initialAIState,
});