'use server'

import { auth } from '@clerk/nextjs/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { type Chat } from '@/ai/morphic/types'
import { getRedisClient, RedisWrapper } from '@/lib/redis/config'

async function getRedis(): Promise<RedisWrapper> {
  return await getRedisClient()
}
const redis = await getRedis()
const CHAT_VERSION = 'v2'
function getUserChatKey(userId: string) {
  return `user:${CHAT_VERSION}:chat:${userId}`
}

export async function getChats(userId?: string | null) {
  if (!userId) {
    return []
  }

  try {
    const chats = await redis.zrange(getUserChatKey(userId), 0, -1, {
      rev: true
    })

    if (chats.length === 0) {
      return []
    }

    const results = await Promise.all(
      chats.map(async chatKey => {
        const chat = await redis.hgetall(chatKey as any)
        return chat
      })
    )

    return results
      .filter((result): result is Record<string, any> => {
        if (result === null || Object.keys(result).length === 0) {
          return false
        }
        return true
      })
      .map(chat => {
        const plainChat = { ...chat }
        if (typeof plainChat.messages === 'string') {
          try {
            plainChat.messages = JSON.parse(plainChat.messages)
          } catch (error) {
            plainChat.messages = []
          }
        }
        if (plainChat.createdAt && !(plainChat.createdAt instanceof Date)) {
          plainChat.createdAt = new Date(plainChat.createdAt)
        }
        return plainChat as Chat
      })
  } catch (error) {
    return []
  }
}

export async function getChat(id: string, userId: string = 'anonymous') {
  const chat = await redis.hgetall<Chat>(`chat:${id}`)
  console.log("chat", chat)
  if (!chat) {
    return null
  }

  // Parse the messages if they're stored as a string
  if (typeof chat.messages === 'string') {
    try {
      chat.messages = JSON.parse(chat.messages)
    } catch (error) {
      chat.messages = []
    }
  }

  // Ensure messages is always an array
  if (!Array.isArray(chat.messages)) {
    chat.messages = []
  }

  return chat
}

export async function removeChat({ 
  id, 
  path 
}: { 
  id: string; 
  path: string 
}) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const uid = await redis.hget<string>(`chat:${id}`, 'userId')

  if (uid !== userId) {
    return {
      error: 'Unauthorized'
    }
  }
  
  await redis.del(`chat:${id}`)
  await redis.zrem(`user:chat:${userId}`, `chat:${id}`)

  revalidatePath(path)
  return revalidatePath(path)
}

export async function clearChats(
  userId: string = 'anonymous'
): Promise<{ error?: string }> {
  const userChatKey = getUserChatKey(userId)
  const chats = await redis.zrange(userChatKey, 0, -1)
  if (!chats.length) {
    return { error: 'No chats to clear' }
  }
  const pipeline = redis.pipeline()

  for (const chat of chats) {
    pipeline.del(chat as any)
    pipeline.zrem(userChatKey, chat)
  }

  await pipeline.exec()

  revalidatePath('/morphic')
  redirect('/morphic')
}

export async function saveChat(chat: Chat, userId: string = 'anonymous') {
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }
  const pipeline = redis.pipeline()
  const chatToSave = {
    ...chat,
    messages: JSON.stringify(chat.messages)
  }
  console.log("chatToSave", chatToSave)

  pipeline.hmset(`chat:${chat.id}`, chatToSave)
  pipeline.zadd(getUserChatKey(userId), Date.now(), `chat:${chat.id}`)
  const results = await pipeline.exec()
  revalidatePath('/morphic')
  return results
  
}

export async function getSharedChat(id: string) {
  const chat = await redis.hgetall<Chat>(`chat:${id}`)

  if (!chat || !chat.sharePath) {
    return null
  }

  return chat
}

export async function shareChat(id: string) {
  const { userId } = await auth()
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }
  const chat = await redis.hgetall<Chat>(`chat:${id}`)

  if (!chat || chat.userId !== userId) {
    return null
  }

  const payload = {
    ...chat,
    sharePath: `/morphic/share/${id}`
  }

  await redis.hmset(`chat:${id}`, payload)

  return payload
}
