import { tool } from 'ai'
import { createStreamableValue } from '@ai-sdk/rsc'
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { createClient } from "@supabase/supabase-js";
import { formatDocumentsAsString } from "langchain/util/document";
import { RetrieverLoading, RetrieverResults } from "@/components/uiCard/retriever";
import { ToolProps } from '.'
import { retrieverSchema } from '../schema/retriever'


export const retrieverTool = ({ uiStream, fullResponse }: ToolProps) => tool({
    description: "Search and return information about me or my report",
    inputSchema: retrieverSchema,
    execute: async ({ query }) => {
      const streamResults = createStreamableValue<string>()
      uiStream.append(<RetrieverLoading />)
  
      try {
        const client = createClient(
          process.env.SUPABASE_URL!,
          process.env.SUPABASE_PRIVATE_KEY!,
        );

        const retriever = new SupabaseHybridSearch(
          new CohereEmbeddings({
            apiKey: process.env.COHERE_API_KEY,
            // @ts-ignore
            model: "embed-multilingual-v3.0", //dimension: 1024
          }),
          {
            client,
            similarityK: 4,
            keywordK: 0,
            tableName: "document_sections_1024",
            similarityQueryName: "match_document_sections_1024",
            keywordQueryName: "kw_match_document_sections",
          }
        );
        const docs = await retriever.getRelevantDocuments(query)
        const results = formatDocumentsAsString(docs)
        
        const toolOutput = {
          query: query,
          results: results
        }
  
        uiStream.update(<RetrieverResults input={query} results={results} tool="search_my_personal_report" />)
        streamResults.done(JSON.stringify(toolOutput))
        
        fullResponse = JSON.stringify(toolOutput)
        return toolOutput
      } catch (error) {
        console.error('Retriever error:', error)
        uiStream.update(null)
        streamResults.done()
        const errorOutput = { error: `An error occurred while searching with "${query}".` }
        fullResponse = JSON.stringify(errorOutput)
        return errorOutput
      }
    }
  })