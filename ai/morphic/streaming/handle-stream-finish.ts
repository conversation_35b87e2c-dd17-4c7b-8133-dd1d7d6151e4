import { auth } from '@clerk/nextjs/server'
import { getChat, saveChat } from '@/ai/morphic/actions/chat'
import { generateRelatedQuestions } from '../agents/generate-related-questions'
import { ExtendedCoreMessage } from '../types'
import { convertToExtendedCoreMessages } from '../utils'
import { ModelMessage, UIMessageStreamWriter, JSONValue, UIMessage } from 'ai'

interface HandleStreamFinishParams {
  responseMessages: ModelMessage[]
  originalMessages: UIMessage[]
  model: string
  chatId: string
  dataStream: UIMessageStreamWriter
  skipRelatedQuestions?: boolean
  annotations?: ExtendedCoreMessage[]
}

export async function handleStreamFinish({
  responseMessages,
  originalMessages,
  model,
  chatId,
  dataStream,
  skipRelatedQuestions = false,
  annotations = []
}: HandleStreamFinishParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      throw new Error('User not authenticated')
    }

    const extendedCoreMessages = convertToExtendedCoreMessages(originalMessages)
    let allAnnotations = [...annotations]

    if (!skipRelatedQuestions) {
      // Notify related questions loading
      const relatedQuestionsAnnotation: JSONValue = {
        type: 'related-questions',
        data: { items: [] }
      }
      dataStream.write({
        'type': 'message-annotations',
        'value': [relatedQuestionsAnnotation]
      })

      // Generate related questions
      const relatedQuestions = await generateRelatedQuestions(
        responseMessages,
        model
      )

      // Create and add related questions annotation
      const updatedRelatedQuestionsAnnotation: ExtendedCoreMessage = {
        role: 'data',
        content: {
          type: 'related-questions',
          data: relatedQuestions.object
        } as JSONValue
      }

      dataStream.write(
        {
          'type': 'message-annotations',
          'value': [updatedRelatedQuestionsAnnotation.content as JSONValue]
        }
      )
      allAnnotations.push(updatedRelatedQuestionsAnnotation)
    }

    // Create the message to save
    const generatedMessages = [
      ...extendedCoreMessages,
      ...responseMessages.slice(0, -1),
      ...allAnnotations, // Add annotations before the last message
      ...responseMessages.slice(-1)
    ] as ExtendedCoreMessage[]

    if (process.env.ENABLE_SAVE_CHAT_HISTORY !== 'true') {
      return
    }

    // Get the chat from the database if it exists, otherwise create a new one
    const savedChat = (await getChat(chatId)) ?? {
      messages: [],
      createdAt: new Date(),
      userId: userId,
      path: `/morphic/search/${chatId}`,
      title: originalMessages[0].content,
      id: chatId
    }

    console.log("generatedMessages: ", generatedMessages)
    // Save chat with complete response and related questions
    await saveChat({
      ...savedChat,
      messages: generatedMessages
    }).catch(error => {
      console.error('Failed to save chat:', error)
      throw new Error('Failed to save chat history')
    })
  } catch (error) {
    console.error('Error in handleStreamFinish:', error)
    throw error
  }
}