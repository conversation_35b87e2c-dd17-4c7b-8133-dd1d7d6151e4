import { UIMessage } from "ai";

export interface ChatHandler {
  messages: UIMessage[];
  input: string;
  isLoading: boolean;
  handleSubmit: (
    e: React.FormEvent<HTMLFormElement>,
    ops?: {
      data?: any;
    },
  ) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  reload?: () => void;
  stop?: () => void;
  onFileUpload?: (file: File) => Promise<void>;
  onFileError?: (errMsg: string) => void;
  setInput?: (input: string) => void;
  append?: (
    message: UIMessage | Omit<UIMessage, "id">,
    ops?: {
      data: any;
    },
  ) => Promise<string | null | undefined>;
}
