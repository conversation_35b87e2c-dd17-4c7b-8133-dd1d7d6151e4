import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "ai";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { MessageAnnotation, MessageAnnotationType } from ".";
import { cn } from "@/lib/utils";
import { IconPlus } from '@/components/ui/icons'
import { ArrowUp } from "lucide-react";
import { LoadingCircle } from '@/components/loaders'
import { Button, buttonVariants } from "@/components/ui/button";
import { Hint } from "@/components/ui/hint";
import FileUploader from "../file-uploader";
import { Input } from "@/components/ui/input";
import UploadCsvPreview from "../upload-csv-preview";
import UploadImagePreview from "../upload-image-preview";
import { ChatHandler } from "./chat.interface";
import { useCsv } from "./hooks/use-csv";

export default function ChatInput(
  props: Pick<
    ChatHandler,
    | "isLoading"
    | "input"
    | "onFileUpload"
    | "onFileError"
    | "handleSubmit"
    | "handleInputChange"
    | "messages"
    | "setInput"
    | "append"
  >,
) {
  const router = useRouter();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const { files: csvFiles, upload, remove, reset } = useCsv();

  const getAnnotations = () => {
    if (!imageUrl && csvFiles.length === 0) return undefined;
    const annotations: MessageAnnotation[] = [];
    if (imageUrl) {
      annotations.push({
        type: MessageAnnotationType.IMAGE,
        data: { url: imageUrl },
      });
    }
    if (csvFiles.length > 0) {
      annotations.push({
        type: MessageAnnotationType.CSV,
        data: {
          csvFiles: csvFiles.map((file) => ({
            id: file.id,
            content: file.content,
            filename: file.filename,
            filesize: file.filesize,
          })),
        },
      });
    }
    return annotations as JSONValue[];
  };

  // default submit function does not handle including annotations in the message
  // so we need to use append function to submit new message with annotations
  const handleSubmitWithAnnotations = (
    e: React.FormEvent<HTMLFormElement>,
    annotations: JSONValue[] | undefined,
  ) => {
    e.preventDefault();
    props.append!({
      content: props.input,
      role: "user",
      createdAt: new Date(),
      annotations,
    });
    props.setInput!("");
  };

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    const annotations = getAnnotations();
    if (annotations) {
      handleSubmitWithAnnotations(e, annotations);
      imageUrl && setImageUrl(null);
      csvFiles.length && reset();
      return;
    }
    props.handleSubmit(e);
  };

  const onRemovePreviewImage = () => setImageUrl(null);

  const readContent = async (file: File): Promise<string> => {
    const content = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      if (file.type.startsWith("image/")) {
        reader.readAsDataURL(file);
      } else {
        reader.readAsText(file);
      }
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
    return content;
  };

  const handleUploadImageFile = async (file: File) => {
    const base64 = await readContent(file);
    setImageUrl(base64);
  };

  const handleUploadCsvFile = async (file: File) => {
    const content = await readContent(file);
    const isSuccess = upload({
      id: uuidv4(),
      content,
      filename: file.name,
      filesize: file.size,
    });
    if (!isSuccess) {
      alert("File already exists in the list.");
    }
  };

  const handleUploadFile = async (file: File) => {
    try {
      if (file.type.startsWith("image/")) {
        return await handleUploadImageFile(file);
      }
      if (file.type === "text/csv") {
        if (csvFiles.length > 0) {
          alert("You can only upload one csv file at a time.");
          return;
        }
        return await handleUploadCsvFile(file);
      }
      props.onFileUpload?.(file);
    } catch (error: any) {
      props.onFileError?.(error.message);
    }
  };

  const handleNewChatButtonClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    const path = '/chatLife/ae5c17ac-af1c-40d9-9b64-f844f5f5588b'
    if (path) {
      router.push(path);
    } else {
      router.push("/");
    }
  };

  return (
    <form
      onSubmit={onSubmit}
      className="p-4 space-y-4 shrink-0 rounded-t-xl border-muted-foreground/10 bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg shadow-inner"
    >
      {imageUrl && (
        <UploadImagePreview url={imageUrl} onRemove={onRemovePreviewImage} />
      )}
      {csvFiles.length > 0 && (
        <div className="flex gap-4 w-full overflow-auto py-2">
          {csvFiles.map((csv) => {
            return (
              <UploadCsvPreview
                key={csv.id}
                csv={csv}
                onRemove={() => remove(csv)}
              />
            );
          })}
        </div>
      )}
      <div className="flex w-full items-start justify-between gap-2">
        <div className="flex flex-col w-[1.4rem] items-center justify-center my-auto">
          <Hint
            label="New Chat"
            side="right"
            align="start"
            sideOffset={18}
          >
            <button
              onClick={handleNewChatButtonClick}
              className={cn(
                buttonVariants({ size: 'newChat', variant: 'secondary' }),
                'inset-y-0 my-auto flex h-7 w-7 items-center justify-center rounded-full bg-secondary px-0]'
              )}
            >
              <IconPlus  />
              <span className="sr-only">New Chat</span>
            </button>
          </Hint>
        </div>
        <Input
          autoFocus
          name="message"
          placeholder="Type a message"
          className="flex-1 placeholder:text-slate-300 ring-inset focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-0 min-h-[60px] lg:min-h-[100px] border-0 text-white bg-transparent"
          value={props.input}
          onChange={props.handleInputChange}
        />
        <FileUploader
          onFileUpload={handleUploadFile}
          onFileError={props.onFileError}
        />
        <Button
          type="submit"
          size="icon"
          disabled={props.isLoading || !props.input.trim()}
          className="h-8 w-8 bg-green-600 hover:bg-green-700 my-auto"
        >
          {props.isLoading ? <LoadingCircle /> :
            <ArrowUp size={18}
              className={cn(
                "",
                props.input.length === 0 ? "text-gray-300" : "text-white",
              )}
            />
          }
          <span className="sr-only">Send message</span>
        </Button>
      </div>
    </form>
  );
}
