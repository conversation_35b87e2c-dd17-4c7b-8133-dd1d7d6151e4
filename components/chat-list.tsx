"use client"

import Image from "next/image";
import { ComponentRef, useEffect, useRef, useState } from "react";
import { type UIMessage } from 'ai'

import { Separator } from '@/components/ui/separator'
import { Companion } from '@prisma/client'
import { ChatMessage } from '@/components/chat-message'
import ImageModal from "@/components/modals/ImageModal";

interface ImageData  {
  type: string;
  content: string;
  url: string;
}

export interface ChatList {
  companion?: Companion
  isLoading?: boolean
  messages: UIMessage[]
  chatId?: string
  imageData?: ImageData[] | undefined
  path?: string
}

export function ChatList({ 
  isLoading, 
  imageData, 
  messages, 
  chatId, 
  path,
  companion }: ChatList) {
  if (!messages.length) {
    return null
  }
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [imageUrls, setImageUrls] = useState<ImageData[]>([]);
  const scrollRef = useRef<ComponentRef<"div">>(null)
  const [fakeLoading, setFakeLoading] = useState(messages.length === 0 ? true : false)
  /*const greetMessage: Message = {
    id: companion.id,
    role: "assistant",
    content: `您好, 我是 ${companion.name}, ${companion.description}`,
  };*/

  useEffect(() => {
    if (imageData && imageData.length > 0) {
      setImageUrls(imageData);
    }
  }, [imageData]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setFakeLoading(false);
    }, 1000);

    return () => {
      clearTimeout(timeout);
    }
  }, []);
  
  useEffect(() => {
    scrollRef?.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages.length])

  return (
    <div className="relative mx-auto max-w-4xl px-4">
      {/*<ChatMessage
        isLoading={fakeLoading}
        src={companion.src}
        message={greetMessage}
      /> */}
      {messages.map((message, index) => (
        <div key={index}>
          <ChatMessage
            isLoading={isLoading}
            src={companion?.src}
            message={message}
            chatId={chatId}
            path={path}
          />
          {index < messages.length - 1 && (
            <Separator className="my-0 md:my-0 bg-transparent" />
          )}
        </div>
      ))}
      {imageUrls && imageUrls.length > 0 && (
        <div className="flex justify-center mx-auto space-x-1">
          {imageUrls.map((data, index) => (
            data?.type === 'image' && (
              <div key={index} className="flex aspect-square relative h-[10rem] w-[10rem] sm:rounded-lg overflow-hidden">
                <ImageModal src={data.url} isOpen={selectedImageIndex === index} onClose={() => setSelectedImageIndex(null)} />
                <Image
                  onClick={() => setSelectedImageIndex(index)}
                  fill
                  src={data.url}
                  alt="Image"
                  height="100"
                  width="100"
                  className="object-cover cursor-pointer hover:scale-110 rounded-lg transition translate"
                />
              </div>
            )
          ))}
        </div>
      )}
      <div ref={scrollRef} />
    </div>
  )
}
