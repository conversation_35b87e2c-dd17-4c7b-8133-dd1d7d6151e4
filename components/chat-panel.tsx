import { type UIMessage } from 'ai'
import { type UseChatHelpers } from '@ai-sdk/react'
import { Button } from '@/components/ui/button'
import { PromptForm } from '@/components/prompt-form'
import { ButtonScrollToBottom } from '@/components/button-scroll-to-bottom'
import { IconRefresh, IconStop } from '@/components/ui/icons'

export interface ChatPanelProps
  extends Pick<
    UseChatHelpers<UIMessage>,
    | 'status'
    | 'regenerate'
    | 'messages'
    | 'stop'
  > {
  id?: string
  path?: string
  sendMessage: (message: UIMessage) => void
  input: string
  setInput: (value: string) => void
}

export function ChatPanel({
  id,
  path,
  status,
  stop,
  sendMessage,
  regenerate,
  input,
  setInput,
  messages
}: ChatPanelProps) {
  return (
    <div className="fixed inset-x-0 bottom-0">
      <ButtonScrollToBottom />
      <div className="mx-auto max-w-2xl 2xl:max-w-4xl sm:px-4">
        <div className="flex h-10 items-center justify-center">
          {status !== 'ready' && status !== 'error' ? (
            <Button
              variant="outline"
              onClick={() => stop()}
              className="bg-secondary"
            >
              <IconStop className="mr-2" />
              Stop generating
            </Button>
          ) : (
            messages?.length > 0 && (
              <Button
                variant="outline"
                onClick={() => regenerate()}
                className="bg-secondary"
              >
                <IconRefresh className="mr-2" />
                Regenerate response
              </Button>
            )
          )}
        </div>
        <div className="w-full space-y-4 bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg px-4 py-2 shadow-lg sm:rounded-t-xl sm:border md:py-4">
          <PromptForm
            onSubmit={async value => {
              sendMessage({
                id,
                parts: [{ type: 'text', text: value }],
                role: 'user'
              })
            }}
            input={input}
            setInput={setInput}
            isLoading={status !== 'ready' && status !== 'error'}
            path={path}
          />
        </div>
      </div>
    </div>
  )
}
