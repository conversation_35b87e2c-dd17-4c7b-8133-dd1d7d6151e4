import { UseChatHelpers } from '@ai-sdk/react'

import { Button } from '@/components/ui/button'
import { ExternalLink } from '@/components/external-link'
import { IconArrowRight } from '@/components/ui/icons'

const exampleMessages = [
  {
    heading: '酪梨椅子',
    message: `酪梨椅子`
  },
  {
    heading: '超能力發展',
    message: '超能力發展 \n'
  }
]

export function EmptyImageScreen({ setInput }: { setInput: (value: string) => void }) {
  return (
    <div className="mx-auto max-w-4xl px-4 pt-1">
      <div className="rounded-lg border bg-background p-8">
        <h1 className="mb-2 text-lg font-semibold">
          歡迎來到圖畫日記空間!
        </h1>
        <p className="leading-normal text-muted-foreground">
          請選擇一個主題或輸入圖像描述開始您的圖畫日記：
        </p>
        <div className="mt-4 flex flex-col items-start space-y-2">
          {exampleMessages.map((message, index) => (
            <Button
              key={index}
              variant="link"
              className="h-auto p-0 text-base"
              onClick={() => setInput(message.message)}
            >
              <IconArrowRight className="mr-2 text-muted-foreground" />
              {message.heading}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}