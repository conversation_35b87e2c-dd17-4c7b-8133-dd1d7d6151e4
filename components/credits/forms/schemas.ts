import { z } from 'zod/v3';
import { CreditBalance } from '@prisma/client'
import { API_MULTIPLIER } from '@/constants'
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { differenceInDays, isValid } from "date-fns";

export const formSchemaID = z.object({
  id: z.number(),
})

export const formSchemaQuestion = z.object({
  question: z.string(),
  answer: z.string(),
  explanation: z.string().optional(),
})

export const formSchemaCreateChapter = z.object({
  title: z.string(),
  content: z.string(),
  questions: z.array(formSchemaQuestion),
})

export const formSchemaCreateCourse = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  published: z.boolean(),
  image: z.any().optional(),
  chapters: z.array(formSchemaCreateChapter),
})

export const schemaPayment = z.object({
  userId: z.string(),
  creditsCount: z.coerce.number().min(1),
})

export const schemaPaymentId = z.string();

export const schemaDoubt = z.object({
  chapterId: z.number(),
  doubt: z.string(),
})

export const schemaSubmitTest = z.object({
  testId: z.number(),
  answers: z.array(
    z.object({
      id: z.number(),
      question: z.string(),
      userAnswer: z.string().optional().nullable(),
    }),
  ),
})

export const OverviewQuerySchema = z
  .object({
    from: z.coerce.date(),
    to: z.coerce.date(),
  })
  .refine((args) => {
    const { from, to } = args;
    const days = differenceInDays(to, from);

    const isValidRange = days >= 0 && days <= MAX_DATE_RANGE_DAYS;
    return isValidRange;
  });
  
  const amountNotExceedCreditBalance = (creditBalance?: CreditBalance) =>
    z.custom<number>((data): data is number => {
      const value = data as number;
      if (!creditBalance || creditBalance.apiBalance < value * API_MULTIPLIER) {
        throw new Error('Amount exceeds available credit balance');
      }
      return true;
    });
  
  // Define formDataSchema with custom validation
  export const formDataSchema = (creditBalance?: CreditBalance) =>
    z.object({
      amount: amountNotExceedCreditBalance(creditBalance)
        .and(z.number().min(1, { message: 'Amount must be greater than 1' })),
      notes: z.string().optional(),
    });
