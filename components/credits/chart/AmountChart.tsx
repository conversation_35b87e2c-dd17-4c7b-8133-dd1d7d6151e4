import React, { useMemo, useCallback } from 'react'
import { GetFormatterForCurrency } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { Separator } from "@/components/ui/separator";
import { Transaction } from '@/app/api/credits/history-data/route'

type TransactionChartProps = {
  transactions: Transaction[];
  currency: string;
}

export function AmountChart({
  transactions,
  currency,
}: TransactionChartProps ) {
  const formatter = useMemo(() => {
    return GetFormatterForCurrency(currency);
  }, [currency]);
  return (
    <div className='w-full rounded-lg p-2'>
      <h2 className='font-bold text-xs'>Spend</h2>
      <ResponsiveContainer width='100%' height={120}>
        <BarChart
          height={120}
          data={transactions}
          barCategoryGap={2}
          margin={{
            top:7,
            left:0,
          }}
        >
          <defs>
            <linearGradient id="amountPurchaseBar" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset={"0"}
                stopColor="#10b981"
                stopOpacity={"1"}
              />
              <stop
                offset={"1"}
                stopColor="#10b981"
                stopOpacity={"0"}
              />
            </linearGradient>

            <linearGradient id="amountExpenseBar" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset={"0"}
                stopColor="#ef4444"
                stopOpacity={"1"}
              />
              <stop
                offset={"1"}
                stopColor="#ef4444"
                stopOpacity={"0"}
              />
            </linearGradient>
          </defs>
          <CartesianGrid
            strokeDasharray="5 5"
            strokeOpacity={"0.2"}
            vertical={false}
          />
          <XAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            padding={{ left: 2, right: 2 }}
            dataKey="date"
          />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Bar
            dataKey={"amountPurchase"}
            label="Credits purchase"
            stackId="a"
            fill="url(#amountPurchaseBar)"
            radius={4}
            className="cursor-pointer"
          />
          <Bar
            dataKey={"amountExpense"}
            label="Credits expense"
            stackId="a"
            fill="url(#amountExpenseBar)"
            radius={4}
            className="cursor-pointer"
          />
          <Tooltip
            cursor={{ opacity: 0.1 }}
            content={(props) => (
              <CustomTooltip formatter={formatter} {...props} />
            )}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

function CustomTooltip({ active, payload, formatter }: any) {
  if (!active || !payload || payload.length === 0) return null;

  const date = payload[0].payload.date;
  const data = payload[0].payload;
  const { amountPurchase, amountExpense } = data;

  return (
    <div className="min-w-[300px] rounded border bg-background p-2">
      <div className="text-xs p-1 bg-muted text-muted-foreground">
        {date}
      </div>
      <Separator />
      <TooltipRow
        formatter={formatter}
        label="Credits purchase"
        value={amountPurchase}
        bgColor="bg-emerald-500"
        textColor="text-emerald-500"
      />
      <TooltipRow
        formatter={formatter}
        label="Credits expense"
        value={amountExpense}
        bgColor="bg-red-500"
        textColor="text-red-500"
      />
    </div>
  );
}

function TooltipRow({
  label,
  value,
  bgColor,
  textColor,
  formatter,
}: {
  label: string;
  textColor: string;
  bgColor: string;
  value: number;
  formatter: Intl.NumberFormat;
}) {
  const formattingFn = useCallback(
    (value: number) => {
      return formatter.format(value);
    },
    [formatter]
  );

  return (
    <div className="flex items-center gap-2">
      <div className={cn("h-2 w-2 rounded-full", bgColor)} />
      <div className="flex w-full justify-between">
        <p className="text-xs text-muted-foreground">{label}</p>
        <div className={cn("text-xs font-bold", textColor)}>
          ${value.toFixed(4)}
        </div>
      </div>
    </div>
  );
}