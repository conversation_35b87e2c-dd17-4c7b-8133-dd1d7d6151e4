import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Sector, ResponsiveContainer } from 'recharts';
import { Total } from '@/app/api/credits/history-data/route';
import '../styles.css';

type UsageChartProps = {
  totals: Total;
};

const renderActiveShape = (props: any) => {
  const RADIAN = Math.PI / 180;
  const {
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    fill,
    payload,
    percent,
    value
  } = props;
  const sin = Math.sin(-RADIAN * midAngle);
  const cos = Math.cos(-RADIAN * midAngle);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 12) * cos; // the line length for indicator
  const my = cy + (outerRadius + 12) * sin; // the line length for indicator
  const ex = mx + (cos >= 0 ? 1 : -1) * 8; // the line length for indicator
  const ey = my;
  const textAnchor = cos >= 0 ? "start" : "end";

  return (
    <g>
      <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill} className='text-sm'>
        {payload.name}
      </text>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill="#808080"
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 6}
        outerRadius={outerRadius + 10}
        fill={fill}
      />
      <path
        d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
        stroke={fill}
        fill="none"
      />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 12}
        y={ey}
        textAnchor={textAnchor}
        fill="#333"
        className='text-xs'
      >{`PV ${(value).toFixed(2)}`}</text>
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 6}
        y={ey}
        dy={18}
        textAnchor={textAnchor}
        fill="#999"
        className='text-xs'
      >
        {`(${(percent * 100).toFixed(2)}%)`}
      </text>
    </g>
  );
}


export function UsagePieChart({
  totals,
}: UsageChartProps ) {
    
  const [activeIndex, setActiveIndex] = useState(1);
  const onPieEnter = useCallback(
    (_: unknown, index: number) => {
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  console.log("totals: ", totals)
  console.log(totals)

  const apiData = [
    { name: 'Income', value: totals.totalApiPurchase },
    { name: 'Expense', value: totals.totalApiExpense },
  ];

  const amountData = [
    { name: 'Income', value: totals.totalAmountPurchase },
    { name: 'Expense', value: totals.totalAmountExpense },
  ];

  console.log("apiData: ", apiData)
  console.log("amountData: ", amountData)

  return (
    <div className='rounded-lg p-2'>
      {totals && <h2 className='font-bold text-xs'>Project's Usage</h2>}
      <div className="grid sm:grid-cols-2">
        <ResponsiveContainer width="100%" height={240} minWidth={350}>
          <PieChart height={240}>
            <Pie
              activeIndex={activeIndex}
              activeShape={renderActiveShape}
              data={apiData}
              cx="50%"
              cy="50%"
              innerRadius={55}
              outerRadius={80}
              fill="#6366f1"
              dataKey="value"
              onMouseEnter={onPieEnter}
            />
          </PieChart>
        </ResponsiveContainer>
        <ResponsiveContainer width="100%" height={240} minWidth={350}>
          <PieChart height={240}>
            <Pie
              activeIndex={activeIndex}
              activeShape={renderActiveShape}
              data={amountData}
              cx="50%"
              cy="50%"
              innerRadius={55}
              outerRadius={80}
              fill="#34D7AE"
              dataKey="value"
              onMouseEnter={onPieEnter}
            />
          </PieChart>
        </ResponsiveContainer>        
      </div>
    </div>
  );
}