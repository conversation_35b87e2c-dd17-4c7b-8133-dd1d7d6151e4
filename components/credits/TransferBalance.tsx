'use client'

import React, { useState, ChangeEvent, FormEvent } from 'react';
import { CreditBalance } from '@prisma/client'
import { z } from 'zod/v3';
import { formDataSchema } from './forms/schemas';
import { useRouter } from "next/navigation";
import { transferBalance } from '@/app/actions/creditsActions';
import { Member } from "@prisma/client";
import { SelectUsers } from './SelectUsers';
import { Button } from '@/components/ui/button';
import { SimpleDialog } from '@/components/ui/simple-dialog';
import { Label } from '@/components/ui/simple-label';
import { Input } from '@/components/ui/input';
import { IconSpinner } from '@/components/ui/icons';
import { CalendarDays, ChevronsDown, Users } from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuGroup,
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"
import { toast } from "sonner";
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { API_MULTIPLIER, UNIT_AMOUNT } from '@/constants'

const userIdsSchema = z.array(z.string()).nonempty({ message: "At least one user must be selected" });

type TransferBalanceProps = {
  organizations: OrganizationWithMembersAndUsers[] | null;
  creditBalance?: CreditBalance;
}

export const TransferBalance = ({ organizations, creditBalance }: TransferBalanceProps) => {
  const router = useRouter();
  const [isPending, setIsPending] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);

  // Generate formDataSchema with the provided credit balance
  const schema = formDataSchema(creditBalance);

  // Define the type based on the schema
  type FormData = z.infer<typeof schema> & { amount: number }

  const [formData, setFormData] = useState<FormData>({
    amount: 1,
    notes: '',
  });
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationWithMembersAndUsers | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<Member[]>([]);
  const path = '/settings/credits'

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const parsedValue = name === 'amount' && value !== '' ? parseFloat(value) : value;
  
    setFormData({
      ...formData,
      [name]: parsedValue, // Store the parsed value in the state
    });
  };

  const handleUserSelect = (selectedUsers: Member[]) => {
    setSelectedUsers(selectedUsers);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!selectedOrganization) {
      toast.error(`Please select an organization before submitting.`);
      return;
    }
  
    try {
      // Validate form data
      const validationResult = schema.safeParse(formData);
      if (!validationResult.success) {
        toast.error(validationResult.error.errors.map((err) => err.message).join(', '));
        return;
      }
    } catch (error: any) {
      toast.error(error.message || 'Validation failed');
      return;
    }
  
    // Validate user IDs
    const toUserIds = selectedUsers.map((user) => user.id);
    const userIdsValidationResult = userIdsSchema.safeParse(toUserIds);
    if (!userIdsValidationResult.success) {
      toast.error(userIdsValidationResult.error.errors.map(err => err.message).join(", "));
      return;
    }

    setIsPending(true);
    try {
      if (formData.amount <= 0) {
        toast.error("Amount must be greater than 0.");
        return;
      }
      await transferBalance(formData, toUserIds, path);
      router.push('/settings/credits');
    } catch (error: any) {
      toast.error(error.message || 'Failed to transfer balance');
    } finally {
      setIsPending(false);
      setOpenDialog(false);
      router.refresh()
    }
  };

  return (
    <div>
      <Button onClick={() => setOpenDialog(true)}>Transfer Balance</Button>
      <SimpleDialog open={openDialog} setOpen={setOpenDialog} title="Transfer Balance">
        <DropdownMenu>
          <DropdownMenuTrigger
            className="w-[200px] inline-flex items-center justify-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/80 h-9 rounded-md px-3 mx-auto"
          >
            {selectedOrganization ? selectedOrganization?.name : "Organizations"}
            <ChevronsDown className="h-4 w-4 ml-auto"/>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 text-md">
            {organizations?.map((organization, index) => (
              <DropdownMenuItem key={index} onClick={() => {
                setSelectedOrganization(organization)
                }}
              >
                <Users className="mr-2 h-4 w-4" />
                <span>{organization.name}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        <form onSubmit={handleSubmit}>
          {/* Select users to transfer balance to */}
          {selectedOrganization && selectedOrganization?.members && 
            <SelectUsers 
              users={selectedOrganization?.members!} 
              onSelect={handleUserSelect} 
              selectedUsers={selectedUsers}
              setSelectedUsers={setSelectedUsers}
            />
          }
          <HoverCard>
            <HoverCardTrigger asChild>
              <Button variant="link">@Price info</Button>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="flex justify-between space-x-4">
                <div className="space-y-1">
                  <h4 className="text-sm font-semibold">Notes</h4>
                  <p className="text-sm">
                    1 unit amount equivalent to {API_MULTIPLIER} times of API access.
                  </p>
                  <p className="text-sm">
                    @ 1 unit = {API_MULTIPLIER} times of API access.
                  </p>
                  <p className="text-sm">  
                    @ 2 units = {2*API_MULTIPLIER} times of API access.
                  </p>
                  <div className="flex items-center pt-2">
                    <CalendarDays className="mr-2 h-4 w-4 opacity-70" />{" "}
                    <span className="text-xs text-muted-foreground">
                      May 25 2024
                    </span>
                  </div>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
          {/* Amount input */}
          <Label title="Total number of units to transfer)">
            <Input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              required
            />
          </Label>
          {/* Notes input */}
          <Label title="Notes">
            <Input
              type="text"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
            />
          </Label>
          {/* Submit button */}
          <Button type="submit" disabled={isPending}>
            {isPending ? <IconSpinner /> : 'Transfer Balance'}
          </Button>
        </form>
      </SimpleDialog>
    </div>
  );
};