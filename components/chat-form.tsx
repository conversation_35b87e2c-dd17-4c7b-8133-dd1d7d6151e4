"use client";

import { ChatRequestOptions } from "ai";
import { Send } from "lucide-react";
import { ChangeEvent, FormEvent } from "react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface ChatFormProps {
  input: string;
  handleInputChangeAction: (e: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>) => void;
  onSubmitAction: (e: FormEvent<HTMLFormElement>, chatRequestOptions?: ChatRequestOptions | undefined) => void;
  isLoading: boolean;
}

export const ChatForm = ({
  input,
  handleInputChangeAction,
  onSubmitAction,
  isLoading,
}: ChatFormProps) => {
  return (
    <form onSubmit={onSubmitAction} className="border-t border-primary/10 py-4 flex items-center gap-x-1">
      <Input
        disabled={isLoading}
        value={input}
        onChange={handleInputChangeAction}
        placeholder="Type a message"
        className="rounded-lg bg-primary/10"
      />
      <Button disabled={isLoading} variant="default">
        <Send className="w-6 h-6" />
      </Button>
    </form>
  )
}