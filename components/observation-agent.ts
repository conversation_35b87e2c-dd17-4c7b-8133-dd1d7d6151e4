'use server';

import type { UIMessage } from 'ai';
import type { DocumentInterface } from "@langchain/core/documents";
import { revalidatePath } from 'next/cache';
import { ChatGroq } from "@langchain/groq";
import { CohereEmbeddings } from "@langchain/cohere";
import { createClient } from "@supabase/supabase-js";
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
//import { TimeWeightedVectorStoreRetriever } from "langchain/retrievers/time_weighted";
import {
  TimeWeightedVectorStoreRetriever,
  GenerativeAgentMemory,
  GenerativeAgent,
} from "@/lib/generative_agents";
import { saveObservationToDatabase } from '@/lib/databaseUtils';

type ObservationAgentProps = {
  sessionId?: string;
  userId: string;
  companionId: string;
  messages: (string | Omit<UIMessage, 'id'>)[];
  name: string;
  age: number;
  traits: string;
  status: string;
  reflectionThreshold: number;
  path?: string;
};

// Define a higher-scope variable to retain the user agents and memories
const userAgentsMap: { [key: string]: GenerativeAgent } = {};
const userMemoriesMap: { [key: string]: GenerativeAgentMemory } = {};

export const ObservationAgent = async ({
  sessionId,
  userId,
  companionId,
  messages,
  name,
  age,
  traits,
  status,
  reflectionThreshold,
  path,
}: ObservationAgentProps) => {
  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
  });

  const userAgentKey = `${userId}${companionId}`;

  // Check for existing memory
  let userGenerativeAgentMemory = userMemoriesMap[userAgentKey];

  const filterMetadata = sessionId
  ? { session_id: sessionId, owner_id: userId }
  : { owner_id: userId };

  const client = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_PRIVATE_KEY!,
  );

  const embeddings = new CohereEmbeddings({
    apiKey: process.env.COHERE_API_KEY,
    model: "embed-multilingual-light-v3.0",
  });

  const createNewMemoryRetriever = async (memoryStream: DocumentInterface[] = []) => {
    const vectorStore = new SupabaseVectorStore(embeddings, {
      client,
      tableName: "observations_cohere",
      queryName: "match_observations_cohere",
      filter: filterMetadata
    });
  
    const retriever = new TimeWeightedVectorStoreRetriever({
      vectorStore,
      searchKwargs: 4,
      decayRate: 0.1,
      otherScoreKeys: ["importance"],
      k: 15,
      //memoryStream,
    });
  
    return retriever;
  };
  

  // Only create new memory if not already existing
  if (!userGenerativeAgentMemory) {
    // Initialize memoryStream if not existing, otherwise load from an existing memory
    const memoryStream: DocumentInterface[] = []
    console.log("$$$$$$$$$$$$$$$$$$$$$$$memoryStream: $$$$$$$$$$$$$$$$$$$$$$$$", memoryStream)

    userGenerativeAgentMemory = new GenerativeAgentMemory(
      llm,
      await createNewMemoryRetriever(memoryStream),
      {
        reflectionThreshold,
        filterMetadata,
      }
    );
    userMemoriesMap[userAgentKey] = userGenerativeAgentMemory; // Store in map
  }

  // Check for existing agent
  let userGenerativeAgent = userAgentsMap[userAgentKey];

  // Only create new agent if not already existing
  if (!userGenerativeAgent) {
    userGenerativeAgent = new GenerativeAgent(
      llm,
      userGenerativeAgentMemory,
      {
        name,
        age,
        traits,
        status,
        verbose: true,
        filterMetadata
      }
    );

    // Store newly created agent in the userAgentsMap
    userAgentsMap[userAgentKey] = userGenerativeAgent;
  }

  console.log("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% ObservationList %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%", messages);
  
  if (messages?.length > 0) {
    const lastMessage = messages.slice(-1)[0];
    try {
      if (typeof lastMessage === 'string') {
        await userGenerativeAgent.addMemory(lastMessage, new Date(), filterMetadata);
      } else if ('parts' in lastMessage) {
        const messagePart = lastMessage?.parts[0];
        if ('content' in messagePart) {
          if (typeof messagePart.content === 'string') {
            await userGenerativeAgent.addMemory(messagePart.content, new Date(), filterMetadata);
          }
        }
      }
      console.log("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% add memory %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
      
      const observationSummary = await userGenerativeAgent.getSummary({ forceRefresh: true });
      console.log("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% ObservationSummary %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%", observationSummary);
      
      if (path) {
        revalidatePath(path);
      }
      
      return observationSummary;

    } catch (error) {
      console.error("Error while adding memory or getting summary:", error);
      return null; // Or handle the error as needed
    }

  } else {
    return null;
  }
};


type InterviewAgentProps = {
  sessionId: string;
  userId: string;
  companionId: string;
  message: string,
  name: string;
  age: number;
  traits: string;
  status: string;
  reflectionThreshold: number;
  path?: string;
}

export const InterviewAgent = async ({
  sessionId,
  userId,
  companionId,
  message,
  name,
  age,
  traits,
  status,
  reflectionThreshold,
  path,
}: InterviewAgentProps): Promise<string> => {
  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
  })

  const userAgentKey = `${userId}${companionId}`;

  // Check for existing memory
  let userGenerativeAgentMemory = userMemoriesMap[userAgentKey];

  const filterMetadata = sessionId
  ? { session_id: sessionId, owner_id: userId }
  : { owner_id: userId };

  const client = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_PRIVATE_KEY!,
  );

  const embeddings = new CohereEmbeddings({
    apiKey: process.env.COHERE_API_KEY,
    model: "embed-multilingual-light-v3.0",
  });

  const createNewMemoryRetriever = async (memoryStream: DocumentInterface[] = []) => {
    const vectorStore = new SupabaseVectorStore(embeddings, {
      client,
      tableName: "observations_cohere",
      queryName: "match_observations_cohere",
      filter: filterMetadata
    });
  
    const retriever = new TimeWeightedVectorStoreRetriever({
      vectorStore,
      searchKwargs: 4,
      decayRate: 0.1,
      otherScoreKeys: ["importance"],
      k: 15,
      //memoryStream,
    });
  
    return retriever;
  };
  

  // Only create new memory if not already existing
  if (!userGenerativeAgentMemory) {
    // Initialize memoryStream if not existing, otherwise load from an existing memory
    const memoryStream: DocumentInterface[] = []
    
    userGenerativeAgentMemory = new GenerativeAgentMemory(
      llm,
      await createNewMemoryRetriever(memoryStream),
      {
        reflectionThreshold,
        filterMetadata,
        verbose: true,
      }
    );
    userMemoriesMap[userAgentKey] = userGenerativeAgentMemory; // Store in map
  }

  // Check for existing agent
  let userGenerativeAgent = userAgentsMap[userAgentKey];

  // Only create new agent if not already existing
  if (!userGenerativeAgent) {
    userGenerativeAgent = new GenerativeAgent(
      llm,
      userGenerativeAgentMemory,
      {
        name,
        age,
        traits,
        status,
        verbose: true,
        filterMetadata,
      }
    );

    // Store newly created agent in the userAgentsMap
    userAgentsMap[userAgentKey] = userGenerativeAgent;
  }

  // Simple wrapper helping the user interact with the agent
  const newMessage = `${name} says ${message}`;
  const response = await userGenerativeAgent.generateDialogueResponse(newMessage);
  return response[1];
  
};