import { useState, useRef } from 'react';
import { useRouter } from "next/navigation";
import Textarea from "react-textarea-autosize";
import clsx from "clsx";
import { cn } from '@/lib/utils'
import { LoadingCircle } from '@/components/loaders'
import { IconPlus } from '@/components/ui/icons'
import { ArrowU<PERSON>, Paperclip, SendHorizontal } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { Button, buttonVariants } from '@/components/ui/button'

interface TalkFormProps {
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  disabled: boolean;
  path: string;
  formWidth?: string
}
  
export function TalkForm({ 
  handleSubmit, 
  input, 
  setInput, 
  isLoading, 
  disabled, 
  path,
  formWidth
}: TalkFormProps) {
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();

  let widthValue = '100%'
  if (formWidth) {
    widthValue = formWidth
  }


  const handleNewChatButtonClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    if (path) {
      router.refresh();
      router.push(path);
    } else {
      router.push("/");
    }
  };

  return (
    <>
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className={`relative w-[${widthValue}] max-w-screen-md sm:rounded-t-xl rounded-2xl bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg px-4 pb-2 pt-3 shadow-lg sm:pb-3 sm:pt-4`}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={handleNewChatButtonClick}
              className={cn(
                buttonVariants({ size: 'sm', variant: 'outline' }),
                'absolute left-[0.8rem] top-4 h-8 w-8 rounded-full bg-secondary p-0 sm:left-[0.8rem]'
              )}
            >
              <IconPlus  />
              <span className="sr-only">New Chat</span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="right">New Chat</TooltipContent>
        </Tooltip>
        <Textarea
          ref={inputRef}
          tabIndex={0}
          required
          rows={1}
          autoFocus
          placeholder="Send a message..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              formRef.current?.requestSubmit();
              e.preventDefault();
            }
          }}
          spellCheck={false}
          className="min-h-[30px] w-full pl-[2.5rem] pr-[3.3rem] resize-none bg-transparent text-white focus-within:outline-none"
        />
        <button
          className={clsx(
            "absolute inset-y-0 right-4 my-auto flex h-10 w-12 items-center justify-center rounded-md transition-all",
            disabled
              ? "cursor-not-allowed bg-indigo-600"
              : "bg-green-600 hover:bg-green-700",
          )}
          disabled={disabled}
        >
          {isLoading ? (
            <LoadingCircle />
          ) : (
            <SendHorizontal
              className={clsx(
                "h-6 w-6",
                input.length === 0 ? "text-gray-300" : "text-white",
              )}
            />
          )}
        </button>
      </form>
      <p className="text-center text-xs text-gray-400"></p>
    </>
  );
}
