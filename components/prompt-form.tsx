import { type UIMessage } from 'ai'
import { UseChatHelpers } from '@ai-sdk/react'
import * as React from 'react'
import Textarea from 'react-textarea-autosize'

import { Button, buttonVariants } from '@/components/ui/button'
import { IconArrowElbow, IconPlus } from '@/components/ui/icons'
import { LoadingCircle } from '@/components/loaders'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { useEnterSubmit } from '@/lib/hooks/use-enter-submit'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'


export interface PromptProps
{
  onSubmit: (value: string) => Promise<void>
  isLoading: boolean
  path?: string
  input: string
  setInput: (value: string) => void
}

export function PromptForm({
  onSubmit,
  input,
  setInput,
  isLoading,
  path
}: PromptProps) {
  const { formRef, onKeyDown } = useEnterSubmit()
  const inputRef = React.useRef<HTMLTextAreaElement>(null)
  const router = useRouter()

  React.useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <form
      onSubmit={async e => {
        e.preventDefault()
        if (!input?.trim()) {
          return
        }
        setInput('')
        await onSubmit(input)
      }}
      ref={formRef}
    >
      <div className="relative flex max-h-60 w-full grow flex-col overflow-hidden bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg shadow-inner px-8 sm:rounded-md sm:border sm:px-12">
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={e => {
                e.preventDefault()
                router.refresh()              
                if (path) {
                  router.push(path)
                } else {
                  router.back()
                }
              }}
              className={cn(
                buttonVariants({ size: 'sm', variant: 'outline' }),
                'absolute left-0 top-4 h-8 w-8 rounded-full bg-secondary p-0 sm:left-4'
              )}
            >
              <IconPlus  />
              <span className="sr-only">New Chat</span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="right">New Chat</TooltipContent>
        </Tooltip>
        <Textarea
          ref={inputRef}
          tabIndex={0}
          onKeyDown={onKeyDown}
          rows={1}
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder="Send a message."
          spellCheck={false}
          className="min-h-[60px] w-full resize-none bg-transparent px-4 py-[1.3rem] text-white focus-within:outline-none sm:text-sm"
        />
        <div className="absolute right-0 top-4 sm:right-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || input === ''}
                className="h-8 w-8 bg-green-600 hover:bg-green-700"
              >
                {isLoading ? <LoadingCircle /> :
                  <IconArrowElbow />
                }
                <span className="sr-only">Send message</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">Send message</TooltipContent>
          </Tooltip>
        </div>
      </div>
    </form>
  )
}
