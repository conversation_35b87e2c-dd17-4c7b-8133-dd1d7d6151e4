"use client"

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useModelSelectorContext } from "@/lib/model-selector-provider";

export function ModelSelector() {
  const { model, setModel } = useModelSelectorContext();

  return (
    <div className="fixed top-0 left-[50%] z-50">
      <Select value={model} onValueChange={v => setModel(v)}>
        <SelectTrigger className="w-fit h-[2rem] border-0 gap-x-1 focus-visible:ring-0 text-white">
          <SelectValue placeholder="Theme" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="openai">OpenAI</SelectItem>
          <SelectItem value="anthropic">Anthropic</SelectItem>
          <SelectItem value="google_genai">Google Gemini</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}