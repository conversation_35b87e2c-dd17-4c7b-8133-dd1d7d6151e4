import { SlideModel } from "@/lib/types";
import { ActionButton } from "./ActionButton";
import { Trash2 } from "lucide-react";

interface DeleteSlideButtonProps {
  currentSlideIndex: number;
  setCurrentSlideIndex: (fn: (i: number) => number) => void;
  slides: SlideModel[];
  setSlides: (fn: (slides: SlideModel[]) => SlideModel[]) => void;
}

export function DeleteSlideButton({
  currentSlideIndex,
  setCurrentSlideIndex,
  slides,
  setSlides,
}: DeleteSlideButtonProps) {
  return (
    <ActionButton
      disabled={slides.length == 1}
      onClick={() => {
        // delete the current slide
        setSlides((slides) => [
          ...slides.slice(0, currentSlideIndex),
          ...slides.slice(currentSlideIndex + 1),
        ]);
        setCurrentSlideIndex((i) => 0);
      }}
    >
      <Trash2 className="h-5 w-5 text-black dark:text-white" />
    </ActionButton>
  );
}
