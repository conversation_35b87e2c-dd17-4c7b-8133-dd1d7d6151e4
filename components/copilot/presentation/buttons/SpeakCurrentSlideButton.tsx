import { useState } from "react";
import { ActionButton } from "./ActionButton";
import { Volume2 } from "lucide-react";
import { resetGlobalAudio, speak } from "@/utils/globalAudio";

interface SpeakCurrentSlideButtonProps {
  spokenNarration: string;
}

export function SpeakCurrentSlideButton({
  spokenNarration,
}: SpeakCurrentSlideButtonProps) {
  const [isSpeaking, setIsSpeaking] = useState(false);
  return (
    <ActionButton inProgress={isSpeaking}>
      <Volume2
        className="h-5 w-5 text-black dark:text-white"
        onClick={async () => {
          resetGlobalAudio();
          try {
            setIsSpeaking(true);
            await speak(spokenNarration);
          } finally {
            setIsSpeaking(false);
          }
        }}
      />
    </ActionButton>
  );
}
