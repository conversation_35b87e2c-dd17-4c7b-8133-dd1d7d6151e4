"use client";

import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Save } from "lucide-react";
import PptxGenJS from "pptxgenjs";
import { SlideModel } from "@/lib/types";

export const SaveMenu = ({ slides }: { slides: SlideModel[] }) => {
  const handleSave = (method: "pptx" | "localStorage" | "database") => {
    saveSlidesData(slides, method);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="flex items-center px-1 gap-x-2 text-black dark:text-white bg-transparent hover:bg-transparent">
          <Save size={18} /> <ChevronDown size={18} />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuItem onClick={() => handleSave("pptx")}>
          Save as PowerPoint (.pptx)
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSave("localStorage")}>
          Save to Local Storage
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSave("database")}>
          Save to Database
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const saveSlidesData = async (
  slides: SlideModel[],
  saveMethod: "pptx" | "localStorage" | "database"
) => {
  try {
    const jsonData = JSON.stringify(slides, null, 2);

    switch (saveMethod) {
      case "pptx":
        const pptx = new PptxGenJS();

        slides.forEach((slide, index) => {
          const slideContent = pptx.addSlide();
          slideContent.addText(slide.content, {
            x: 1,
            y: 1,
            fontSize: 18,
            color: "363636",
            align: "center",
          });

          if (slide.backgroundImageUrl) {
            slideContent.background = {
              path: slide.backgroundImageUrl,
            };
          }

          if (slide.spokenNarration) {
            slideContent.addText(`Narration: ${slide.spokenNarration}`, {
              x: 1,
              y: 5,
              fontSize: 12,
              color: "666666",
              italic: true,
            });
          }
        });

        // Correctly save the presentation with a specified file name
        await pptx.writeFile({ fileName: "presentation.pptx" });
        alert("Slides saved as a PowerPoint file.");
        break;

      case "localStorage":
        localStorage.setItem("slides", jsonData);
        alert("Slides saved to local storage.");
        break;

      case "database":
        const response = await fetch("/api/saveSlides", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: jsonData,
        });

        if (response.ok) {
          alert("Slides saved to the database.");
        } else {
          throw new Error("Failed to save to database.");
        }
        break;

      default:
        throw new Error("Unsupported save method.");
    }
  } catch (error) {
    console.error("Error saving slides:", error);
    alert("An error occurred while saving the slides.");
  }
};
