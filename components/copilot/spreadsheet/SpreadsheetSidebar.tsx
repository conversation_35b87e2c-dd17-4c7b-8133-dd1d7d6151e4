import React, { useState } from 'react';
import { ChevronFirst } from 'lucide-react';
import { SpreadsheetData } from '@/lib/types';
import {
  DndContext,
  closestCenter,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
  arrayMove,
} from '@dnd-kit/sortable';
import { GripHorizontal } from 'lucide-react';
import { CSS } from '@dnd-kit/utilities';

interface SidebarProps {
  spreadsheets: SpreadsheetData[];
  selectedSpreadsheetIndex: number;
  setSelectedSpreadsheetIndex: (index: number) => void;
  setSpreadsheets: (fn: (spreadsheets: SpreadsheetData[]) => SpreadsheetData[]) => void;
}

interface SortableItemProps {
  spreadsheet: SpreadsheetData;
  index: number;
  selectedSpreadsheetIndex: number;
  setSelectedSpreadsheetIndex: (index: number) => void;
}

const SortableItem = ({
  spreadsheet,
  index,
  selectedSpreadsheetIndex,
  setSelectedSpreadsheetIndex,
}: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: spreadsheet.id,
  });

  return (
    <li
      ref={setNodeRef}
      key={spreadsheet.id}
      className={`text-sm cursor-pointer ${
        index === selectedSpreadsheetIndex
          ? 'ring-0 ring-blue-500 ring-inset p-1 rounded-lg'
          : 'p-1'
      }`}
      onClick={() => setSelectedSpreadsheetIndex(index)}
      style={{
        transform: CSS.Transform.toString(transform), // Apply transform correctly
        transition, // Apply the transition
      }}
    >
      <div className="flex items-center gap-y-3">
        <GripHorizontal
          className="mr-1 size-3 cursor-grab text-muted-foreground focus:outline-none"
          {...attributes}
          {...listeners}
        />
        {spreadsheet.title}
      </div>
    </li>
  );
};

const Sidebar = ({
  spreadsheets,
  selectedSpreadsheetIndex,
  setSelectedSpreadsheetIndex,
  setSpreadsheets,
}: SidebarProps) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  // Initialize sensors for dragging
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = spreadsheets.findIndex((sheet) => sheet.id === active.id);
      const newIndex = spreadsheets.findIndex((sheet) => sheet.id === over.id);

      setSpreadsheets((prevSheets) => {
        return arrayMove(prevSheets, oldIndex, newIndex);
      });
      setSelectedSpreadsheetIndex(newIndex); // Update the selected index
    }
  };

  return (
    <div
      className={`relative h-screen text-white overflow-auto 
        ${isExpanded ? 'bg-gradient-to-b from-sky-500 via-teal-500 to-blue-500' : 'bg-transparent'}`}
    >
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`absolute top-1.5 text-orange-500 p-1 mx-5 rounded-full focus:outline-none hover:text-orange-600 ${isExpanded ? 'right-[-18px]' : 'ml-1'}`}
        style={{ transform: isExpanded ? 'rotate(0deg)' : 'rotate(180deg)' }}
      >
        <ChevronFirst size={20} />
      </button>

      {/* Sidebar Content */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={spreadsheets}
          strategy={verticalListSortingStrategy}
        >
          <div
            className={`transition-all duration-300 ${isExpanded ? 'w-64 p-2' : 'w-8 p-2'}`}
          >
            {isExpanded && (
              <ul className="mt-6">
                {spreadsheets.map((spreadsheet, index) => (
                  <SortableItem
                    key={spreadsheet.id}
                    spreadsheet={spreadsheet}
                    index={index}
                    selectedSpreadsheetIndex={selectedSpreadsheetIndex} // Pass selectedSpreadsheetIndex
                    setSelectedSpreadsheetIndex={setSelectedSpreadsheetIndex}
                  />
                ))}
              </ul>
            )}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default Sidebar;