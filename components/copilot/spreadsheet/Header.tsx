import { AddSpreadsheetButton } from "./buttons/AddSpreadsheetButton";
import { DeleteSpreadsheetButton } from "./buttons/DeleteSpreadsheetButton";
import { UploadButton } from "./buttons/UploadButton";
import { NavButton } from "./buttons/NavButton";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { SheetNumberIndicator } from "./SheetNumberIndicator";
import { SpreadsheetData } from "@/lib/types";
import { SaveMenu } from "./buttons/SaveMenu"
import clsx from "clsx";
import { useMemo } from "react";

interface HeaderProps {
  currentSheetIndex: number;
  setCurrentSheetIndex: (fn: (i: number) => number) => void;
  spreadsheets: SpreadsheetData[];
  setSpreadsheets: (fn: (spreadsheets: SpreadsheetData[]) => SpreadsheetData[]) => void;
  spreadsheet: SpreadsheetData;
  setSpreadsheet: (spreadsheet: SpreadsheetData) => void;
}

export function Header({
  currentSheetIndex,
  setCurrentSheetIndex,
  spreadsheets,
  setSpreadsheets,
  spreadsheet,
  setSpreadsheet
}: HeaderProps) {
  const currentSheet = useMemo(
    () => spreadsheets[currentSheetIndex],
    [spreadsheets, currentSheetIndex]
  );

  const handleDataExtracted = (data: SpreadsheetData[]) => {
    setSpreadsheets((prevSpreadsheets) => [...prevSpreadsheets, ...data]);
    console.log("Spreadsheets updated:", data);
  };

  return (
    <header className={clsx("flex w-full h-[2.3rem]")}>
      <div className="flex-0 flex space-x-1 my-auto">
        {/* Back */}
        <NavButton
          disabled={currentSheetIndex == 0}
          onClick={() => setCurrentSheetIndex((i) => i - 1)}
        >
          <ChevronLeft className="h-5 w-5" />
        </NavButton>

        {/* Forward */}
        <NavButton
          disabled={currentSheetIndex == spreadsheets.length - 1}
          onClick={() => setCurrentSheetIndex((i) => i + 1)}
        >
          <ChevronRight className="h-5 w-5" />
        </NavButton>
      </div>
      <SheetNumberIndicator
        currentSheetIndex={currentSheetIndex}
        totalSheets={spreadsheets.length}
      />      
      <div className="flex-1 space-x-4">
        <input
          type="text"
          value={spreadsheet.title}
          className="w-full p-1 text-center text-lg font-bold outline-none bg-transparent"
          onChange={(e) =>
            setSpreadsheet({ ...spreadsheet, title: e.target.value })
          }
        />
      </div>
      <UploadButton onDataExtracted={handleDataExtracted} />
      <div className="flex space-x-4 mx-4">
        <AddSpreadsheetButton
          currentSheetIndex={currentSheetIndex}
          setCurrentSheetIndex={setCurrentSheetIndex}
          setSpreadsheets={setSpreadsheets}
        />
        <DeleteSpreadsheetButton
          currentSheetIndex={currentSheetIndex}
          setCurrentSheetIndex={setCurrentSheetIndex}
          spreadsheets={spreadsheets}
          setSpreadsheets={setSpreadsheets}
        />
         
        <SaveMenu spreadsheets={spreadsheets} />

      </div>
    </header>
  );
}
