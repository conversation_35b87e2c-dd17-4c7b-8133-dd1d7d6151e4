import { SpreadsheetData } from "@/lib/types";
import { generateId } from "ai";
import { PlusCircle } from "lucide-react";

interface AddSpreadsheetButtonProps {
  currentSheetIndex: number;
  setCurrentSheetIndex: (fn: (i: number) => number) => void;
  setSpreadsheets: (fn: (spreadsheets: SpreadsheetData[]) => SpreadsheetData[]) => void;
}

export function AddSpreadsheetButton({
  currentSheetIndex,
  setCurrentSheetIndex,
  setSpreadsheets,
}: AddSpreadsheetButtonProps) {
  const handleAddSpreadsheet = () => {
    const newSpreadsheet: SpreadsheetData = {
      id: generateId(),
      title: `Sheet ${Date.now()}`,
      rows: Array(8).fill(Array(12).fill({ value: "" })),
    };

    setSpreadsheets((prevSheets) => {
      const updatedSheets = [...prevSheets, newSpreadsheet];
      setCurrentSheetIndex(() => updatedSheets.length - 1);
      return updatedSheets;
    });
  };

  return (
    <button
      onClick={handleAddSpreadsheet}
    >
      <PlusCircle className="h-5 w-5 text-black dark:text-white hover:text-orange-600" />
    </button>
  );
}