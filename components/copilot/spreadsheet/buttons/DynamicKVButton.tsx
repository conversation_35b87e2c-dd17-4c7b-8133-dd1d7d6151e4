import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

type KVData = { month: string; item: string; value: any, sheetName: string }

export const DynamicKeyValueButton = ({
  open,
  onClose,
  normalizedData,
  mappingType,
  spreadsheets,
}: {
  open: boolean;
  onClose: () => void;
  normalizedData: KVData[];
  mappingType: "single" | "multi" | null;
  spreadsheets: { title: string }[];
}) => {
  const [categories, setCategories] = useState<{ [sheetName: string]: string }>({});

  const handleCategoryChange = (sheetName: string, category: string) => {
    setCategories((prev) => ({ ...prev, [sheetName]: category }));
  };

  const handleSaveToDatabase = async (data: KVData[]) => {
    // Save to Database (Assume an API exists)
    const response = await fetch("/api/saveSpreadsheet", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
    });

    if (response.ok) {
    alert("Data saved to Database.");
    } else {
    throw new Error("Database save failed.");
    }
  }

  return (
    <Dialog modal={true} open={open} onOpenChange={onClose}>
      <DialogContent 
        className="h-full max-w-[50rem]"
        onInteractOutside={(e: any) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>{mappingType === "single" ? "Single Key-Value Mapping" : "Multi Key-Value Mapping"}</DialogTitle>
        </DialogHeader>

        {/* Category Selection */}
        <div className="text-sm mb-2">
          {spreadsheets.map((sheet) => (
            <div key={sheet.title} className="flex items-center gap-4 mb-2">
              <span>{sheet.title}</span>
              <select
                value={categories[sheet.title] || ""}
                onChange={(e) => handleCategoryChange(sheet.title, e.target.value)}
                className="border rounded px-2 py-1"
              >
                <option value="">Select Category</option>
                <option value="income">收入</option>
                <option value="expense">支出</option>
                <option value="common_fund">公共基金</option>
                <option value="mps_fund">機械車位</option>
              </select>
            </div>
          ))}
        </div>

        {/* Data Preview */}
        <div className="overflow-auto max-h-100 text-sm">
          <table className="w-full border">
            <thead>
              <tr>
                <th className="p-2 border">Month</th>
                <th className="p-2 border">Item</th>
                <th className="p-2 border">Value</th>
                <th className="p-2 border">Sheet</th>
              </tr>
            </thead>
            <tbody>
              {normalizedData.map((row, index) => (
                <tr key={index}>
                  <td className="p-2 border">{row.month}</td>
                  <td className="p-2 border">{row.item}</td>
                  <td className="p-2 border">{row.value}</td>
                  <td className="p-2 border">{row.sheetName}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Save Button */}
        <Button className="mt-4 w-full" onClick={() => alert("Data uploaded with categories!")}>
          Upload to Database
        </Button>
      </DialogContent>
    </Dialog>
  );
};
