import { FolderUp } from "lucide-react";
import { SpreadsheetData } from "@/lib/types";
import { UseFileDropzone } from "./UseFileDropzone";

interface UploadButtonProps {
  onDataExtracted: (data: SpreadsheetData[]) => void;
}

export const UploadButton: React.FC<UploadButtonProps> = ({ onDataExtracted }) => {
  const { getRootProps, getInputProps, isDragActive, error } = UseFileDropzone({
    onDataExtracted,
  });

  return (
    <div className="relative inline-block">
      <div
        {...getRootProps()}
        className={`flex items-center gap-2 border-2 border-dashed rounded-lg px-4 py-[0.4rem] mx-2 text-center cursor-pointer ${
          isDragActive ? "border-primary bg-primary/10" : "border-indigo-300"
        }`}
      >
        <input {...getInputProps()} />
        <FolderUp size={20} className="text-black dark:text-white" />
        <p className="text-sm text-gray-500">.xlsx, .xls</p>
      </div>
      {error && <p className="text-red-500 mt-2">{error}</p>}
    </div>
  );
};
