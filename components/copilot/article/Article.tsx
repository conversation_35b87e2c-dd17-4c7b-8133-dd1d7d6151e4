"use client";

import { useRef, useState, useEffect } from "react";
import {
  useCopilotReadable,
  useCopilotAction,
} from "@copilotkit/react-core";
import {
  CopilotTextarea,
  HTMLCopilotTextAreaElement,
} from "@copilotkit/react-textarea";

export function Article() {
  // Define state variables for article outline, copilot text, and article title
  const [articleOutline, setArticleOutline] = useState("");
  const [copilotText, setCopilotText] = useState("");
  const [articleTitle, setArticleTitle] = useState("");
  useCopilotReadable({
    description: "This is the current Blog article title",
    value: articleTitle,
  });
  useCopilotReadable({
    description: "This is the current Blog article outline",
    value: "Blog article outline: " + JSON.stringify(articleOutline),
  });
  const copilotTextareaRef = useRef<HTMLCopilotTextAreaElement>(null);

  useEffect(() => {
    console.log("articleTitle: ", articleTitle)
    console.log("articleOutline: ", articleOutline)
    console.log("copilotText: ", copilotText)
  }, [articleOutline, articleTitle, copilotText]);

  useCopilotAction(
    {
      name: "researchBlogArticleTopic",
      description: "Research a given topic for a blog article.",
      inputSchema: [
        {
          name: "articleTitle",
          type: "string",
          description: "Title for a blog article.",
          required: true,
        },
        {
          name: "articleOutline",
          type: "string",
          description:"Outline for a blog article that shows what the article covers.",
          required: true,
        },
      ],
      handler: async ({ articleOutline, articleTitle }) => {
        setArticleOutline(articleOutline);
        setArticleTitle(articleTitle);
      },
    },
    []
  );


  return (
    // Form element for article input
    <form
      action={""}
      className="w-full h-[90vh] gap-10 flex flex-col items-center p-10">
      {/* Input field for article title */}
      <div className="flex w-full items-start gap-3">
        <textarea
          className="p-2 w-full h-12 rounded-lg flex-grow overflow-x-auto overflow-y-hidden whitespace-nowrap"
          id="title"
          name="title"
          value={articleTitle}
          placeholder="Article Title"
          onChange={(e) => setArticleTitle(e.target.value)}
        />
      </div>
      <CopilotTextarea
        value={copilotText}
        ref={copilotTextareaRef}
        placeholder="Write your article content here"
        onChange={(e) => setCopilotText(e.target.value)}
        onValueChange={(value: string) => setCopilotText(value)}
        className="p-4 w-full aspect-square font-bold text-xl bg-slate-800 text-white rounded-lg resize-none"
        placeholderStyle={{
          color: "white",
          opacity: 0.5,
        }}
        autosuggestionsConfig={{
          textareaPurpose: articleTitle,
          chatApiConfigs: {
            suggestionsApiConfig: {
              //@ts-ignore
              maxOutputTokens: 5,
              stop: ["\n", ".", ","],
            },
            insertionApiConfig: {},
          },
          debounceTime: 250,
        }}
      />
      {/* Textarea for article content */}
      <textarea
        className="p-4 w-full aspect-square font-bold text-xl bg-slate-800 text-white rounded-lg resize-none"
        id="content"
        name="content"
        value={copilotText}
        placeholder="Write your article content here"
        onChange={(e) => setCopilotText(e.target.value)}
      />
      {/* Publish button */}
      <button
        type="submit"
        className="p-4 w-full !bg-slate-800 text-white rounded-lg">Publish</button>
    </form>
  );
}