'use client'

import { AITodoCreator } from "@/components/todovex/add-tasks/ai-task-creator";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IconCheck, IconCopy } from "@/components/ui/icons";
import { useCopyToClipboard } from "@/lib/hooks/use-copy-to-clipboard";
import { cn } from "@/lib/utils";
import {
  FaRegThumbsUp,
  FaRegThumbsDown,
  FaRegStickyNote,
  FaThumbsDown,
  FaThumbsUp,
  FaStickyNote,
  FaCalendarCheck,
} from "react-icons/fa";

import { LangfuseWeb } from "langfuse";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { Textarea } from "@/components/ui/textarea";
import { GridLoader } from "react-spinners";
import { TextToSpeech } from "@/components/lobe-edge-tts"

interface ChatMessageActionsProps extends React.ComponentProps<"div"> {
  message: any;
  chatId?: string;
  path?: string;
}

const langfuse = new LangfuseWeb({
  publicKey: process.env.NEXT_PUBLIC_LANGFUSE_PUBLIC_KEY ?? "",
  baseUrl: process.env.NEXT_PUBLIC_LANGFUSE_BASE_URL,
});

langfuse.debug();

type Feedback = "positive" | "negative";

export function ChatMessageActions({
  message,
  chatId,
  path,
  className,
  ...props
}: ChatMessageActionsProps) {
  const router = useRouter();
  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2000 });
  const [isPosting, setIsPosting] = useState<boolean>(false);
  const [currentFeedback, setCurrentFeedback] = useState<
    Feedback | "submitting" | null
  >(null);

  const showFeedbackButtons =
    message.role === "assistant" &&
    chatId !== null && // need thread id to submit feedback
    message.id?.length === 888; // only show feedback buttons for messages with uuids as ids {36) (updated after streaming is finished)

  const [modalState, setModalState] = useState<{
    feedback: Feedback;
    comment: string;
  } | null>(null);

  const [aiTodoDialogOpen, setAiTodoDialogOpen] = useState<boolean>(false);

  const onCopy = () => {
    if (isCopied) return;
    copyToClipboard(message.content);
  };

  const onPost = async () => {
    if (!message?.content) return;
    try {
      setIsPosting(true);
      const title = message.content.substring(0, 16);
      const response = await fetch("/api/notes", {
        method: "POST",
        body: JSON.stringify({
          title,
          content: message.content,
          path,
        }),
      });
      if (!response.ok) throw Error("Status code: " + response.status);
    } catch (error) {
      console.error("Error posting to notes:", error);
    } finally {
      setIsPosting(false);
      router.refresh();
    }
  };

  const handleSubmit = () => {
    if (!langfuse) return;
    if (currentFeedback === "submitting" || !chatId || !modalState) return;

    setCurrentFeedback("submitting");

    langfuse
      .score({
        traceId: `lf-ai-chat:${chatId}`,
        observationId: message.id,
        name: "user-feedback",
        value: modalState.feedback === "positive" ? 1 : -1,
        comment: modalState.comment !== "" ? modalState.comment : undefined,
      })
      .then(() => {
        setCurrentFeedback(modalState.feedback);
      })
      .catch((err) => {
        toast.error("Something went wrong");
        setCurrentFeedback(null);
      });

    // close modal
    setModalState(null);
  };

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-end transition-opacity group-hover:opacity-100 md:relative md:-top-2 md:opacity-0",
        className
      )}
      {...props}
    >
      {showFeedbackButtons ? (
        <Button
          variant="ghost"
          size="iconXs"
          className={cn(
            currentFeedback === "positive"
              ? "md:opacity-100"
              : "group-hover:opacity-100 md:opacity-0"
          )}
          onClick={() =>
            setModalState({
              feedback: "positive",
              comment: "",
            })
          }
          disabled={currentFeedback === "submitting"}
        >
          {currentFeedback === "positive" ? (
            <FaThumbsUp className="h-3 w-3" />
          ) : (
            <FaRegThumbsUp className="h-3 w-3" />
          )}
          <span className="sr-only">Upvote</span>
        </Button>
      ) : null}
      {showFeedbackButtons ? (
        <Button
          variant="ghost"
          size="iconXs"
          className={cn(
            currentFeedback === "negative"
              ? "md:opacity-100"
              : "group-hover:opacity-100 md:opacity-0"
          )}
          onClick={() =>
            setModalState({
              feedback: "negative",
              comment: "",
            })
          }
          disabled={currentFeedback === "submitting"}
        >
          {currentFeedback === "negative" ? (
            <FaThumbsDown className="h-3 w-3" />
          ) : (
            <FaRegThumbsDown className="h-3 w-3" />
          )}
          <span className="sr-only">Downvote</span>
        </Button>
      ) : null}
      <Button
        variant="ghost"
        size="iconXs"
        onClick={onCopy}
        className="group-hover:opacity-100 md:opacity-0"
      >
        {isCopied ? <IconCheck /> : <IconCopy />}
        <span className="sr-only">Copy message</span>
      </Button>
      {message.role === "assistant" && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="iconXs"
              onClick={onPost}
              className="group-hover:opacity-100 md:opacity-0"
            >
              {isPosting ? (
                <GridLoader color={"#36d7b7"} size={15} />
              ) : (
                <FaRegStickyNote />
              )}
              <span className="sr-only">Take notes</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Take Notes</p>
          </TooltipContent>
        </Tooltip>
      )}
      {message.role === "assistant" && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="iconXs"
              onClick={() => setAiTodoDialogOpen(true)}
              className="group-hover:opacity-100 md:opacity-0"
            >
              <FaCalendarCheck />
              <span className="sr-only">Create AI Todo</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Create AI Todo</p>
          </TooltipContent>
        </Tooltip>
      )}
      {message.role === "assistant" && (
        <TextToSpeech 
          text={message?.content!}
        />
      )}
      <Dialog open={!!modalState} onOpenChange={() => handleSubmit()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Do you want to add a comment?</DialogTitle>
            <DialogDescription>
              <Textarea
                className="mt-4 mb-2"
                value={modalState?.comment ?? ""}
                onChange={(e) => {
                  setModalState({ ...modalState!, comment: e.target.value });
                }}
              />
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => handleSubmit()}
              variant="secondary"
            >
              No, thank you
            </Button>
            <Button type="submit" onClick={() => handleSubmit()}>
              Submit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog open={aiTodoDialogOpen} onOpenChange={setAiTodoDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Todos by AI</DialogTitle>
              {/* Optional Description */}
              <DialogDescription>
                Use this AI-powered tool to generate todo lists efficiently.
              </DialogDescription>
          </DialogHeader>
          <AITodoCreator setAiTodoDialogOpen={setAiTodoDialogOpen} content={message.content} />
        </DialogContent>
      </Dialog>
    </div>
  );
}
