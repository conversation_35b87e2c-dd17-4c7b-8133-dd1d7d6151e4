// Inspired by Cha<PERSON><PERSON>-<PERSON><PERSON> and modified to fit the needs of this project
// @see https://github.com/mckaywrigley/chatbot-ui/blob/main/components/Chat/ChatMessage.tsx
// Can be use to filter img in the paragraph

import Image from "next/image";
import React, { 
  JSX,
  useState, 
  useEffect,
  useRef, 
  Dispatch, 
  SetStateAction } from "react";
import { UIMessage } from 'ai'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'

import { cn } from '@/lib/utils'
import { Companion } from '@prisma/client'
import { CodeBlock } from '@/components/ui/codeblock'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from '@/components/user-avatar'
import { ChatMessageActions } from '@/components/chat-message-actions'
import { Tab } from "@headlessui/react";
import ImageModal from "@/components/modals/ImageModal";
import TransformModal from "@/components/modals/TransformModal";
//import ChatWithVisionModal from "@/components/modals/ChatWithVisionModal";
import InPaintModal from "@/components/modals/InPaintModal";
import { 
  TbCopy,
  TbPhotoEdit,
  TbCloudDownload, 
  TbCloudHeart, 
  TbMessageCirclePlus } from "react-icons/tb";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/canvas-dialog'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
//import '@/app/vision/index.css'

/*interface Message {
  id: string;
  role: "system" | "user" | "assistant";
  content: string;
  image?: string | null;
}*/

declare module 'ai' {
  interface UIMessage {
    image?: string | null;
  }
}

interface ImageElement extends React.ReactElement {
  props: {
    src: string;
    alt: string;
  };
}

interface AnchorElement extends React.ReactElement {
  props: {
    href: string;
    children: React.ReactNode;
  };
}

export interface ImageMessageProps {
  companionId: string
  isLoading?: boolean
  src?: string
  message: UIMessage
  formData: FormData
  setFormData: Dispatch<SetStateAction<FormData>>
  selectedMediaUrl: string | null
  setChatWithVisionImage: Dispatch<SetStateAction<string>>
  path?: string
}

export function ImageMessage({
  companionId,
  isLoading,
  src,
  message,
  formData,
  setFormData,
  selectedMediaUrl,
  setChatWithVisionImage,
  path,
  ...props
}: ImageMessageProps) {
  //const [chatWithVisionModalOpen, setChatWithVisionModalOpen] = useState(false);
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [transformModalOpen, setTransformModalOpen] = useState(false);
  const [inPaintModalOpen, setInPaintModalOpen] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [showTopButtons, setShowTopButtons] = useState(false);
  const [showBottomButtons, setShowBottomButtons] = useState(false);
  const hideTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [copying, setCopying] = useState(false);
  const codeBlockRef = useRef<SyntaxHighlighter>(null);

  // Extract content from message parts
  const textContent = getTextContent(message);
  const imageUrls = getImageUrls(message);

  useEffect(() => {
    if (selectedImageIndex !== null) {
      setImageModalOpen(true);
      console.log("selectedImageIndex", selectedImageIndex);
    }
  }, [selectedImageIndex]);

  const handleImageCopy = (imageUrl: string) => {
    if (imageUrl) {
      fetch(imageUrl, {
        headers: new Headers({
          Origin: location.origin,
        }),
        mode: "cors",
      })
        .then((response) => response.blob())
        .then((blob) => {
          let blobUrl = window.URL.createObjectURL(blob);
          navigator.clipboard.write([
            new ClipboardItem({
              "image/png": blob,
            }),
          ]);
          setCopying(false);
        })
        .catch((e) => console.error(e));
    }
  };

  const handleImageDownload = (imageUrl: string) => {
    if (imageUrl) {
      const filename = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
      //const filename = nanoid()
      fetch(imageUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
    }
  }; 

  const handleMouseEnter = () => {
    setShowTopButtons(true);
    setShowBottomButtons(true);
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
  };

  const handleMouseLeave = () => {
    hideTimeoutRef.current = setTimeout(() => {
      setShowTopButtons(false);
      setShowBottomButtons(false);
    }, 500);
  };

  // code({ node, inline, className, children, ...props }) To fix

  return (
    <div
      className={cn('group flex items-start gap-x-2 py-2 w-full',
      message.role === "user" && "justify-end")}
      {...props}
    >
      {message.role !== 'user' && src && <BotAvatar src={src} />}
      <div className="rounded-md px-3 py-2 max-w-lg text-sm break-words bg-primary/10">
        <TransformModal
          companionId={companionId!}
          src={message.image}
          isOpen={transformModalOpen}
          onClose={() => setTransformModalOpen(false)}
        />
        {message.image && (
          <div className="relative"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <Image
              alt="Image"
              height="160"
              width="160"
              onClick={() => setTransformModalOpen(true)}
              src={message.image}
              className="
                object-cover
                cursor-pointer
                scale-110
                transition
                translate
                rounded-md
              "
            />
            {/* Top-aligned buttons */}
            <Dialog modal={false} open={inPaintModalOpen} onOpenChange={setInPaintModalOpen} >
              <DialogTrigger asChild>
              {showTopButtons && (
                <div className='absolute top-0 right-0 items-center rounded-full bg-transparent p-1 z-20'>
                  <div className='mt-0'>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          className="flex items-center rounded-full cursor-pointer bg-secondary p-1"
                          onMouseEnter={handleMouseEnter}
                        >
                          <TbPhotoEdit color="cyan" className="w-5 h-5" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>修改圖片</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              )}

              </DialogTrigger>
              <DialogContent
                onInteractOutside={(e: any) => e.preventDefault()}
                className="max-w-[39rem] h-[39rem] gap-0"
              >
                <InPaintModal
                  companionId={companionId}
                  image={message.image!}
                  formData={formData} 
                  setFormData={setFormData}
                  selectedMediaUrl={selectedMediaUrl}
                  setChatWithVisionImage={setChatWithVisionImage}
                />
              </DialogContent>
            </Dialog>
            {/* <ChatWithVisionModal
              companionId={companionId!}
              src={message.image}
              isOpen={chatWithVisionModalOpen}
              onClose={() => setChatWithVisionModalOpen(false)}
            /> */}
            {showTopButtons && (
              <div className='absolute top-0 right-8 items-center rounded-full bg-transparent p-1 z-20'>
                <div className='mt-0'>
                 <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="flex items-center rounded-full bg-secondary p-1"
                        disabled={isLoading}
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                          e.preventDefault();
                          {/* setChatWithVisionModalOpen(true) */}
                          setChatWithVisionImage(message?.image!)
                        }}
                        onMouseEnter={handleMouseEnter}
                      >
                        <TbMessageCirclePlus color="cyan" className="w-5 h-5" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>聊聊這張圖片</p>
                    </TooltipContent>
                  </Tooltip>
                  {/* Add more buttons here */}
                </div>
              </div>
            )}

            {/* Bottom-aligned buttons */}
            {showBottomButtons && (
              <div className='absolute bottom-0 right-0 items-center rounded-full bg-transparent p-1 z-20'>
                <div className='mb-0'>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="flex items-center rounded-full bg-secondary p-1"
                        disabled={isLoading}
                        onClick={() => {
                          setCopying(true)
                          handleImageCopy(message.image as string)
                        }}
                        onMouseEnter={handleMouseEnter}
                      >
                        <TbCopy color="cyan" className="w-5 h-5" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className='mt-1'>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="flex items-center rounded-full bg-secondary p-1"
                        disabled={isLoading}
                        onClick={() => handleImageDownload(message.image as string)}
                        onMouseEnter={handleMouseEnter}
                      >
                        <TbCloudHeart color="cyan" className="w-5 h-5" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>下載</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>
            )}
          </div>
        )}
        {!message.image && (
          <MemoizedReactMarkdown
            className="prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0 prose-a:text-pink-600 hover:prose-a:text-pink-500"
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              p({ children }) {
                const childArray = React.Children.toArray(children);
            
                const remainingContent = childArray.reduce<{
                  images: React.ReactNode[];
                  text: (string | JSX.Element)[];
                }>((acc, child) => {
                  if (typeof child === 'string') {
                    acc.text.push(child);
                  } else if (React.isValidElement(child)) {
                    if (child.type === 'img') {
                      acc.images.push(child);
                    } else if (child.type === 'a') {
                      acc.text.push(
                        <a href={(child as React.ReactElement<any>).props.href} key={acc.text.length}>
                          {(child as React.ReactElement<any>).props.children}
                        </a>
                      );
                    } else {
                      acc.text.push(child);
                    }
                  }
                  return acc;
                }, { images: [], text: [] });

                const images = childArray.filter(
                  (child) =>
                    React.isValidElement(child) &&
                    child.type === "img" &&
                    typeof child !== "string" // Filter out string nodes
                );
                {console.log("childArray@images-map", images)}
                
                const renderedImages = images.map((image, index) => {
                  if (typeof image === 'object' && image !== null && 'type' in image && typeof image.type === 'function') {
                    //@ts-ignore
                    const { src, alt } = image.props;
                    {console.log("childArray@image-src", src)}
                    return (
                      <div key={index}>
                        <Image
                          onClick={() => setSelectedImageIndex(index)}
                          src={src as string}
                          alt={alt as string}
                          style={{
                            maxWidth: '160px',
                            maxHeight: '160px',
                          }}
                          className="
                          w-full h-auto
                          object-cover
                          cursor-pointer
                          hover:scale-110
                          transition
                          rounded-lg
                          border-1
                          m-0
                          "
                        />
                        {selectedImageIndex === index && (
                          <ImageModal
                            src={src as string}
                            isOpen={imageModalOpen} // Ensuring imageModalOpen controls the modal visibility
                            onClose={() => setSelectedImageIndex(null)}
                          />
                        )}
                      </div>
                    );
                  }
                  return null;
                });
         
                return (
                  <div>
                    {images.length > 0 && (
                      <div className={`grid grid-cols-${images.length === 2 ? 2 : images.length <= 1 ? 1 : 3} gap-2`}
                      >
                        {renderedImages}
                      </div>
                    )}
                    {/* Displaying text content */}
                    <p className="mt-1 mb-2 text-justify last:mb-0">{remainingContent.text}</p>
                  </div>
                );
              },
              h1({ children }) {
                return <h1 className="my-0">{children}</h1>;
              },
              h2({ children }) {
                return <h2 className="my-0">{children}</h2>;
              },
              h3({ children }) {
                return <h3 className="my-0">{children}</h3>;
              },
              h4({ children }) {
                return <h4 className="my-0">{children}</h4>;
              }, 
              h5({ children }) {
                return <h5 className="my-0">{children}</h5>;
              }, 
              h6({ children }) {
                return <h6 className="my-0">{children}</h6>;
              }, 
              ol({ children }) {
                return <ol className="my-0">{children}</ol>;
              },
              ul({ children }) {
                return <ul className="my-0">{children}</ul>;
              },
              li({ children }) {
                return <li className="my-0">{children}</li>;
              },
              code(props) {
                const {children, className, node, ...rest} = props
                const match = /language-(\w+)/.exec(className || '')
                return match ? (
                  <SyntaxHighlighter
                    {...rest}
                    ref={codeBlockRef}
                    PreTag="div"
                    children={String(children).replace(/\n$/, '')}
                    language={match[1]}
                    style={dark}
                  />
                ) : (
                  <code {...rest} className={className}>
                    {children}
                  </code>
                );
              }
            }}
          >
            {textContent}
          </MemoizedReactMarkdown>
        )}
      </div>
      {message.role === 'user' && <UserAvatar />}
      <ChatMessageActions message={message} path={path} />
    </div>
  );
}

// Helper function to extract text content from UIMessage parts
const getTextContent = (message: UIMessage): string => {
  if (!message.parts || message.parts.length === 0) return '';
  
  return message.parts
    .filter(part => part.type === 'text')
    .map(part => ('text' in part ? part.text : ''))
    .join('\n');
};

// Helper function to extract image URLs from UIMessage parts
const getImageUrls = (message: UIMessage): string[] => {
  if (!message.parts || message.parts.length === 0) return [];
  
  return message.parts
    .filter(part => 
      (part.type === 'file' && 'mimeType' in part && typeof part.mimeType === 'string' && part.mimeType.startsWith('image/'))
    )
    .map(part => {
      if ('url' in part && part.url) return part.url;
      if ('image' in part && typeof part.image === 'string') return part.image;
      return null;
    })
    .filter((url): url is string => url !== null);
};