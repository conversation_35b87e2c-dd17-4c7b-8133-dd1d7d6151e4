'use client';

import axios from 'axios';
import React, { useState, useEffect, useTransition } from 'react'
import { useUser } from "@clerk/nextjs";
import { useRouter } from 'next/navigation';
import { FieldValues, SubmitHandler, useForm } from 'react-hook-form';
import { updatePersonaAction } from "@/app/actions";
import { Persona } from '@prisma/client';
import { CldUploadButton } from 'next-cloudinary';
import { 
  clearPersonaToDatabase
} from "@/lib/databaseUtils";

import clsx from "clsx";
import Input from "@/components/inputs/Input";
import Modal from '@/components/modals/Modal';
import Button from '@/components/Button';
import Image from 'next/image';
import { toast } from 'react-hot-toast';
import { RefreshCw } from "lucide-react";

interface ProfileModalProps {
  isOpen?: boolean;
  onClose: () => void;
  currentUser: Persona;
  path: string;
}

export const ProfileModal: React.FC<ProfileModalProps> = ({ 
  isOpen, 
  onClose, 
  currentUser = {} as Persona,
  path,
}) => {
  const router = useRouter();
  const { user } = useUser()
  const [isPending, startTransition] = useTransition();

  //console.log(currentUser, '&TEST_CURRENT_USER')

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: {
      errors, isSubmitSuccessful
    }
  } = useForm<FieldValues>({
    defaultValues: {
      name: currentUser?.name,
      nickName: currentUser?.nickName,
      age: currentUser?.age,
      gender: currentUser?.gender,
      traits: currentUser?.traits,
      status: currentUser?.status,
      currentRole: currentUser?.currentRole,
      image: currentUser?.image
    }
  });

  const image = watch('image');

  const handleUpload = (result: any) => {
    setValue('image', result.info.secure_url, { 
      shouldValidate: true 
    });
  }

  const onSubmit: SubmitHandler<FieldValues> = async (data) => {
    startTransition(async () => {
      await updatePersonaAction(
        { 
          name: data.name,
          nickName: data.nickName,
          age: parseInt(data.age),
          gender: data.gender,
          currentRole: data.currentRole,
          image: data.image 
        },
        path,
      );
      setValue("nickName", data.nickName);
      setValue("age", data.age);
      setValue("gender", data.gender);
      setValue("currentRole", data.currentRole);
      setValue("image", data.image);
    });
    router.refresh()
  }

  useEffect(() => {
    reset();
  }, [isSubmitSuccessful]);


  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-12">
          <div className="border-b border-gray-900/10 pb-12">
            <h2 
              className="
                text-base 
                font-semibold 
                leading-7 
                text-gray-900
              "
            >
              Profile
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-600">
              Update your personal information.
            </p>

            <div className="mt-2 flex flex-col gap-y-4">
            <div>
                <label 
                  htmlFor="photo" 
                  className="
                    block 
                    text-sm 
                    font-medium 
                    leading-6 
                    text-gray-900
                  "
                >
                  Photo
                </label>
                <div className="mt-2 flex items-center gap-x-3">
                  <Image
                    width="48"
                    height="48" 
                    className="rounded-full" 
                    src={image || currentUser?.image || '/images/placeholder.jpg'}
                    alt="Avatar"
                  />
                  <CldUploadButton 
                    options={{ maxFiles: 1 }} 
                    onUpload={handleUpload} 
                    uploadPreset="wfoehym3"
                  >
                    <Button
                      disabled={isPending}
                      secondary
                      type="button"
                    >
                      Change
                    </Button>
                  </CldUploadButton>
                  {/*
                  <Button
                    disabled={isPending}
                    className={`relative flex h-9 w-9 items-center justify-center rounded-md transition-all ${
                      isPending ? "bg-gray-400" : "bg-teal-500"
                    }`}
                    onClick={() =>
                      startTransition(async () => {
                        await clearPersonaToDatabase(user!.id, "", "", "", 0, "", false, path);
                        router.refresh()
                        onClose();
                      })
                    }
                  >
                    <RefreshCw
                      className={clsx(
                        "h-6 w-6",
                        isPending ? "bg-gray-400" : "bg-teal-500"
                      )}
                    />
                    Reset
                  </Button>
                    */}
                </div>
              </div>
              <Input
                disabled={isPending}
                label="Nick Name" 
                id="nickName" 
                errors={errors} 
                required 
                register={register}
              />
              <Input
                disabled={isPending}
                label="Age" 
                id="age" 
                errors={errors} 
                required 
                register={register}
              />
              <Input
                disabled={isPending}
                label="Gender" 
                id="gender" 
                errors={errors} 
                required 
                register={register}
              />
              <Input
                disabled={isPending}
                label="Current Role" 
                id="currentRole" 
                errors={errors} 
                required 
                register={register}
              />
            </div>
          </div>
        </div>

        <div 
          className="
            mt-6 
            flex 
            items-center 
            justify-end 
            gap-x-6
          "
        >
          <Button 
            disabled={isPending}
            secondary 
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button 
            disabled={isPending}
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Modal>
  )
}