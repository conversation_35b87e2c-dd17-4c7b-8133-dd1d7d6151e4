import Script from 'next/script';
import React, { useEffect, useState, useRef, type FormEvent } from 'react';
import { useRouter } from "next/navigation";
import { useCompletion, type UseChatHelpers } from '@ai-sdk/react';
import { nanoid } from '@/lib/utils'
import { useToast } from "@/components/ui/use-toast";
import { useDebouncedCallback } from "use-debounce";
import { Message } from "./ChatMessageBubble";
import {
  getDetailContentFromFile,
  isImageFileType,
} from "@/app/client/fetch/sfile";
import { URLDetail, URLDetailContent, isURL } from "@/app/client/fetch/surl";
import { FileWrap } from "@/utils/file";
import {
    ALLOWED_DOCUMENT_EXTENSIONS,
    ALLOWED_IMAGE_EXTENSIONS,
    ALLOWED_TEXT_EXTENSIONS,
    DOCUMENT_FILE_SIZE_LIMIT,
} from "@/constants";
import Locale from "@/locales";
import FileUploader from "@/components/ui/file-uploader";
import ImagePreview from "@/components/ui/image-preview";
import { isVisionModel } from "@/app/client/platforms/llm";

import { useMediaQuery } from "@/hooks/use-media-query"
import Textarea from "react-textarea-autosize";
import clsx from "clsx";
import { cn } from '@/lib/utils'
import { LoadingCircle } from '@/components/loaders'
import { IconPlus } from '@/components/ui/icons'
import { ArrowUp, Paperclip } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { Button, buttonVariants } from '@/components/ui/button'
import { LanguageSelection } from '@/components/ui/ai-language'
import { Companion } from '@prisma/client'


interface ChatFormProps {
  messages: Message[]
  setMessages: (messages: Message[]) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  input: string;
  setInput: (value: string) => void;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
  disabled: boolean;
  companionId: string;
  userName: string;
  threadId: string;
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  path: string;
  formWidth?: string;
  showIngestForm?: boolean;
  setTemporaryURLInput: (url: string) => void;
}
  
export function ChatForm({ 
  messages,
  setMessages,
  handleSubmit, 
  input, 
  setInput, 
  setIsLoading,
  isLoading, 
  disabled, 
  companionId,
  userName,
  threadId,
  existingThread,
  path,
  formWidth,
  showIngestForm,
  setTemporaryURLInput
}: ChatFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [imageFile, setImageFile] = useState<URLDetail>();
  const [temporaryBlobUrl, setTemporaryBlobUrl] = useState<string>();
  const latestFileDetail = useRef<URLDetailContent| null>(null)

  let widthValue = '100%'
  if (formWidth) {
    widthValue = formWidth
  }


  const handleNewChatButtonClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    if (path) {
      router.refresh();
      router.push(path);
    } else {
      router.push("/");
    }
  };


  const showError = (errMsg: string) => {
    toast({
      title: errMsg,
      variant: "destructive",
    });
  };

  const {
    complete, 
    completion
  } = useCompletion({
    api: `/api/summary/${companionId}/openrouter`,
    body: {
      threadId,
      existingThread,
      latestFileDetail,
      path,
    },
    /*onResponse: res => {
      if (res.status === 429) {
        toast({
          title: "You have reached your request limit for the day.",
          variant: "destructive",
        });
      }
    },*/
    onFinish(_prompt, completion) {
      console.log("completion: ", completion);
      router.refresh()
    },
  });

  const getSummary = async (fileDetail?: URLDetailContent) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/summary/${companionId}/anthropic`, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          threadId,
          existingThread,
          fileDetail,
          path,
        }),
      });
  
      if (response.status === 200) {
        const body = response.body;
        if (!body) {
          throw new Error("Response body is null");
        }
  
        const reader = body.getReader();
        let completion = '';
  
        // Read chunks of data from the response body
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          completion += new TextDecoder().decode(value);
        }
  
        const newMessages: Message[] = [
          {
            id: nanoid(),
            content: `${fileDetail?.url} \n : ${fileDetail?.type} • ${((fileDetail?.size ?? 0) / 1024).toFixed(2)} KB`,
            role: "user"          
          },
          {
            id: nanoid(),
            content: completion,
            role: "assistant"
          }
        ];
  
        setMessages([...messages, ...newMessages]);
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while summarizing the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error summarizing document:", error);
      toast({
        title: "An error occurred while summarizing the document.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  const ingest = async (fileDetail?: URLDetailContent) => {
    try {
      setIsLoading(true);
    const response = await fetch(
        "/api/retrieval/ingest/supabase/cohere", 
      {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileDetail }),
      });
  
      if (response.status === 200) {
        toast({
          title: "Uploaded successful!",
          variant: "success",
        });
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  const callLLM = async ({
    input,
    fileDetail,
  }: {
    input?: string;
    fileDetail?: URLDetailContent;
  }) => {
    if (fileDetail && fileDetail.content) {
      try {
        setIsLoading(true);     
        await ingest(fileDetail) 
      } catch (error) {
        console.error("Error summarizing document:", error);
        toast({
          title: "An error occurred while summarizing the document.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
        setImageFile(undefined);
        setTemporaryURLInput("");
        setInput("");
        await getSummary(fileDetail) 
      }
    }
  };

  const manageTemporaryBlobUrl = (
    file: File,
    action: () => Promise<void>,
  ): Promise<void> => {
    let tempUrl: string;
    if (isImageFileType(file.type)) {
      tempUrl = URL.createObjectURL(file);
      setTemporaryBlobUrl(tempUrl);
    }

    return action().finally(() => {
      if (isImageFileType(file.type)) {
        URL.revokeObjectURL(tempUrl);
        setTemporaryBlobUrl(undefined);
      }
    });
  };

  const doSubmitFile = async (fileInput: FileWrap) => {
    try {
      await manageTemporaryBlobUrl(fileInput.file, async () => {
        const fileDetail = await getDetailContentFromFile(fileInput);
        
        if (isImageFileType(fileInput.file.type)) {
          setImageFile(fileDetail);
        } else {
          latestFileDetail.current = fileDetail
          callLLM({ fileDetail})
        }
      });
    } catch (error) {
      showError(Locale.Upload.Failed((error as Error).message));
    }
  };

  //console.log("latestFileDetail: ", latestFileDetail)
  const autoFocus = isDesktop; // wont auto focus on mobile screen

  const removeImage = () => {
    setImageFile(undefined);
  };

  const previewImage = temporaryBlobUrl || imageFile?.url;
  const isUploadingImage = temporaryBlobUrl !== undefined;

  const checkExtension = (extension: string) => {
    if (!ALLOWED_DOCUMENT_EXTENSIONS.includes(extension)) {
      return Locale.Upload.Invalid(ALLOWED_DOCUMENT_EXTENSIONS.join(","));
    }
    if (
      !isVisionModel("gpt-4.1-mini") && // TODO: add image caption, currently disabled.
      ALLOWED_IMAGE_EXTENSIONS.includes(extension)
    ) {
      return Locale.Upload.ModelDoesNotSupportImages(
        ALLOWED_TEXT_EXTENSIONS.join(","),
      );
    }
    return null;
  };

  const ingestForm = showIngestForm && 
  <FileUploader
    config={{
      inputId: "document-uploader",
      allowedExtensions: ALLOWED_DOCUMENT_EXTENSIONS,
      checkExtension,
      fileSizeLimit: DOCUMENT_FILE_SIZE_LIMIT,
      disabled: isLoading,
    }}
    onUpload={doSubmitFile}
    onError={showError}
  />

  return (
    <>
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className={`flex flex-1 relative w-[${widthValue}] max-w-[660px] 2xl:max-w-[980px] rounded-t-xl bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg shadow-inner px-4 pb-2 pt-3 sm:pb-3 sm:pt-4`}
      >
        {previewImage && (
          <div className="absolute top-[-4px] left-[12px] w-[50px] h-[50px] rounded-xl cursor-pointer">
            <ImagePreview
              url={previewImage}
              uploading={isUploadingImage}
              onRemove={removeImage}
            />
          </div>
        )}
        <LanguageSelection />
        <div className="flex flex-col w-[1.5rem] lg:w-[2.4rem] items-center justify-center">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={handleNewChatButtonClick}
                className={cn(
                  buttonVariants({ size: 'newChat', variant: 'secondary' }),
                  'inset-y-0 my-auto mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-secondary px-0]'
                )}
              >
                <IconPlus  />
                <span className="sr-only">New Chat</span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="right">New Chat</TooltipContent>
          </Tooltip> 
        </div>
        <Textarea
          className={cn("min-h-[64px] w-full pl-2 resize-none bg-transparent text-white focus-within:outline-none",
            {
              "pt-16": previewImage,
            },
          )}
          ref={inputRef}
          tabIndex={0}
          required
          rows={2}
          autoFocus={autoFocus}
          placeholder="Send a message..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              formRef.current?.requestSubmit();
              e.preventDefault();
            }
          }}
          spellCheck={false}
        />
        <div className="flex flex-col w-[4.5rem] items-center justify-center space-x-1">
          {ingestForm}
          <button
            className={clsx(
              "absolute right-2 my-auto flex h-8 w-8 items-center justify-center rounded-md transition-all",
              disabled
                ? "cursor-not-allowed bg-indigo-600"
                : "bg-green-600 hover:bg-green-700",
            )}
            disabled={disabled}
          >
            {isLoading ? (
              <LoadingCircle />
            ) : (              
              <ArrowUp size={18}
                className={clsx(
                  "",
                  input.length === 0 ? "text-gray-300" : "text-white",
                )}
              />
            )}
            <span className="sr-only">Send message</span>
          </button>
        </div>
      </form>
      <p className="text-center text-xs text-gray-400"></p>
    </>
  );
}
