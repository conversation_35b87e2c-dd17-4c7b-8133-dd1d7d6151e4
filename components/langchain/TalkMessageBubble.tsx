import type { UIMessage } from '@ai-sdk/react';
import Image from "next/image";
import React, { JSX, useState, useRef } from "react";
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronUp, ChevronDown, Search } from "lucide-react";
import { ChatMessageActions } from '@/components/chat-message-actions'

export function ChatMessageBubble(props: { message: any, aiEmoji?: string, sources: any[], path: string }) {
  const [expanded, setExpanded] = useState(false)
  const codeBlockRef = useRef<SyntaxHighlighter>(null);
  const colorClassName =
    props.message.role === "user" ? "bg-gradient-to-r from-amber-400/50 to-amber-300/70 ... drop-shadow-lg shadow-inner bg-opacity-70 text-secondary-text" : "bg-secondary/30 text-secondary-text";
  const alignmentClassName =
    props.message.role === "user" ? "ml-auto" : "mr-auto";
  const prefix = props.message.role === "user" ? "🧑" : props.aiEmoji;
  //console.log("props.sources: ", props.sources)
  const parsedContent = props.message.content
  .replace(/\*\*\*(.*?)\*\*\*/g, '$1')  // Triple asterisks
  .replace(/\*\*(.*?)\*\*/g, '$1')  // Double asterisks
  .replace(/\*(.*?)\*/g, '$1');  // Single asterisk
  return (
    <>
      <div
        className={`${alignmentClassName} group max-w-full md:max-w-[80%] mb-2 flex flex-col`}
      >
        <div className="mr-0">
          {/*{prefix}*/}
        </div>
        <div className={`${colorClassName} whitespace-pre-wrap flex flex-col rounded-lg px-3 py-1`}>
          {/*<span>{props.message.content}</span>*/}
          <MemoizedReactMarkdown
            className="prose max-w-[100ch] break-words dark:prose-invert prose-p:leading-relaxed prose-pre:m-0 prose-h3:my-0 prose-h4:my-0 prose-code:p-6 prose-ol:whitespace-normal prose-ul:whitespace-normal prose-li:py-0 prose-prose-a:text-pink-600 hover:prose-a:text-pink-500"
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              p({ children }) {
                const childArray = React.Children.toArray(children);
            
                const remainingContent = childArray.reduce<{
                  images: React.ReactNode[];
                  text: (string | JSX.Element)[];
                }>((acc, child) => {
                  if (typeof child === 'string') {
                    acc.text.push(child);
                  } else if (React.isValidElement(child)) {
                    if (child.type === 'img') {
                      acc.images.push(child);
                    } else if (child.type === 'a') {
                      acc.text.push(
                        <a href={(child as React.ReactElement<any>).props.href} key={acc.text.length}>
                          {(child as React.ReactElement<any>).props.children}
                        </a>
                      );
                    } else {
                      acc.text.push(child);
                    }
                  }
                  return acc;
                }, { images: [], text: [] });

                const images = childArray.filter(
                  (child) =>
                    React.isValidElement(child) &&
                    child.type === "img" &&
                    typeof child !== "string" // Filter out string nodes
                );
                
                const renderedImages = images.map((image, index) => {
                  if (typeof image === 'object' && image !== null && 'type' in image && typeof image.type === 'function') {
                    //@ts-ignore
                    const { src, alt } = image.props;
                    return (
                      <div key={index}>
                        <Image
                          src={src as string}
                          alt={alt as string}
                          style={{
                            maxWidth: '160px',
                            maxHeight: '160px',
                          }}
                          className="
                          w-full h-auto
                          object-cover
                          cursor-pointer
                          hover:scale-110
                          transition
                          rounded-lg
                          border-1
                          m-0
                          "
                        />
                      </div>
                    );
                  }
                  return null;
                });
         
                return (
                  <div>
                    {images.length > 0 && (
                      <div className={`grid grid-cols-${images.length === 2 ? 2 : images.length <= 1 ? 1 : 3} gap-2`}
                      >
                        {renderedImages}
                      </div>
                    )}
                    {/* Displaying text content */}
                    <p className="mt-1 mb-2 text-justify last:mb-0">{remainingContent.text}</p>
                  </div>
                );
              },
              h1({ children }) {
                return <h1 className="my-0">{children}</h1>;
              },
              h2({ children }) {
                return <h2 className="my-0">{children}</h2>;
              },
              h3({ children }) {
                return <h3 className="my-0">{children}</h3>;
              },
              h4({ children }) {
                return <h4 className="my-0">{children}</h4>;
              }, 
              h5({ children }) {
                return <h5 className="my-0">{children}</h5>;
              }, 
              h6({ children }) {
                return <h6 className="my-0">{children}</h6>;
              }, 
              ol({ children }) {
                return <ol className="my-0">{children}</ol>;
              },
              ul({ children }) {
                return <ul className="my-0">{children}</ul>;
              },
              li({ children }) {
                return <li className="my-0">{children}</li>;
              },          
              code(props) {
                const {children, className, node, ...rest} = props
                const match = /language-(\w+)/.exec(className || '')
                return match ? (
                  <SyntaxHighlighter
                    {...rest}
                    ref={codeBlockRef}
                    PreTag="div"
                    children={String(children).replace(/\n$/, '')}
                    language={match[1]}
                    style={dark}
                  />
                ) : (
                  <code {...rest} className={className}>
                    {children}
                  </code>
                );
              }
            }}
          >
            {parsedContent}
          </MemoizedReactMarkdown>
        </div>
        <ChatMessageActions message={props.message} path={props.path} className={"flex-row mt-1"}/>
      </div>
      {props.sources && props.sources.length > 0  ? 
        <div
          className={`flex flex-col items-center bg-green-600 rounded-md p-2 max-w-[80%] ml-auto mb-1 whitespace-pre-wrap cursor-pointer`}
        >  
       
          <div className={`text-left ${expanded ? "w-full space-y-2" : ""}`} onClick={(e) => setExpanded(!expanded)}>
            <code className={`flex rounded-lg px-2 mr-auto bg-gradient-to-r from-amber-300/50 to-amber-200/70 ... drop-shadow-lg shadow-inner}`}>
              <h2 className="flex flex-row items-center py-1">
                <Search size={16} /> Sources
              </h2>
              <span className={expanded ? "hidden" : "flex flex-row items-center cursor-pointer"}><ChevronDown /></span>
              <span className={expanded ? "flex flex-row items-center cursor-pointer" : "hidden"}><ChevronUp /></span>
            </code>
            <ScrollArea className={expanded ? 'h-[360px]': "h-[0px]"}>
              <div className={`max-h-[0px] transition-[max-height] ease-in-out ${expanded ? "max-h-[360px]" : ""}`}>
                <div className={`rounded-md p-4 bg-gradient-to-r from-lime-200 to-amber-100 ... drop-shadow-lg shadow-inner max-w-0 ${expanded ? "max-w-full" : "transition-[max-width] delay-100"}`}>
                  <code className={`mt-1 mr-2 px-2 py-1 rounded-lg text-xs text-black opacity-0 max-h-[100px] transition ease-in-out delay-150 ${expanded ? "opacity-100" : ""}`}>
                    {props.sources?.map((source, i) => (
                      <div className="mt-2" key={"source:" + i}>
                        {i + 1}. &quot;{source.pageContent}&quot;{
                          source.metadata?.loc?.lines !== undefined
                            ? <div><br/>Lines {source.metadata?.loc?.lines?.from} to {source.metadata?.loc?.lines?.to}</div>
                            : ""
                          }
                      </div>
                    ))}
                  </code>
                </div>
              </div>
            </ScrollArea>
          </div>   
        </div> : ""}
    </>
  );
}