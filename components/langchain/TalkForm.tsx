import React, { 
    useEffect, 
    useState, 
    useRef, 
    type ChangeEvent, 
    type FormEvent 
} from 'react';
import { useRouter } from "next/navigation";
import { useCompletion, type UIMessage, type UseChatHelpers } from '@ai-sdk/react';
import { nanoid } from '@/lib/utils'
import { useToast } from "@/components/ui/use-toast";
import { useDebouncedCallback } from "use-debounce";
import {
  getBase64,
  getDetailContentFromFile,
  isImageFileType,
} from "@/app/client/fetch/sfile";
import { URLDetail, URLDetailContent, UrlDetailType, isURL } from "@/app/client/fetch/surl";
import { FileWrap } from "@/utils/file";
import {
    ALLOWED_DOCUMENT_EXTENSIONS,
    ALLOWED_IMAGE_EXTENSIONS,
    ALLOWED_TEXT_EXTENSIONS,
    DOCUMENT_FILE_SIZE_LIMIT,
    DOCUMENT_FILE_UPLOAD_LIMIT,
} from "@/constants";
import Locale from "@/locales";
import FileUploader from "@/components/ui/file-uploader";
import ImagePreview from "@/components/ui/image-preview";
import { isVisionModel } from "@/app/client/platforms/llm";

import { useMediaQuery } from "@/hooks/use-media-query"
import Textarea from "react-textarea-autosize";
import { cn } from '@/lib/utils'
import { LoadingCircle } from '@/components/loaders'
import { IconPlus } from '@/components/ui/icons'
import { ArrowUp, Paperclip } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { Checkbox } from "@/components/ui/checkbox";
import { buttonVariants } from '@/components/ui/button'
import { useLlamaParseJob, StatusType } from '@/hooks/useLlamaParseJob';
import { LanguageSelection } from '@/components/ui/ai-language'
import { AIVoice } from '@/components/ui/ai-voice'
import { type Language } from "@/components/ui/ai-language"
//import Microphone from "@/components/Microphone";
import { LOBETTS } from "@/components/lobe-tts";



interface ChatFormProps {
  messages: any[]
  sendMessage: (e: FormEvent<HTMLFormElement>) => Promise<void>;
  setMessages: (messages: any[]) => void;
  input: string;
  setInput: (value: string) => void;
  handleInputChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  chatEndpointIsLoading: boolean;
  intermediateStepsLoading: boolean;
  setIntermediateStepsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  disabled: boolean;
  companionId: string;
  threadId: string;
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  path: string;
  formWidth?: string;
  endpoint: string;
  showIngestForm?: boolean;
  setTemporaryURLInput: (url: string) => void;
  showIntermediateStepsToggle?: boolean
  showIntermediateSteps?: boolean 
  setShowIntermediateSteps: React.Dispatch<React.SetStateAction<boolean>>;
  uploadedFilesNum?: number;
  aiLanguage?: Language;
  userId: string;
}

export function ChatForm({ 
  messages,
  sendMessage,
  setMessages, 
  input, 
  setInput, 
  handleInputChange,
  chatEndpointIsLoading,
  intermediateStepsLoading,
  setIntermediateStepsLoading,
  disabled, 
  companionId,
  threadId,
  existingThread,
  path,
  formWidth,
  endpoint,
  showIngestForm,
  setTemporaryURLInput,
  showIntermediateStepsToggle,
  showIntermediateSteps,
  setShowIntermediateSteps,
  uploadedFilesNum,
  aiLanguage,
  userId,
}: ChatFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [imageFile, setImageFile] = useState<URLDetail>();
  const [temporaryBlobUrl, setTemporaryBlobUrl] = useState<string>();
  const { 
    jobId, setJobId, 
    jobStatus, setJobStatus,
    jobResult, setJobResult,
    uploadFile, setUploadFile,
    triggerCheck, setTriggerCheck, 
  } = useLlamaParseJob();
  const [stopTranscript, setStopTranscript] = useState<(() => void) | null>(null);

  let widthValue = '100%'
  if (formWidth) {
    widthValue = formWidth
  }

  /*useEffect(() => {
    if (!transcript) return;
    //onSubmit(transcript)
    if (input.trim() === "") {
      setInput(transcript);
    } else {
      //@ts-ignore
      setInput((prevInput: string) => prevInput + transcript);
    }
  }, [transcript]);*/

  const intemediateStepsToggle = showIntermediateStepsToggle && (
    <div className="flex items-center">
      <Checkbox
        id="show_intermediate_steps"
        checked={showIntermediateSteps}
        onCheckedChange={(checked) => {
          if (checked) {
            setShowIntermediateSteps(true);
          } else {
            setShowIntermediateSteps(false);
          }
        }}
        className="border-secondary/50 mx-1"
      />
      <label
        htmlFor="show_intermediate_steps"
        className="text-xs text-secondary-text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        Show intermediate steps
      </label>
    </div>
  );

  const handleNewChatButtonClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    if (path) {
      router.refresh();
      router.push(path);
    } else {
      router.push("/");
    }
  };


  const showError = (errMsg: string) => {
    toast({
      title: errMsg,
      variant: "destructive",
    });
  };

  const getSummary = async (fileDetail: URLDetailContent) => {
    if (!fileDetail) return
    try {  
      const response = await fetch(`/api/summary/${companionId}/groq`, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          threadId,
          existingThread,
          fileDetail,
          aiLanguage,
          path,
        }),
      });
  
      if (response.status === 200) {
        const body = response.body;
        if (!body) {
          throw new Error("Response body is null");
        }
  

        const reader = body.getReader();
        let completion = '';

        // Read chunks of data from the response body
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          completion += new TextDecoder().decode(value);
        }
  
        const newMessages: any[] = [
          {
            id: nanoid(),
            content: `${fileDetail?.url} \n : ${fileDetail?.type} • ${((fileDetail?.size ?? 0) / 1024).toFixed(2)} KB`,
            role: "user"          
          },
          {
            id: nanoid(),
            content: completion,
            role: "assistant"
          }
        ];
  
        setMessages([...messages, ...newMessages]);
        //await ingest(fileDetail, completion) //disable when use ingestLlamaParseEdgeData
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while summarizing the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error summarizing document:", error);
      toast({
        title: "An error occurred while summarizing the document.",
        variant: "destructive",
      });
    }
  }
  
  const createLlamaParseJob = async (
    formData: FormData
  ) => {
    try {
      setIntermediateStepsLoading(true);
      const response = await fetch( 
        "/api/llama-parse/backend/createJob",
      {
        method: "POST",
        body: formData,
      });
      if (response.status === 403) {
        toast({
          title: "Server is busy, please retry it later.",
          variant: "destructive",
        });
      } else if (response.status === 200) {
        const job = await response.json();
        const jobId = job?.job_id!
        console.log('jobId:', jobId);
        setJobId(jobId);
        
        setJobStatus({ id: jobId, status: 'PENDING' });
        if (jobId) {
          setTimeout(() => {
            setTriggerCheck(true);
          }, 5000); // 5 seconds delay
        }
      } else {
        const json = await response.json();
        //if (json.error) {
        if (json.error.message) {
          toast({
            //title: json.error,
            title: json.error.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      setIntermediateStepsLoading(false);
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    } finally {
      if (jobStatus?.status === "SUCCESS") {
        setIntermediateStepsLoading(false);
      }
    }
  }

  const ingestLlamaParseData = async (
    formData: FormData
  ) => {
    try {
      const response = await fetch( 
        "/api/llama-parse/backend",
      {
        method: "POST",
        body: formData,
      });
      if (response.status === 200) {
        type JobResponse = {
          id_: string;
          text: string;
        }
        const jobResponse: JobResponse[] = await response.json();
        console.log('jobResponse:', jobResponse);
        
        // Extracting id and text from each object in jobResponse array
        jobResponse.forEach((item: JobResponse) => {
          const id = item.id_;
          const text = item.text;
          console.log('ID:', id);
          console.log('Text:', text);
          setJobResult(text)
        });
      } else {
        const json = await response.json();
        //if (json.error) {
        if (json.error.message) {
          toast({
            //title: json.error,
            title: json.error.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    }
  }

  const ingestLlamaParseEdgeData = async (
    formData: FormData,
    thisFile: URLDetailContent
  ) => {
    try {
      const response = await fetch( 
        "/api/llama-parse/engine",
      {
        method: "POST",
        body: formData,
      });
      if (response.status === 200) {
        let responseData;
        try {
          responseData = await response.json();
        } catch (jsonError) {
          console.error("Error parsing JSON response:", jsonError);
          toast({
            title: "Failed to parse server response.",
            variant: "destructive",
          });
          return;
        }
  
        const { docsContent } = responseData;

        if (docsContent && Array.isArray(docsContent) && docsContent.length > 0) {
          const contentToSummarize = {
            ...thisFile,
            content: docsContent[0],
          };
          console.log("fileDetail with content: ", contentToSummarize);
          
          if (contentToSummarize) {
            await callLLM({ fileDetail: contentToSummarize });
          } else {
            console.warn("Upload file is not set.");
          }
        } else {
          toast({
            title: "Received empty or invalid document content.",
            variant: "destructive",
          });
        }
        toast({
          title: "Uploaded successful!",
          variant: "success",
        });
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error || "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    }
  }

  const ingestUnstructuredData = async (
    formData: FormData
  ) => {
    try {
      setIntermediateStepsLoading(true);
      const response = await fetch(
        //"/api/unstructured/ingest/supabase/cohere", 
        //"/api/llama-parse/uploadFile", 
        "/api/llama-parse/backend",
      {
        method: "POST",
        body: formData,
      });
      if (response.status === 200) {
        // Unstructured.io
        /*toast({
          title: "Uploaded successful!",
          variant: "success",
        });*/
        // Unstructured.io
        // LlamaParse
        const responseData = await response.json();
        console.log("Response data:", responseData);
        const { id, status } = responseData;
        setJobId(id)

        setJobStatus({ id, status })
        if (id && status === "SUCCESS") {
          toast({
            title: "Uploaded successful!",
            variant: "success",
          });
        } else if (id && status === "PENDING") {
          setTimeout(() => {
            setTriggerCheck(true);
          }, 10000); // 10 seconds delay
        }
        // LlamaParse
      } else {
        const json = await response.json();
        //if (json.error) {
        if (json.error.message) {
          toast({
            //title: json.error,
            title: json.error.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      setIntermediateStepsLoading(false);
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    } finally {
      if (jobStatus?.status === "SUCCESS") {
        setIntermediateStepsLoading(false);
      }
    }
  }

  const ingest = async (
    fileDetail: URLDetailContent, 
    fileSummary: string, 
  ) => {
    try {
      const response = await fetch(
        "/api/retrieval/ingest/supabase/cohere", 
      {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileDetail, fileSummary, path }),
      });
  
      if (response.status === 200) {
        toast({
          title: "Uploaded successful!",
          variant: "success",
        });
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    }
  }

  const callLLM = async ({
    input,
    fileDetail,
  }: {
    input?: string;
    fileDetail?: URLDetailContent;
  }) => {
    if (fileDetail && fileDetail.content) {
      try {
        setIntermediateStepsLoading(true);
        await getSummary(fileDetail)
        
      } catch (error) {
        console.error("Error summarizing document:", error);
        toast({
          title: "An error occurred while summarizing the document.",
          variant: "destructive",
        });
      } finally {
        setImageFile(undefined);
        setTemporaryURLInput("");
        setInput("");
        setIntermediateStepsLoading(false);
        router.refresh()
      }
    }
  };

  const manageTemporaryBlobUrl = async (
    file: File,
    action: () => Promise<void>,
  ): Promise<void> => {
    let tempUrl: string;
    if (isImageFileType(file.type)) {
      tempUrl = URL.createObjectURL(file);
      setTemporaryBlobUrl(tempUrl);
    }

    return action().finally(() => {
      if (isImageFileType(file.type)) {
        URL.revokeObjectURL(tempUrl);
        setTemporaryBlobUrl(undefined);
      }
    });
  };

  const doSubmitFile = async (fileInput: FileWrap) => {
    console.log("fileInput", fileInput)
    try {
      await manageTemporaryBlobUrl(fileInput.file, async () => {
        // For file.extension eq to "pdf", it will return pdfBase64
        //const fileDetail = await getDetailContentFromFile(fileInput);
        
        if (isImageFileType(fileInput.file.type)) {
          const fileDetail = await getDetailContentFromFile(fileInput);
          setImageFile(fileDetail);
          setTemporaryURLInput(fileDetail.url)
        } else {
          // callLLM({ fileDetail })
          // Instead of callLLM, use unstructured.io or LlmaParse to process file
          const fileBlob: Blob = await getBase64(fileInput);
          console.log("fileBlob: ", fileBlob)
          if (fileBlob !== null) {
            // Unstructure.io
            // Poor Table recognization, Time consuming
            /*const formData = new FormData();
            formData.append("type", fileInput.file.type);
            formData.append("size", fileInput.size.toString());
            formData.append("file", fileBlob, fileInput.file.name);
            formData.append("aiLanguage", aiLanguage?.label ?? 'English');
            formData.append("path", path);*/
            // Unstructure.io

            // Instead of callLLM, use LlamaParse to process file
            // File upload seems to not get the correct file content.
            let language: string = 'en'
            if (aiLanguage && aiLanguage?.value === "zh-TW") {
              language = 'ch_tra'
            }
            const fileType = fileInput.file.type
            const instruction = `The document titled "多元情境九型人格報告" is a personal enneagram report, it contains many tables. Answer questions using the information in this article and be precise.`
            const formData = new FormData();
            //formData.append("file", fileInput.file, fileInput.file.name);
            formData.append("user_id", userId);
            formData.append("file", fileBlob, fileInput.file.name);
            formData.append("name", fileInput.file.name);
            formData.append("type", fileInput.file.type);
            formData.append("size", fileInput.size.toString());
            formData.append("language", language);
            formData.append("parsing_instruction", instruction)
            formData.append("result_type", "markdown")
            formData.append("path", path);
            const thisFile = {
              url: fileInput.file.name, 
              size: fileInput.size, 
              type: fileInput.file.type,
            } as URLDetailContent
            //setUploadFile(thisFile)

            console.log("formData: ", formData)
            if (formData !== null) {
              //await ingestLlamaParseData(formData)
              //await createLlamaParseJob(formData)
              await ingestLlamaParseEdgeData(formData, thisFile)
            }
          
          }
        }
      });
    } catch (error) {
      showError(Locale.Upload.Failed((error as Error).message));
    }
  };

  const autoFocus = isDesktop; // wont auto focus on mobile screen

  const removeImage = () => {
    setImageFile(undefined);
  };

  const previewImage = temporaryBlobUrl || imageFile?.url;
  const isUploadingImage = temporaryBlobUrl !== undefined;

  const checkExtension = (extension: string) => {
    if (!ALLOWED_DOCUMENT_EXTENSIONS.includes(extension)) {
      return Locale.Upload.Invalid(ALLOWED_DOCUMENT_EXTENSIONS.join(","));
    }
    if (
      isVisionModel("gpt-4.1-mini") && // TODO: add image caption, currently disabled.
      ALLOWED_IMAGE_EXTENSIONS.includes(extension)
    ) {
      return Locale.Upload.ModelDoesNotSupportImages(
        ALLOWED_TEXT_EXTENSIONS.join(","),
      );
    }
    return null;
  };

  const ingestForm = showIngestForm &&
  <FileUploader
    config={{
      inputId: "document-uploader",
      allowedExtensions: ALLOWED_DOCUMENT_EXTENSIONS,
      checkExtension,
      fileSizeLimit: DOCUMENT_FILE_SIZE_LIMIT,
      disabled: chatEndpointIsLoading || intermediateStepsLoading || (uploadedFilesNum ?? 0) >= DOCUMENT_FILE_UPLOAD_LIMIT,
    }}
    onUpload={doSubmitFile}
    onError={showError}
  />

  return (
    <>
      <form
        ref={formRef}
        onSubmit={sendMessage}
        className={`flex relative w-[${widthValue}] max-w-[660px] lg:max-w-[760px] 2xl:max-w-[980px] rounded-t-xl bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 px-2 pb-2 pt-3 shadow-lg sm:pt-4`}
      >
        {endpoint === "/api/retrieval/agents/supabase/new" && (
          <div className="absolute right-4 top-[-20px]">
            {intemediateStepsToggle}
          </div>
        )}
        {previewImage && (
          <div className="absolute top-[-32px] left-[8px] w-[60px] h-[60px] rounded-xl cursor-pointer">
            <ImagePreview
              url={previewImage}
              uploading={isUploadingImage}
              onRemove={removeImage}
            />
          </div>
        )}
        <div className="flex flex-rows justify-between items-center">
          <LanguageSelection />
          <AIVoice />        
        </div>
        <div className="flex flex-col w-[2rem] lg:w-[2.4rem] items-center justify-center">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={chatEndpointIsLoading || intermediateStepsLoading}
                onClick={handleNewChatButtonClick}
                className={cn(
                  buttonVariants({ size: 'newChat', variant: 'secondary' }),
                  'inset-y-0 my-auto mr-2 flex h-7 w-7 items-center justify-center rounded-full bg-secondary px-0]'
                )}
              >
                <IconPlus  />
                <span className="sr-only">New Chat</span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="right">New Chat</TooltipContent>
          </Tooltip> 
        </div>
        <Textarea
          className={cn("min-h-[64px] w-full pl-2 resize-none bg-transparent text-white focus-within:outline-none",
            {
              "pt-4": previewImage,
            },
          )}
          ref={inputRef}
          tabIndex={0}
          required
          rows={2}
          autoFocus={autoFocus}
          placeholder={
            jobStatus && jobStatus?.status?.length > 0
              ? jobStatus?.status
              : "Send a message..."
          }
          value={input}
          onChange={handleInputChange}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey && !disabled) {
              formRef.current?.requestSubmit();
              e.preventDefault();
            }
            if (e.key === "Backspace" && stopTranscript) {
              stopTranscript(); 
            }
          }}
          spellCheck={false}
        />
        <div className="flex flex-col w-[4.5rem] items-center justify-center space-x-1">
          {ingestForm}
          {!chatEndpointIsLoading && !intermediateStepsLoading && (
            <LOBETTS 
              input={input}
              setInput={setInput} 
              setStopTranscript={setStopTranscript} 
              className={cn("", input.length === 0 ? "" : "bottom-2")} 
            />
          )}
          {(chatEndpointIsLoading || intermediateStepsLoading) && (
            <div className="absolute right-2 my-auto flex size-8 items-center justify-center rounded-md transition-all bg-indigo-600">
              <LoadingCircle />
            </div>
          )}
          {input.length > 0 && !chatEndpointIsLoading && !intermediateStepsLoading && (
            <button
              className={cn(
                "absolute right-2 my-auto flex h-8 w-8 items-center justify-center rounded-md transition-all",
                disabled
                  ? "cursor-not-allowed bg-indigo-600"
                  : "top-2 bg-green-600 hover:bg-green-500",
              )}
              disabled={disabled}
            >
              <ArrowUp size={18}
                className={cn(
                  "",
                  input.length === 0 ? "text-gray-300" : "text-white",
                )}
              />
              <span className="sr-only">Send message</span>
            </button>
          )}
        </div>
      </form>
      <p className="text-center text-xs text-gray-400"></p>
    </>
  );
}