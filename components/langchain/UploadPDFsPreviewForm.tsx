"use client";

import Script from 'next/script';
import { useEffect, useState, useRef, type FormEvent, ChangeEvent } from "react";
import { useCompletion, type UseChatHelpers } from '@ai-sdk/react';
import { useToast } from "@/components/ui/use-toast";
import ImagePreview from "@/components/ui/image-preview";
import { IconSpinner } from '@/components/ui/icons';


export function UploadPDFsPreviewForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [document, setDocument] = useState("");
  const [summary, setSummary] = useState("");
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [pdfjs, setPdfjs] = useState(null);

 /* useEffect(() => {
    const initPdfJs = async () => {
      //if (window.pdfjsLib) {
        // PDF.js is already loaded
        const pdfjsLib = window.pdfjsLib as typeof import('pdfjs-dist/types/src/pdf');
        const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.min.mjs');
        pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

        setPdfjs(pdfjsLib);
      //} else {
      //  console.error('PDF.js library not loaded');
      //}
    };

    initPdfJs();
  }, []);*/

  const ingest = async (inputDocument: string) => {
    setIsLoading(true);
  
    try {
      const response = await fetch("/api/retrieval/ingest/supabase/cohere", {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: inputDocument }),
      });
  
      if (response.status === 200) {
        toast({
          title: "Uploaded successful!",
          variant: "destructive",
        });
      } else {
        const json = await response.json();
        if (json.error) {
          toast({
            title: json.error,
            variant: "destructive",
          });
        } else {
          toast({
            title: "An error occurred while ingesting the document.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error ingesting document:", error);
      toast({
        title: "An error occurred while ingesting the document.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const {
    complete, 
    completion
  } = useCompletion({
    api: `/api/summary/ded2ca7f-7f17-4223-92bd-1a35c6c82337/octoai`,
    body: {
      threadId: "a08190da-dd72-4ce7-8705-a2ce8def8517",
    },
    /*onResponse: res => {
      if (res.status === 429) {
        toast({
          title: "You have reached your request limit for the day.",
          variant: "destructive",
        });
      }
    },*/
    onFinish(_prompt, completion) {
      setSummary(completion)
      console.log("completion: ", completion);

      setIsLoading(false);

    },
  });

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    try {
      if (file && file.type !== "application/pdf") {
        console.error(file.name, "is not a PDF file.");
        const errorMsg = `{file.name}, is not a PDF file.`
        toast({
          title: errorMsg,
          variant: "destructive",
        });
        return;
      }

      /*if (pdfjs) {
        const fileReader = new FileReader();
        fileReader.onload = async (event) => {
          const pdfData = new Uint8Array(event.target.result);
          const doc = await pdfjs.getDocument({ data: pdfData }).promise;
          let pdfText = '';

          for (let pageNum = 1; pageNum <= doc.numPages; pageNum++) {
            const page = await doc.getPage(pageNum);
            const content = await page.getTextContent();
            content.items.forEach((item) => {
              pdfText += item.str + '\n';
            });
          }

          // Display text content
          setIsLoading(true);
          //complete(pdfText);
          setDocument(pdfText);
          ingest(pdfText);
        };

        fileReader.readAsArrayBuffer(file);
      } else {
        console.error('PDF.js library not initialized');
      }*/
    } catch (error) {
      toast({
        title: "Something went wrong.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  

  useEffect(() => {
    console.log("Document: ", document)
  }, [document]);
  

  return (
    <div
      className={`flex flex-col w-full relative h-[55vh] items-center py-2 px-12`}
    >
      <div className="flex flex-row w-full items-center justify-center mb-1">
        <input className="hidden" type="file" ref={fileInputRef} id="file-input" onChange={handleFileChange} />
        <button
          onClick={() => fileInputRef.current?.click()}
          className="rounded gap-2 text-white text-sm bg-gradient-to-tr from-orange-400 to-orange-500 px-2 py-1 pointer-events-auto z-30 flex items-center"
        >
          {isLoading ? (
            <IconSpinner />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-6"
            >
              <path
                fillRule="evenodd"
                d="M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <span>Upload PDF</span>          
        </button>
      </div>
      <div className="flex flex-row gap-5 mt-2 mb-4 w-full h-[55rem] items-center overflow-auto">
        <div className="w-1/2 h-full overflow-auto">
        {(document?.length > 0 && <h2 className="text-center mb-1 text-base text-secondary-text font-bold">Document</h2>
        )}
          <div className="text-justify text-sm text-secondary-text p-2" id="pdfContent">{document}</div>
        </div>

        <div className="w-1/2 h-full overflow-auto">
          {(summary?.length > 0 && <h2 className="text-left mb-4 text-base text-secondary-text font-bold">
            Summary
          </h2>
          )}
          {isLoading && (
            <p className="flex flex-col items-center text-left text-sm text-secondary-text"><IconSpinner className="mr-2 animate-spin" /></p>
          )}
          {!isLoading && (
            <>
              <div className="text-justify text-secondary-text text-sm overflow-auto p-2">{completion}</div>
            </>
          )}
        </div>
      </div>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.min.mjs"
        type="module"
        strategy="beforeInteractive"
        crossOrigin="anonymous"
      />
    </div>
  );
}