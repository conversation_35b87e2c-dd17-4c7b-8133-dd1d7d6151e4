import Image from "next/image";
import React, { JSX, useRef, useState } from "react";
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
//import type { UIMessage } from '@ai-sdk/react';
import { MemoizedReactMarkdown } from '@/components/markdown'
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronUp, ChevronDown, GanttChart } from "lucide-react";

export function IntermediateStep(props: { message: any }) {
  const parsedInput = JSON.parse(props.message.content);
  console.log("parsedInput: ", parsedInput)
  const action = parsedInput.action;
  const raw = parsedInput.observation;
  const observation = raw.replace(/^"|"$/g, '');
  const codeBlockRef = useRef<SyntaxHighlighter>(null);
  const [expanded, setExpanded] = useState(false)

  return (
    <div
      className={`ml-auto bg-gradient-to-r from-indigo-500 from-10% via-sky-500 via-30% to-emerald-500 to-90% rounded px-4 max-w-[90%] whitespace-pre-wrap flex flex-col cursor-pointer ${expanded ? "py-4" : "py-2"}`}
    >
      <div className={`flex flex-row items-center justify-end text-right ${expanded ? "w-full" : ""}`} onClick={(e) => setExpanded(!expanded)}>
        <code className="flex flex-row mr-2 bg-secondary/30 px-2 py-1 rounded *:hover:text-blue-600 dark:*:hover:text-blue-200">
          <GanttChart /><b>{action.name}</b>
        </code>
        <span className={expanded ? "hidden" : ""}><ChevronDown /></span>
        <span className={expanded ? "" : "hidden"}><ChevronUp /></span>
      </div>
      <ScrollArea className={expanded ? 'h-[360px]': "h-[0px]"}>
       <div className={`max-h-[0px] transition-[max-height] ease-in-out ${expanded ? "max-h-[360px]" : ""}`}>
        <div className={`bg-secondary/30 rounded p-4 mt-1 max-w-0 ${expanded ? "max-w-full" : "transition-[max-width] delay-100"}`}>
          <code className={`opacity-0 max-h-[100px] overflow-auto transition ease-in-out delay-150 ${expanded ? "opacity-100" : ""}`}>
            Tool Input:
            <br></br>
            <br></br>
            {JSON.stringify(action.args)}
          </code>
        </div>
        <div className={`bg-secondary/30 rounded p-4 mt-1 mb-4 max-w-0 ${expanded ? "max-w-full" : "transition-[max-width] delay-100"}`}>
          <div className={`opacity-0 max-h-[260px] overflow-auto transition ease-in-out delay-150 ${expanded ? "opacity-100" : ""}`}>
            {/*observation*/}
            <MemoizedReactMarkdown
              className="prose max-w-[100ch] break-words dark:prose-invert prose-h1:text-sm prose-p:text-sm prose-p:leading-relaxed prose-pre:py-0 prose-pre:px-[0.4rem] prose-pre:bg-white/10 prose-pre:text-pink-600 prose-code:p-2 prose-ol:whitespace-normal prose-ul:whitespace-normal prose-li:py-0 prose-prose-a:text-pink-600 hover:prose-a:text-pink-500"
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={{
                p({ children }) {
                  const childArray = React.Children.toArray(children);
              
                  const remainingContent = childArray.reduce<{
                    images: React.ReactNode[];
                    text: (string | JSX.Element)[];
                  }>((acc, child) => {
                    if (typeof child === 'string') {
                      acc.text.push(child);
                    } else if (React.isValidElement(child)) {
                      if (child.type === 'img') {
                        acc.images.push(child);
                      } else if (child.type === 'a') {
                        acc.text.push(
                          <a href={(child as React.ReactElement<any>).props.href} key={acc.text.length}>
                            {(child as React.ReactElement<any>).props.children}
                          </a>
                        );
                      } else {
                        acc.text.push(child);
                      }
                    }
                    return acc;
                  }, { images: [], text: [] });
  
                  const images = childArray.filter(
                    (child) =>
                      React.isValidElement(child) &&
                      child.type === "img" &&
                      typeof child !== "string" // Filter out string nodes
                  );
                  {console.log("childArray@images-map", images)}
                  
                  const renderedImages = images.map((image, index) => {
                    if (typeof image === 'object' && image !== null && 'type' in image && typeof image.type === 'function') {
                      //@ts-ignore
                      const { src, alt } = image.props;
                      {console.log("childArray@image-src", src)}
                      return (
                        <div key={index}>
                          <Image
                            src={src as string}
                            alt={alt as string}
                            style={{
                              maxWidth: '160px',
                              maxHeight: '160px',
                            }}
                            className="
                            w-full h-auto
                            object-cover
                            cursor-pointer
                            hover:scale-110
                            transition
                            rounded-lg
                            border-1
                            m-0
                            "
                          />
                        </div>
                      );
                    }
                    return null;
                  });
           
                  return (
                    <div>
                      {images.length > 0 && (
                        <div className={`grid grid-cols-${images.length === 2 ? 2 : images.length <= 1 ? 1 : 3} gap-2`}
                        >
                          {renderedImages}
                        </div>
                      )}
                      {/* Displaying text content */}
                      <p className="mt-1 mb-2 text-justify last:mb-0">{remainingContent.text}</p>
                    </div>
                  );
                },
                h1({ children }) {
                  return <h1 className="my-0">{children}</h1>;
                },
                h2({ children }) {
                  return <h2 className="my-0">{children}</h2>;
                },
                h3({ children }) {
                  return <h3 className="my-0">{children}</h3>;
                },
                h4({ children }) {
                  return <h4 className="my-0">{children}</h4>;
                }, 
                h5({ children }) {
                  return <h5 className="my-0">{children}</h5>;
                }, 
                h6({ children }) {
                  return <h6 className="my-0">{children}</h6>;
                }, 
                ol({ children }) {
                  return <ol className="my-0">{children}</ol>;
                },
                ul({ children }) {
                  return <ul className="my-0">{children}</ul>;
                },
                li({ children }) {
                  return <li className="my-0">{children}</li>;
                },          
                code(props) {
                  const {children, className, node, ...rest} = props
                  const match = /language-(\w+)/.exec(className || '')
                  return match ? (
                    <SyntaxHighlighter
                      {...rest}
                      ref={codeBlockRef}
                      PreTag="div"
                      children={String(children).replace(/\n$/, '')}
                      language={match[1]}
                      style={dark}
                    />
                  ) : (
                    <code {...rest} className={className}>
                      {children}
                    </code>
                  );
                }
              }}
            >
              {observation}
            </MemoizedReactMarkdown>
          </div>
        </div>
      </div>       
      </ScrollArea>
    </div>
  );
}