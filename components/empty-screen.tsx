import { UseChatHelpers } from '@ai-sdk/react'

import { Button } from '@/components/ui/button'
import { ExternalLink } from '@/components/external-link'
import { IconArrowRight } from '@/components/ui/icons'

const exampleMessages = [
  {
    heading: '',
    message: ``
  },
  {
    heading: '',
    message: '\n'
  },
  {
    heading: '',
    message: `\n`
  }
]

export interface EmptyScreenProps
{
  introduction?: string
  setInput: (value: string) => void
}

export function EmptyScreen({ setInput, introduction }: EmptyScreenProps) {
  return (
    <div className="mx-auto max-w-4xl px-4 pt-1">
      <div className="rounded-lg border p-8">
        <h1 className="mb-2 text-lg font-semibold">
          Welcome to Comfyminds Lab!
        </h1>
        <p className="leading-normal text-primary-text">
          {introduction}
        </p>
        <div className="mt-4 flex flex-col items-start space-y-2">
          {exampleMessages.map((message, index) => (
            <Button
              key={index}
              variant="link"
              className="h-auto p-0 text-base"
              onClick={() => setInput(message.message)}
            >
              <IconArrowRight className="mr-2 text-muted-foreground" />
              {message.heading}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}
