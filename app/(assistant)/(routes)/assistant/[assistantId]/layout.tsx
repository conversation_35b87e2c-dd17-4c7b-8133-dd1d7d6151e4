import { SidebarHeader } from '@/components/assistant/sidebar-header'
import '@/app/scrollbar-hidden.css'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface AssistantLayoutProps {
  params: Promise<{
    assistantId: string
  }>
}

const AssistantLayout = async (
  props: AssistantLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/assistant/${params.assistantId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.assistantId} path={path} />
      {children}
    </div>
  );
}

export default AssistantLayout;