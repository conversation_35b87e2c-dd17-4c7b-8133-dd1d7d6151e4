"use client";

import axios from 'axios'
import DOMPurify from 'dompurify'
import { userThreadAtom } from "@/atoms";
import { useAtom } from "jotai";
import { useCallback, useRef, useEffect, useState, useTransition } from "react";
import { 
  useCompletion, 
  useAssistant, 
  type UIMessage 
} from '@ai-sdk/react';
import { Assistant } from "openai/resources/beta/assistants";
import { Message as ThreadMessage } from 'openai/resources/beta/threads/messages';
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from 'uuid'
import { cn } from '@/lib/utils'
import { useUser } from "@clerk/nextjs";
import { deleteMessageAction, saveObservationAction } from "@/app/actions";
import { addTodoAction } from "@/app/actions/todoActions"
import { 
    Companion,
    Message as chatMessage,
    Observations,
    ChatSummary,
    Persona,
    Todos,
    Note,
} from '@prisma/client'
import { 
    SentimentData,
    PolarAreaChartType,
    QuestionWithOptions
} from '@/lib/post.types'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button, buttonVariants } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import va from "@vercel/analytics";
import clsx from "clsx";
import toast from "react-hot-toast";
import ObservationNew from '@/components/observation/observation-new'
import PolarAreaChart from '@/components/post/PolarAreaChart'
import TodoList from '@/components/suggestion/TodoList'
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
import NoteNew from '@/components/note/NoteNew'
import ThemeChanger from "@/components/nextly/DarkSwitch";
import { ChatHeader } from "@/components/chat-header";
import { AssistantList } from '@/components/assistant/assistant-list'
import { AssistantHistory } from '@/components/assistant/assistant-history'
import { AssistantDocument } from '@/components/storage/AssistantDocument'
import { TalkForm } from '@/components/talk/talk-form'
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { updatePolarAreaChart } from '@/components/post/Sentiment'
import { markdownToList } from '@/lib/markdownToList-parser'
import { GradientPicker } from "@/components/GradientPicker.tsx"
import { useBackground } from '@/components/providers/BackgroundContext';
//import { ObservationAgent } from '@/components/observation-agent'
import { ObservationModal } from '@/components/modals/ObservationModal'
import { useLocalStorage } from '@/lib/hooks/use-local-storage'
import { Trash2 } from "lucide-react";
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from '@/components/user-avatar'


type ThreadInfo = { threadId: string; name: string } | null;

interface AssistantClientProps {
  companion: Companion & {
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  myAssistant: Assistant
  foundPersona : Persona | null
  pathThreadId: string
  existingThread: ThreadInfo
  notes: Note[]
};


const examples = [
  "",
  "",
  "",
];
  

export function AssistantClient({
    companion,
    myAssistant,
    foundPersona,
    pathThreadId,
    existingThread,
    notes,
  }: AssistantClientProps) {
  const router = useRouter();
  const { user } = useUser();
  const [userThread] = useAtom(userThreadAtom);
  const [isMounted, setIsMounted] = useState(false);
  const [chatMessages, setChatMessages] = useState<ThreadMessage[]>([]);
  const [observations, setObservations] = useState<Observations[]>(companion ? companion.observations : []);
  const [todos, setTodos] = useState<Todos[]>(companion ? companion.todos : []);
  const [note, setNote] = useState<Note[]>(notes);
  const [isPending, startTransition] = useTransition();
  const [fetching, setFetching] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const labels: string[] = [
    "Excited", "Delighted", "Happy", "Content", 
    "Relaxed", "Calm", "Tired", "Bored", "Depressed", 
    "Frustrated", "Angry", "Tense"]
  const backgroundColors = [
    'rgb(104, 159, 56, 0.9)',
    'rgb(255, 99, 132, 0.9)',
    'rgb(75, 192, 192, 0.9)',
    'rgb(255, 128, 171, 0.9)',
    'rgb(255, 205, 86, 0.9)',
    'rgb(54, 162, 235, 0.9)',
    'rgb(101, 31, 255, 0.9)',
    'rgb(158, 158, 158, 0.9)',
    'rgb(48, 79, 254, 0.9)',
    'rgb(0, 200, 83, 0.9)',
    'rgb(245, 0, 87, 0.9)',
    'rgb(244, 81, 30, 0.9)'
  ];
  const initialEmotionsData: PolarAreaChartType = {
    labels: labels,
    datasets: [
      {
        label: 'Emotions',
        //data: [4, 5, 3, 4, 5, 3, 4, 3, 4, 5, 3, 5],
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        fill: true,
        backgroundColor: backgroundColors,
        borderColor: 'rgb(132, 255, 255, 0.0)',
        pointBackgroundColor: 'rgb(132, 255, 255)',
        pointBorderColor: 'rgb(132, 255, 255, 0.0)',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(132, 255, 255, 0.2)'
      },
    ],
  }
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [totalWordCount, setTotalWordCount] = useState<number>(0)
  const [observationList, setObservationList] = useState<string[]>([]);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [chatSummaryList, setChatSummaryList] = useState<string[]>([]);
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(8)
  const [emotionsData, setEmotionsData] = useState<PolarAreaChartType>(initialEmotionsData)
  const [currentData, setCurrentData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })
  const { background, setBackground } = useBackground();
  const latestThreadId = useRef<string | null>(null)
  const latestCompletion = useRef<string | null>("getUserChatSummary")
  const [useSemanticCaptions, setUseSemanticCaptions] = useState<boolean>(false)
  const path = `/assistant/${companion.id}/${pathThreadId}`
  const newChatPath = `/assistant/${companion.id}`


  console.log("userThread: ", userThread)
  /*const combinedMessages = [
    {
      role: 'user',
      content: '今天去參加了以前大學教授的告別式,心理蠻複雜的'
    },
      { role: 'assistant', content: '您好,怡瑾在此聆聽。參加教授的告別式,想必喚起了您許多的回憶和情感。複雜的心情中,有哪些感受是您現在願意分享的呢?' },
    {
      role: 'user',
      content: '其實我也還釐不清,就是有點感慨,今天走進校園,有很多以前的回憶'
    },
      {
      role: 'assistant',
      content: '回到曾經熟悉的校園,時間彷彿在那一刻凝固,過往的片段一幕幕浮現。在這些回憶中,是否有特別讓您感慨或是印象深刻的事情呢?您提到的感慨,是不是像一股溫柔的潮流,慢慢地沖刷著內心深處的某些角落?'
    },
      { role: 'user', content: '對,想到以前上這位老師的課程,轉眼間,我已成為兩個孩子的父親。內心有很多感受,但不知如何表達' },
      { role: 'assistant', content: '成長的過程中,我們經歷了許多變化,從學生時代的懵懂到肩負父親角色的成熟,這之間的轉變是如此迅速又深刻。當回憶與現實交織時,內心的感受往往難以用言語表達。您認為,這些難以言喻的情緒,對您現在的生活有著怎樣的影響或意義呢?' }
  ]
  
  useEffect(() => {
    const createGenerativeAgent = async () => {
      const latestUserMessages = combinedMessages
      .filter((message: { role: string }) => message.role === 'user')

      console.log("latestUserMessages@assistant: ", latestUserMessages)
      const observationSummary = await ObservationAgent({
        userId: user?.id!,
        companionId: companion.id, 
        messages: latestUserMessages,
        name: usePersona?.name! ?? "tommie",
        age: usePersona?.age ?? 0,
        traits: usePersona?.traits ?? "",
        status: usePersona?.status ?? "",
        reflectionThreshold: useReflectionThreshold,
      })

      if (observationSummary) {
        console.log("observationSummary@assistant: ", observationSummary)
        await saveObservationAction({ 
          companionId: companion.id, 
          title: observationSummary,
          message: observationSummary,
          roomId: 'user_status_sum',
          path}` 
        })
      }

    }

    createGenerativeAgent()
  }, [])*/
  
  /*
  useEffect(() => {
    if (companion.messages.length === 0) {
      // Trigger the 'append' function with the "Hello" message when the page loads
      const helloMessage: Message = {
        id: companion.id,
        content: `您好，我是${companion.name}`,
        role: 'system',
      };

      append(helloMessage)
    }
  }, []);
  */

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setChatSummary(companion.chatSummaries)
  }, [companion.chatSummaries])

  useEffect(() => {
    if (chatSummary.length > 0 && chatRuns <= 1) {
      const latestChatSummaries = companion.chatSummaries
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(latestChatSummaries);
    } else {
      const latestChatSummary = companion.chatSummaries.slice(-1)
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(prevChatSummaryList => [...prevChatSummaryList, ...latestChatSummary]);
    }
  }, [chatSummary, chatRuns]); 

  useEffect(() => {
    //console.log("companion.observations:", companion.observations);
    setObservations(companion.observations)
  }, [companion.observations])

  useEffect(() => {
    setTodos(companion.todos)
  }, [companion.todos])

  useEffect(() => {
    setNote(notes)
  }, [notes])

  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
  };

  const incrementWordCount = (count: number) => {
    setTotalWordCount((prevCount) => prevCount + count);
  };


  const fetchMessages = useCallback(async () => {
    if (!existingThread) return;

    setFetching(true);

    try {
      const response = await axios.post<{
        success: boolean;
        error?: string;
        messages?: ThreadMessage[];
      }>("/api/message/list", { threadId: existingThread.threadId });

      // Validation
      if (!response.data.success || !response.data.messages) {
        console.error(response.data.error ?? "Unknown error.");
        return;
      }

      let newMessages = response.data.messages;

      // Sort in descending order
      newMessages = newMessages
        .sort((a, b) => {
          return (
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
        })
        .filter(
          (message) =>
            message.content[0].type === "text" &&
            message.content[0].text.value.trim() !== ""
        );

      setChatMessages(newMessages);
    } catch (error) {
      console.error(error);
      setChatMessages([]);
    } finally {
      setFetching(false);
      console.log("chatMessages: ", chatMessages)
    }
  }, [existingThread]);

  useEffect(() => {
    // Call fetchMessages when existingThread changes
    fetchMessages();
  }, [fetchMessages]);

  const {
    complete, 
    completion
  } = useCompletion({
    /*api: `/api/observation/${companion.id}`,
    body: {      
      companion,
      observationList,
      useReflectionThreshold,
      persona: {
        name: usePersona?.name,
        nickName: usePersona?.nickName,
        age: usePersona?.age,
        traits: usePersona?.traits,
        status: usePersona?.status,
      },
      path
    },*/
    api: `/api/summary/${companion.id}`,
    body: {
      observationList,
      companionName: companion.name,
      userName: usePersona?.nickName,
      pathThreadId,
      chatRuns,
      useReflectionThreshold,
      latestCompletion,
      path,
    },
    onResponse: res => {
      if (res.status === 429) {
        toast.error("You have reached your request limit for the day.");
      }
    },
    onFinish(_prompt, completion) {
      console.log("completion: ", completion);
      if (user?.id && completion && latestCompletion.current === "getObservation") {
        const newCompletion = {
          id: uuidv4(),
          title: completion,
          message: completion,
          roomId: 'user_status_sum',
          createdAt: new Date(),
          updatedAt: new Date(),
          companionId: companion.id!,
          userId: user.id!,
        };
        setObservations((prevObservations) => [newCompletion, ...prevObservations]);
        setTotalWordCount(0)
        latestCompletion.current = "getUserChatSummary"
      }
      if (user?.id && completion && latestCompletion.current === "getUserChatSummary") {
        latestCompletion.current = "getObservation"
      }
    },
  });

  const { 
    status, 
    messages, 
    threadId,
    input,
    setInput,
    submitMessage, 
    handleInputChange, 
    error 
  } = useAssistant({
    api: `/api/assistant/${companion.id}`,
    body: {
      companion,      
      existingThread,
      totalWordCount,
      chatSummaryList,
      useReflectionThreshold,
      chatRuns,
      persona: {
        name: usePersona?.name,
        nickName: usePersona?.nickName,
        age: usePersona?.age,
        traits: usePersona?.traits,
        status: usePersona?.status,
      },
      userThreadId: existingThread?.threadId,
      path: newChatPath,
    },
  });

  if (!isMounted) {
    return null;
  }

  const disabled = status !== 'awaiting_message' || input.length === 0;

  return (
    <div className="flex flex-row items-center justify-between max-w-screen max-h-screen overflow-hidden" 
      style={{ background }}
    >
      {/* Left Panel */}
      <div className="hidden lg:block md:flex h-[100vh] w-[45%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">
        <Tabs defaultValue="observations" className="flex flex-col items-center pt-3 bg-transparent">
          <TabsList className="bg-background/70">
            <TabsTrigger value="observations">Observations</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
          </TabsList>
          <TabsContent value="observations">
            <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
              <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
                <div className="pt-2">
                  <ObservationModal companionId={companion.id} userId={user?.id!} path={path} />
                </div>
                <div className="pt-2">
                  My Observations
                </div>
              </div>
              <div className="mt-0">
                <ObservationNew observations={observations} />
              </div>
            </div>
            <div className="flex flex-col h-[35%] w-full px-2 justify-center items-center overflow-overlay">
              {/*<PolarAreaChart data={emotionsData} />*/}
            </div>
          </TabsContent>
          <TabsContent value="storage">
            <AssistantDocument companion={companion} myAssistant={myAssistant} userId={user?.id!} path={path} />
          </TabsContent>
        </Tabs>     
      </div>
      {/* Center Panel */}
      <div className="flex flex-col h-[100vh] 2xl:h-screen w-full pt-0 lg:px-2">
        <ChatHeader companion={companion} myAssistant={myAssistant} path={path} />
        <div className="flex-grow h-[100vh] w-[100%] pb-[100px] overflow-x-hidden overflow-y-auto">
          {messages.length || chatMessages.length ? (
            <>
              {/* 3. LISTING OUT THE MESSAGES */}
              <AssistantHistory 
                companion={companion}
                isLoading={fetching}
                messages={chatMessages}
                path={path}
              />
              <AssistantList 
                companion={companion}
                isLoading={status === 'in_progress'}
                messages={messages}
                path={path}
              />
            <ChatScrollAnchor trackVisibility={status === 'in_progress'} />
            </>
          ) : (
            <div className="border-gray-200 sm:mx-0 mx-5 mt-2 rounded-md border sm:w-full">
              <div className="flex flex-col space-y-4 p-7 sm:p-10">
                <h1 className="text-lg font-semibold primary-text">
                  Welcome to ComfyMinds Lab
                </h1>
                <p className="primary-text">
                  {companion.introduction}
                </p>
              </div>
              <div className="flex flex-col space-y-4 border-t border-gray-0 p-1 sm:p-10">
                {examples.map((example, i) => (
                  <button
                    key={i}
                    className="rounded-md border border-gray-0 bg-primary/10 px-5 py-1 text-left text-sm text-gray-500 transition-all duration-75 hover:border-black hover:text-gray-700 active:bg-gray-50"
                    onClick={() => {
                    setInput(example);
                    inputRef.current?.focus();
                    }}
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        {/*<div className="h-[100px]"></div>*/}
        <div className="relative bottom-0 flex w-full flex-col justify-center items-center space-y-0 py-2 pb-0 sm:px-0">
          <TalkForm
            handleSubmit={submitMessage}
            input={input}
            setInput={setInput}
            isLoading={status === 'in_progress'}
            disabled={disabled}
            path={newChatPath}
            formWidth={'100%'}
          />
        </div>
      </div>
      {/*<div className="flex flex-col space-y-4">
        {messages.length > 0 && (
          <button
            disabled={isPending}
            className={`relative inset-y-0 my-auto flex h-9 w-9 items-center justify-center rounded-md transition-all ${
              isPending ? "bg-gray-400" : "bg-red-500"
            }`}
            onClick={() =>
              startTransition(async() => {
                await deleteMessageAction({ companionId: companion.id, path })
              })
            }
          >
            <Trash2
              className={clsx(
                "h-6 w-6",
                  isPending ? "bg-gray-400" : "bg-red-500",
              )}
            />
          </button>
        )}
      </div>
      */}
      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[100vh] 2xl:h-[100vh] w-[45%] md:w-[45%] lg:w-[45%] pt-0 px-1 overflow-hidden">
        <div className="flex flex-col px-2 justify-center items-center">
          <Tabs defaultValue="notes" className="flex flex-col items-center pt-3 bg-transparent">
            <TabsList className="bg-background/70">
            <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="todos">Todos</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="theme">
              <div className="w-full h-[3rem] flex justify-center items-center rounded !bg-cover !bg-center transition-all gap-1">
                <GradientPicker />
                <ThemeChanger />
              </div>
            </TabsContent>
            <TabsContent value="todos">
              {companion.id !== "" && (
                <TodoList companionId={companion.id} todosData={todos ?? []}/>
              )}
              <ChatSummaryNew chatSummary={chatSummary} />
            </TabsContent>
            <TabsContent value="notes">
              <NoteNew note={note} path={path} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
