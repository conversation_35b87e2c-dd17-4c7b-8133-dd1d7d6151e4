"use client";

import { userThread<PERSON>tom } from "@/atoms";
import { UserThread } from "@prisma/client";
import { useUser } from "@clerk/nextjs";
import NotificationModal from "@/components/workout/NotificationModal";
import useServiceWorker from "@/hooks/useServiceWorker";
import axios from "axios";
import { useAtom } from "jotai";
import { use, useCallback, useEffect, useState } from "react";
import toast, { Toaster } from "react-hot-toast";
import { getUserThread } from "@/app/actions/assistantActions"
import { AssistantPageProps } from './page';

type ThreadInfo = { threadId: string; name: string } | null;

export default function AppLayout(
  props: { 
      children: React.ReactNode, 
      params: AssistantPageProps['params']
    }
) {
  const params = use(props.params);

  const {
    children
  } = props;

  const { user } = useUser()
  const userId = user?.id!
  // Atom State
  const [userThread, setUserThread] = useAtom(userThreadAtom);

  // State
  const [isNotificationModalVisible, setIsNotificationModalVisible] =
    useState(false);

  // Hooks
  useServiceWorker();

  useEffect(() => {
    async function fetchUserThread() {
      if (!userId || params?.threadId === null || 
        userThread != null && userThread?.threadId === params.threadId) return;

      if (params.threadId.startsWith("thread_")) {
        try {
          const existingThread: ThreadInfo = await getUserThread(
            userId, params.assistantId, params.threadId
          );

          if (existingThread) {
            setUserThread(existingThread);
          } else {
            setUserThread(null);
          }
            
        } catch (error) {
          console.error(error);
          setUserThread(null);
        }
      } else {
        setUserThread(null);
      }
    }

    fetchUserThread();
  }, [setUserThread, userId, params.assistantId, params.threadId]);


  useEffect(() => {
    if ("Notification" in window) {
      setIsNotificationModalVisible(Notification.permission === "default");
      console.log("Notification permission:", Notification.permission);
    }
  }, []);

  const saveSubscription = useCallback(async () => {
    const serviceWorkerRegistration = await navigator.serviceWorker.ready;
    const subscription = await serviceWorkerRegistration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
    });

    try {
      const response = await axios.post("/api/subscription", subscription);

      if (!response.data.success) {
        console.error(response.data.message ?? "Unknown error.");
        toast.error("Failed to save subscription.");
        return;
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to save subscription.");
    }
  }, []);

  useEffect(() => {
    if ("Notification" in window && "serviceWorker" in navigator) {
      if (Notification.permission === "granted") {
        saveSubscription();
      }
    }
  }, [saveSubscription]);

  const handleNotificationModalClose = (didConstent: boolean) => {
    setIsNotificationModalVisible(false);

    if (didConstent) {
      toast.success("You will now receive notifications.");
    }
  };

  return (
    <div className="flex flex-col w-full max-h-screen overflow-hidden">
      {children}
      {isNotificationModalVisible && (
        <NotificationModal
          onRequestClose={handleNotificationModalClose}
          saveSubscription={saveSubscription}
        />
      )}
      <Toaster />
    </div>
  );
}