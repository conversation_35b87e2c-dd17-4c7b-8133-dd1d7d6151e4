import { notFound, redirect } from 'next/navigation'

//import OpenAI from "openai";
import prismadb from "@/lib/prismadb";
import { auth } from "@clerk/nextjs/server"
import { Companion, Persona } from "@prisma/client";
import { AssistantClient } from "../_components/client";
import { getAssistantById, getUserThread } from "@/app/actions/assistantActions"
import { getNotes } from "@/app/actions/noteActions"
import { Assistant } from "openai/resources/beta/assistants";
import OpenAI from "openai";


//export const runtime = 'edge'

export const preferredRegion = ['sfo1']

export interface AssistantPageProps {
  params: Promise<{
    threadId: string
    assistantId: string
  }>
}

type ThreadInfo = { threadId: string; name: string } | null;


export default async function AssistantPage(props: AssistantPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
    return redirectToSignIn();
    // redirect(`/sign-in?next=/cheers/${params.assistantId}/${params.threadId}`)
  }

  const existingThread: ThreadInfo = await getUserThread(
    userId, params.assistantId, params.threadId
  );


  const companion = await getAssistantById(userId, params.assistantId, params.threadId)
  //console.log("companionById@app/Assistant: ", companion)

  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  let myAssistant: Assistant = {} as Assistant
  if (companion.assistantId) {
    try {
      const openai = new OpenAI();
      myAssistant = await openai.beta.assistants.retrieve(companion.assistantId);
    } catch (e) {
      console.log(e);
    }
  }

  return <AssistantClient companion={companion} myAssistant={myAssistant ?? {}} foundPersona={foundPersona} pathThreadId={params.threadId} existingThread={existingThread} notes={notes} />
}