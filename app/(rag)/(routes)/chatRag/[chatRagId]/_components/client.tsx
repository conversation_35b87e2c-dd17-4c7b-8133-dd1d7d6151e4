"use client";

import axios from 'axios'
import DOMPurify from 'dompurify'
import { useRef, useEffect, useState, useTransition } from "react";
import {
  useChat,
  useCompletion,
  type Message,
  type UseChatHelpers,
  DefaultChatTransport,
} from '@ai-sdk/react';
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from 'uuid'
import { useUser } from "@clerk/nextjs";
import { useSessionStorage } from 'usehooks-ts'
import { deleteMessageAction, saveObservationAction } from "@/app/actions";
import { addTodoAction } from "@/app/actions/todoActions"
import { 
    Companion,
    Message as chatMessage,
    Source,
    Observations,
    ChatSummary,
    Persona,
    Todos,
    Note,
} from '@prisma/client'
import { 
    SentimentData,
    PolarAreaChartType,
    QuestionWithOptions
} from '@/lib/post.types'
import { Button, buttonVariants } from '@/components/ui/button'
import { 
  Ta<PERSON>, 
  Ta<PERSON>Content, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import va from "@vercel/analytics";
import clsx from "clsx";
import toast from "react-hot-toast";
import ObservationNew from '@/components/observation/observation-new'
import PolarAreaChart from '@/components/post/PolarAreaChart'
import TodoList from '@/components/suggestion/TodoList'
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
import NoteNew from '@/components/note/NoteNew'
import ThemeChanger from "@/components/nextly/DarkSwitch";
import { SupabaseDocument } from '@/components/storage/SupabaseDocument'
import { SupabaseFile } from '@/lib/types'
import { ChatHeader } from "@/components/chat-header";
import { ChatWindow } from "@/components/langchain/ChatWindow";
import { updatePolarAreaChart } from '@/components/post/Sentiment'
import { markdownToList } from '@/lib/markdownToList-parser'
import { GradientPicker } from "@/components/GradientPicker.tsx"
import { useBackground } from '@/components/providers/BackgroundContext';
//import { ObservationAgent } from '@/components/observation-agent'
import { ObservationModal } from '@/components/modals/ObservationModal'
import { useLocalStorage } from '@/lib/hooks/use-local-storage'
import { Trash2 } from "lucide-react";


interface ChatRagClientProps {
  companion: Companion & {
    messages: (chatMessage & {
      sources?: Source[];
    })[];
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  notes: Note[]
  supabaseFiles: SupabaseFile[]
};


const examples = [
  "",
  "",
  "",
];

export function ChatRagClient({
    companion,
    foundPersona,
    threadId,
    existingThread,
    notes,
    supabaseFiles,
  }: ChatRagClientProps) {
  const router = useRouter();
  const { user } = useUser();
  const [isMounted, setIsMounted] = useState(false);
  const [chatMessages, setChatMessages] = useState(companion.messages);
  const [observations, setObservations] = useState<Observations[]>(companion ? companion.observations : []);
  const [todos, setTodos] = useState<Todos[]>(companion ? companion.todos : []);
  const [note, setNote] = useState<Note[]>(notes);
  const [isPending, startTransition] = useTransition();
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const userId = user?.id!

  const labels: string[] = [
    "Excited", "Delighted", "Happy", "Content", 
    "Relaxed", "Calm", "Tired", "Bored", "Depressed", 
    "Frustrated", "Angry", "Tense"]
  const backgroundColors = [
    'rgb(104, 159, 56, 0.9)',
    'rgb(255, 99, 132, 0.9)',
    'rgb(75, 192, 192, 0.9)',
    'rgb(255, 128, 171, 0.9)',
    'rgb(255, 205, 86, 0.9)',
    'rgb(54, 162, 235, 0.9)',
    'rgb(101, 31, 255, 0.9)',
    'rgb(158, 158, 158, 0.9)',
    'rgb(48, 79, 254, 0.9)',
    'rgb(0, 200, 83, 0.9)',
    'rgb(245, 0, 87, 0.9)',
    'rgb(244, 81, 30, 0.9)'
  ];
  const initialEmotionsData: PolarAreaChartType = {
    labels: labels,
    datasets: [
      {
        label: 'Emotions',
        //data: [4, 5, 3, 4, 5, 3, 4, 3, 4, 5, 3, 5],
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        fill: true,
        backgroundColor: backgroundColors,
        borderColor: 'rgb(132, 255, 255, 0.0)',
        pointBackgroundColor: 'rgb(132, 255, 255)',
        pointBorderColor: 'rgb(132, 255, 255, 0.0)',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(132, 255, 255, 0.2)'
      },
    ],
  }
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [value, setValue] = useSessionStorage('test-key', 1)
  const [totalWordCount, setTotalWordCount] = useState<number>(0)
  const [observationList, setObservationList] = useState<string[]>([]);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [chatSummaryList, setChatSummaryList] = useState<string[]>([]);
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(8)
  const [emotionsData, setEmotionsData] = useState<PolarAreaChartType>(initialEmotionsData)
  const [currentData, setCurrentData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })
  const { background, setBackground } = useBackground();
  const latestThreadId = useRef<string | null>(null)
  const latestCompletion = useRef<string | null>("getUserChatSummary")
  const [useSemanticCaptions, setUseSemanticCaptions] = useState<boolean>(false)
  const path = `/chatRag/${companion.id}`
  const newChatPath = `/chatRag/${companion.id}`

  //console.log("companion: ", companion)

  /*const combinedMessages = [
    {
      role: 'user',
      content: '今天去參加了以前大學教授的告別式,心理蠻複雜的'
    },
      { role: 'assistant', content: '您好,怡瑾在此聆聽。參加教授的告別式,想必喚起了您許多的回憶和情感。複雜的心情中,有哪些感受是您現在願意分享的呢?' },
    {
      role: 'user',
      content: '其實我也還釐不清,就是有點感慨,今天走進校園,有很多以前的回憶'
    },
      {
      role: 'assistant',
      content: '回到曾經熟悉的校園,時間彷彿在那一刻凝固,過往的片段一幕幕浮現。在這些回憶中,是否有特別讓您感慨或是印象深刻的事情呢?您提到的感慨,是不是像一股溫柔的潮流,慢慢地沖刷著內心深處的某些角落?'
    },
      { role: 'user', content: '對,想到以前上這位老師的課程,轉眼間,我已成為兩個孩子的父親。內心有很多感受,但不知如何表達' },
      { role: 'assistant', content: '成長的過程中,我們經歷了許多變化,從學生時代的懵懂到肩負父親角色的成熟,這之間的轉變是如此迅速又深刻。當回憶與現實交織時,內心的感受往往難以用言語表達。您認為,這些難以言喻的情緒,對您現在的生活有著怎樣的影響或意義呢?' }
  ]
  
  useEffect(() => {
    const createGenerativeAgent = async () => {
      const latestUserMessages = combinedMessages
      .filter((message: { role: string }) => message.role === 'user')

      console.log("latestUserMessages@cheers: ", latestUserMessages)
      const observationSummary = await ObservationAgent({
        userId: user?.id!,
        companionId: companion.id, 
        messages: latestUserMessages,
        name: usePersona?.name! ?? "tommie",
        age: usePersona?.age ?? 0,
        traits: usePersona?.traits ?? "",
        status: usePersona?.status ?? "",
        reflectionThreshold: useReflectionThreshold,
      })

      if (observationSummary) {
        console.log("observationSummary@cheers: ", observationSummary)
        await saveObservationAction({ 
          companionId: companion.id, 
          title: observationSummary,
          message: observationSummary,
          roomId: 'user_status_sum',
          path}` 
        })
      }

    }

    createGenerativeAgent()
  }, [])*/

  /*
  useEffect(() => {
    if (companion.messages.length === 0) {
      // Trigger the 'append' function with the "Hello" message when the page loads
      const helloMessage: Message = {
        id: companion.id,
        content: `您好，我是${companion.name}`,
        role: 'system',
      };

      append(helloMessage)
    }
  }, []);
  */

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setChatMessages(companion.messages)
  }, [companion.messages])

  useEffect(() => {
    setChatSummary(companion.chatSummaries)
    //console.log("companion.chatSummaries:", companion.chatSummaries);
  }, [companion.chatSummaries])

  useEffect(() => {
    if (chatSummary.length > 0 && chatRuns <= 1) {
      const latestChatSummaries = companion.chatSummaries
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(latestChatSummaries);
    } else {
      const latestChatSummary = companion.chatSummaries.slice(-1)
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(prevChatSummaryList => [...prevChatSummaryList, ...latestChatSummary]);
    }
  }, [chatSummary, chatRuns]);

  useEffect(() => {
    //console.log("companion.observations:", companion.observations);
    setObservations(companion.observations)
  }, [companion.observations])

  useEffect(() => {
    setTodos(companion.todos)
  }, [companion.todos])

  useEffect(() => {
    setNote(notes)
  }, [notes])

  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
    setValue((prevValue) => prevValue + 1);
  };

  const incrementWordCount = (count: number) => {
    setTotalWordCount((prevCount) => prevCount + count);
  };

  const {
    complete, 
    completion
  } = useCompletion({
    /*api: `/api/observation/${companion.id}`,
    body: {      
      companion,
      observationList,
      useReflectionThreshold,
      persona: {
        name: usePersona?.name,
        nickName: usePersona?.nickName,
        age: usePersona?.age,
        traits: usePersona?.traits,
        status: usePersona?.status,
      },
      path
    },*/
    api: `/api/summary/${companion.id}/openrouter`,
    body: {
      observationList,
      companionName: companion.name,
      userName: usePersona?.nickName,
      threadId,
      chatRuns,
      useReflectionThreshold,
      latestCompletion,
      path,
    },
    onResponse: res => {
      if (res.status === 429) {
        toast.error("You have reached your request limit for the day.");
      }
    },
    onFinish(_prompt, completion) {
      console.log("completion: ", completion);
      if (user?.id && completion && latestCompletion.current === "getObservation") {
        const newObservation = {
          id: uuidv4(),
          title: completion,
          message: completion,
          roomId: 'user_status_sum',
          createdAt: new Date(),
          updatedAt: new Date(),
          companionId: companion.id!,
          userId: user.id!,
        };
        setObservations((prevObservations) => [newObservation, ...prevObservations]);
        setTotalWordCount(0)
        latestCompletion.current = "getUserChatSummary"
      }
      if (user?.id && completion && latestCompletion.current === "getUserChatSummary") {
        const newChatSummary = {
          id: uuidv4(),
          title: completion,
          content: completion,
          createdAt: new Date(),
          companionId: companion.id!,
          companionName: companion.name!,
          userId: user.id!,
          userName: usePersona?.nickName!,
          threadId,
        };
        setChatSummary((prevChatSummary) => [newChatSummary, ...prevChatSummary]);
        latestCompletion.current = "getObservation"
      }
    },
  });

  const [input, setInput] = useState('');

  const {
    messages,
    setMessages,
    append,
    reload,
    stop,
    setInput,
    handleSubmit,
    isLoading
  } = useChat({
    initialMessages: chatMessages,

    body: {
      companion,
      threadId,
      existingThread,
      totalWordCount,
      chatSummaryList,
      useReflectionThreshold,
      chatRuns,
      persona: {
        name: usePersona?.name,
        nickName: usePersona?.nickName,
        age: usePersona?.age,
        traits: usePersona?.traits,
        status: usePersona?.status,
      },
      path,
    },

    onResponse: (response) => {
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
        va.track("Rate limited");
        return;
      } else {
        va.track("Chat initiated");
      }
    },

    onError: (error) => {
      //toast.error(error.message)
      va.track("Chat errored", {
        input,
        error: error.message,
      });
    },

    onFinish: async (message: Message) => {
      //console.log("chatMessages@cheers: ", chatMessages)

      let latestUserMessage = messages.slice(-2).find((chatMessage: { role: string }) => chatMessage.role === 'user');
      console.log("latestUserMessage@cheers: ", latestUserMessage);
      if (latestUserMessage) {
        // Count the number of words in latestUserMessage.content
        const words: string = latestUserMessage.content;
        console.log("words", words);
      
        // Count the number of words in latestUserMessage.content
        incrementWordCount(words.length);
        // Update the wordList state
        setObservationList(prevObservationList => [...prevObservationList, words]);
      }
      console.log("totalWordCount", totalWordCount)
  
      if (chatRuns % (useReflectionThreshold *1.5 ) === 0 && totalWordCount >= 50) {
        latestCompletion.current = "getObservation"
        console.log("getObservation")
        complete("Summarize my capabilities, personalities, values, motivation, and interest.");
      } else if (chatRuns % (useReflectionThreshold ) === 0) {
        latestCompletion.current = "getUserChatSummary"
        console.log("getUserChatSummary")
        complete("Summarize the key messages");
      }

      /*if (chatRuns % (useReflectionThreshold ) === 0) {
        const observationSummary = await ObservationAgent({
          userId: user?.id!,
          companionId: companion.id, 
          messages: latestUserMessages,
          name: usePersona?.name! ?? "您",
          age: usePersona?.age ?? 0,
          traits: usePersona?.traits ?? "",
          status: usePersona?.status ?? "",
          reflectionThreshold: useReflectionThreshold,
          path
        })
        console.log("observationSummary@cheers: ", observationSummary)

        if (observationSummary) {
          await saveObservationAction({ 
            companionId: companion.id, 
            title: observationSummary,
            message: observationSummary,
            roomId: 'user_status_sum',
            path}` 
          })         
        }
      }*/

      /*
      if (chatRuns % (useReflectionThreshold) === 100) {
        try {
          const userMessages = messages.filter((message) => message.role === "user")
          const sentiment: { data: SentimentData } = await axios.post('/api/sentiment', {
            userMessages,
            useReflectionThreshold,
          })
          
          const sentimentResult = sentiment.data
          console.log("sentimentResult: ", sentimentResult)

          const updateSentimentData = async() => {
            const newData: SentimentData = {
              emotions: labels.map((emotion) => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion)
                return emotionIndex !== -1 ? emotion : emotion;
              }),
              stars: labels.map((emotion) => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
                return emotionIndex !== -1 ? (sentimentResult?.stars ?? [])[emotionIndex] : 0
              })
            }
            //console.log("newData", newData)

            const updateEmotions = async() => {
              const newEmotionsData: PolarAreaChartType = updatePolarAreaChart(newData)
              await setEmotionsData(newEmotionsData)
            }
            updateEmotions()
          }
          updateSentimentData()
          
        } catch (error) {
          toast.error("Something went wrong.");
          console.error("Error in sentiment analysis:", error);
        }
      }
      */

      /*
      const summaryMessage = [
      {
        id: "RMglgUP",
        createdAt: "2024-01-02T09:23:30.711Z",
        content: "當然！以下是您的關鍵行動摘要：\n\n <<關鍵行動摘要>>\n\n| 優點       | 行動計劃 |  \n| ---------- | ---------- |  \n| 立即求助     | 拨打119送醫院，並即刻接受抗病毒藥物治療 |\n\n您所採取的行動非常明智，您的迅速反應為母親的健康帶來了積極的影響。希望她能盡快康復。",
        role: "assistant"
      }]
      */

      const markdownTable = [message]
        .filter((message: Message) => message.role === 'assistant')
        .map((message: Message) => message.content)
        .join('\n');

      console.log("markdownTable@cheers: ", markdownTable)
      const user_todos = markdownToList(markdownTable)
      console.log("user_todos@cheers: ", user_todos)
      if (user_todos && user_todos.length > 0) {        
        try {
          // Loop through the todo_list and insert each task into the todos table
          for (const todo of user_todos) {
            const taskText = todo.action;
            startTransition(async() => {
              await addTodoAction({ 
                companionId: companion.id, 
                task: taskText, 
                path 
              })
            })
          }
        } catch (error) {
          toast.error("Something went wrong.");
        } finally {
          router.refresh()
        }
      }

      incrementChatRunCount()

      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId
        latestThreadId.current = newThreadId
        router.refresh() // for getThreads update
      }
    },

    transport: new DefaultChatTransport({
      api: `/api/cheers/${companion.id}/anthropic`
    })
  })

  //if (!isMounted) {
  //  return null;
  //}

  const disabled = isLoading || input.length === 0;

  return (
    <div className="flex flex-row items-center justify-between max-w-screen max-h-screen overflow-hidden" 
      style={{ background }}
    >
      {/* Left Panel */}
      <div className="hidden lg:block md:flex h-[100vh] w-[45%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">
      <Tabs defaultValue="observations" className="flex flex-col items-center pt-3 bg-transparent">
          <TabsList className="bg-background/70">
            <TabsTrigger value="observations">Observations</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
          </TabsList>
          <TabsContent value="observations">
            <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
              <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
                <div className="pt-2">
                  <ObservationModal companionId={companion.id} userId={user?.id!} path={path} />
                </div>
                <div className="pt-2">
                  My Observations
                </div>
              </div>
              <div className="mt-0">
                <ObservationNew observations={observations} />
              </div>
            </div>
            <div className="flex flex-col h-[35%] w-full px-2 justify-center items-center overflow-overlay">
              {/*<PolarAreaChart data={emotionsData} />*/}
            </div>
          </TabsContent>
          <TabsContent value="storage">
            <SupabaseDocument companion={companion} userId={user?.id!} supabaseFiles={supabaseFiles} path={path} />
          </TabsContent>
        </Tabs>
      </div>
      {/* Center Panel */}
      <div className="flex flex-col h-[100vh] 2xl:h-screen w-full pt-0 lg:px-2">
        <ChatHeader companion={companion} supabaseFiles={supabaseFiles} />
        <ChatWindow 
          companion={companion}
          userId={userId}
          userName={usePersona?.nickName!}
          threadId={threadId}
          existingThread={existingThread}
          path={path} 
          showIngestForm={true}
        />
      </div>
      {/*<div className="flex flex-col space-y-4">
        {messages.length > 0 && (
          <button
            disabled={isPending}
            className={`relative inset-y-0 my-auto flex h-9 w-9 items-center justify-center rounded-md transition-all ${
              isPending ? "bg-gray-400" : "bg-red-500"
            }`}
            onClick={() =>
              startTransition(async() => {
                await deleteMessageAction({ companionId: companion.id, path })
              })
            }
          >
            <Trash2
              className={clsx(
                "h-6 w-6",
                  isPending ? "bg-gray-400" : "bg-red-500",
              )}
            />
          </button>
        )}
      </div>
      */}
      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[100vh] 2xl:h-[100vh] w-[45%] md:w-[45%] lg:w-[45%] pt-3 px-1 overflow-hidden">
        <div className="flex flex-col px-2 justify-center items-center">
          <Tabs defaultValue="notes" className="flex flex-col items-center pt-3 bg-transparent">
            <TabsList className="bg-background/70">
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="todos">Todos</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="theme">
              <div className="w-full h-[3rem] flex justify-center items-center rounded !bg-cover !bg-center transition-all gap-1">
                <GradientPicker />
                <ThemeChanger />
              </div>
            </TabsContent>
            <TabsContent value="todos">
              {companion.id !== "" && (
                <TodoList companionId={companion.id} todosData={todos ?? []}/>
              )}
              <ChatSummaryNew chatSummary={chatSummary} />
            </TabsContent>
            <TabsContent value="notes">
              <NoteNew note={note} path={path} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
