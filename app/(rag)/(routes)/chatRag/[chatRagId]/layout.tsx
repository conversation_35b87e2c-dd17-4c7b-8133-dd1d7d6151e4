import { SidebarHeader } from '@/components/talk/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface ChatRagLayoutProps {
  params: Promise<{ 
    chatRagId: string
  }>
}

const ChatRagLayout = async (
  props: ChatRagLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/chatRag/${params.chatRagId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.chatRagId} path={path} />
      {children}
    </div>
  );
}

export default ChatRagLayout;