import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseFile } from '@/lib/types'
import { Suspense } from 'react'
import Loading from '@/app/loading'
import { Persona } from "@prisma/client";
import { getFullCompanionById, getThread } from "@/app/actions/threadActions"
import { getNotes } from "@/app/actions/noteActions"
import { Home } from "../_components/home";

//export const runtime = 'edge'
export const revalidate = 0
//export const preferredRegion = ['sfo1']
//export const experimental_ppr = true

export interface ChatAgentPageProps {
  params: Promise<{
    threadId: string
    chatAgentId: string
  }>
}

type ThreadInfo = { id: string; name: string } | null;

export default async function ChatAgentPage(props: ChatAgentPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }



  const companion = await getFullCompanionById(userId, params.chatAgentId, params.threadId)

  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    console.log("Companion not found or invalid data:", companion);
    return <div>Companion not found.</div>;
  }
  //console.log("companion: ", companion)
  const existingThread: ThreadInfo = await getThread(
    userId, params.chatAgentId, params.threadId
  );
  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  const { data, error } = await client.from("documents_1024").select();
  const supabaseFiles: SupabaseFile[] = data ?? [];
  const path = `/chatAgent/${params.chatAgentId}`;

  return <Suspense fallback={<Loading />}><Home companion={companion} foundPersona={foundPersona} threadId={params.threadId} existingThread={existingThread} notes={notes} supabaseFiles={supabaseFiles} path={path} /></Suspense>
}