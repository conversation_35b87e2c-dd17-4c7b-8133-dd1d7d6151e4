"use client";

import dynamic from 'next/dynamic';
import { 
    useRef, 
    useEffect, 
    useState, 
    useTransition, 
    ElementRef,
    type ChangeEvent, 
} from "react";
import { 
    useChat, 
    useCompletion, 
    type Message, 
    type UseChatHelpers 
} from "ai/react";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from 'uuid'
import { useUser } from "@clerk/nextjs";
import { useSessionStorage } from 'usehooks-ts'
import { addTodoAction } from "@/app/actions/todoActions"
import { 
    Companion,
    Message as chatMessage,
    Source,
    Observations,
    ChatSummary,
    Persona,
    Todos,
    Note,
} from '@prisma/client'
//import { 
//    SentimentData,
//    PolarAreaChartType,
//} from '@/lib/post.types'
import type { FormEvent } from "react";
import type { AgentStep } from "@langchain/core/agents";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs"
import va from "@vercel/analytics";
import toast from "react-hot-toast";
import ObservationNew from '@/components/observation/observation-new'
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
//import PolarAreaChart from '@/components/post/PolarAreaChart'
//import TodoList from '@/components/suggestion/TodoList'
//import NoteNew from '@/components/note/NoteNew'
//import ThemeChanger from "@/components/nextly/DarkSwitch";
//import { updatePolarAreaChart } from '@/components/post/Sentiment'
import { usePayPerUseModal } from "@/hooks/use-pay-per-use-modal";
const NoteNew = dynamic(() => import('@/components/note/NoteNew'), { ssr: false });
const ThemeChanger = dynamic(() => import('@/components/nextly/DarkSwitch'), { ssr: false });
const TodoList = dynamic(() => import('@/components/suggestion/TodoList'), { ssr: false });
//const PolarAreaChart = dynamic(() => import('@/components/post/PolarAreaChart'), { ssr: false });
//const updatePolarAreaChart = dynamic(() => import('@/components/post/Sentiment').then((mod) => mod.updatePolarAreaChart), { ssr: false });
import { ChatHeader } from "@/components/chat-header";
import { ChatMessageBubble } from "@/components/langchain/TalkMessageBubble";
import { ChatForm } from '@/components/langchain/TalkForm'
import { IntermediateStep } from "@/components/langchain/IntermediateStep";
import { SupabaseDocument } from '@/components/storage/SupabaseDocument'
import { SupabaseFile } from '@/lib/types'
import { useLocalStorage } from 'usehooks-ts'
import { type Language, DEFAULT_LANGUAGE } from "@/components/ui/ai-language"
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";

import { useMediaQuery } from "@/hooks/use-media-query"
import { markdownToList } from '@/lib/markdownToList-parser'
//import { GradientPicker } from "@/components/GradientPicker.tsx"
import { useBackground } from '@/components/providers/BackgroundContext';
const GradientPicker = dynamic(() => import('@/components/GradientPicker.tsx').then((mod) => mod.GradientPicker), { ssr: false });
const ObservationModal = dynamic(() => import('@/components/modals/ObservationModal').then((mod) => mod.ObservationModal), { ssr: false });
const InsightDrawer = dynamic(() => import('@/components/storage/insight-drawer').then((mod) => mod.InsightDrawer), { ssr: false });
//import { ObservationModal } from '@/components/modals/ObservationModal'
import { EmptyState } from "@/components/langchain/EmptyState";
import { inputSchema } from '@/lib/utils'; 


interface ChatAgentClientProps {
  companion: Companion & {
    messages: (chatMessage & {
      sources?: Source[];
    })[];
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  notes: Note[]
  supabaseFiles: SupabaseFile[]
};


export function ChatAgentClient({
    companion,
    foundPersona,
    threadId,
    existingThread,
    notes,
    supabaseFiles,
  }: ChatAgentClientProps) {
  const router = useRouter();
  const payPerUseModal = usePayPerUseModal();
  const { user } = useUser();
  const [isMounted, setIsMounted] = useState(false);
  const [observations, setObservations] = useState<Observations[]>(companion ? companion.observations : []);
  //const [todos, setTodos] = useState<Todos[]>(companion ? companion.todos : []);
  //const [note, setNote] = useState<Note[]>(notes);
  const [isPending, startTransition] = useTransition();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const userId = user?.id!
  const endpoint="/api/retrieval/agents/supabase/cohere"
  const isDesktop = useMediaQuery("(min-width: 768px)")

  /*
  const labels: string[] = [
    "Excited", "Delighted", "Happy", "Content", 
    "Relaxed", "Calm", "Tired", "Bored", "Depressed", 
    "Frustrated", "Angry", "Tense"]
  const backgroundColors = [
    'rgb(104, 159, 56, 0.9)',
    'rgb(255, 99, 132, 0.9)',
    'rgb(75, 192, 192, 0.9)',
    'rgb(255, 128, 171, 0.9)',
    'rgb(255, 205, 86, 0.9)',
    'rgb(54, 162, 235, 0.9)',
    'rgb(101, 31, 255, 0.9)',
    'rgb(158, 158, 158, 0.9)',
    'rgb(48, 79, 254, 0.9)',
    'rgb(0, 200, 83, 0.9)',
    'rgb(245, 0, 87, 0.9)',
    'rgb(244, 81, 30, 0.9)'
  ];
  const initialEmotionsData: PolarAreaChartType = {
    labels: labels,
    datasets: [
      {
        label: 'Emotions',
        //data: [4, 5, 3, 4, 5, 3, 4, 3, 4, 5, 3, 5],
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        fill: true,
        backgroundColor: backgroundColors,
        borderColor: 'rgb(132, 255, 255, 0.0)',
        pointBackgroundColor: 'rgb(132, 255, 255)',
        pointBorderColor: 'rgb(132, 255, 255, 0.0)',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(132, 255, 255, 0.2)'
      },
    ],
  }
  */
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [value, setValue] = useSessionStorage('test-key', 1)
  const [totalWordCount, setTotalWordCount] = useState<number>(0)
  const [observationList, setObservationList] = useState<string[]>([]);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [chatSummaryList, setChatSummaryList] = useState<string[]>([]);
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(2)
  //const [emotionsData, setEmotionsData] = useState<PolarAreaChartType>(initialEmotionsData)
  //const [currentData, setCurrentData] = useState<SentimentData>({
  //  emotions: labels,
  //  stars: []
  //})
  //const [useSemanticCaptions, setUseSemanticCaptions] = useState<boolean>(false)
  const { background, setBackground } = useBackground();
  const latestThreadId = useRef<string | null>(null)
  const latestCompletion = useRef<string | null>("getUserChatSummary")
  
  const scrollRef = useRef<ElementRef<"div">>(null)
  const messageContainerRef = useRef<HTMLDivElement | null>(null);
  const [chatWithVisionImage, setChatWithVisionImage] = useState<string>("");
  const [showIntermediateSteps, setShowIntermediateSteps] = useState(true);
  const [intermediateStepsLoading, setIntermediateStepsLoading] = useState(false);
  const [temporaryURLInput, setTemporaryURLInput] = useState("");
  const [sourcesForMessages, setSourcesForMessages] = useState<Record<string, any>>({});
  const [selectedLanguage, setSelectedLanguage] = useLocalStorage<Language | null>('selectedLanguage', DEFAULT_LANGUAGE);

  
  const path = `/chatAgent/${companion.id}`

  const sendInitialQuestion = async (question: string) => {
    setInput(question);
    inputRef.current?.focus();
  };
  
  /*
  useEffect(() => {
    if (companion.messages.length === 0) {
      // Trigger the 'append' function with the "Hello" message when the page loads
      const helloMessage: Message = {
        id: companion.id,
        content: `您好，我是${companion.name}`,
        role: 'system',
      };

      append(helloMessage)
    }
  }, []);
  */

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (chatSummary.length > 0 && chatRuns <= 1) {
      const latestChatSummaries = companion.chatSummaries
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(latestChatSummaries);
    } else {
      const latestChatSummary = companion.chatSummaries.slice(-1)
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(prevChatSummaryList => [...prevChatSummaryList, ...latestChatSummary]);
    }
  }, [chatSummary, chatRuns]); 

  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
    setValue((prevValue) => prevValue + 1);
  };

  const incrementWordCount = (count: number) => {
    setTotalWordCount((prevCount) => prevCount + count);
  };

  const {
    complete, 
    completion
  } = useCompletion({
    api: `/api/summary/${companion.id}/openrouter`,
    body: {
      aiLanguage: selectedLanguage,
      observationList,
      companionName: companion.name,
      userName: usePersona?.nickName,
      threadId,
      chatRuns,
      useReflectionThreshold,
      latestCompletion,
      path,
    },
    onResponse: (response: Response) => {
      if (response.status === 403) {
        payPerUseModal.onOpen();
      } else {
        toast.error("Something went wrong.");
      }
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
      }
    },
    onFinish(_prompt, completion) {
      console.log("completion: ", completion);
      if (user?.id && completion && latestCompletion.current === "getObservation") {
        const newObservation = {
          id: uuidv4(),
          title: completion,
          message: completion,
          roomId: 'user_status_sum',
          createdAt: new Date(),
          updatedAt: new Date(),
          companionId: companion.id!,
          userId: user.id!,
        };
        setObservations((prevObservations) => [newObservation, ...prevObservations]);
        setTotalWordCount(0)
        latestCompletion.current = "getUserChatSummary"
      }
      if (user?.id && completion && latestCompletion.current === "getUserChatSummary") {
        const newChatSummary = {
          id: uuidv4(),
          title: completion,
          content: completion,
          createdAt: new Date(),
          companionId: companion.id!,
          companionName: companion.name!,
          userId: user.id!,
          userName: usePersona?.nickName!,
          threadId,
        };
        setChatSummary((prevChatSummary) => [newChatSummary, ...prevChatSummary]);
        latestCompletion.current = "getObservation"
      }
    },
  });

  const {
    data,
    messages,
    setMessages,
    reload,
    stop,
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    isLoading: chatEndpointIsLoading,
  } = useChat({
    api: endpoint,
    initialMessages: companion?.messages,
    streamMode: 'text',
    body: {
      selectedLanguage
    },
    onResponse: (response) => {
      if (response.status === 403) {
        payPerUseModal.onOpen();
      } else {
        toast.error("Something went wrong.");
      }
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
        va.track("Rate limited");
        return;
      } else {
        va.track("Chat initiated");
      }
      const sourcesHeader = response.headers.get("x-sources");
      const sources = sourcesHeader ? JSON.parse((Buffer.from(sourcesHeader, 'base64')).toString('utf8')) : [];
      const messageIndexHeader = response.headers.get("x-message-index");
      if (sources.length && messageIndexHeader !== null) {
        setSourcesForMessages({...sourcesForMessages, [messageIndexHeader]: sources});
      }
    },
    onError: (error) => {
      //toast.error(error.message)
      va.track("Chat errored", {
        input,
        error: error.message,
      });
    },
    onFinish: async (message: Message) => {
      //console.log("chatMessages@cheers: ", chatMessages)

      let latestUserMessage = messages.slice(-2).find((message: { role: string }) => message.role === 'user');
      console.log("latestUserMessage@cheers: ", latestUserMessage);
      if (latestUserMessage) {
        // Count the number of words in latestUserMessage.content
        const words: string = latestUserMessage.content;
        console.log("words: ", words);
      
        // Count the number of words in latestUserMessage.content
        incrementWordCount(words.length);
        // Update the wordList state
        setObservationList(prevObservationList => [...prevObservationList, words]);
      }
      console.log("totalWordCount", totalWordCount)
  
      if (chatRuns % (useReflectionThreshold *1.5 ) === 0 && totalWordCount >= 50) {
        latestCompletion.current = "getObservation"
        console.log("getObservation")
        complete("Summarize my capabilities, personalities, values, motivation, and interest.");
      } else if (chatRuns % (useReflectionThreshold ) === 0) {
        latestCompletion.current = "getUserChatSummary"
        console.log("getUserChatSummary")
        complete("Summarize the key messages");
      }

      /*if (chatRuns % (useReflectionThreshold ) === 0) {
        const observationSummary = await ObservationAgent({
          userId: user?.id!,
          companionId: companion.id, 
          messages: latestUserMessages,
          name: usePersona?.name! ?? "您",
          age: usePersona?.age ?? 0,
          traits: usePersona?.traits ?? "",
          status: usePersona?.status ?? "",
          reflectionThreshold: useReflectionThreshold,
          path
        })
        console.log("observationSummary@cheers: ", observationSummary)

        if (observationSummary) {
          await saveObservationAction({ 
            companionId: companion.id, 
            title: observationSummary,
            message: observationSummary,
            roomId: 'user_status_sum',
            path}` 
          })         
        }
      }*/

      /*
      if (chatRuns % (useReflectionThreshold) === 100) {
        try {
          const userMessages = messages.filter((message) => message.role === "user")
          const sentiment: { data: SentimentData } = await axios.post('/api/sentiment', {
            userMessages,
            useReflectionThreshold,
          })
          
          const sentimentResult = sentiment.data
          console.log("sentimentResult: ", sentimentResult)

          const updateSentimentData = async() => {
            const newData: SentimentData = {
              emotions: labels.map((emotion) => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion)
                return emotionIndex !== -1 ? emotion : emotion;
              }),
              stars: labels.map((emotion) => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
                return emotionIndex !== -1 ? (sentimentResult?.stars ?? [])[emotionIndex] : 0
              })
            }
            //console.log("newData", newData)

            const updateEmotions = async() => {
              const newEmotionsData: PolarAreaChartType = updatePolarAreaChart(newData)
              await setEmotionsData(newEmotionsData)
            }
            updateEmotions()
          }
          updateSentimentData()
          
        } catch (error) {
          toast.error("Something went wrong.");
          console.error("Error in sentiment analysis:", error);
        }
      }
      */

      /*
      const summaryMessage = [
      {
        id: "RMglgUP",
        createdAt: "2024-01-02T09:23:30.711Z",
        content: "當然！以下是您的關鍵行動摘要：\n\n <<關鍵行動摘要>>\n\n| 優點       | 行動計劃 |  \n| ---------- | ---------- |  \n| 立即求助     | 拨打119送醫院，並即刻接受抗病毒藥物治療 |\n\n您所採取的行動非常明智，您的迅速反應為母親的健康帶來了積極的影響。希望她能盡快康復。",
        role: "assistant"
      }]
      */

      const markdownTable = [message]
        .filter((message: Message) => message.role === 'assistant')
        .map((message: Message) => message.content)
        .join('\n');

      console.log("markdownTable@cheers: ", markdownTable)
      const user_todos = markdownToList(markdownTable)
      console.log("user_todos@cheers: ", user_todos)
      if (user_todos && user_todos.length > 0) {        
        try {
          // Loop through the todo_list and insert each task into the todos table
          for (const todo of user_todos) {
            const taskText = todo.action;
            startTransition(async() => {
              await addTodoAction({ 
                companionId: companion.id, 
                task: taskText, 
                path 
              })
            })
          }
        } catch (error) {
          toast.error("Something went wrong.");
        } finally {
          router.refresh()
        }
      }

      if (existingThread) {
        await saveCompletionToDatabase(
          companion?.id!, userId, input, "user", threadId
        );
        await saveCompletionToDatabase(
          companion?.id!, userId, message.content, "assistant", threadId
        );
      } else {
        const name = input.substring(0, 100);
        await newThread({
          threadId, 
          name, 
          companionId: companion?.id!, 
          content: input,
          role: "user", 
        });
        // revalidatePath only on new thread
        await saveCompletionToDatabase(
          companion?.id!, userId, message.content, "assistant", threadId, path
        );
      }

      incrementChatRunCount()

      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId
        latestThreadId.current = newThreadId
        //router.refresh() // for getThreads update
      }
    },
  })


  const handleInputChangeWithValidation = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const newInput = e.target.value;
    const words = newInput.trim().split(/\s+/);
  
    if (words.length <= 300) {
      try {
        inputSchema.parse(newInput);
        handleInputChange(e); // Call the original handleInputChange function
      } catch (err: any) {
        console.error('Invalid input:', err.message);
        toast.error("Input must be at most 300 words", { position: "bottom-center" });
      }
    }
  };

  async function sendMessage(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    try {
      inputSchema.parse(input);
      if (messageContainerRef.current) {
        messageContainerRef.current.classList.add("grow");
      }
      if (!messages.length) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
      if (chatEndpointIsLoading ?? intermediateStepsLoading) {
        return;
      }
      if (!showIntermediateSteps) {
        handleSubmit(e, {
          data: {
            chatWithVisionImageUrl: chatWithVisionImage, // TODO: add imageURL
          },
        });

      // Some extra work to show intermediate steps properly
      } else {
        setIntermediateStepsLoading(true);
        setInput("");
        const messagesWithUserReply = messages.concat({ id: messages.length.toString(), content: input, role: "user" });
        setMessages(messagesWithUserReply);
        const response = await fetch(endpoint, {
          method: "POST",
          body: JSON.stringify({
            messages: messagesWithUserReply,
            show_intermediate_steps: true,
            selectedLanguage,
          })
        });
        const json = await response.json();
        console.log("json: ", json)
        setIntermediateStepsLoading(false);
        if (response.status === 403) {
          //setIntermediateStepsLoading(false);
          payPerUseModal.onOpen();
        } else if (response.status === 200) {
          // Represent intermediate steps as system messages for display purposes
          const intermediateStepMessages = (json.intermediate_steps ?? []).map((intermediateStep: AgentStep, i: number) => {
            return {id: (messagesWithUserReply.length + i).toString(), content: JSON.stringify(intermediateStep), role: "system"};
          });
          console.log("messagesWithUserReply: ", messagesWithUserReply)
          console.log("intermediateStepMessages: ", intermediateStepMessages)

          const newMessages = messagesWithUserReply;
          for (const message of intermediateStepMessages) {
            newMessages.push(message);
            setMessages([...newMessages]);
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
          }
          setMessages([...newMessages, { id: (newMessages.length + intermediateStepMessages.length).toString(), content: json.output, role: "assistant" }]);
          
          console.log("messages: ", messages)
          if (existingThread) {
            await saveCompletionToDatabase(
              companion?.id!, userId, input, "user", threadId
            );
            await saveCompletionToDatabase(
              companion?.id!, userId, json.output, "assistant", threadId
            );
          } else {
            const name = input.substring(0, 100);
            await newThread({
              threadId, 
              name, 
              companionId: companion?.id!, 
              content: input,
              role: "user", 
            });
            await saveCompletionToDatabase(
              companion?.id!, userId, json.output, "assistant", threadId, path
            );
          }

          incrementChatRunCount()

          let latestUserMessage = input;
          if (latestUserMessage) {
            // Count the number of words in latestUserMessage.content
            const words: string = latestUserMessage;
            console.log("words: ", words);
            console.log("words count: ", words.length);
          
            // Count the number of words in latestUserMessage.content
            incrementWordCount(words.length);
            // Update the wordList state
            setObservationList(prevObservationList => [...prevObservationList, words]);
          }
          console.log("totalWordCount", totalWordCount)
      
          if (chatRuns % (useReflectionThreshold *1.5 ) === 0 && totalWordCount >= 50) {
            latestCompletion.current = "getObservation"
            console.log("getObservation")
            complete("Summarize my capabilities, personalities, values, motivation, and interest.");
          } else if (chatRuns % (useReflectionThreshold ) === 0) {
            latestCompletion.current = "getUserChatSummary"
            console.log("getUserChatSummary")
            complete("Summarize the key messages");
          }
          
        } else {
          //setIntermediateStepsLoading(false);
          //toast.error("Something went wrong with the request.", { position: "bottom-center" });
          if (json.error) {
            toast.error(json.error, { position: "bottom-center" });
            throw new Error(json.error);
          }
        }
      }     
    } catch (err: any) {
      toast.error("Input must be at most 300 words", { position: "bottom-center" });
    }
  }

  useEffect(() => {
    scrollRef?.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages.length])

  const disabled = chatEndpointIsLoading || intermediateStepsLoading || input.length === 0;

  return (
    <div className="flex flex-row items-center justify-between max-w-screen max-h-screen overflow-hidden" 
      style={{ background }}
    >
      {/* Left Panel */}
      <div className="hidden lg:block md:flex h-[100vh] w-[45%] flex-grow px-1 overflow-x-overlay overflow-y-hidden">
        <Tabs defaultValue="observations" className="flex flex-col items-center pt-3 bg-transparent">
          <TabsList className="bg-background/70">
            <TabsTrigger value="observations">Observations</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
          </TabsList>
          <TabsContent value="observations">
            <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
              <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
                <div className="pt-2">
                  <ObservationModal companionId={companion.id} userId={user?.id!} path={path} />
                </div>
                <div className="pt-2">
                  My Observations
                </div>
              </div>
              <div className="mt-0">
                <ObservationNew observations={observations} />
              </div>
            </div>
            <div className="flex flex-col h-[35%] w-full px-2 justify-center items-center overflow-overlay">
              {/*<PolarAreaChart data={emotionsData} />*/}
            </div>
          </TabsContent>
          <TabsContent value="theme">
            <div className="w-full h-[3rem] flex justify-center items-center rounded !bg-cover !bg-center transition-all gap-1">
              <GradientPicker />
              <ThemeChanger />
            </div>
          </TabsContent>
          <TabsContent value="storage">
            <SupabaseDocument companion={companion} userId={user?.id!} supabaseFiles={supabaseFiles} path={path} />
          </TabsContent>
        </Tabs>
      </div>
      {/* Center Panel */}
      <div className="flex flex-col h-[100vh] 2xl:h-screen w-full pt-0 lg:px-2 overflow-hidden">
        <ChatHeader companion={companion} />
        <div className="flex flex-col items-center p-2 rounded max-h-full overflow-hidden">
          <div
            className="flex flex-col-reverse h-full w-full pb-[100px] mb-4 transition-[flex] ease-in-out overflow-auto"
            ref={messageContainerRef}
          >
            {messages.length > 0 ? (
              [...messages]
                .reverse()
                .map((m, i) => {
                  const sourceKey = (messages.length - 1 - i).toString();
                  return (m.role === "system" ? 
                    <IntermediateStep key={m.id} message={m} />
                    : 
                    <ChatMessageBubble 
                      key={m.id} 
                      message={m} 
                      aiEmoji={""} 
                      sources={sourcesForMessages[sourceKey]}
                      path={path}
                    />
                  )
                })
              ) : (
                <EmptyState 
                  name={companion?.name!}
                  description={companion?.description!}
                  introduction={companion?.introduction!}
                  onChoice={sendInitialQuestion} 
                />
            )}
          </div>
          <div ref={scrollRef} />
          <div className="fixed bottom-[4.3rem] left-[25%] translate-x-[50%] z-50">
            {!isDesktop && (
              <InsightDrawer 
                companion={companion} 
                observations={observations}
                chatSummary={chatSummary}
                todos={companion?.todos!}
                notes={notes}
                supabaseFiles={supabaseFiles} 
                userId={user?.id!}
                path={path} 
              />
            )}
          </div>
          <div className="fixed bottom-0 flex w-full flex-row justify-center items-center space-y-0 py-2 pb-0 sm:px-0">
            <ChatForm
              messages={messages}
              sendMessage={sendMessage}
              setMessages={setMessages}
              input={input}
              setInput={setInput}
              handleInputChange={handleInputChangeWithValidation}
              chatEndpointIsLoading={chatEndpointIsLoading}
              intermediateStepsLoading={intermediateStepsLoading}
              setIntermediateStepsLoading={setIntermediateStepsLoading}
              disabled={disabled}
              companionId={companion.id}
              threadId={threadId}
              existingThread={existingThread}
              path={path}
              formWidth={'100%'}
              endpoint={endpoint}
              showIngestForm={true}
              showIntermediateStepsToggle={true}
              setTemporaryURLInput={setTemporaryURLInput}
              showIntermediateSteps={showIntermediateSteps}
              setShowIntermediateSteps={setShowIntermediateSteps}
              uploadedFilesNum={supabaseFiles?.length}
              aiLanguage={selectedLanguage!}
              userId={userId}
            />
          </div>
        </div>
      </div>
      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[100vh] 2xl:h-[100vh] w-[45%] md:w-[45%] lg:w-[45%] px-1 overflow-hidden">
        <div className="flex flex-col px-2 justify-center items-center">
          <Tabs defaultValue="chatSummary" className="flex flex-col items-center pt-3 bg-transparent">
            <TabsList className="bg-background/70">              
              <TabsTrigger value="chatSummary">Summary</TabsTrigger>
              <TabsTrigger value="todos">Todos</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="chatSummary">
              <ChatSummaryNew chatSummary={chatSummary} />
            </TabsContent>
            <TabsContent value="todos">
              {companion.id !== "" && (
                <TodoList companionId={companion.id} todosData={companion ? companion.todos : []}/>
              )}
            </TabsContent>
            <TabsContent value="notes">
              <NoteNew note={notes} path={path} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
