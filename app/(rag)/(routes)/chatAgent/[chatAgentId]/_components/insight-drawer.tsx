'use client'

import dynamic from 'next/dynamic';
import { 
  Companion,
  Observations,
  ChatSummary,
  Todos,
  Note,
  File as FileType,
} from '@prisma/client'
import { SupabaseFile } from '@/lib/types'

import ObservationNew from '@/components/observation/observation-new'
const ObservationModal = dynamic(() => import('@/components/modals/ObservationModal').then((mod) => mod.ObservationModal), { ssr: false });
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
const NoteNew = dynamic(() => import('@/components/note/NoteNew'), { ssr: false });
const TodoList = dynamic(() => import('@/components/suggestion/TodoList'), { ssr: false });
import { SupabaseDocument } from '@/components/storage/SupabaseDocument'
import {
    Drawer,
    DrawerClose,
    DrawerContent,
    DrawerFooter,
    DrawerTrigger,
  } from "@/components/ui/drawer"
import { 
  Tabs, 
  TabsContent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from "@/components/ui/button";

export interface Props {
  companion: Companion
  observations: Observations[]
  chatSummary: ChatSummary[]
  todos: Todos[]
  notes: Note[]
  supabaseFiles: SupabaseFile[]
  userId: string
  path: string
}

export function InsightDrawer({ 
  companion, 
  observations, 
  chatSummary, 
  todos,
  notes,
  supabaseFiles, 
  userId, 
  path 
}: Props) {
  return (
    <div className="flex items-center">
      <Drawer direction='bottom'>
        <DrawerTrigger asChild>
          <Tooltip>
            <TooltipTrigger>
              <Button 
              variant='ghost' 
              className="flex justify-center items-center mx-auto h-2 w-[100px] rounded-full align-middle bg-muted py-[0.45rem]">
              ⋯⋯
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">Show Insight</TooltipContent>
          </Tooltip>
        </DrawerTrigger>
        <DrawerContent 
          className='h-[92%]'
        >
          <div className="mx-auto w-full max-w-sm">
            <Tabs defaultValue="observations" className="flex flex-col items-center pt-4 bg-transparent">
              <TabsList className="bg-background/70">
                <TabsTrigger value="observations" className="px-1">Observations</TabsTrigger>
                <TabsTrigger value="summary" className="px-1">Summary</TabsTrigger>
                <TabsTrigger value="todos" className="px-1">Todos</TabsTrigger>
                <TabsTrigger value="notes" className="px-1">Notes</TabsTrigger>
                <TabsTrigger value="storage" className="px-1">Storage</TabsTrigger>
              </TabsList>
              <ScrollArea>
                <TabsContent value="observations">
                  <ObservationNew observations={observations} />
                </TabsContent>
                <TabsContent value="chatSummary">
                  <ChatSummaryNew chatSummary={chatSummary} />
                </TabsContent>
                <TabsContent value="todos">
                  {companion.id !== "" && (
                    <TodoList companionId={companion.id} todosData={todos}/>
                  )}
                </TabsContent>
                <TabsContent value="notes">
                  <NoteNew note={notes} path={path} />
                </TabsContent>
                <TabsContent value="storage">
                  <SupabaseDocument companion={companion} userId={userId} supabaseFiles={supabaseFiles} path={path} />
                </TabsContent>
              </ScrollArea>
            </Tabs>
            <DrawerFooter>
              <div className="absolute right-0 top-0 rounded-lg">
                <DrawerClose>
                  <Button variant="ghost" className="rounded-lg">X</Button>
                </DrawerClose>
              </div>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}