import { Suspense } from 'react'
import Loading from '@/app/loading'
import { SidebarHeader } from '@/components/talk/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
//export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
//export const revalidate = 0

export interface ChatAgentLayoutProps {
  params: Promise<{ 
    chatAgentId: string
  }>
}

const ChatAgentLayout = async (
  props: ChatAgentLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/chatAgent/${params.chatAgentId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <Suspense fallback={<Loading />}>
        <SidebarHeader companionId={params.chatAgentId} path={path} />
      </Suspense>
      {children}
    </div>
  );
}

export default ChatAgentLayout;