import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import { Analytics } from '@vercel/analytics/react';
import { Suspense } from 'react'
import Loading from '@/app/loading'

import { cn } from '@/lib/utils';
import { ThemeProvider } from '@/components/providers/theme-provider'
import { ConvexClientProvider } from '@/components/providers/convex-provider'
import { Toaster } from '@/components/ui/toaster';
import { SoToaster } from "@/components/ui/sonner";
import { ProModal } from '@/components/pro-modal';
import { PayPerUseModal } from '@/components/credits/PayPerUseModal';
import { SubscriptionProvider } from '@/components/providers/SubscriptionContext';

import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ComfyMinds Lab',
  description: 'Your customized companion.',
  manifest: '/manifest.webmanifest',
  icons: { apple: '/icon-192x192.png' },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-tw" suppressHydrationWarning>
      <link rel="manifest" href="/manifest.webmanifest" crossOrigin="use-credentials" />
      <head>
        <script src="/serviceWorkerRegister.js" defer></script>
      </head>
      <body className={cn("bg-secondary", inter.className)} style={{ background: 'var(--gradient)' }}>
        <Suspense fallback={<Loading />}>
          <ClerkProvider>
            <ConvexClientProvider>
              <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
                <SubscriptionProvider>
                  <ProModal />
                  <PayPerUseModal />
                    {children}
                  <Analytics />
                  <Toaster />
                  <SoToaster richColors position="bottom-right" />
                </SubscriptionProvider>
              </ThemeProvider>
            </ConvexClientProvider>
          </ClerkProvider>
        </Suspense>
      </body>
    </html>
  )
}
