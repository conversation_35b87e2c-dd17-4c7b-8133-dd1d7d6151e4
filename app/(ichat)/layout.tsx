import { auth } from '@clerk/nextjs/server'
//import { DeepgramContextProvider } from "@/components/providers/DeepgramContextProvider";
//import { MicrophoneContextProvider } from '@/components/providers/MicrophoneContextProvider'
//import dynamic from 'next/dynamic';

//const MicrophoneContextProvider = dynamic(
//  () => import('@/components/providers/MicrophoneContextProvider').then(mod => mod.MicrophoneContextProvider),
//  { ssr: false }
//);

export default async function iChatLayout(
  {
    children,
  }: {
    children: React.ReactNode;
  }
) {
  await auth.protect()
  return (
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      {children}
    </div>
  );
}