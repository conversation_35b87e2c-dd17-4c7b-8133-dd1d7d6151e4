import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { formatDate } from '@/lib/utils'
import { getSharedChat } from '@/app/actions/ichatActions'
import { ChatList } from '@/app/(ichat)/(routes)/finance/[financeId]/_components/chat-list'
import { FooterText } from '@/components/gen-ui/footer'
import { AI, UIState, getUIStateFromAIState } from '@/ai/financeAI'

//export const runtime = 'edge'
export const preferredRegion = ['sfo1']

interface SharePageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata(props: SharePageProps): Promise<Metadata> {
  const params = await props.params;
  const chat = await getSharedChat(params.id)

  return {
    title: chat?.title.slice(0, 50) ?? 'Chat'
  }
}

export default async function SharePage(props: SharePageProps) {
  const params = await props.params;
  const chat = await getSharedChat(params.id)

  if (!chat || !chat?.sharePath) {
    notFound()
  }

  const uiState: UIState = getUIStateFromAIState(chat)

  return (
    <>
      <div className="flex-1 space-y-6">
        <div className="border-b bg-background px-4 py-6 md:px-6 md:py-8">
          <div className="mx-auto max-w-2xl">
            <div className="space-y-1 md:-mx-8">
              <h1 className="text-2xl font-bold">{chat.title}</h1>
              <div className="text-sm text-muted-foreground">
                {formatDate(chat.createdAt)} · {chat.messages.length} messages
              </div>
            </div>
          </div>
        </div>
        <AI>
          <ChatList messages={uiState} isShared={true} />
        </AI>
      </div>
      <FooterText className="py-8" />
    </>
  )
}