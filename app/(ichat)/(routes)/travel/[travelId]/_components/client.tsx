'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation'
import { useUser } from "@clerk/nextjs";

import { useUIState, useAIState } from '@ai-sdk/rsc';
import { 
  Companion,
  Message,
  Source,
  Observations,
  ChatSummary,
  Persona,
  Todos,
  Note,
} from '@prisma/client'
import { SupabaseFile } from '@/lib/types'
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs"

import { cn } from '@/lib/utils'
import { ChatFixedHeader } from '@/components/chat-fixed-header'
import { ChatList } from './chat-list'
import { ChatPanel } from './chat-panel'
import { EmptyScreen } from '@/components/gen-ui/empty-screen'
import { useLocalStorage } from '@/lib/hooks/use-local-storage'
import { useScrollAnchor } from '@/lib/hooks/use-scroll-anchor'
import { toast } from 'sonner'

export interface Props extends React.ComponentProps<'div'> {
  companion: Companion & {
    messages: (Message & {
      sources?: Source[];
    })[];
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  notes: Note[]
  supabaseFiles: SupabaseFile[]
  userId: string
  path: string
}

export function Chat({ 
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  supabaseFiles,
  userId,
  path,
  className 
  }: Props) {
  const router = useRouter()
  const fullPath = usePathname()
  const { user } = useUser();
  const [input, setInput] = useState('')
  const [messages] = useUIState()
  const [aiState] = useAIState()
  const [_, setNewChatId] = useLocalStorage('newChatId', threadId)

  useEffect(() => {
    if (threadId) {
      setNewChatId(threadId)
    }
  }, [threadId, setNewChatId])

  /*useEffect(() => {
    const messagesLength = aiState.messages?.length
    if (messagesLength === 2) {
      router.refresh()
    }
  }, [aiState.messages, router])*/



  const { messagesRef, scrollRef, visibilityRef, isAtBottom, scrollToBottom } =
  useScrollAnchor()

  return (
    <div className="flex flex-col h-[100vh] w-full items-center lg:px-4 pb-2 space-y-2">
      {companion ? <ChatFixedHeader companion={companion} countMessages={messages?.length} /> : null}
      <div className="flex-grow w-full pb-[150px] overflow-y-auto"
        ref={scrollRef}
      >
        <div
          className={cn('md:max-w-3xl 2xl:max-w-[1080px] mx-auto pb-[200px] pt-16', className)}
          ref={messagesRef}
        >
          {messages.length ? (
            <ChatList messages={messages} isShared={false} userId={user?.id!} />
          ) : (
            <EmptyScreen />
          )}
          <div className="w-full h-px" ref={visibilityRef} />
        </div>
        <ChatPanel
          id={threadId}
          path={path}
          input={input}
          setInput={setInput}
          isAtBottom={isAtBottom}
          scrollToBottom={scrollToBottom}
        />
      </div>
    </div>
  );
}
