"use client";

import React, { useContext, useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import { 
  Companion,
  Message,
  Source,
  Observations,
  ChatSummary,
  Persona,
  Todos,
  Note,
} from '@prisma/client'
import { SupabaseFile } from '@/lib/types'
import { SupabaseDocument } from '@/components/storage/SupabaseDocument'
import { useBackground } from '@/components/providers/BackgroundContext';
import { useMobileScreen } from "@/utils/mobile";
import { LoadingPage } from "@/components/ui/loading";
import { Chat } from './client'
import { LeftPanel } from '@/components/left-panel'
import { RightPanel } from '@/components/right-panel'
import { 
  SidebarContextProvider, 
  useSidebarContext 
} from "@/components/providers/SidebarContextProvider"

interface iChatProps {
  companion: Companion & {
    messages: (Message & {
      sources?: Source[];
    })[];
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  notes: Note[]
  supabaseFiles: SupabaseFile[]
  path: string
}


const useHasHydrated = () => {
  const [hasHydrated, setHasHydrated] = useState<boolean>(false);

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  return hasHydrated;
};

function Screen({ 
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  supabaseFiles,
  path
}: iChatProps) {
  const { user } = useUser();
  const { background, setBackground } = useBackground();
  const isMobileScreen = useMobileScreen();
  const { showLeftSidebar, setShowLeftSidebar, showRightSidebar, setShowRightSidebar } = useSidebarContext();

  const toggleLeftSidebar = () => {
    setShowLeftSidebar(!showLeftSidebar);
  };
  const toggleRightSidebar = () => {
    setShowRightSidebar(!showRightSidebar);
  };
  const showLeftSidebarOnMobile = showLeftSidebar || !isMobileScreen && showLeftSidebar;
  const showRightSidebarOnMobile = showRightSidebar || !isMobileScreen && showRightSidebar;

  useEffect(() => {
    const updateSidebarWidth = () => {
      let width = '200px';
      if (window.innerWidth >= 1536) { // 2xl breakpoint
        width = '420px';
      } else if (window.innerWidth >= 1024) { // lg breakpoint
        width = '270px';
      }
      document.documentElement.style.setProperty('--sidebar-width', width);
    };
  
    updateSidebarWidth();
    window.addEventListener('resize', updateSidebarWidth);
  
    return () => window.removeEventListener('resize', updateSidebarWidth);
  }, []);

  return (
    <div className="flex flex-row items-center justify-between max-w-screen max-h-screen overflow-hidden" 
      style={{ background }}
    >
      <div className="flex">
        <div
          className={`transition-all duration-500 ease-in-out flex-shrink-0 ${
            showLeftSidebar ? '2xl:w-[420px] lg:w-[270px] w-[200px]' : 'w-0'
          }`}
        >
          {showLeftSidebar &&
            <LeftPanel
              companion={companion}
              observations={companion ? companion.observations : []}
              supabaseFiles={supabaseFiles}
              //background={background}
              //setBackground={setBackground}
              userId={user?.id!}
              path={path}
            />
          }
        </div>
        <button
          onClick={toggleLeftSidebar}
          className="inset-y-0 z-20 my-auto transition-all duration-500 ease-in-out group flex h-16 w-4 flex-col items-center justify-center -space-y-1 outline-none *:h-3 *:w-1 *:rounded-full *:hover:bg-gray-600 max-md:hidden dark:*:hover:bg-gray-300 *:bg-gray-800 dark:*:bg-gray-200"
          style={{
            position: 'absolute',
            left: showLeftSidebar ? 'var(--sidebar-width)' : '0',
          }}
        >
          {!isMobileScreen && (showLeftSidebar ? (
            <>
              <div className="rotate-[20deg] w-2 h-2 transition-transform"></div>
              <div className="-rotate-[20deg] w-2 h-2 transition-transform"></div>
            </>
          ) : (
            <>
              <div className="-rotate-[20deg] w-2 h-2 transition-transform"></div>
              <div className="rotate-[20deg] w-2 h-2 transition-transform"></div>
            </>
          ))}
        </button>
      </div>
      <div className="flex-grow overflow-hidden">
        <Chat
          companion={companion}
          foundPersona={foundPersona}
          threadId={threadId}
          existingThread={existingThread}
          notes={notes}
          path={path}
          supabaseFiles={supabaseFiles}
          userId={user?.id!}
        />
      </div>
      <div className="flex">
        <div
          className={`transition-all duration-500 ease-in-out flex-shrink-0 ${
            showRightSidebar ? '2xl:w-[420px] lg:w-[270px] w-[200px]' : 'w-0'
          }`}
        >
          {showRightSidebar && 
            <RightPanel
              companionId={companion?.id!}
              chatSummary={companion?.chatSummaries!}
              todos={companion ? companion.todos : []}
              notes={notes}
              path={path}
            />
          }
        </div>
        <button 
          onClick={toggleRightSidebar}
          className="inset-y-0 z-10 my-auto transition-all duration-500 ease-in-out group flex h-16 w-4 flex-col items-center justify-center -space-y-1 outline-none *:h-3 *:w-1 *:rounded-full *:hover:bg-gray-600 max-md:hidden dark:*:hover:bg-gray-300 *:bg-gray-800 dark:*:bg-gray-200"
          style={{ 
            position: 'absolute', 
            right: showRightSidebar ? 'var(--sidebar-width)' : '0',
          }}
        >
          {!isMobileScreen && (showRightSidebar ? (
            <>
              <div className="-rotate-[20deg] w-2 h-2 transition-transform"></div> 
              <div className="rotate-[20deg] w-2 h-2 transition-transform"></div>
            </>
          ) : (
            <>
              <div className="rotate-[20deg] w-2 h-2 transition-transform" ></div>
              <div className="-rotate-[20deg] w-2 h-2 transition-transform"></div>
            </>
          ))}
        </button>
      </div>
    </div>
  );
}

export function Home({ 
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  supabaseFiles,
  path,
}: iChatProps) {
  if (!useHasHydrated()) {
    return <LoadingPage />;
  }

  return (
    <SidebarContextProvider>
      <Screen 
        companion={companion} 
        foundPersona={foundPersona} 
        threadId={threadId} 
        existingThread={existingThread} 
        notes={notes}
        supabaseFiles={supabaseFiles}
        path={path}
      />
    </SidebarContextProvider>
  );
}
