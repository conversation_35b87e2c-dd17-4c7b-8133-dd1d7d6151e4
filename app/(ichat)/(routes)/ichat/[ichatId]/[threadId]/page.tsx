import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'
import { auth } from "@clerk/nextjs/server";
import { Suspense } from 'react'
import Loading from '@/app/loading'
import prismadb from "@/lib/prismadb";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseFile } from '@/lib/types'
import { Persona } from "@prisma/client";
import { getFullCompanionById, getThread } from "@/app/actions/threadActions"
import { getChat } from '@/app/actions/ichatActions'
import { getNotes } from "@/app/actions/noteActions"
import { Home } from "../_components/home";
//import { Chat } from '../_components/client'

//export const runtime = 'edge'

export const preferredRegion = 'soft1'
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

export interface ChatPageProps {
  params: Promise<{
    threadId: string
    ichatId: string
  }>
}

type ThreadInfo = { id: string; name: string } | null;

export default async function ChatPage(props: ChatPageProps) {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    redirect(`/sign-in}`)
  }
  const companion = await getFullCompanionById(userId, params.ichatId, params.threadId)
  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    console.log("Companion not found or invalid data:", companion);
    return <div>Companion not found.</div>;
  }
  const existingThread: ThreadInfo = await getThread(
    userId, params.ichatId, params.threadId
  );
  const chat = await getChat(params.threadId, userId, params.ichatId)
  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })
  const notes =  await getNotes(userId)

  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  const { data, error } = await client.from("documents_1024").select();
  const supabaseFiles: SupabaseFile[] = data ?? [];
  const path = `/ichat/${companion?.id!}`


  return (
    <Suspense fallback={<Loading />}>
      <Home 
        companion={companion}
        foundPersona={foundPersona} 
        threadId={params.threadId} 
        existingThread={existingThread} 
        notes={notes} 
        supabaseFiles={supabaseFiles}
        path={path}
      />
    </Suspense>
  )
}