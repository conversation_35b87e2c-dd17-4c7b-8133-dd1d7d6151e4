import { EndpointsContext } from "@/app/(ichat)/(routes)/ichat/_ai/agent"
import { ReactNode } from "react"

export default function RootLayout(props: { children: ReactNode }) {
  return <EndpointsContext>{props.children}</EndpointsContext>
}
/*
import ClientLayout from './ClientLayout'
import { ReactNode } from "react"

export default function RootLayout({ children }: { children: ReactNode }) {
  return <ClientLayout>{children}</ClientLayout>
}*/
