"use client";

import dynamic from 'next/dynamic';
import { JSX, useState, useEffect, useRef, useCallback } from "react";
import { usePathname, useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { EndpointsContext } from "@/app/(ichat)/(routes)/ichat/_ai/agent";
import { useActions } from "@/utils/client";
import { Dispatcher } from "@/components/semantic-router/dispatcher";
import { LocalContext } from "@/ai/shared";
import { AIMessageText, HumanMessageText } from "@/components/prebuilt/message";
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { ChatFixedHeader } from "@/components/chat-fixed-header";
import { ChatList } from '@/components/prebuilt/chat-list'
import { ChatPanel } from '@/components/prebuilt/chat-panel'
import { PlayNeetsAudio } from '@/components/PlayNeetsAudio'
const InsightDrawer = dynamic(() => import('@/components/storage/insight-drawer').then((mod) => mod.InsightDrawer), { ssr: false });
import { useMediaQuery } from "@/hooks/use-media-query"
import { SupabaseFile } from '@/lib/types'
import { useLocalStorage } from 'usehooks-ts'
import { useAILanguage } from '@/hooks/useAILanguage'
import { useAIVoice } from '@/hooks/useAIVoice'
import { BeatLoader } from "react-spinners";

import { 
  Companion,
  Message,
  Source,
  Observations,
  ChatSummary,
  Persona,
  Todos,
  Note,
} from '@prisma/client'
import { type Language, DEFAULT_LANGUAGE } from "@/components/ui/ai-language"

export type LastEvent = {
  invokeModel?: {
    toolCall?: {
      id: string;
      name: string;
      parameters: Record<string, unknown>;
    };
    result?: string;
  };
  generate?: {
    result: string;
  };
  normalChat?: {
    result: string;
  };
  freeChat?: {
    result: string;
  };
  invokeTools?: {
    toolResult: any;
  };
  [key: string]: any; // This allows for other dynamic properties
}

export interface ChatProps {}

/*function convertFileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      resolve(base64String.split(",")[1]); // Remove the data URL prefix
    };
    reader.onerror = (error) => {
      reject(error);
    };
    reader.readAsDataURL(file);
  });
}

function FileUploadMessage({ fileName }: { fileName: string }) {
  return (
    <div className="flex w-full max-w-fit ml-auto">
      <p>File uploaded: {fileName}</p>
    </div>
  );
}*/

function FileUploadMessage({ fileName }: { fileName: string }) {
  return (
    <div className="flex w-full max-w-fit ml-auto">
      <p>File uploaded: {fileName}</p>
    </div>
  );
}

export interface Props extends React.ComponentProps<'div'> {
  companion: Companion & {
    messages: (Message & {
      sources?: Source[];
    })[];
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: { 
    id: string; 
    name: string 
  } | null;
  notes: Note[]
  supabaseFiles: SupabaseFile[]
  userId: string
  path: string
}

export function Chat({
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  supabaseFiles,
  userId,
  path
}: Props) {
  const router = useRouter()
  const actions = useActions<typeof EndpointsContext>();

  const [elements, setElements] = useState<JSX.Element[]>([]);
  const [tmpElements, setTmpElements] = useState<JSX.Element[]>([]);
  const [history, setHistory] = useState<[role: string, content: string][]>([]);
  const [input, setInput] = useState("");
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);
  const [lastEvent, setLastEvent] = useState<string | null>(null);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [temporaryURLInput, setTemporaryURLInput] = useState("");
  const [temporaryBlobUrl, setTemporaryBlobUrl] = useState<string>();
  const [chatEndpointIsLoading, setChatEndpointIsLoading] = useState(false);
  const [intermediateStepsLoading, setIntermediateStepsLoading] = useState(false);
  const [_, setNewChatId] = useLocalStorage('newChatId', threadId)
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const [selectedLanguage, setSelectedLanguage] = useAILanguage()
  const [aiVoice, setAIVoice] = useAIVoice()
  const tempInput = useRef<string>('');

  const updateTempInput = (value: string) => {
    tempInput.current = value;
  };

  useEffect(() => {
    console.log("aiVoice: ", aiVoice)
  }, [aiVoice])

  useEffect(() => {
    if (threadId) {
      setNewChatId(threadId)
    }
  }, [threadId, setNewChatId])

  /*useEffect(() => {
    const messagesLength = elements.length?.length
    if (messagesLength === 1) {
      router.refresh()
    }
  }, [elements, router])*/

  useEffect(() => {
    if (!lastEvent || !aiVoice) return
    const processAIVoice = async () => {
      PlayNeetsAudio(lastEvent, setAudio, selectedLanguage?.value);
      setLastEvent(null)
    };

    processAIVoice();
  }, [lastEvent, aiVoice]);
 
  async function onSubmit(input: string, kwargs?: any) {
    setIntermediateStepsLoading(true)
    await handleDispatch(input, kwargs).catch(console.error);
  }

  type Topic = 'travel' | 'enneagram';
  async function handleDispatch(input: string, kwargs?: any) {
    //const topic = await Dispatcher(input);
    const topic: Topic = "travel"
    console.log("topic: ", topic)
  
    switch (topic) {
      // @ts-ignore
      case 'enneagram':
        await onSubmitAgentGraph1(input, kwargs);
        break;
      // @ts-ignore
      case 'travel':
        await onSubmitAgentGraph2(input, kwargs);
        break;
      case null:
        await onSubmitAgentTraveler(input, kwargs);
        break;
    }
  }

  async function onSubmitAgentGraph1(input: string, kwargs?: any) {
    setLastEvent(null)
    //updateTempInput(kwargs ?? input);
    const newElements = [...elements];
    let fileBlobUrl: string | undefined = temporaryBlobUrl;
    
    /*let base64File: string | undefined = undefined;
    let fileExtension = selectedFile?.type.split("/")[1];
    if (selectedFile) {
      base64File = await convertFileToBase64(selectedFile);
    }*/

    const element = await actions.agent({
      input,
      input_options: {
        companionId: companion?.id!,
        threadId: threadId,
        aiLanguage: selectedLanguage,
      },
      chat_history: history,
      file:
        fileBlobUrl ? { blobUrl: fileBlobUrl } : undefined,
        /*base64File && fileExtension
          ? {
              base64: base64File,
              extension: fileExtension,
            }
          : undefined,*/
    });
    console.log("element: ", element);
    console.log("elements: ", elements);
  
    const uniqueKey = `${Date.now()}-${newElements.length}`;
    const toolUI = await element.toolUI;
    const aiUI = await element.aiUI;

    const humanUIComponent = (
      <div className="flex flex-col w-full gap-2 mt-auto">
        {temporaryBlobUrl && temporaryURLInput && (
          <FileUploadMessage fileName={temporaryURLInput} />
        )}
        <HumanMessageText content={kwargs ?? input} />
      </div>
    );
    const aiUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {aiUI}
      </div>
    );
    const toolUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {toolUI}
      </div>
    );
    newElements.push(
      <div className="flex flex-col w-full gap-2 mt-auto" key={uniqueKey}>
        {humanUIComponent}
        {toolUIComponent}
        {aiUIComponent}
      </div>
    );
    
    setElements(newElements);
    // consume the value stream to obtain the final value
    // after which we can append to our chat history state
    (async () => {
      const lastEvent = await element.lastEvent as LastEvent;

      //updateTempInput("");
      console.log("lastEvent: ", lastEvent);
      let userContent = kwargs ?? input;
      let assistantContent = '';

      /*if (lastEvent?.invokeModel && lastEvent?.invokeModel?.result) {
        assistantContent = lastEvent.invokeModel.result;        
      } else if (lastEvent?.generate && lastEvent?.generate?.result) {
        assistantContent = lastEvent.generate.result;
      } else if (lastEvent?.normalChat && lastEvent?.normalChat?.result) {
        assistantContent = lastEvent.normalChat.result;
      } else if (lastEvent?.invokeTools && lastEvent?.invokeTools?.toolResult) {
        assistantContent = `Tool result: ${JSON.stringify(lastEvent.invokeTools.toolResult, null, 2)}`;
      } else {
        console.log("ELSE!", lastEvent);
        return; // Exit if no valid event is found
      }*/

      if (lastEvent?.result) {
        assistantContent = lastEvent.result;        
      } else if (lastEvent?.toolResult) {
        assistantContent = `Tool result: ${JSON.stringify(lastEvent.toolResult, null, 2)}`;
      } else {
        console.log("ELSE!", lastEvent);
        return; // Exit if no valid event is found
      }

      console.log("assistantContent!", assistantContent);
      if (assistantContent) {
        setLastEvent(assistantContent)
        setHistory(prev => [
          ...prev,
          ["user", userContent],
          ["assistant", assistantContent]
        ]);
      }
      
      setIntermediateStepsLoading(false)
    })();

    console.log("History: ", history);
    setInput("");
    setTemporaryBlobUrl(undefined)
    
  }

  async function onSubmitAgentGraph2(input: string, kwargs?: any) {
    setLastEvent(null)
    //updateTempInput(kwargs ?? input);
    const newElements = [...elements];
    let fileBlobUrl: string | undefined = temporaryBlobUrl;

    const element = await actions.agent2({
      input,
      input_options: {
        companionId: companion?.id!,
        threadId: threadId,
        aiLanguage: selectedLanguage,
      },
      chat_history: history,
      file:
        fileBlobUrl ? { blobUrl: fileBlobUrl } : undefined,
    });
    console.log("element: ", element)
    console.log("elements: ", elements)

    const uniqueKey = `${Date.now()}-${newElements.length}`;
    const toolUI = await element.toolUI;
    const aiUI = await element.aiUI;
    const humanUIComponent = (
      <div className="flex flex-col w-full gap-2 mt-auto">
        {temporaryBlobUrl && temporaryURLInput && (
          <FileUploadMessage fileName={temporaryURLInput} />
        )}
        <HumanMessageText content={kwargs ?? input} />
      </div>
    );
    const aiUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {aiUI}
      </div>
    );
    const toolUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {toolUI}
      </div>
    );
    newElements.push(
      <div className="flex flex-col w-full gap-2 mt-auto" key={uniqueKey}>
        {humanUIComponent}
        {toolUIComponent}
        {aiUIComponent}
      </div>
    );
    
    // consume the value stream to obtain the final value
    // after which we can append to our chat history state
    (async () => {
      const lastEvent = await element.lastEvent as LastEvent;

      //updateTempInput("");
      
      let userContent = kwargs ?? input;
      let assistantContent = '';
    
      /*if (lastEvent?.invokeModel && lastEvent?.invokeModel?.result) {
        assistantContent = lastEvent.invokeModel.result;        
      } else if (lastEvent?.generate && lastEvent?.generate?.result) {
        assistantContent = lastEvent.generate.result;
      } else if (lastEvent?.normalChat && lastEvent?.normalChat?.result) {
        assistantContent = lastEvent.normalChat.result;
      } else if (lastEvent?.freeChat && lastEvent?.freeChat?.result) {
        assistantContent = lastEvent.freeChat.result;
      } else if (lastEvent?.invokeTools && lastEvent?.invokeTools?.toolResult) {
        assistantContent = `Tool result: ${JSON.stringify(lastEvent.invokeTools.toolResult, null, 2)}`;
      } else {
        console.log("ELSE!", lastEvent);
        return; // Exit if no valid event is found
      }*/

      if (lastEvent?.result) {
        assistantContent = lastEvent.result;        
      } else if (lastEvent?.toolResult) {
        assistantContent = `Tool result: ${JSON.stringify(lastEvent.toolResult, null, 2)}`;
      } else {
        console.log("ELSE!", lastEvent);
        return; // Exit if no valid event is found
      }

      if (assistantContent) {
        setLastEvent(assistantContent)
        setHistory(prev => [
          ...prev,
          ["user", userContent],
          ["assistant", assistantContent]
        ]);
      }
      //updateTempInput("");
      setIntermediateStepsLoading(false)
    })();

    setElements(newElements);
    setInput("");
    setTemporaryBlobUrl(undefined)
    //setSelectedFile(undefined);
  }

  async function onSubmitAgentTraveler(input: string, kwargs?: any) {
    setLastEvent(null)
    //updateTempInput(kwargs ?? input);
    const newElements = [...elements];

    // execute the agent with user input and chat history
    const element = await actions.traveler({ input, chat_history: history });
    console.log("newElements: ", newElements)
    console.log("element: ", element)
    const uniqueKey = `${Date.now()}-${newElements.length}`;
    const toolUI = await element.toolUI;
    const aiUI = await element.aiUI;

    const aiUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {aiUI}
      </div>
    );
    const toolUIComponent = (
      <div className="flex flex-col gap-2 w-full max-w-fit mr-auto">
        {toolUI}
      </div>
    );
    newElements.push(
      <div className="flex flex-col w-full gap-2 mt-auto" key={uniqueKey}>
        <HumanMessageText content={kwargs ?? input} />
        {toolUIComponent}
        {aiUIComponent}
      </div>
    );
        
    setElements(newElements);

    (async () => {
      let lastEvent = await element.lastEvent;    
      if (typeof lastEvent === "string") {
        console.log("LastEvent: ", lastEvent);  
        setLastEvent(lastEvent)
        setHistory((prev) => [
          ...prev,
          ["user", input],
          ["assistant", lastEvent],
        ]);
      }
      setIntermediateStepsLoading(false)
    })();
    setInput("")
    setTemporaryBlobUrl(undefined)
  }

  const disabled = chatEndpointIsLoading || intermediateStepsLoading || input.length === 0;

  return (
    <div className="flex flex-col h-[100vh] w-full px-4 overflow-hidden">
      <ChatFixedHeader companion={companion} countMessages={elements?.length} />
      <div className="flex-grow w-full pb-[150px] px-4 overflow-y-auto">
        <LocalContext.Provider value={onSubmit}>
          <div className={cn('max-w-[660px] 2xl:max-w-[980px] px-2 mx-auto md:pt-10')}>
            <ChatList elements={elements} userId={userId} isShared={false} />              
            {/*intermediateStepsLoading ? (  
              <div className="py-6">
                <BeatLoader color="indigo" size={8}/>
              </div>
            ) : (
              null
            )*/}
            <ChatScrollAnchor trackVisibility={intermediateStepsLoading} />
          </div>
        </LocalContext.Provider>
        <div className="fixed bottom-[4.3rem] left-[25%] translate-x-[50%] z-50">
          {!isDesktop && (
            <InsightDrawer 
              companion={companion} 
              observations={companion?.observations!}
              chatSummary={companion?.chatSummaries!}
              todos={companion?.todos!}
              notes={notes}
              supabaseFiles={supabaseFiles} 
              userId={userId}
              path={path} 
            />
          )}
        </div>
        <ChatPanel
          onSubmit={onSubmit}
          input={input}
          setInput={setInput}
          elements={elements}
          setElements={setElements}
          history={history}
          setHistory={setHistory}
          chatEndpointIsLoading={chatEndpointIsLoading}
          intermediateStepsLoading={intermediateStepsLoading}
          setIntermediateStepsLoading={setIntermediateStepsLoading}
          disabled={disabled}
          companionId={companion?.id!}
          threadId={threadId}
          existingThread={existingThread}
          path={path}
          formWidth={'100%'}
          showIngestForm={true}
          setTemporaryURLInput={setTemporaryURLInput}
          temporaryBlobUrl={temporaryBlobUrl}
          setTemporaryBlobUrl={setTemporaryBlobUrl}
          uploadedFilesNum={supabaseFiles?.length}
          aiLanguage={selectedLanguage!}
          userId={userId}
        />
      </div>

    </div>
  );
}
