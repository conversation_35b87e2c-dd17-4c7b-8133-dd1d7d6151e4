import { SidebarHeader } from '@/components/gen-ui/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface AgentLayoutProps {
  params: Promise<{ 
    ichatId: string
  }>
}

const AgentLayout = async (
  props: AgentLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/ichat/${params.ichatId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.ichatId} path={path} />
      {children}
    </div>
  );
}

export default AgentLayout;