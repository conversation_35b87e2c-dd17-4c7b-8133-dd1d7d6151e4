import "server-only";

import { agentExecutor } from "@/ai/graph";
import { agentExecutor2 } from "@/ai/graph2";
import { chainExecutor } from "@/ai/chain";
import { exposeEndpoints, streamRunnableUI } from "@/utils/graph-server";
import { AIMessage, HumanMessage } from "@langchain/core/messages";

//export const runtime = 'edge'
export const preferredRegion = ['sfo1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

const convertChatHistoryToMessages = (
  chat_history: [role: string, content: string][]
) => {
  return chat_history.map(([role, content]) => {
    switch (role) {
      case "human":
        return new HumanMessage(content);
      case "assistant":
      case "ai":
        return new AIMessage(content);
      default:
        return new HumanMessage(content);
    }
  });
};

function processInput(input: {
  input: string;
  chat_history: [role: string, content: string][];
  file?: {
    base64: string;
    extension: string;
  };
}) {
  if (input.file) {
    const imageTemplate = new HumanMessage({
      content: [
        {
          type: "image_url",
          image_url: {
            url: `data:image/${input.file.extension};base64,${input.file.base64}`,
          },
        },
      ],
    });
    return {
      input: input.input,
      chat_history: [
        ...convertChatHistoryToMessages(input.chat_history.slice(-12)),
        imageTemplate,
      ],
    };
  } else {
    return {
      input: input.input,
      chat_history: convertChatHistoryToMessages(input.chat_history.slice(-12)),
    };
  }
}

function processFile(input: {
  input: string;
  input_options: {
    companionId: string;
    threadId: string;
    aiLanguage: { value: string; label: string };
  };
  chat_history: [role: string, content: string][];
  file?: {
    blobUrl: string;
  };
}) {
  if (input.file) {
    const imageTemplate = new HumanMessage({
      content: [
        {
          type: "image_url",
          image_url: {
            url: input.file.blobUrl,
          },
        },
      ],
    });
    return {
      input: input.input,
      input_options: input.input_options,
      chat_history: [
        ...convertChatHistoryToMessages(input.chat_history.slice(-12)),
        imageTemplate,
      ],
    };
  } else {
    return {
      input: input.input,
      input_options: input.input_options,
      chat_history: convertChatHistoryToMessages(input.chat_history.slice(-12)),
    };
  }
}

async function agent(inputs: {
  input: string;
  input_options: {
    companionId: string;
    threadId: string;
    aiLanguage: { value: string; label: string };
  };
  chat_history: [role: string, content: string][];
  file?: {
    blobUrl: string;
  };
}) {
  "use server";
  const processedInputs = processFile(inputs);
  return streamRunnableUI(agentExecutor(), processedInputs);
}

async function agent2(inputs: {
  input: string;
  input_options: {
    companionId: string;
    threadId: string;
    aiLanguage: { value: string; label: string };
  };
  chat_history: [role: string, content: string][];
  file?: {
    blobUrl: string;
  };
}) {
  "use server";
  const processedInputs = processFile(inputs);
  return streamRunnableUI(agentExecutor2(), processedInputs);
}

async function traveler(inputs: {
  input: string;
  chat_history: [role: string, content: string][];
  file?: {
    base64: string;
    extension: string;
  };
}) {
  "use server";
  const processedInputs = processInput(inputs);
return streamRunnableUI(chainExecutor, processedInputs);
}

export const EndpointsContext = exposeEndpoints({ agent, agent2, traveler });