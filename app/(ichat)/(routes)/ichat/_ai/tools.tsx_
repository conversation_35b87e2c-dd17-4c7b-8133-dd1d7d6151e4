import { DynamicStructuredTool } from "langchain/tools";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { createRetrieverTool } from "langchain/tools/retriever";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";
import { createRunnableUI } from "@/utils/graph-server";
import {
  CurrentWeatherLoading,
  CurrentWeather,
} from "@/components/uiCard/weather";
import { Place } from "@/components/uiCard/place";
import { Images } from "@/components/uiCard/image";

export async function images(input: { query: string }) {
  type UrlParameters = Record<
    string,
    string | number | boolean | undefined | null
  >;

  function buildUrl<P extends UrlParameters>(
    path: string,
    parameters: P,
    baseUrl: string,
  ): string {
    const nonUndefinedParams: [string, string][] = Object.entries(parameters)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => [key, `${value}`]);
    const searchParams = new URLSearchParams(nonUndefinedParams);
    return `${baseUrl}/${path}?${searchParams}`;
  }

  const baseUrl = "https://serpapi.com";
  const response = await fetch(
    buildUrl(
      "search",
      {
        api_key: process.env.SERPAPI_API_KEY,
        q: input.query,
        engine: "google_images",
      },
      baseUrl,
    ),
  );

  const res = await response.json();

  if (res.error) {
    throw new Error(`Got error from serpAPI: ${res.error}`);
  }

  return z
    .object({
      images_results: z.array(
        z.object({
          original: z.string(),
          thumbnail: z.string(),
        }),
      ),
    })
    .parse(res);
}

// copied from the implementation of SerpAPI tool
// in @langchain/community
export async function search(input: { query: string }) {
  type UrlParameters = Record<
    string,
    string | number | boolean | undefined | null
  >;

  function buildUrl<P extends UrlParameters>(
    path: string,
    parameters: P,
    baseUrl: string,
  ): string {
    const nonUndefinedParams: [string, string][] = Object.entries(parameters)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => [key, `${value}`]);
    const searchParams = new URLSearchParams(nonUndefinedParams);
    return `${baseUrl}/${path}?${searchParams}`;
  }

  const baseUrl = "https://serpapi.com";
  const response = await fetch(
    buildUrl(
      "search",
      { api_key: process.env.SERPAPI_API_KEY, q: input.query },
      baseUrl,
    ),
  );

  const res = await response.json();

  if (res.error) {
    throw new Error(`Got error from serpAPI: ${res.error}`);
  }

  const answer_box = res.answer_box_list
    ? res.answer_box_list[0]
    : res.answer_box;
  if (answer_box) {
    if (answer_box.result) {
      return answer_box.result;
    } else if (answer_box.answer) {
      return answer_box.answer;
    } else if (answer_box.snippet) {
      return answer_box.snippet;
    } else if (answer_box.snippet_highlighted_words) {
      return answer_box.snippet_highlighted_words.toString();
    } else {
      const answer: { [key: string]: string } = {};
      Object.keys(answer_box)
        .filter(
          (k) =>
            !Array.isArray(answer_box[k]) &&
            typeof answer_box[k] !== "object" &&
            !(
              typeof answer_box[k] === "string" &&
              answer_box[k].startsWith("http")
            ),
        )
        .forEach((k) => {
          answer[k] = answer_box[k];
        });
      return JSON.stringify(answer);
    }
  }

  if (res.events_results) {
    return JSON.stringify(res.events_results);
  }

  if (res.sports_results) {
    return JSON.stringify(res.sports_results);
  }

  if (res.top_stories) {
    return JSON.stringify(res.top_stories);
  }

  if (res.news_results) {
    return JSON.stringify(res.news_results);
  }

  if (res.jobs_results?.jobs) {
    return JSON.stringify(res.jobs_results.jobs);
  }

  if (res.questions_and_answers) {
    return JSON.stringify(res.questions_and_answers);
  }

  if (res.popular_destinations?.destinations) {
    return JSON.stringify(res.popular_destinations.destinations);
  }

  if (res.top_sights?.sights) {
    const sights: Array<{ [key: string]: string }> = res.top_sights.sights
      .map((s: { [key: string]: string }) => ({
        title: s.title,
        description: s.description,
        price: s.price,
      }))
      .slice(0, 8);
    return JSON.stringify(sights);
  }

  if (res.shopping_results && res.shopping_results[0]?.title) {
    return JSON.stringify(res.shopping_results.slice(0, 3));
  }

  if (res.images_results && res.images_results[0]?.thumbnail) {
    return res.images_results
      .map((ir: { thumbnail: string }) => ir.thumbnail)
      .slice(0, 10)
      .toString();
  }

  const snippets: any[] = [];
  if (res.knowledge_graph) {
    if (res.knowledge_graph.description) {
      snippets.push(res.knowledge_graph.description);
    }

    const title = res.knowledge_graph.title || "";
    Object.keys(res.knowledge_graph)
      .filter(
        (k) =>
          typeof res.knowledge_graph[k] === "string" &&
          k !== "title" &&
          k !== "description" &&
          !k.endsWith("_stick") &&
          !k.endsWith("_link") &&
          !k.startsWith("http"),
      )
      .forEach((k) =>
        snippets.push(`${title} ${k}: ${res.knowledge_graph[k]}`),
      );
  }

  const first_organic_result = res.organic_results?.[0];
  if (first_organic_result) {
    if (first_organic_result.snippet) {
      snippets.push(first_organic_result.snippet);
    } else if (first_organic_result.snippet_highlighted_words) {
      snippets.push(first_organic_result.snippet_highlighted_words);
    } else if (first_organic_result.rich_snippet) {
      snippets.push(first_organic_result.rich_snippet);
    } else if (first_organic_result.rich_snippet_table) {
      snippets.push(first_organic_result.rich_snippet_table);
    } else if (first_organic_result.link) {
      snippets.push(first_organic_result.link);
    }
  }

  if (res.buying_guide) {
    snippets.push(res.buying_guide);
  }

  if (res.local_results?.places) {
    snippets.push(res.local_results.places);
  }

  if (snippets.length > 0) {
    return JSON.stringify(snippets);
  } else {
    return "No good search result found";
  }
}

const client = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_PRIVATE_KEY!,
);
const retriever = new SupabaseHybridSearch(
  new CohereEmbeddings({
    apiKey: process.env.COHERE_API_KEY,
    // @ts-ignore
    model: "embed-multilingual-v3.0", //dimension: 1024
  }),
  {
    client,
    similarityK: 4,
    keywordK: 0,
    tableName: "document_sections_1024",
    similarityQueryName: "match_document_sections_1024",
    keywordQueryName: "kw_match_document_sections",
});
const retrieverTool = createRetrieverTool(retriever, {
  name: "search_my_personal_report",
  description: "Search and return information about me or my report"
});

export const searchTool = new DynamicStructuredTool({
  name: "SerpAPI",
  description:
    "A search engine. useful for when you need to answer questions about current events. input should be a search query.",
  schema: z.object({ query: z.string() }),
  func: async (input, config) => {
    const stream = await createRunnableUI(config);
    stream.update(<div>Searching the internet...</div>);

    const result = await search(input);

    stream.done(
      <div className="flex gap-2 flex-wrap justify-end">
        {JSON.parse(result).map((place: any, index: number) => (
          <Place place={place} key={index} />
        ))}
      </div>,
    );

    return result;
  },
});

export const imagesTool = new DynamicStructuredTool({
  name: "Images",
  description: "A tool to search for images. input should be a search query.",
  schema: z.object({
    query: z.string().describe("The search query used to search for cats"),
    limit: z.number().describe("The number of pictures shown to the user"),
  }),
  func: async (input, config) => {
    const stream = await createRunnableUI(config);
    stream.update(<div>Searching...</div>);

    const result = await images(input);
    stream.done(
      <Images
        images={result.images_results
          .map((image) => image.thumbnail)
          .slice(0, input.limit)}
      />,
    );

    return `[Returned ${result.images_results.length} images]`;
  },
});

export async function weatherData(input: z.infer<typeof weatherSchema>) {
  const geoCodeApiKey = process.env.GEOCODE_API_KEY;
  if (!geoCodeApiKey) {
    throw new Error("Missing GEOCODE_API_KEY secret.");
  }

  const geoCodeResponse = await fetch(
    `https://geocode.xyz/${input.city.toLowerCase()},${input.state.toLowerCase()},${input.country.toLowerCase()}?json=1&auth=${geoCodeApiKey}`,
  );
  if (!geoCodeResponse.ok) {
    console.error("No geocode data found.");
    throw new Error("Failed to get geocode data.");
  }
  const geoCodeData = await geoCodeResponse.json();
  const { latt, longt } = geoCodeData;

  const openWeatherApiKey = process.env.OPENWEATHER_API_KEY
  if (!openWeatherApiKey) {
    throw new Error("Missing OPENWEATHER_API_KEY secret.");
  }
  const openWeatherResponse = await fetch(
    `https://api.openweathermap.org/data/2.5/weather?lat=${latt}&lon=${longt}&units=metric&appid=${openWeatherApiKey}`,
  );
  if (!openWeatherResponse.ok) {
    console.error("No weather data found.");
    throw new Error("Failed to get weather data.");
  }
  const openWeatherData = await openWeatherResponse.json();
  //console.log("openWeatherData", openWeatherData)

  return {
    ...input,
    temperature: openWeatherData.main.temp,
    feels_like: openWeatherData.main.feels_like,
    temp_min: openWeatherData.main.temp_min,
    temp_max: openWeatherData.main.temp_max,
    pressure: openWeatherData.main.pressure,
    humidity: openWeatherData.main.humidity,
    description: openWeatherData.weather[0].description,
    timezone: openWeatherData.timezone,
    icon: openWeatherData.weather[0].icon,
  };
}

export const weatherSchema = z.object({
  city: z.string().describe("The city name to get weather for"),
  state: z
  .string()
  .describe("The two letter state abbreviation to get weather for"),
  country: z
    .string()
    .optional()
    .default("usa")
    .describe("The two letter country abbreviation to get weather for"),
});

export const weatherTool = new DynamicStructuredTool({
  name: "get_weather",
  description:
    "A tool to fetch the current weather, given a city and state. If the city/state is not provided, ask the user for both the city and state.",
  schema: weatherSchema,
  func: async (input, config) => {
    const stream = await createRunnableUI(config, <CurrentWeatherLoading />);
    const data = await weatherData(input);
    stream.done(<CurrentWeather {...data} />);
    return JSON.stringify(data, null);
  },
});