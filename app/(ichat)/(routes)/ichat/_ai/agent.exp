import { JS<PERSON>, ReactNode, isValidElement } from "react";
import { unstable_noStore as noStore } from 'next/cache';
import { Runnable } from "@langchain/core/runnables";
import { exposeEndpoints, streamRunnableUI } from "@/utils/graph-server";
import "server-only";
import { StreamEvent } from "@langchain/core/tracers/log_stream";
import { EventHandlerFields } from "@/utils/graph-server";
import { createStreamableUI, createStreamableValue, StreamableValue } from "ai/rsc";
import { createRunnableUI } from "@/utils/graph-server";
import { type RunnableConfig } from "@langchain/core/runnables";
import { agentExecutor } from "@/ai/graph";
import { agentExecutor2 } from "@/ai/graph2";
import { chainExecutor } from "@/ai/chain";
import { AIMessage as AIBaseMessage, HumanMessage } from "@langchain/core/messages";
import { AIMessage } from "@/ai/message";
import { RetrieverLoading } from "@/components/uiCard/retriever";
import { sleep } from '@/lib/utils';

import { STREAM_UI_RUN_NAME, LAMBDA_STREAM_WRAPPER_NAME } from "@/utils/graph-server";

export const preferredRegion = ['sfo1']
export const maxDuration = 30;
export const dynamic = 'force-dynamic';

type AgentInputs = {
  input: string;
  input_options?: {
    companionId: string;
    threadId: string;
    aiLanguage: { value: string; label: string };
  };
  chat_history: [role: string, content: string][]
  file?: {
    blobUrl?: string;
    base64?: string;
    extension?: string;
  };
}
type OutputComponent = {
  loading: (props?: any) => JSX.Element;
  final: (props?: any) => JSX.Element;
};

type OutputComponentMap = {
  [key: string]: OutputComponent;
};

const OUTPUT_COMPONENT_MAP: OutputComponentMap = {
  "generate": {
    loading: (props?: any) => <RetrieverLoading />,
    final: (content: string) => <AIMessageText content={content} />,
  },
};

function AIMessageText(props: {
  content: string;
}) {
  return (
    <div className="flex mr-auto w-fit max-w-[700px] bg-gradient-to-r from-emerald-400 to-cyan-400 dark:from-teal-800 dark:to-indigo-700 ... drop-shadow-lg shadow-inner bg-opacity-70 text-secondary-text rounded-md p-2 px-3 mt-3">
      <div className="text-normal text-gray-800 text-left break-words">
        {props.content}
      </div>
    </div>
  );
}

const convertChatHistoryToMessages = (
  chatHistory: [role: string, content: string][]
) => {
  return chatHistory.map(([role, content]) => {
    switch (role) {
      case "human":
        return new HumanMessage(content);
      case "assistant":
      case "ai":
        return new AIBaseMessage(content);
      default:
        return new HumanMessage(content);
    }
  });
};

function processInput(input: AgentInputs) {
  const chatHistory = convertChatHistoryToMessages(input.chat_history.slice(-12));
  
  if (input.file) {
    const imageContent = input.file.base64 
      ? { url: `data:image/${input.file.extension};base64,${input.file.base64}` }
      : { url: input.file.blobUrl };
    
    const imageTemplate = new HumanMessage({
      content: [{ type: "image_url", image_url: imageContent }],
    });
    
    chatHistory.push(imageTemplate);
  }

  return {
    input: input.input,
    input_options: input.input_options,
    chat_history: chatHistory,
  };
}

let selectedOutputComponent: OutputComponent | null = null;
let selectedOutputUI: ReturnType<typeof createStreamableUI> | null = null;

const handleInvokeToolsEvent = (
  event: StreamEvent
) => {
  const [type] = event.event.split("_").slice(2);
  //console.log("####################hasDisplay1####################");
  //console.log("####################handleInvokeToolsEventName####################", event.name);
  //console.log("####################handleInvokeToolsEventOutput####################", event.data.output);
  if (
    event.event !== "on_chain_end" ||
    !event.data.output ||
    typeof event.data.output !== "object" ||
    event.name !== "STREAM_UI_RUN_NAME"
  ) {
    return;
  }

  console.log("####################handleInvokeToolsName####################", event.name);
  console.log("####################handleInvokeToolsEvent####################", event.event);
  console.log("####################handleInvokeToolsEventOutput####################", event.data.output);
};
  
const handleChatModelStreamEvent = (
  event: StreamEvent, 
  fields: EventHandlerFields
) => {
  if (
    event.event !== "on_chat_model_stream" ||
    !event.data.chunk ||
    typeof event.data.chunk !== "object"
  )
    return;
    console.log("####################handleChatModelStreamEventName####################", event.name);
    console.log("####################handleChatModelStreamEvent####################", event.event);
    console.log("####################handleChatModelStreamEventChunk####################", event.data.chunk);
  if (!fields.callbacks[event.run_id]) {
    const textStream = createStreamableValue();
    fields.ui.append(<AIMessage value={textStream.value} />);
    fields.callbacks[event.run_id] = textStream;
  }

  if (fields.callbacks[event.run_id]) {
    fields.callbacks[event.run_id].append(event.data.chunk.content);
  }
};

const handleLLMStreamEvent = (
  event: StreamEvent, 
  fields: EventHandlerFields
) => {
  if (
    event.event !== "on_llm_stream" ||
    !event.data.chunk ||
    typeof event.data.chunk !== "object"
  )
    return;
    console.log("####################handleLLMStreamEventName####################", event.name);
    console.log("####################handleLLMStreamEvent####################", event.event);
    console.log("####################handleLLMStreamEventChunk####################", event.data.chunk);
 
    if (!fields.callbacks[event.run_id]) {
      const textStream = createStreamableValue();
      fields.ui.append(<AIMessage value={textStream.value} />);
      fields.callbacks[event.run_id] = textStream;
    }
  
    if (fields.callbacks[event.run_id]) {
      fields.callbacks[event.run_id].append(event.data.chunk.content);
    }
};

function handleLangGraphEvent(
  event: StreamEvent, 
  fields: EventHandlerFields
) {

  if (
    event.name !== "LangGraph" ||
    event.event !== "on_chain_end" ||
    !event.data.output ||
    typeof event.data.output !== "object"
  )
    return;
    console.log("####################handleLangGraphEventName####################", event.name);
    console.log("####################handleLangGraphEventEvent####################", event.event);
    console.log("####################handleLangGraphEventOutput####################", event.data.output);

    const output = event.data.output 
    if (output) {
      (async () => {
        /*if (!selectedOutputComponent && !selectedOutputUI) {
          selectedOutputComponent = OUTPUT_COMPONENT_MAP["generate"];
          selectedOutputUI = await createStreamableUI(selectedOutputComponent.loading());
          fields.ui.append(selectedOutputUI?.value);
        }
        if (selectedOutputUI && selectedOutputComponent) {
          const outputData = output.generate.result;
          selectedOutputUI.done(selectedOutputComponent.final(outputData));
        }*/
        //fields.ui.append(<AIMessageText content={output.generate.result} />);
    })()
  }
}

async function runAgent(
  inputs: AgentInputs, 
  executor: any
) {
  "use server";
  const processedInputs = processInput(inputs);
  return streamRunnableUI(executor, processedInputs, {
    eventHandlers: [handleLangGraphEvent],
  });
}

async function agent(inputs: AgentInputs) {
  "use server";
  return runAgent(inputs, agentExecutor());
}

async function agent2(inputs: AgentInputs) {
  "use server";
  return runAgent(inputs, agentExecutor2());
}

async function traveler(inputs: AgentInputs) {
  "use server";
  return runAgent(inputs, chainExecutor);
}

export const EndpointsContext = exposeEndpoints({ agent, agent2, traveler });