'use client'

import * as React from 'react'
import Textarea from 'react-textarea-autosize'

import { useActions, useUIState } from '@ai-sdk/rsc'

import { UserMessage } from '@/components/uiCard/stocks/message'
import type { AI }from '@/ai/financeAI';
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { IconArrowElbow, IconPlus } from '@/components/ui/icons'
import { Hint } from "@/components/ui/hint";
import { useEnterSubmit } from '@/lib/hooks/use-enter-submit'
import { generateId } from 'ai'
import { useRouter } from 'next/navigation'

export function PromptForm({
  input,
  setInput,
  path
}: {
  input: string
  setInput: (value: string) => void
  path?: string
}) {
  const router = useRouter()
  const { formRef, onKeyDown } = useEnterSubmit()
  const inputRef = React.useRef<HTMLTextAreaElement>(null)
  const { submitUserMessage } = useActions()
  const [_, setMessages] = useUIState<typeof AI>()

  React.useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <form
      ref={formRef}
      onSubmit={async (e: any) => {
        e.preventDefault()

        // Blur focus on mobile
        if (window.innerWidth < 600) {
          e.target['message']?.blur()
        }

        const value = input.trim()
        setInput('')
        if (!value) return

        // Optimistically add user message UI
        setMessages(currentMessages => [
          ...currentMessages,
          {
            id: generateId(),
            display: <UserMessage>{value}</UserMessage>
          }
        ])

        // Submit and get response message
        const responseMessage = await submitUserMessage(value)
        setMessages(currentMessages => [...currentMessages, responseMessage])
      }}
    >
      <div className="relative flex flex-col w-full overflow-hidden max-h-60 grow bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... px-4 sm:rounded-md border-0 sm:px-4">
        <Hint
          label="New Chat"
          side="right"
          align="start"
          sideOffset={18}
        >
          <Button
            variant="outline"
            size="icon"
            className="absolute left-1 top-4 h-8 w-8 rounded-full bg-secondary p-0 sm:left-1"
            onClick={() => {         
              if (path) {
                router.push(path)
              } else {
                router.back()
              }
            }}
          >
            <IconPlus />
            <span className="sr-only">New Chat</span>
          </Button>
        </Hint>
        <Textarea
          ref={inputRef}
          tabIndex={0}
          onKeyDown={onKeyDown}
          placeholder="Send a message."
          className="min-h-[60px] w-full px-8 resize-none bg-transparent text-white focus-within:outline-none"
          autoFocus
          spellCheck={false}
          autoComplete="off"
          autoCorrect="off"
          name="message"
          rows={1}
          value={input}
          onChange={e => setInput(e.target.value)}
        />
        <div className="absolute right-1 top-4">
          <Hint
            label="Send message"
            side="left"
            align="start"
            sideOffset={18}
          >
            <Button type="submit" size="icon" disabled={input === ''}>
              <IconArrowElbow />
              <span className="sr-only">Send message</span>
            </Button>
          </Hint>
        </div>
      </div>
    </form>
  )
}