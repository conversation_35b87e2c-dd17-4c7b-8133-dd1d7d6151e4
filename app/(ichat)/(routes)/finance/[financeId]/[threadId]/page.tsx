import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { Suspense } from 'react'
import Loading from '@/app/loading'
import prismadb from "@/lib/prismadb";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseFile } from '@/lib/types'
import { Persona } from "@prisma/client";
import { getFullCompanionById, getThread } from "@/app/actions/threadActions"
import { AI } from '@/ai/financeAI';
import { getChat } from '@/app/actions/ichatActions'
import { getNotes } from "@/app/actions/noteActions"
import { Home } from '../_components/home'

//export const runtime = 'edge'

export const preferredRegion = ['sfo1']

export interface StockClientPageProps {
  params: Promise<{
    threadId: string
    financeId: string
  }>
}

export async function generateMetadata(props: StockClientPageProps): Promise<Metadata> {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    return {}
  }

  const chat = await getChat(params.threadId, userId, params.financeId)

  return {
    title: chat?.title.toString().slice(0, 50) ?? 'Stock'
  }
}

type ThreadInfo = { id: string; name: string } | null;

export default async function StockClientPage(props: StockClientPageProps) {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    return redirect(`/sign-in`);
  }

  const companion = await getFullCompanionById(userId, params.financeId, params.threadId)
  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    console.log("Companion not found or invalid data:", companion);
    return <div>Companion not found.</div>;
  }
  const existingThread: ThreadInfo = await getThread(
    userId, params.financeId, params.threadId
  );

  const chat = await getChat(params.financeId, userId, params.threadId)
  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })
  const notes =  await getNotes(userId)
  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  const { data, error } = await client.from("documents_1024").select();
  const supabaseFiles: SupabaseFile[] = data ?? [];
  const path = `/finance/${params.financeId}`;

  return (
    <Suspense fallback={<Loading />}>
      <AI initialAIState={{ companionId: params.financeId, chatId: params.threadId, messages: chat?.messages! || [] }}>
        <Home 
          companion={companion}
          foundPersona={foundPersona} 
          threadId={params.threadId} 
          existingThread={existingThread} 
          notes={notes} 
          supabaseFiles={supabaseFiles}
          path={path}
        />
      </AI>
    </Suspense> 
  )
}