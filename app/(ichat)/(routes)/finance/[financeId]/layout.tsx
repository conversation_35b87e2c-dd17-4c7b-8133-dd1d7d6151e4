import { Suspense } from 'react'
import Loading from '@/app/loading'
import { SidebarHeader } from '@/components/gen-ui/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface AgentLayoutProps {
    params: Promise<{
      financeId: string
    }>
  }

const AgentLayout = async (
  props: AgentLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/finance/${params.financeId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <Suspense fallback={<Loading />}>
        <SidebarHeader companionId={params.financeId} path={path} />
      </Suspense>
      {children}
    </div>
  );
}

export default AgentLayout;