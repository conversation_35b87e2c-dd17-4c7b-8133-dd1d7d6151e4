import { Suspense } from 'react'
import Loading from '@/app/loading'
import { SidebarHeader } from '@/components/talk/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface AgentLayoutProps {
  params: Promise<{ 
    interviewId: string
  }>
}

const AgentLayout = async (
  props: AgentLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/interview/${params.interviewId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <Suspense fallback={<Loading />}>
        <SidebarHeader companionId={params.interviewId} path={path} />
      </Suspense>
      {children}
    </div>
  );
}

export default AgentLayout;