import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { nanoid } from '@/lib/utils'

//export const runtime = 'edge'

export const preferredRegion = ['sfo1']

export interface InterviewIdPageProps {
  params: Promise<{
    interviewId: string
  }>
}

export default async function GenUIdPage(props: InterviewIdPageProps) {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
      return redirect(`/sigin-in`);
    }

  const threadId = nanoid()
  if (threadId) {
    redirect(`/interview/${params.interviewId}/${threadId}`)
  } else {
    redirect(`/`)
  }
}