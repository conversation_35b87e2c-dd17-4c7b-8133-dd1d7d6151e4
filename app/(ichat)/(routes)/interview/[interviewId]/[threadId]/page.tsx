import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { Suspense } from 'react'
import Loading from '@/app/loading'
import prismadb from "@/lib/prismadb";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseFile } from '@/lib/types'
import { Persona } from "@prisma/client";
import { getFullCompanionById, getThread } from "@/app/actions/threadActions"
import { 
  type OrganizationView, 
  getOrganizationsView 
} from "@/app/actions/getOrganizations";

import { getChat } from '@/app/actions/ichatActions'
import { getNotes } from "@/app/actions/noteActions"
import { Home } from '../_components/home'

export const dynamic = 'force-dynamic';

export const preferredRegion = ['sfo1']

export interface InterviewClientPageProps {
  params: Promise<{
    threadId: string
    interviewId: string
  }>
}

export async function generateMetadata(props: InterviewClientPageProps): Promise<Metadata> {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    return {}
  }

  //const chat = await getChat(params.threadId, userId, params.interviewId)

  //return {
  //  title: chat?.title.toString().slice(0, 50) ?? 'Interview'
  //}
}

type ThreadInfo = { id: string; name: string } | null;

export default async function InterviewClientPage(props: InterviewClientPageProps) {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    return redirect(`/sign-in`);
  }

  const organizations: OrganizationView[] = await getOrganizationsView({ adminId: userId });

  const companion = await getFullCompanionById(userId, params.interviewId, params.threadId)
  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    console.log("Companion not found or invalid data:", companion);
    return <div>Companion not found.</div>;
  }
  const existingThread: ThreadInfo = await getThread(
    userId, params.interviewId, params.threadId
  );

  //const chat = await getChat(params.threadId, userId, params.interviewId)
  //console.log("getChat:", chat);
  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })
  const notes =  await getNotes(userId)
  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  const { data, error } = await client.from("documents_1024").select();
  const supabaseFiles: SupabaseFile[] = data ?? [];
  const path = `/interview/${params.interviewId}`;

  return (
    <Suspense fallback={<Loading />}>
      <Home 
        organizations={organizations}
        companion={companion}
        foundPersona={foundPersona} 
        threadId={params.threadId} 
        existingThread={existingThread} 
        notes={notes} 
        supabaseFiles={supabaseFiles}
        path={path}
      />
    </Suspense> 
  )
}