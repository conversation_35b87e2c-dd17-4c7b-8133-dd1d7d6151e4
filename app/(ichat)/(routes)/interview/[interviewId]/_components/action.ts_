'use server';

import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { createOpenAI } from '@ai-sdk/openai';
import { createStreamableValue } from 'ai/rsc';
import { currentUser } from '@clerk/nextjs/server'
import { 
  InterviewAgent
} from '@/components/observation/ObservationAgent'

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});

type InterviewProps = {
  threadId: string;
  companionId: string;
  message: string;
  persona: {
    name: string;
    nickName: string;
    age: number;
    traits: string;
    status: string; 
  };
  useReflectionThreshold: number;
  path?: string;
  targetSessionId?: string
  targetUserId?: string
}

export const interviewAgent = async ({
  companionId,
  message,
  persona,
  useReflectionThreshold,
  path,
  targetSessionId,
  targetUserId
}: InterviewProps) => {
  const user = await currentUser()
  const userId = user?.id!

  if (!userId) {
    throw new Error("Unauthorized");
  }

  const stream = createStreamableValue('');

  (async () => {
    try {
      const model = groq('llama-3.3-70b-versatile');

      const interviewSummary = await InterviewAgent({
        companionId,
        message,
        name: persona?.name || user?.firstName!,
        age: persona?.age || 25,
        traits: persona?.traits || "",
        status: persona?.status || "",
        reflectionThreshold: useReflectionThreshold,
        path,
        targetSessionId,
        targetUserId
      });

      if (!interviewSummary) {
        stream.update('No summary available');
        stream.done();
        return;
      }

      // Simulate streaming by sending the text chunk by chunk
      const chunkSize = 5; // Adjust this value to control the chunk size
      for (let i = 0; i < interviewSummary.length; i += chunkSize) {
        const chunk = interviewSummary.slice(i, i + chunkSize);
        stream.update(chunk);
        // Add a small delay to simulate network latency
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      stream.done();
    } catch (error: any) {
      console.error("Error fetching observations:", error);
      stream.update(`Error: ${error.message}`);
      stream.done();
    }
  })();

  return { output: stream.value };
};


export async function InterviewContext(context: string, prompt: string) {
  console.log("context", context)
  const user = await currentUser()
  const userId = user?.id!
  if (!userId) {
    throw new Error("Unauthorized");
  }

  const stream = createStreamableValue('');

  (async () => {
    const { textStream } = await streamText({
      model: groq('llama-3.3-70b-versatile'),
      prompt: `
      You are an AI email assistant embedded in an email client app. Your purpose is to help the user compose emails by providing suggestions and relevant information based on the context of their previous emails.
        
      THE TIME NOW IS ${new Date().toLocaleString()}
        
      START CONTEXT BLOCK
      ${context}
      END OF CONTEXT BLOCK
        
      USER PROMPT:
      ${prompt}
        
      When responding, please keep in mind:
      - Be helpful, clever, and articulate. 
      - Rely on the provided email context to inform your response.
      - If the context does not contain enough information to fully address the prompt, politely give a draft response.
      - Avoid apologizing for previous responses. Instead, indicate that you have updated your knowledge based on new information.
      - Do not invent or speculate about anything that is not directly supported by the email context.
      - Keep your response focused and relevant to the user's prompt.
      - Don't add fluff like 'Heres your email' or 'Here's your email' or anything like that.
      - Directly output the email, no need to say 'Here is your email' or anything like that.
      - No need to output subject
      `,
    });

    for await (const delta of textStream) {
      stream.update(delta);
    }

    stream.done();
  })();

  return { output: stream.value };
}

export async function SimpleInterview(
  input: string
) {
  const user = await currentUser()
  const userId = user?.id!
  if (!userId) {
    throw new Error("Unauthorized");

  }
  const stream = createStreamableValue('');

  console.log("input", input);
  (async () => {
    const { textStream } = await streamText({
      model: groq('llama-3.3-70b-versatile'),
      prompt: `
      ALWAYS RESPOND IN PLAIN TEXT, no html or markdown.
      You are a helpful AI embedded in a email client app that is used to autocomplete sentences, similar to google gmail autocomplete
      The traits of AI include expert knowledge, helpfulness, cleverness, and articulateness.
      AI is a well-behaved and well-mannered individual.
      AI is always friendly, kind, and inspiring, and he is eager to provide vivid and thoughtful responses to the user.
      I am writing a piece of text in a notion text editor app.
      Help me complete my train of thought here: <input>${input}</input>
      keep the tone of the text consistent with the rest of the text.
      keep the response short and sweet. Act like a copilot, finish my sentence if need be, but don't try to generate a whole new paragraph.
      Do not add fluff like "I'm here to help you" or "I'm a helpful AI" or anything like that.

      Example:
      Dear Alice, I'm sorry to hear that you are feeling down.

      Output: Unfortunately, I can't help you with that.

      Your output is directly concatenated to the input, so do not add any new lines or formatting, just plain text.
      `,
    });

    for await (const delta of textStream) {
      stream.update(delta);
    }

    stream.done();
  })();

  return { output: stream.value };
}