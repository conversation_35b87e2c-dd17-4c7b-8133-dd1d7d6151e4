"use client";

import "./_components/styles.css";
import { ArrowUp } from "lucide-react";
import { useState, useEffect } from "react";
import { generateId} from "ai"
import { Header } from "@/components/copilot/spreadsheet/Header";
import SpreadsheetSidebar from "@/components/copilot/spreadsheet/SpreadsheetSidebar";
import SingleSpreadsheet from "@/components/copilot/spreadsheet/SingleSpreadsheet";
import {
  CopilotKit,
  useCopilotAction,
  useCopilotReadable,
} from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import { INSTRUCTIONS } from "./_components/instructions";
import { canonicalSpreadsheetData } from "@/utils/canonicalSpreadsheetData";
import { SpreadsheetData } from "@/lib/types";
import { PreviewSpreadsheetChanges } from "@/components/copilot/spreadsheet/PreviewSpreadsheetChanges";

const SpreedsheetPage = () => {
  return (
    <CopilotKit
      //publicApiKey={process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY}
      // Alternatively, you can use runtimeUrl to host your own CopilotKit Runtime
      runtimeUrl="/api/copilotkit/spreadsheet"
      //transcribeAudioUrl="/api/transcribe"
      //textToSpeechUrl="/api/tts"
    >
      <CopilotSidebar
        icons={{sendIcon: <ArrowUp size={20}/>}}
        instructions={INSTRUCTIONS}
        labels={{
          initial: "Welcome to the spreadsheet app! How can I help you?",
        }}
        defaultOpen={false}
        clickOutsideToClose={false}
      >
        <Main />
      </CopilotSidebar>
    </CopilotKit>
  );
};

const Main = () => {
  const now = new Date();
  const formattedDate = now.toISOString().split('T')[0]; // Extract date in YYYY-MM-DD format
  const hours = now.getHours();
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const period = hours >= 12 ? 'pm' : 'am';
  const formattedTime = `${hours % 12 || 12}${minutes}${period}`; // Convert to 12-hour format with am/pm
  const sheetName = `Sheet ${formattedDate}_${formattedTime}`;
  const [spreadsheets, setSpreadsheets] = useState<SpreadsheetData[]>([
    {
      id: generateId(),
      title: sheetName,
      rows: Array(8).fill(Array(12).fill({ value: "" })),
    },
  ]);

  const [selectedSpreadsheetIndex, setSelectedSpreadsheetIndex] = useState(0);

  useEffect(() => {
    const savedData = localStorage.getItem("spreadsheets");
    if (savedData) {
      setSpreadsheets(JSON.parse(savedData));
    }
  }, []);

  useCopilotAction({
    name: "createSpreadsheet",
    description: "Create a new  spreadsheet",
    inputSchema: [
      {
        name: "rows",
        type: "object[]",
        description: "The rows of the spreadsheet",
        attributes: [
          {
            name: "cells",
            type: "object[]",
            description: "The cells of the row",
            attributes: [
              {
                name: "value",
                type: "string",
                description: "The value of the cell",
              },
            ],
          },
        ],
      },
      {
        name: "title",
        type: "string",
        description: "The title of the spreadsheet",
      },
    ],
    render: (props) => {
      const { rows, title } = props.args;
      const newRows = canonicalSpreadsheetData(rows);

      return (
        <PreviewSpreadsheetChanges
          preCommitTitle="Create spreadsheet"
          postCommitTitle="Spreadsheet created"
          newRows={newRows}
          commit={(rows) => {
            const newSpreadsheet: SpreadsheetData = {
              id: generateId(),
              title: title || "Untitled Spreadsheet",
              rows: rows,
            };
            setSpreadsheets((prev) => [...prev, newSpreadsheet]);
            setSelectedSpreadsheetIndex(spreadsheets.length);
          }}
        />
      );
    },
    handler: ({ rows, title }) => {
      // Do nothing.
      // The preview component will optionally handle committing the changes.
    },
  });

  useCopilotReadable({
    description: "Today's date",
    value: new Date().toLocaleDateString(),
  });

  return (
    <div className="flex">
      <SpreadsheetSidebar
        spreadsheets={spreadsheets}
        selectedSpreadsheetIndex={selectedSpreadsheetIndex}
        setSelectedSpreadsheetIndex={setSelectedSpreadsheetIndex}
        setSpreadsheets={setSpreadsheets}
      />
      <div className="flex-1 flex-col overflow-auto">
        <Header 
          currentSheetIndex={selectedSpreadsheetIndex}
          setCurrentSheetIndex={setSelectedSpreadsheetIndex}
          spreadsheets={spreadsheets}
          setSpreadsheets={setSpreadsheets}
          spreadsheet={spreadsheets[selectedSpreadsheetIndex]}
          setSpreadsheet={(spreadsheet) => {
            setSpreadsheets((prev) => {
              console.log("setSpreadsheet", spreadsheet);
              const newSpreadsheets = [...prev];
              newSpreadsheets[selectedSpreadsheetIndex] = spreadsheet;
              return newSpreadsheets;
            });
          }}
        /> 
        <SingleSpreadsheet
          spreadsheet={spreadsheets[selectedSpreadsheetIndex]}
          setSpreadsheet={(spreadsheet) => {
            setSpreadsheets((prev) => {
              console.log("setSpreadsheet", spreadsheet);
              const newSpreadsheets = [...prev];
              newSpreadsheets[selectedSpreadsheetIndex] = spreadsheet;
              return newSpreadsheets;
            });
          }}
        />        
      </div>

    </div>
  );
};

export default SpreedsheetPage;
