@media (min-width: 640px) {
  .copilotKitSidebarContentWrapper.sidebarExpanded {
    margin-right: 24rem;
  }
}

@media (min-width: 640px) {
  .copilotKitWindow {
    transform-origin: bottom right;
    bottom: 5rem;
    right: 1rem;
    top: auto;
    left: auto;
    border-width: 0px;
    margin-bottom: 1rem;
    width: 24rem;
    height: 600px;
    min-height: 200px;
    max-height: calc(100% - 6rem);
  }

  .copilotKitSidebar .copilotKitWindow {
    bottom: 0;
    right: 0;
    top: auto;
    left: auto;
    width: 24rem;
    min-height: 100%;
    margin-bottom: 0;
    max-height: none;
  }
}

.copilotKitInput {
  border-top: none;
  background-color: white;
  margin-bottom: 10px;
  margin-left: 10px;
  margin-right: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 10px;
}

::-webkit-scrollbar {
  background-color: #468497;
  width: 10px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background-color: #f0f5f7;
  border-radius: 20px;
  width: 10px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 20px;
  width: 10px;
  height: 12px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c0c0c0;
  width: 10px;
  height: 12px;
}