'use client'

import { CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import { Header } from "@/components/copilot/article/Header"
import { Article } from "@/components/copilot/article/Article"


export default function WriteArticle() {
  return (
    <>
      <Header />
      <CopilotKit 
        runtimeUrl="/api/copilotkit/presentation"
      >
        <CopilotSidebar
          instructions="Help the user research a blog article topic."
          defaultOpen={true}
          labels={{
            title: "Blog Article Copilot",
            initial:
              "Hi you! 👋 I can help you research any topic for a blog article.",
          }}
          clickOutsideToClose={false}>
          <Article />
        </CopilotSidebar>
      </CopilotKit>
    </>
  );
}