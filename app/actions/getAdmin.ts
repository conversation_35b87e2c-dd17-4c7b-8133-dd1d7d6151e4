'use server'

import prismadb from "@/lib/prismadb";

//export const runtime = 'edge'

interface getAdminProps {
  id: string;
}

const getAdmin = async ({
  id
}: getAdminProps) => {
    try {
    // Fetch the Admin record associated with the current user's profile
    const admin = await prismadb.admin.findUnique({
      where: {
        id,
      },
        cacheStrategy: {
        swr: 60,
        ttl: 30
      },
      include: {
        adminOrganizations: true, // Include the organization in the result
      },
    });
    
    return admin;
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null;
  }
}
 
export default getAdmin;