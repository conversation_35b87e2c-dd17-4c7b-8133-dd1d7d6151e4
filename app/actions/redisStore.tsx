'use server'

import { auth } from '@clerk/nextjs/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { type Morphic } from '@/lib/types'
import { Redis } from '@upstash/redis'

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
})

export async function getChats(userId?: string | null) {
  if (!userId) {
    return []
  }

  try {
    const pipeline = redis.pipeline()
    const chats: string[] = await redis.zrange(`user:chat:${userId}`, 0, -1, {
      rev: true
    })

    for (const chat of chats) {
      pipeline.hgetall(chat)
    }

    const results = await pipeline.exec()

    return results as Morphic[]
  } catch (error) {
    return []
  }
}

export async function getChat(id: string, userId: string) {
  const chat = await redis.hgetall<Morphic>(`chat:${id}`)

  if (!chat) {
    return null
  }

  return chat
}

export async function removeChat({ 
  id, 
  path 
}: { 
  id: string; 
  path: string 
}) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const uid = await redis.hget<string>(`chat:${id}`, 'userId')

  if (uid !== userId) {
    return {
      error: 'Unauthorized'
    }
  }
  
  await redis.del(`chat:${id}`)
  await redis.zrem(`user:chat:${userId}`, `chat:${id}`)

  revalidatePath(path)
  return revalidatePath(path)
}

export async function clearChats(
): Promise<{ error?: string }> {
  const { userId } = await auth()
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }
  const chats: string[] = await redis.zrange(`user:chat:${userId}`, 0, -1)
  if (!chats.length) {
    return { error: 'No chats to clear' }
  }
  const pipeline = redis.pipeline()

  for (const chat of chats) {
    pipeline.del(chat)
    pipeline.zrem(`user:chat:${userId}`, chat)
  }

  await pipeline.exec()

  revalidatePath('/morphic')
  redirect('/morphic')
}

export async function saveChat(chat: Morphic) {
  const { userId } = await auth()
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }
  const pipeline = redis.pipeline()
  pipeline.hmset(`chat:${chat.id}`, chat)
  pipeline.zadd(`user:chat:${chat.userId}`, {
    score: Date.now(),
    member: `chat:${chat.id}`
  })
  await pipeline.exec()
  revalidatePath('/morphic')
}

export async function getSharedChat(id: string) {
  const chat = await redis.hgetall<Morphic>(`chat:${id}`)

  if (!chat || !chat.sharePath) {
    return null
  }

  return chat
}

export async function shareChat(id: string) {
  const { userId } = await auth()
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }
  const chat = await redis.hgetall<Morphic>(`chat:${id}`)

  if (!chat || chat.userId !== userId) {
    return null
  }

  const payload = {
    ...chat,
    sharePath: `/morphic/share/${id}`
  }

  await redis.hmset(`chat:${id}`, payload)

  return payload
}
