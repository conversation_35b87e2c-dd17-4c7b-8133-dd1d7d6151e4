'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from "@clerk/nextjs/server"

import prismadb from "@/lib/prismadb";
import { Note, Image } from "@prisma/client";
import { notesIndex } from "@/lib/pinecone";
//import { getEmbedding } from "@/lib/openai";
import { getEmbedding } from "@/lib/cohere";

import {
  createNoteSchema,
  deleteNoteSchema,
  updateNoteSchema,
} from "@/lib/validation/note";


export async function postNote({
    title,
    content,  
    companionName,
    path,
  }:{
    title: string;
    content: string,
    companionName: string,
    path?: string
  }) {
    try {
      const { userId } = await auth()
  
      if (!userId) {
        return {
          error: 'Unauthorized'
        }
      }
  
      if (!title || !content) {
        throw new Error('Missing required fields');
      };

      const embedding = await getEmbeddingForNote(title, content);

      const note = await prismadb.$transaction(async (tx) => {
        const note = await tx.note.create({
          data: {
            title,
            content,
            companionName,
            userId,
          },
        });
  
        await notesIndex.upsert([
          {
            id: note.id,
            values: embedding as number[],
            metadata: { userId },
          },
        ]);
      });
      
      if (path) {
        revalidatePath(path);
      }
    
      return note;

    } catch (error) {
      console.log("[NOTE_CREATED]", error);
      throw new Error("Note not created");
    }
  }

  export async function getNotes(
    userId: string
    ) {  
    if (!userId) {
      return []
    }
  
    try {
      const notes = await prismadb.note.findMany({
        where: {
          userId
        },
        orderBy: {
          createdAt: 'asc'
        },
      });
  
      return notes;
    } catch (error: any) {
      return [];
    }
  };

  export async function editNote({
    id,
    title,
    content,
    path,
  }: {
    id: string;
    title: string;
    content: string;
    path?: string;
  }) {
  try {
    if (!id || !title || !content) {
      return {
        error: 'Missing required fields'
      }
    };

    const note = await prismadb.note.findUnique({ where: { id } });
    
    if (!note) {
      return { error: "Note not found" };
    }

    const { userId,redirectToSignIn } = await auth();

    if (!userId || userId !== note.userId) {
      return redirectToSignIn();
    }

    const embedding = await getEmbeddingForNote(title, content);

    const updatedNote = await prismadb.$transaction(async (tx) => {
      const updatedNote = await tx.note.update({
        where: { id },
        data: {
          title,
          content,
        },
      });

      await notesIndex.upsert([
        {
          id,
          values: embedding as number[],
          metadata: { userId },
        },
      ]);

      return updatedNote;
    });
    
    if (path) {
      revalidatePath(path)
    }    

  } catch (error) {
    console.log("[NOTE_UPDATED]", error);
    throw new Error("Note not updated");

  }
};

  export async function deleteNote({
    id,
    path,
  }: {
    id: string;
    path?: string;
  }) {
  try {
    const { userId, redirectToSignIn } = await auth();

    if (!userId) {
      return redirectToSignIn();
    }

    if (!id) {
      return {
        error: 'Missing required fields'
      }
    };

    const note = await prismadb.note.findUnique({ where: { id } });
    
    if (!note) {
      return { error: "Note not found" };
    }

    await prismadb.$transaction(async (tx) => {
      await tx.note.delete({ where: { userId, id } });
      await notesIndex.deleteOne(id);
    });
    
    if (path) {
      revalidatePath(path)
    }    

  } catch (error) {
    console.log("[NOTE_DELETED]", error);
    throw new Error("Note not deleted");
  }
};

async function getEmbeddingForNote(title: string, content: string | undefined) {
  return getEmbedding(title + "\n\n" + (content ?? ""));
}