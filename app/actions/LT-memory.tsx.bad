import { 
  ChatPromptTemplate, 
  SystemMessagePromptTemplate, 
  MessagesPlaceholder 
} from "@langchain/core/prompts";

import {
  RunnablePassthrough,
  RunnableSequence,
} from "@langchain/core/runnables";

import { ChatGroq } from "@langchain/groq";
import { ChatOpenAI } from "@langchain/openai";
import { codeBlock } from 'common-tags';
import { ToolExecutor } from "@langchain/langgraph/prebuilt";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { 
  convertToOpenAITool } from "@langchain/core/utils/function_calling";
import { StateGraph, StateGraphArgs, START, END } from "@langchain/langgraph";
import {
  BaseMessage, ToolMessage, 
  HumanMessage, } from "@langchain/core/messages";
import { z } from 'zod';
import { pull } from "langchain/hub";
import { AgentExecutor, createOpenAIToolsAgent } from "langchain/agents";

const system_prompt_initial = codeBlock`
  Your job is to assess a brief chat history in order to determine if the conversation contains any details about a family's dining habits. 

  You are part of a team building a knowledge base regarding a family's dining habits to assist in highly customized meal planning.

  You play the critical role of assessing the message to determine if it contains any information worth recording in the knowledge base.

  You are only interested in the following categories of information:

  1. The family's food allergies (e.g. a dairy or soy allergy)
  2. Foods the family likes (e.g. likes pasta)
  3. Foods the family dislikes (e.g. doesn't eat mussels)
  4. Attributes about the family that may impact weekly meal planning (e.g. lives in Austin; has a husband and 2 children; has a garden; likes big lunches; etc.)

  When you receive a message, you perform a sequence of steps consisting of:

  1. Analyze the message for information.
  2. If it has any information worth recording, return TRUE. If not, return FALSE.

  You should ONLY RESPOND WITH TRUE OR FALSE. Absolutely no other information should be provided.

  Take a deep breath, think step by step, and then analyze the following message:
`

export enum Category {
  FoodAllergy = "Allergy",
  FoodLike = "Like",
  FoodDislike = "Dislike",
  FamilyAttribute = "Attribute",
}

export enum Action {
  Create = "Create",
  Update = "Update",
  Delete = "Delete",
}

interface AddKnowledge {
  knowledge: string;
  knowledgeOld?: string | undefined;
  category: Category;
  action: Action;
}

const modifyKnowledge = async ({ knowledge, category, action, knowledgeOld = "" }: AddKnowledge): Promise<string> => {
  console.log("Modifying Knowledge: ", knowledge, knowledgeOld, category, action);

  // Your logic to modify the knowledge goes here
  const modifiedKnowledge = "Modified Knowledge";

  return modifiedKnowledge;
};

export const addKnowledgeSchema = z.object({
  knowledge: z.string()
    .nonempty({ message: "Knowledge is required" })
    .max(500, "Knowledge must be at most 500 characters")
    .describe("Condensed bit of knowledge to be saved for future reference in the format: [person(s) this is relevant to] [fact to store] (e.g. Husband doesn't like tuna; I am allergic to shellfish; etc)"),
  knowledgeOld: z.string()
    .optional()
    .describe("If updating or deleting record, the complete, exact phrases that need to be modified"),
  category: z.nativeEnum(Category)
    .describe("Category that this knowledge belongs to"),
  action: z.nativeEnum(Action)
    .describe("Whether this knowledge is adding a new record, updating a record, or deleting a record"),
});


const toolModifyKnowledge = new DynamicStructuredTool({
  name: "Knowledge_Modifier",
  description: "Add, update, or delete a bit of knowledge",
  func: modifyKnowledge,
  schema: addKnowledgeSchema,
});

// Set up the agent's tools
const tools = [toolModifyKnowledge]

//const toolExecutor = new ToolExecutor({ tools });


interface AgentState {
  // The list of previous messages in the conversation
  messages: BaseMessage[];
  // The long-term memories to remember
  memories: string[];
  // Whether the information is relevant
  containsInformation: string;
}

// This defines the agent state
const graphState: StateGraphArgs<AgentState>["channels"] = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  memories: {
    value: (left?: string[], right?: string[]) =>
      right ? right : left || [],
    default: () => [],
  },
  containsInformation: {
    value: (left?: string, right?: string) => right ? right : left || '', // Keep the latest value
    default: () => "",
  },
};

async function callSentinel(state: AgentState): Promise<{ containsInformation: string }> {
  const messages = state.messages;
  console.log("callSentinel: ", state)
  // Get the prompt to use
  const prompt = ChatPromptTemplate.fromMessages([
    SystemMessagePromptTemplate.fromTemplate(system_prompt_initial),
    new MessagesPlaceholder("messages"),
    ["system", "Remember, only respond with TRUE or FALSE. Do not provide any other information."],
  ]);

  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
    streaming: true,
  });

  const sentinelRunnable = RunnableSequence.from([
    { messages: new RunnablePassthrough() },
    prompt,
    llm,
  ]);

  // Assuming sentinel_runnable.invoke is an asynchronous function that takes messages and returns a response
  const response = await sentinelRunnable.invoke(messages);
  console.log("sentinelResponse: ", response)
  
  if (typeof response.content === 'string') {
    return { containsInformation: response.content.includes("TRUE") ? "yes" : "no" };
  } else {
    // Handle the case where response.content is an object
    return { containsInformation: "no" }; // or any other fallback value
  }
}


async function callKnowledgeMaster(state: AgentState): Promise<AgentState> {
  const messages = state.messages;
  const memories = state.memories;
  console.log("---KNOWLEDGEMASTER: STATE---", state);
  
  const systemPromptInitial = `
  You are a supervisor managing a team of knowledge experts.
  
  Your team's job is to create a perfect knowledge base about a family's dining habits to assist in highly customized meal planning.
  
  The knowledge base should ultimately consist of many discrete pieces of information that add up to a rich persona (e.g. I like pasta; I am allergic to shellfish; I don't eat mussels; I live in Austin, Texas; I have a husband and 2 children aged 5 and 7).
  
  Every time you receive a message, you will evaluate if it has any information worth recording in the knowledge base.
  
  A message may contain multiple pieces of information that should be saved separately.
  
  You are only interested in the following categories of information:
  
  1. The family's food allergies (e.g. a dairy or soy allergy) - These are important to know because they can be life-threatening. Only log something as an allergy if you are certain it is an allergy and not just a dislike.
  2. Foods the family likes (e.g. likes pasta) - These are important to know because they can help you plan meals, but are not life-threatening.
  3. Foods the family dislikes (e.g. doesn't eat mussels or rarely eats beef) - These are important to know because they can help you plan meals, but are not life-threatening.
  4. Attributes about the family that may impact weekly meal planning (e.g. lives in Austin; has a husband and 2 children; has a garden; likes big lunches, etc.)
  
  When you receive a message, you perform a sequence of steps consisting of:
  
  1. Analyze the most recent Human message for information. You will see multiple messages for context, but we are only looking for new information in the most recent message.
  2. Compare this to the knowledge you already have.
  3. Determine if this is new knowledge, an update to old knowledge that now needs to change, or should result in deleting information that is not correct. It's possible that a food you previously wrote as a dislike might now be a like, or that a family member who previously liked a food now dislikes it - those examples would require an update.
  
  Here are the existing bits of information that we have about the family.
  
  memories: {memories}
  
  Call the right tools to save the information, then respond with DONE. If you identiy multiple pieces of information, call everything at once. You only have one chance to call tools.
  
  I will tip you $20 if you are perfect, and I will fine you $40 if you miss any important information or change any incorrect information.
  
  Take a deep breath, think step by step, and then analyze the following message:
  `
  
  const prompt = ChatPromptTemplate.fromMessages([
    SystemMessagePromptTemplate.fromTemplate(systemPromptInitial),
    new MessagesPlaceholder("messages"),
    ["system", "Remember, only respond with TRUE or FALSE. Do not provide any other information."],
  ]);
  
  const gradeTool = tools.map((tool) => convertToOpenAITool(tool));
  console.log("---gradeTool---", gradeTool);

  const llm = new ChatGroq({
    model: "llama-3.3-70b-versatile",
    temperature: 0,
    streaming: true,
  })
  
  const knowledgeMasterRunnable = RunnableSequence.from([prompt, llm.bind({ tools: gradeTool })]);
  // Assuming knowledge_master_runnable.invoke is an asynchronous function that takes an object with messages and memories
  const response = await knowledgeMasterRunnable.invoke({ messages, memories });
  console.log("knowledgeMasterResponse: ", response)
  return {...state, messages: [...messages, response] }; // Assuming response is a BaseMessage
}

function shouldCallknowledgeMaster(state: AgentState): string {
  console.log("---DECIDE CALL KNOWLEDGEMASTER---");
  console.log("state", state);
  const containsInformation = [...state.containsInformation];
  console.log("---ContainsInformation---", containsInformation);

  // If there is no function call then we finish.
  //if (!lastMessage.additional_kwargs.function_call) {
  if (!containsInformation) {
    console.log("---DECISION: DO NOT CALL KNOWLEDGEMASTER / DONE---");
    return "no";
  }
  console.log("---DECISION: CALL KNOWLEDGEMASTER---");
  return "yes";
};

function shouldContinue(state: AgentState): string {
  if (state.messages.length === 0) {
    console.log("---DECISION: NO MESSAGES YET---");
    return "continue"; // or any other desired behavior
  }
  const lastMessage = state.messages[state.messages.length - 1];
  if (!lastMessage.additional_kwargs.tool_calls) {
    console.log("---DECISION: DO NOT ADD KNOWLEDGE / DONE---");
    return "end";
  } else {
    console.log("---DECISION: RETRIEVE---");
    return "continue";
  }
}

async function callTool(state: AgentState) {
  let messages = [...state.messages]; // Make a copy to avoid mutating the original state
  const lastMessage = messages[messages.length - 1];
  console.log("state: ", state)
  console.log("lastMessage: ", lastMessage)

  for (const toolCall of lastMessage.additional_kwargs?.tool_calls || []) {
    const action = {
      tool: toolCall.function.name,
      toolInput: JSON.parse(toolCall.function.arguments),
      id: toolCall.id,
    }

    // Assuming toolExecutor.invoke is an asynchronous function that takes an action and returns a response
    //const response = await toolExecutor.invoke(action);
    const prompt = await pull<ChatPromptTemplate>(
      "hwchase17/openai-tools-agent"
    );
    
    const llm = new ChatGroq({
      model: "llama-3.3-70b-versatile",
      temperature: 0,
      streaming: true,
    });
    const agent = await createOpenAIToolsAgent({
      llm,
      tools,
      prompt,
    });
    
    const agentExecutor = new AgentExecutor({
      agent,
      tools,
      verbose: true,
    });
    const response = await agentExecutor.invoke({ input: lastMessage });
    const functionMessage = new ToolMessage({
      content: response.toString(),
      name: action.tool,
      tool_call_id: toolCall.id,
    });

    messages.push(functionMessage);
  }

  return { messages };
}



/**==================================================================
 * Build Graph
 ==================================================================*/
// Define a new graph
const workflow = new StateGraph<AgentState>({
  channels: graphState,
})
  .addNode("sentinel", callSentinel)
  .addNode("knowledgeMaster", callKnowledgeMaster)
  .addNode("action", callTool)

// Define all our Edges

// Set the Starting Edge
workflow.setEntryPoint("sentinel");

// Define conditional edges
workflow.addConditionalEdges(
  "sentinel",
  shouldCallknowledgeMaster,
  {
    yes: "knowledgeMaster",
    no: END,
  }
);

workflow.addConditionalEdges(
  // First, we define the start node. We use `agent`.
  // This means these are the edges taken after the `agent` node is called.
  "knowledgeMaster",
  // Next, we pass in the function that will determine which node is called next.
  shouldContinue,
  {
    // If `tools`, then we call the tool node.
    continue: "action",
    // Otherwise we finish.
    end: END
  }
);

workflow.addEdge("action", END)

// We compile the entire workflow as a runnable
const app = workflow.compile()
const testMessage = "There are 6 people in my family. My wife doesn't eat meat and my youngest daughter is allergic to dairy."
export async function semanticMemory(message: string) {
  const inputs = {
    messages: [new HumanMessage(message)]
  }

  let finalGeneration;
  for await (const output of await app.withConfig({runName: "Memory"}).stream(inputs)) {
    for (const [key, value] of Object.entries(output)) {
      console.log(`Node: '${key}'`);
      // Optional: log full state at each node
      // console.log(JSON.stringify(value, null, 2));
      finalGeneration = value;
    }
    console.log("\n---\n");
  }
  
  // Log the final generation.
  console.log(JSON.stringify(finalGeneration, null, 2));
}
