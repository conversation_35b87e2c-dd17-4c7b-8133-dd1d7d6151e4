'use server';

import { auth, currentUser } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Observations } from '@prisma/client';
import { generateText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { createClient } from "@supabase/supabase-js";
import { CohereEmbeddings } from "@langchain/cohere";
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { codeBlock } from 'common-tags';
import { 
  ObservationAgent, InterviewAgent
} from '@/components/observation/ObservationAgent'

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});
const model = groq('llama-3.3-70b-versatile', {
});

const getObservations = async (companionId: string): Promise<Observations[]> => {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    const observations = await prismadb.observations.findMany({
      where: {
        userId,
        companionId,
      },
    });

    if (!observations || observations.length === 0) {
      return []
    }

    return observations;
  } catch (error: any) {
    console.error("Error fetching observations:", error);
    throw new Error(error.message || "Error fetching observations");
  }
};

export default getObservations;


type ObservationProps = {
  threadId: string;
  companionId: string;
  prompt: string;
  persona: {
    name: string;
    nickName: string;
    age: number;
    traits: string;
    status: string; 
  };
  useReflectionThreshold: number;
  path?: string;
}

export const addObservation = async ({
  threadId,
  companionId,
  prompt,
  persona,
  useReflectionThreshold,
  path
}: ObservationProps) => {
  const user = await currentUser()
  const userId = user?.id!

  if (!userId) {
    throw new Error("Unauthorized");
  }

  try {
    console.log("prompt: ", prompt)
    const filterMetadata = threadId
    ? { session_id: threadId, owner_id: userId }
    : { owner_id: userId };

    const client = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_PRIVATE_KEY!,
    );

    const embeddings = new CohereEmbeddings({
      apiKey: process.env.COHERE_API_KEY,
      model: "embed-multilingual-light-v3.0",
    });
    const vectorStore = new SupabaseVectorStore(embeddings, {
      client,
      tableName: "observations_cohere",
      queryName: "match_observations_cohere",
      filter: filterMetadata
    });
    const prevObservations = await vectorStore.similaritySearch(prompt, 3);
    // Convert and prepend `last_accessed_at` to each pageContent
    const observedContent = prevObservations.map(doc => {
      const lastAccessedAt = new Date(doc.metadata.last_accessed_at * 1000); // Assuming `last_accessed_at` is a Unix timestamp
      const formattedDate = lastAccessedAt.toLocaleString(); // Formats based on the user's locale
      return `${formattedDate}: ${doc.pageContent}`;
    }).join("\n");

    console.log("///////////////////// prevObservations: ///////////////////", prevObservations)
    const generateFactsPrompt = codeBlock`Given the conversation and perviouse observations below:
    Current conversation:
    """ THE TIME NOW IS ${new Date().toLocaleString()}
    ${prompt}
    """
    Previous observations:
    """
    ${observedContent}
    """

    Observe ${persona?.name!}'s facts, life goals or interests. 
    Note any standout characteristics or traits about the user.


    If no observations are available, respond with "none".

    Do not embellish.
    Do not mention what you did not observe.
    Do not repeat the question and previouse observations.
    Response in concise. Ex: ${persona?.name!} ensures project members receive appropriate rewards and feels a sense of accomplishment when completing difficult projects.
    `

    console.log("=======================generateFactsPrompt: =======================", generateFactsPrompt)
    const { text, finishReason, usage } = await generateText({
      model,
      prompt: generateFactsPrompt,
    });

    if (text && text !== "none") {
      const observationSummary = await ObservationAgent({
        userId,
        sessionId: threadId,
        companionId,
        prompt,
        messages: [text],
        prevObservations: prevObservations,
        name: persona?.name || user?.firstName!,
        age: persona?.age || 25,
        traits: persona?.traits || "",
        status: persona?.status || "",
        reflectionThreshold: useReflectionThreshold,
        path,
      })
      
      if (observationSummary) {
        return observationSummary;
      }
      
      // If observationSummary is not valid, return null
      return null;
    } else {
      return null;
    }

  } catch (error: any) {
    console.error("Error fetching observations:", error);
  }
};