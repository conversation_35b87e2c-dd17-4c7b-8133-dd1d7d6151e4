'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from "@clerk/nextjs/server";
import { OpenAI } from "openai";
import prismadb from "@/lib/prismadb";
import { 
  Companion, 
  Thread, 
  UserThread,
  Message, 
  ChatSummary,
  Persona, 
  Observations, 
  Todos,
  File } from '@prisma/client'

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || '',
  });

type ThreadInfo = { threadId: string; name: string } | null;

export async function getAssistantById(
  userId?: string | null, 
  companionId?: string | null,
  threadId?: string | null
  ): Promise<Companion & {
    observations: Observations[];
    chatSummaries: ChatSummary[];
    todos: Todos[];
    _count: {
      messages: number;
    };
  } | null> {
  if (!userId || !companionId || !threadId) {
    return null
  }

  try {
  const companion = await prismadb.companion.findUnique({
    where: {
      id: companionId
    },
    include: {
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      chatSummaries: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
          threadId,
        },
        take: 5,
      },
      todos: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

    //console.log("getAssistantById: ", companion)
    return companion;
  } catch (error: any) {
    return null;
  }
};

export async function getUserThreads(
  userId?: string | null, 
  companionId?: string | null,
  ) {  
  if (!userId || !companionId) {
    return []
  }

  try {
    const threads = await prismadb.userThread.findMany({
      where: {
        companionId,
        userId
      },
      orderBy: {
        createdAt: 'asc'
      },
    });

    //console.log("getThreads: ", threads)
    return threads;
  } catch (error: any) {
    return [];
  }
};


export async function getUserThread(
  userId: string | null, 
  companionId: string | null,
  threadId: string | null
): Promise<ThreadInfo> {
  
  if (!userId || !companionId || !threadId) {
    throw new Error('Missing required fields');
  }
  
  try {
    const thread = await prismadb.userThread.findFirst({
      where: {
        threadId,
        companionId,
        userId,        
      },
    });

    if (thread) {
      return {
        threadId: thread.threadId,
        name: thread.name,
      };
    } else {
      return null;
    }
  } catch (error: any) {
    console.error("Error fetching thread:", error);
    throw new Error("Unable to fetch thread");
  }
};


export async function newUserThread({
  threadId,
  name,  
  companionId,
  path,
}:{
  threadId: string,
  name: string,
  companionId: string,
  path?: string;
}) {
  try {
    const { userId, redirectToSignIn } = await auth();

    if (!userId) {
      return redirectToSignIn();
    }

    if (!threadId || !name || !companionId || !userId) {
      return {
        error: 'Missing required fields'
      }
    };

    const thread: Thread = await prismadb.userThread.create({
      data: {
        name,
        companionId,
        userId,
        threadId,
      },
    });


    if (!thread) {
      throw new Error("Thraed not created");
    }
    
    if (path) {
      revalidatePath(path, 'layout');
    }
    
    return thread;

  } catch (error) {
    console.log("[THREAD_CREATED]", error);
    throw new Error("Thread not created");

  }
};

export async function renameUserThread({
  threadId,
  threadName,  
  path,
}:{
  threadId: string,
  threadName: string,
  path?: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: 'Unauthorized'
      }
    }

    if (!threadId || !threadName) {
      return {
        error: 'Missing required fields'
      }
    };


    const existingThread = await prismadb.userThread.update({
      where: {
        threadId,
      },
      data: {
        name: threadName,
      },
    });

    if (!existingThread) {
      throw new Error("Thread name not updated");
    }
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[THREAD_NAME_UPDATED]", error);
    throw new Error("Thread name not updated");

  }
};

export async function removeUserThread({
  threadId, 
  companionId, 
  path 
}:{ 
  threadId: string; 
  companionId: string; 
  path?: string 
}): Promise<void> {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Missing required fields');
    }

    if (!threadId || !companionId) {
      throw new Error('Missing required fields');
    }

    const userThread = await prismadb.userThread.findUnique({ 
      where: { 
        userId,
        companionId,
        threadId,
      } 
    });

    if (!userThread) {
      throw new Error('Thread not found');
    }


    await prismadb.$transaction(async (tx) => {
      await tx.userThread.delete({
        where: { id: userThread.id },
      });

      const response = await openai.beta.threads.del(threadId);
      console.log(response);
    });

    /*const removeThread = await prismadb.userThread.delete({
      where: {
        userId,
        companionId,
        threadId,
      },
    });

    const response = await openai.beta.threads.del(threadId);
    console.log(response);  

    if (!removeThread) {
      throw new Error("Thread not removed");
    }*/
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[THREAD_REMOVED]", error);
    throw new Error("Thread not removed");

  }
};


export async function clearUserThreads({ 
  companionId,
  path 
}:{ 
  companionId: string; 
  path: string 
}): Promise<void> {
  try {
    const { userId } = await auth()

    if (!userId || !companionId) {
      throw new Error('Missing required fields');
    }

    await prismadb.$transaction(async (tx) => {
      // Get all threads associated with the user and companion
      const threadIds = await getUserThreads(userId, companionId);
  
      if (!threadIds || threadIds.length === 0) {
        throw new Error("Thread not found");
      }
  
      // Delete entries from the database
      await tx.userThread.deleteMany({
        where: {
          id: { in: threadIds.map(thread => thread.threadId) }, // Use 'in' operator with an array of thread IDs
        }
      });
  
      // Delete threads from OpenAI
      for (const threadIdObject of threadIds) {
        const response = await openai.beta.threads.del(threadIdObject.threadId);
        console.log(response);
      }
    });

    // Delete threads from OpenAI
    /*for await (const threadIdObject of threadIds) {
      const response = await openai.beta.threads.del(threadIdObject.threadId);
      console.log(response); // Log the response from OpenAI
    }

    // Delete entries from the database
    const clearThreads = await prismadb.userThread.deleteMany({
      where: {
        userId,
        companionId,
      },
    });

    if (!clearThreads) {
      throw new Error("Threads not cleared");
    }*/
    
    if (path) {
      revalidatePath(path)
    } 

  } catch (error) {
    console.log("[THREADS_CLEARED]", error);
    throw new Error("Threads not cleared");

  }
  return redirect(path)
}


export async function createUploadFile({
  fileId,
  filename, 
  type,
  size, 
  companionId,
  assistantId,
  purpose,
  path,
}:{
  fileId: string,
  filename: string,
  type: string,
  size: number,
  companionId: string,
  assistantId: string,
  purpose: string,
  path?: string;
}): Promise<File> {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Missing required fields');
    }

    if (!fileId || !filename || !type || !companionId || !assistantId || !purpose) {
      throw new Error('Missing required fields');
    };

    const fileUploaded: File = await prismadb.file.create({
      data: {
        fileId,
        filename,
        type,
        size,
        companionId,
        assistantId,
        userId,
        purpose,
      },
    });


    if (!fileUploaded) {
      throw new Error("File not uploaded");
    }
    
    if (path) {
      revalidatePath(path);
    }
    
    return fileUploaded;

  } catch (error) {
    console.log("[FILE_UPLOADED]", error);
    throw new Error("File not uploaded");

  }
};

export async function updateAssistantFile({
  id,
  vectorStoreId,
  path,
}:{
  id: string,
  vectorStoreId: string,
  path?: string;
}) {
  try {
    const { userId, redirectToSignIn } = await auth();

    if (!userId) {
      return redirectToSignIn();
    }

    if (!vectorStoreId) {
      return {
        error: 'Missing required fields'
      }
    };

    const file: File = await prismadb.file.update({
      where: {
        id,
      },
      data: {
        assistantId: vectorStoreId,
      },
    });


    if (!file) {
      throw new Error("Assistant File not updated");
    }
    
    if (path) {
      revalidatePath(path);
    }
    
    return file;

  } catch (error) {
    console.log("[ASSISTANT_FILE_UPDATED]", error);
    throw new Error("Assistant File not updated");

  }
};

export async function updateFilename({
  id,
  filename,
  path,
}:{
  id: string;
  filename: string;
  path?: string;
}) {
  try {
    const { userId, redirectToSignIn } = await auth();

    if (!userId) {
      return redirectToSignIn();
    }

    if (!id || !filename) {
      return {
        error: 'Missing required fields'
      }
    };

    const file: File = await prismadb.file.update({
      where: {
        id,
      },
      data: {
        filename,
      },
    });


    if (!file) {
      throw new Error("Filename not updated");
    }
    
    if (path) {
      revalidatePath(path);
    }
    
    return file;

  } catch (error) {
    console.log("[FILENAME_UPDATED]", error);
    throw new Error("Filename not updated");

  }
};


export async function retrieveFiles(userId: string, assistantId: string) {
  try {
    if (!userId) throw new Error('userId is required');
    if (!assistantId) throw new Error('assistantId is required');

    const retrievedUserFiles = await prismadb.file.findMany({
      where: {
        userId: userId,
      },
    });

    if (assistantId) {
      const filesWithAssistantId = retrievedUserFiles.filter((file) => file.assistantId === assistantId);
      const filesWithoutAssistantId = retrievedUserFiles.filter((file) => file.assistantId !== assistantId);
  
      return { retrievedUserFiles, filesWithAssistantId, filesWithoutAssistantId };
    } else {
      return { retrievedUserFiles, filesWithAssistantId: [], filesWithoutAssistantId: [] };
    }


  } catch (error) {
    console.error('Error retrieving files:', error);
    throw new Error('Failed to retrieve files');
  }
}


/*export async function retrieveFiles(userId: string) {
  try {
    if (!userId) throw new Error('userId is required');

    const retrievedUserFiles = await prismadb.file.findMany({
      where: {
        userId: userId,
      },
    });
    return retrievedUserFiles;

  } catch (error) {
    console.error('Error retrieving files:', error);
    throw new Error('Failed to retrieve files');
  }
}*/

export async function deleteFile({ id, path }: { id: string, path?: string; }) {
  try {
    const { userId, redirectToSignIn } = await auth();

    if (!userId) {
      return redirectToSignIn();
    }
    if (!id) throw new Error('file Id is required');

    const deletedFile = await prismadb.file.delete({
      where: {
        id,
      },
    });
    if (path) {
      revalidatePath(path);
    }
    return deletedFile;
  } catch (error) {
    console.error('Error deleting file:', error);
    throw new Error('Failed to delete file');
  }
}