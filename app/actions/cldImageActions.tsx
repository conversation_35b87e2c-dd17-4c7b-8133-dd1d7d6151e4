'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from "@clerk/nextjs/server";
import { v2 as cloudinary } from "cloudinary";

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

export async function cldImageActions({
  imageTransformMode,
  imageUrl,
  granularValue,
  rotateValue,
  path,
}: {
  imageTransformMode: string,
  imageUrl: string,
  granularValue: number,
  rotateValue: number,
  path: string,
}) {
  const { userId } = await auth();
  
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  try {
    let transformImgURL: string = ''
    if (imageTransformMode === "rotate") {
      const transformImg = await cloudinary.image(
        imageUrl,
        {transformation: [
          {width: 512, crop: "scale"},
          {angle: rotateValue}
      ]})
      transformImgURL = transformImg;
      console.log("transformImgToURL: ", transformImgURL)
      //transformImgToURL:  <img src='https://res.cloudinary.com/dyuei3zjr/image/upload/v1700726782/images/tlkzplji3dbzvffngccw.png' />

    } else if (imageTransformMode === "resizeCrop") {
      const transformImg = await cloudinary.image(
        imageUrl,
        { height: granularValue,
          width: granularValue,
          crop: "crop",
          gravity: "auto"
        }
      )
      transformImgURL = transformImg;
      console.log("transformImgToURL: ", transformImgURL)

    } else if (imageTransformMode === "resizeScale") {
      const transformImg = await cloudinary.image(
        imageUrl,
        { height: "2.0",
          width: "2.0",
          crop: "scale",
        }
      )
      transformImgURL = transformImg;
      console.log("transformImgToURL: ", transformImgURL)
    }
    revalidatePath(path)
    return transformImgURL;
  } catch (error) {
    console.error('[IMAGE_TRANSFORM_ERROR]', error);
    throw new Error("IMAGE_TRANSFORM_ERROR");
  }
}