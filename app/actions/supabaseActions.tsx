'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth, currentUser } from "@clerk/nextjs/server";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';

// Type definition for supabase content
interface SearchResult {
  favicon: string;
  link: string;
  title: string;
  snippet: string;
}
interface Message {
  id: string;
  type: string;
  content: string;
  userMessage: string;
  images: Image[];
  videos: Video[];
  followUp: FollowUp | null;
  isStreaming: boolean;
  searchResults?: SearchResult[];
}
interface Image {
  link: string;
}
interface Video {
  link: string;
  imageUrl: string;
}
interface FollowUp {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export async function updateFilename({
  id,
  filename,
  path,
}:{
  id: string;
  filename: string;
  path?: string;
}) {
  try {
    const user = await currentUser()
    const userId = user?.id!
    const { getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null; 

    if (!authToken || !userId) {
      return {
        error: 'Unauthorized'
      }
    }
    const supabase = await createClerkSupabaseServerClient(authToken);
    console.log("id", id)
    console.log("filename", filename)
  
    if (!id || !filename) {
      return {
        error: 'Missing required fields'
      }
    };

    const file = await supabase
      .from("documents")
      .update({ filename: filename })
      .eq('id', id)
      .eq('owner_id', userId)
  
    if (file.error) {
      throw new Error(
        `Error creating document: ${file.error.message} ${file.status} ${file.statusText}`
      );
    }

    if (path) {
      revalidatePath(path);
    }
    
    return file;

  } catch (error) {
    console.log("[FILENAME_UPDATED]", error);
    throw new Error("Filename not updated");

  }
};


export async function deleteFile({ id, path }: { id: string, path?: string; }) {
  try {
    const user = await currentUser()
    const userId = user?.id!
    const { getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null; 

    if (!authToken || !userId) {
      return {
        error: 'Unauthorized'
      }
    }
    const supabase = await createClerkSupabaseServerClient(authToken);

    if (!id) throw new Error('file Id is required');

    const file = await supabase
      .from("documents_1024")
      .delete()
      .eq('id', id)
      .eq('owner_id', userId)

      if (file.error) {
        throw new Error(
          `Error creating document: ${file.error.message} ${file.status} ${file.statusText}`
        );
      }

    if (path) {
      revalidatePath(path);
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    throw new Error('Failed to delete file');
  }
}
  

export async function saveSearchMessagesToSupabase(
  threadId: string,
  existingThreadId: string,
  messages: Message[]
): Promise<void> {
  try {
    //const user = await currentUser()
    //const userId = user?.id!
    const { getToken } = await auth();
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    if (!authToken) {
      throw new Error('Missing required fields');
    }
    if (!threadId || !messages || messages.length === 0) {
      throw new Error('Missing or empty messages array');
    }
    const supabase = await createClerkSupabaseServerClient(authToken);

    // Insert messages into the messages table
    if (existingThreadId && existingThreadId.length > 0) {
      const { data: messageData, error: messageError } = await supabase
      .from('messages')
      .insert(messages.map(message => ({
        id:message.id,
        thread_id: existingThreadId,
        type: "userMessage",
        userMessage: message.userMessage,
        content: message.content,
        //owner_id: userId
      })));

      if (messageError) {
        throw new Error(`Error inserting messages: ${messageError.message}`);
      }
    } else {
      // Insert new thread and messages into the table 
      const { data: threadData, error: threadError } = await supabase
      .from('thread')
      .insert({
        id:threadId,
        name: messages[0].userMessage.substring(0, 30),
        //user_id: userId
      });

      if (threadError) {
        throw new Error(`Error inserting thread: ${threadError.message}`);
      } 
      const { data: messageData, error: messageError } = await supabase
      .from('messages')
      .insert(messages.map(message => ({
        id:message.id,
        thread_id: threadId,
        type: "userMessage",
        userMessage: message.userMessage,
        content: message.content,
        //owner_id: userId
      })));

      if (messageError) {
        throw new Error(`Error inserting messages: ${messageError.message}`);
      }
    }


    // Define an array to store promises for sub-table insertions
    const subTableInsertPromises: Promise<void>[] = [];

    // Insert images, videos, follow-ups, and search results for each message
    messages.forEach(message => {
      // Insert images
      message.images.forEach(async image => {
        try {
          const insertPromise = Promise.resolve(
            supabase.from('images').insert({ message_id: message.id, link: image.link }).then(({ error }) => {
              if (error) {
                console.error('Error inserting image:', error);
              }
              return Promise.resolve();
            })
          );
          subTableInsertPromises.push(insertPromise);
        } catch (error) {
          console.error('Error inserting image:', error);
        }
      });

      // Insert videos
      message.videos.forEach(async video => {
        try {
          const insertPromise = Promise.resolve(
            supabase.from('videos').insert({ message_id: message.id, link: video.link, imageUrl: video.imageUrl }).then(({ error }) => {
              if (error) {
                console.error('Error inserting video:', error);
              }
              // Return a resolved Promise to ensure the correct type
              return Promise.resolve();
            })
          );
          subTableInsertPromises.push(insertPromise);
        } catch (error) {
          console.error('Error inserting video:', error);
        }
      });

      // Insert search results
      if (message.searchResults && message.searchResults.length > 0) {
        message.searchResults.forEach(async searchResult => {
          try {
            const insertPromise = Promise.resolve(
              supabase.from('search_results').insert({ message_id: message.id, title: searchResult.title, snippet: searchResult.snippet, link: searchResult.link, favicon: searchResult.favicon }).then(({ error }) => {
                if (error) {
                  console.error('Error inserting search result:', error);
                }
                // Return a resolved Promise to ensure the correct type
                return Promise.resolve();
              })
            );
            subTableInsertPromises.push(insertPromise);
          } catch (error) {
            console.error('Error inserting search result:', error);
          }
        });
      }
    });

    // Wait for all sub-table insertions to complete
    await Promise.all(subTableInsertPromises);
    console.log('Messages saved successfully.');
  } catch (error) {
    console.error('Error saving messages:', error);
  }
}




export async function getThreads(
  //userId: string, 
  authToken: any
  ) {  
  try {
    //const { userId, getToken } = auth();
    //const token = await getToken({ template: "supabase" });
    //const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    const { userId } = await auth();
    if (!authToken || !userId) {
      return [];
    }

    const supabase = await createClerkSupabaseServerClient(authToken);
    const { data: threads, error: threadsError } = await supabase
    .from("thread")
    .select("*")
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

    //console.log('threads:', threads);
    if (threadsError) {
      console.error('Error fetching threads data:', threadsError);
      return [];
    }
    return threads;
  } catch (error: any) {
    return [];
  }
};


export async function getThread(
  //userId?: string | null,
  //authToken?: any | null,
  threadId?: string | null
) {  
  try {
    const { getToken, userId } = await auth();
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    if (!authToken || !userId || !threadId) {
      return null;
    }

    const supabase = await createClerkSupabaseServerClient(authToken);
    const { data: thread, error: threadError } = await supabase
      .from("thread")
      .select("*")
      .eq('user_id', userId)
      .eq('id', threadId)
  
      console.log('thread:', thread);
    if (threadError) {
      console.error('Error fetching thread data:', threadError);
      return [];
    }

    if (thread) {
      return thread
    } else {
      return null;
    }
  } catch (error: any) {
    console.error("Error fetching thread:", error);
    throw new Error("Unable to fetch thread");
  }
};


export async function renameThread({
  id,
  threadName,
  //userId,
  //authToken,
  path,
}: {
  id: string;
  threadName: string;
  //userId: string;
  //authToken: any;
  path?: string;
}) {
  try {
    const { getToken, userId } = await auth();
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;

    if (!userId || !authToken) {
      return {
        error: 'Unauthorized'
      }
    }

    if (!id || !threadName) {
      return {
        error: 'Missing required fields'
      }
    };

    const supabase = await createClerkSupabaseServerClient(authToken);
    const { data: updatedThread, error: updateError } = await supabase
    .from("thread")
    .update({ name: threadName })
    .eq('user_id', userId)
    .eq('id', id);

    if (updateError) {
      throw new Error("Thread name not updated");
    }
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[THREAD_NAME_UPDATED]", error);
    throw new Error("Thread name not updated");

  }
};

export async function removeThread({
  id, 
  //userId,
  //authToken,
  path 
}: { 
  id: string; 
  //userId: string;
  //authToken: any;
  path?: string 
}): Promise<void> {
  try {
    const { getToken, userId } = await auth();
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  
    if (!userId || !authToken) {
      throw new Error('Missing required fields');
    }

    if (!id) {
      throw new Error('Missing required fields');
    }

    const supabase = await createClerkSupabaseServerClient(authToken);
    const { data: removeThread, error: messagesError } = await supabase
    .from("thread")
    .delete()
    .eq('user_id', userId)
    .eq('id', id)

    if (messagesError) {
      throw new Error("Thread not removed");
    }
    
    if (path) {
      revalidatePath(path)
    }    

  } catch (error) {
    console.log("[THREAD_REMOVED]", error);
    throw new Error("Thread not removed");

  }
};


export async function clearThreads({ 
  //userId,
  //authToken,
  path 
}: { 
  //userId: string;
  //authToken: any;
  path: string 
}): Promise<void> {
  try {
    const { getToken, userId } = await auth();
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  
    if (!userId || !authToken) {
      throw new Error('Missing required fields');
    }

    const supabase = await createClerkSupabaseServerClient(authToken);
    const { data: deletedThreads, error: deleteError } = await supabase
      .from("thread")
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting threads:', deleteError);
      throw new Error('Failed to delete threads');
    }

    if (path) {
      revalidatePath(path)
    } 
  } catch (error) {
    console.error('Error:', error);
    throw new Error('Failed to delete threads');
  }
  return redirect(path)
}
