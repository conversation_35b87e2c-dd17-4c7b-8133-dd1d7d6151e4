import prismadb from "@/lib/prismadb";

//export const runtime = 'edge'

interface getCompanionProps {
  companionId: string;
}

const getCompanion = async ({
  companionId
}: getCompanionProps) => {
  try {
    const companion = await prismadb.companion.findUnique({
      where: {
        id:companionId
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    return companion
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null;
  }
}
 
export default getCompanion;