import { auth } from '@clerk/nextjs/server'
import { 
  threadSummaryTool, 
  userProfileTool, 
  coreBeliefAnalysisTool,

} from './memoryActionsTool'
import anthropic from "@/lib/anthropic";
import { codeBlock } from 'common-tags';
import { semanticMemory } from '@/lib/semanticMemory';

const MODEL_NAME = "claude-3-haiku-20240307"


interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
  metadata?: {
    user_id: string;
  };
}

interface Msg {
  name?: string;
  role?: 'user' | 'system';
  content: string;
  [key: string]: any; // To allow accessing dynamic properties
}

const createPrettyString = (msgs: Msg[]) => {
  const resultLines: string[] = [];
  for (const msg of msgs) {
    const name = msg.get('name', 'Unknown User') || (msg.get('role') === 'user' ? 'system' : 'user');
    resultLines.push(`${name}: ${msg['content']}`);
  }
  return resultLines.join("\n");
};



export async function ThreadSummary(
  messages: any[]
) {
  try {
    const query = codeBlock`
      <conversation>
      ${createPrettyString(messages)}
      </conversation>
      Use the \`thread_summary\` tool
    `;
    const response = await anthropic.beta.tools.messages.create({
      model: MODEL_NAME,
      max_tokens: 4096,
      tools: threadSummaryTool,
      messages: [{ "role": "user", "content": query }]
    })

    let jsonSummary = null;
    for (const content of response.content) {
      if (content.type === "tool_use" && content.name === "thread_summary") {
        jsonSummary = content.input;
        break;
      }
    }

    if (jsonSummary) {
      return jsonSummary;
    } else {
      throw new Error('No JSON summary found in the response.');
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};


export async function UserProfile(
  query: string, 
  messages: any[]
) {
  try {

    const userProfileQuery = codeBlock`
      <document>
      ${query}
      </document>
      Use the user_state tool.
      `;
    const response = await anthropic.beta.tools.messages.create({
      model: MODEL_NAME,
      max_tokens: 4096,
      tools: userProfileTool,
      messages: [{ "role": "user", "content": userProfileQuery }]
    })

    let jsonEntities = null;
    for (const content of response.content) {
      if (content.type === "tool_use" && content.name === "user_state") {
        jsonEntities = content.input;
        break;
      }
    }
    
    if (jsonEntities) {
      console.log("JSON Entities: ", jsonEntities);
    } else {
      console.log("No entities found in the response.");
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};


export async function CoreBeliefAnalysis(
  messages: any[]
) {
  try {
    const query = codeBlock`
      <conversation>
      ${createPrettyString(messages)}
      </conversation>
      Use the \`formative_event_logging\` and \`core_belief_analysis\` tools
    `;

    const response = await anthropic.beta.tools.messages.create({
      model: MODEL_NAME,
      max_tokens: 4096,
      tools: coreBeliefAnalysisTool,
      messages: [{ "role": "user", "content": query }]
    })

    let jsonSummary = null;
    for (const content of response.content) {
      if (
        content.type === "tool_use" &&
        ["formative_event_logging", "core_belief_analysis"].includes(content.name)
      ) {
        jsonSummary = content.input;
        break;
      }
    }
    
    if (jsonSummary) {
      console.log("JSON Summary: ", jsonSummary);
    } else {
      console.log("No entities found in the response.");
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};


export async function UserSemanticMemory(
  messages: any[]
) {
  try {
    const query = codeBlock`
      <conversation>
      ${createPrettyString(messages)}
      </conversation>
      Use the \`user_reflection_analysis\` tool
    `;

    const response = await anthropic.beta.tools.messages.create({
      model: MODEL_NAME,
      max_tokens: 4096,
      tools: UserSemanticMemoryTool,
      messages: [{ "role": "user", "content": query }]
    })

    let jsonSummary = null;
    for (const content of response.content) {
      if (content.type === "tool_use" && content.name === "user_reflection_analysis") {
        jsonSummary = content.input;
        break;
      }
    }
    
    if (jsonSummary) {
      console.log("JSON Summary: ", jsonSummary);
    } else {
      console.log("No entities found in the response.");
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};


const addMessages = async (_threadId, _messages) => {
  try {
    const memories = await semanticMemory();
    await memories.createIndex({ content: 'text' });
    const updatedMessages = _messages.map(msg => {
      const updatedMsg = { ...msg };
      if (!updatedMsg.metadata) {
        updatedMsg.metadata = {};
      }
      updatedMsg.metadata.threadId = _threadId;
      return updatedMsg;
    });
    await memories.insertMany(updatedMessages);
  } catch (error) {
    console.error('Error adding messages:', error);
  }
};

const listMessages = async (threadId) => {
  try {
    const memories = await semanticMemory();
    const query = { 'metadata.threadId': threadId };
    const messages = await memories.find(query).toArray();
    return messages;
  } catch (error) {
    console.error('Error listing messages:', error);
    return [];
  }
};


const queryUserMemory = async ({ text, k = 3 }) => {
  try {
    const { userId } = auth
    // Construct the query to match the userId and perform a case-insensitive search on the content field
    const memories = await semanticMemory();
    const query = {
      "metadata.userId": userId,
      content: { $regex: text, $options: 'i' }
    };

    // Define the projection, in this case, it is not projecting any specific fields
    const projection = {
      "score": { $meta: "textScore" }
    };

    // Execute the query, sort by relevance (textScore), and limit to top k results
    const results = await memories.find(query, { projection }).sort({ "score": { $meta: "textScore" } }).limit(k).toArray();

    return results;
  } catch (error) {
    console.error('Error querying user memory:', error);
    return [];
  }
};

export { addMessages, listMessages, queryUserMemory };
