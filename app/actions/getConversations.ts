import prismadb from "@/lib/prismadb";
import getCurrentUser from "./getCurrentUser";



const getConversations = async () => {
  const currentUser = await getCurrentUser();

  if (!currentUser?.id) {
    return [];
  }

  try {
    const conversations = await prismadb.conversation.findMany({
      orderBy: {
        lastMessageAt: 'asc',
      },
      where: {
        users: {
          some: {
            userId: currentUser.userId
          }
        }
      },
      include: {
        users: true,
        messages: {
          orderBy: {
            createdAt: 'asc'
          },
          include: {
            sender: true,
            seen: true,
          }
        },
      },
      //cacheStrategy: {
      //  swr: 0,
      //  ttl: 0
      //},
    });

    console.log("getConversations: ", conversations)
    return conversations;
  } catch (error: any) {
    return [];
  }
};

export default getConversations;