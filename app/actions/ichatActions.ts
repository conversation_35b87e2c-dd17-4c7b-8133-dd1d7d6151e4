'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { kv } from '@vercel/kv'

import { auth } from "@clerk/nextjs/server"
import { type Chat } from '@/lib/itypes'
//import { NotFoundError } from "@getzep/zep-cloud/api"
//import zepClient from "@/lib/zepClient"

export async function getChats(userId?: string | null, companionId?: string | null) {  
  if (!userId || !companionId) {
    return []
  }

  try {
    const pipeline = kv.pipeline()
    const chats: string[] = await kv.zrange(`user:ichat:${userId}:${companionId}`, 0, -1, {
      rev: true
    })

    for (const chat of chats) {
      pipeline.hgetall(chat)
    }

    const results = await pipeline.exec()

    return results as Chat[]
  } catch (error) {
    return []
  }
}

export async function saveChat(chat: Chat) {
  const { userId } = await auth();
console.log("chat", chat)
  if (userId) {
    const pipeline = kv.pipeline()
    pipeline.hmset(`ichat:${chat.id}`, chat)
    pipeline.zadd(`user:ichat:${chat.userId}:${chat.companionId}`, {
      score: Date.now(),
      member: `ichat:${chat.id}`
    })
    await pipeline.exec()
  } else {
    return
  }
}

export async function getChat(id: string, userId: string, companionId: string) {
  const chat = await kv.hgetall<Chat>(`ichat:${id}`)
  console.log("chat", chat)
  if (!chat || (userId && chat.userId !== userId || (companionId && chat.companionId !== companionId))) {
    return null
  }

  return chat
}

export async function removeChat({ 
  id, 
  companionId, 
  path 
}: { 
  id: string; 
  companionId: string; 
  path: string 
}) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const uid = await kv.hget<string>(`ichat:${id}`, 'userId')

  if (uid !== userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const cid = await kv.hget<string>(`ichat:${id}`, 'companionId')
  if (cid !== companionId) {
    return {
      error: 'Unauthorized'
    }
  }
  
  await kv.del(`ichat:${id}`)
  await kv.zrem(`user:ichat:${userId}:${companionId}`, `ichat:${id}`)

  /*try {
    const session = await zepClient.memory.getSession(id);
    console.log(session);
    if (session) {
      await zepClient.memory.delete(id);
    }
  } catch (error) {
    if (error instanceof NotFoundError) {
      console.error("Session not found:", error.message);
    } else {
      console.error("Got error:", error);
    }
  }*/

  revalidatePath(path)
  return revalidatePath(path)
}

export async function clearChats({
  companionId, 
  path 
}: { 
  companionId: string; 
  path: string 
}) {
  const { userId } = await auth()

  if (!userId || !companionId) {
    return {
      error: 'Unauthorized'
    }
  }

  const chats: string[] = await kv.zrange(`user:ichat:${userId}:${companionId}`, 0, -1)
  if (!chats.length) {
  return redirect(path)
  }
  const pipeline = kv.pipeline()

  for (const chat of chats) {
    pipeline.del(chat)
    pipeline.zrem(`user:ichat:${userId}:${companionId}`, chat)
    console.log("chat", chat);
    /*try {
      const session = await zepClient.memory.getSession(chat);

      if (session) {
        await zepClient.memory.delete(chat);
      }
    } catch (error) {
      if (error instanceof NotFoundError) {
        console.error("Session not found:", error.message);
      } else {
        console.error("Got error:", error);
      }
    }*/
  }

  await pipeline.exec()

  revalidatePath(path)
  return redirect(path)
}

export async function getSharedChat(id: string) {
  const chat = await kv.hgetall<Chat>(`ichat:${id}`)

  if (!chat || !chat.sharePath) {
    return null
  }

  return chat
}

export async function shareChat(id: string) {
  const { userId } = await auth()

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const chat = await kv.hgetall<Chat>(`ichat:${id}`)

  if (!chat || chat.userId !== userId) {
    return {
      error: 'Something went wrong'
    }
  }

  const payload = {
    ...chat,
    sharePath: `/ishare/${chat.id}`
  }

  await kv.hmset(`ichat:${chat.id}`, payload)

  return payload
}