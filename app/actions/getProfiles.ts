'use server'

import prismadb from "@/lib/prismadb";
import { currentUser } from '@clerk/nextjs/server'

const getProfiles = async () => {
  const user = await currentUser();

  if (!user?.id) {
    return [];
  }

  try {
    const profiles = await prismadb.profile.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      where: {
        NOT: {
          externalId: user?.id
        }
      }
    });

    return profiles;
  } catch (error: any) {
    return [];
  }
};

export default getProfiles;