'use server'

import { revalidatePath } from 'next/cache'
import { auth } from "@clerk/nextjs/server"
import { Cloudinary } from "@cloudinary/url-gen";
import { crop, fill, scale, thumbnail } from "@cloudinary/url-gen/actions/resize";
import { autoGravity } from "@cloudinary/url-gen/qualifiers/gravity";
import { byAngle } from "@cloudinary/url-gen/actions/rotate";


export async function cldUrlGenActions({
  imageTransformMode,
  imageUrl,
  granularValue,
  rotateValue,
  path,
}: {
  imageTransformMode: string,
  imageUrl: string,
  granularValue: number,
  rotateValue: number,
  path: string,
}) {
  const { userId } = await auth();
  
  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  try {
    const cld = new Cloudinary({
      cloud: {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME,
      },
      url: {
          secure: true
      }
    });

    let transformImgURL: string = ''
    if (imageTransformMode === "rotate") {
      const transformImg = await cld
      .image(imageUrl)
      .rotate(byAngle(rotateValue));
      console.log("transformImgToURL: ", transformImg.toURL())

      transformImgURL = transformImg.toURL();
    } else if (imageTransformMode === "resizeCrop") {
      const transformImg = await cld
        .image(imageUrl)
        //.resize(fill().width(granularValue).height(granularValue).gravity(autoGravity()))
        .resize(crop().width(granularValue).height(granularValue).gravity(autoGravity()));
        //.resize(thumbnail()
        //.height(granularValue)
        //.aspectRatio("1:1")
        //.zoom(0.75)
        //.gravity(autoGravity()))

        console.log("transformImgToURL: ", transformImg.toURL())

      transformImgURL = transformImg.toURL();
    } else if (imageTransformMode === "disabled") {
      const transformImg = await cld
        .image(imageUrl)
        //.resize(fill().width(granularValue).height(granularValue).gravity(autoGravity()))
        .resize(scale().width("2.0").width("2.0"))
        console.log("transformImgToURL: ", transformImg.toURL())

      transformImgURL = transformImg.toURL();
    }
    revalidatePath(path)
    return transformImgURL;
  } catch (error) {
    console.error('[IMAGE_TRANSFORM_ERROR]', error);
    throw new Error("IMAGE_TRANSFORM_ERROR");
  }
}