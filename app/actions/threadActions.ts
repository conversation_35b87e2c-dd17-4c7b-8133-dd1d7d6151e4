'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from '@clerk/nextjs/server';
import { OpenAI } from "openai";
import prismadb from "@/lib/prismadb";
import { 
  Companion, 
  Thread, 
  Message, 
  Source,
  ChatSummary,
  Persona, 
  Observations, 
  Todos } from '@prisma/client'
import zepClient from "@/lib/zepClient"
import { createClient } from '@supabase/supabase-js';
import { deleteCheckpointsByThreadId } from '@/ai/utils/delete_checkpoints'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

type ThreadInfo = { threadId: string; name: string } | null;

export async function getFullCompanionById(
  userId?: string | null, 
  companionId?: string | null,
  threadId?: string | null
): Promise<Companion & {
  messages: (Message & {
    sources?: Source[];
  })[];
  observations: Observations[];
  chatSummaries: ChatSummary[];
  todos: Todos[];
  _count: {
    messages: number;
  };
} | null> {
  if (!userId || !companionId || !threadId) {
    return null;
  }

  try {
    const companion = await prismadb.companion.findUnique({
      where: {
        id: companionId
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc"
          },
          where: {
            userId,
            threadId,
          },
          include: {
            sources: {
              orderBy: {
              createdAt: "desc"
              }
            },
          },
        },
        observations: {
          orderBy: {
            createdAt: "desc"
          },
          where: {
            userId,
          },
          take: 2,
        },
        chatSummaries: {
          orderBy: {
            createdAt: "desc"
          },
          where: {
            userId,
            threadId,
          },
          take: 5,
        },
        todos: {
          orderBy: {
            createdAt: "asc"
          },
          where: {
            userId,
          },
        },
        _count: {
          select: {
            messages: true,
          }
        }
      }
    });

    return companion;
  } catch (error: any) {
    return null;
  }
}

export async function getCompanionById(
  userId?: string | null, 
  companionId?: string | null,
  threadId?: string | null
  ): Promise<Companion & {
    messages: Message[];
    observations: Observations[];
    chatSummaries: ChatSummary[];
    todos: Todos[];
    _count: {
      messages: number;
    };
  } | null> {
  if (!userId || !companionId || !threadId) {
    return null
  }

  try {
    const companion = await prismadb.companion.findUnique({
      where: {
        id: companionId
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc"
          },
          where: {
            userId,
            threadId,
          },
        },
        observations: {
          orderBy: {
            createdAt: "desc"
          },
          where: {
            userId,
          },
          take: 2,
        },
        chatSummaries: {
          orderBy: {
            createdAt: "desc"
          },
          where: {
            userId,
            threadId,
          },
          take: 5,
        },
        todos: {
          orderBy: {
            createdAt: "asc"
          },
          where: {
            userId,
          },
        },
        _count: {
          select: {
            messages: true,
          }
        }
      }
    });

    return companion;
  } catch (error: any) {
    return null;
  }
};

export async function getCompanionForIntake(
  userId?: string | null, 
  companionId?: string | null,
  threadId?: string | null
  ): Promise<Companion & {
    messages: Message[];
    chatSummaries: ChatSummary[];
    _count: {
      messages: number;
    };
  } | null> {
  if (!userId || !companionId || !threadId) {
    return null
  }

  try {
  const companion = await prismadb.companion.findUnique({
    where: {
      id: companionId
    },
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
          threadId,
        },
      },
      chatSummaries: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
          threadId,
        },
        take: 5,
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

    return companion;
  } catch (error: any) {
    return null;
  }
};

export async function getThreads(
  userId?: string | null, 
  companionId?: string | null,
  ) {  
  if (!userId || !companionId) {
    return []
  }

  try {
    const threads = await prismadb.thread.findMany({
      where: {
        companionId,
        userId
      },
      orderBy: {
        createdAt: 'desc'
      },
    });

    return threads;
  } catch (error: any) {
    return [];
  }
};


export async function getThread(
  userId?: string | null, 
  companionId?: string | null,
  threadId?: string | null
) {  
  if (!userId || !companionId || !threadId) {
    return null;
  }

  try {
    const thread = await prismadb.thread.findFirst({
      where: {
        id: threadId,
        companionId,
        userId,        
      },
    });

    if (thread) {
      return {
        id: thread.id,
        name: thread.name,
      };
    } else {
      return null;
    }
  } catch (error: any) {
    console.error("Error fetching thread:", error);
    throw new Error("Unable to fetch thread");
  }
};

export async function createThread({
  threadId,
  name,  
  companionId,
  path,
}: {
  threadId: string;
  name: string;
  companionId: string;
  path?: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!threadId || !name || !companionId || !userId) {
      throw new Error('Missing required fields');
    };

    const thread: Thread = await prismadb.thread.create({
      data: {
        id: threadId,
        name,
        companionId,
        userId,
      },
    });


    if (!thread) {
      throw new Error("Thread not created");
    }
    
    if (path) {
      revalidatePath(path)
    }
    
    return thread;

  } catch (error) {
    console.log("[THREAD_CREATED]", error);
    throw new Error("Thread not created");

  }
};

export async function newThread({
  threadId,
  name,  
  companionId,
  content,
  role,
  path,
}: {
  threadId: string;
  name: string;
  companionId: string;
  content: string;
  role: "assistant" | "user";
  path?: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!threadId || !name || !companionId || !userId || !content || !role) {
      throw new Error('Missing required fields');
    };

    const thread: Thread = await prismadb.thread.create({
      data: {
        id: threadId,
        name,
        companionId,
        userId,
        messages: {
          create: [{
            role: role,
            content: content,
            userId: userId,
            companionId: companionId,
          }],
        },
      },
    });


    if (!thread) {
      throw new Error("Thread not created");
    }
    
    if (path) {
      revalidatePath(path)
    }
    
    return thread;

  } catch (error) {
    console.log("[THREAD_CREATED]", error);
    throw new Error("Thread not created");

  }
};


export async function updateInitialThread({
  threadId,
  name,  
  companionId,
  content,
  role,
  path,
}: {
  threadId: string,
  name: string,
  companionId: string,
  content: string,
  role: "assistant" | "user",
  path?: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!threadId || !name) {
      throw new Error('Missing required fields');
    };


    const existingThread = await prismadb.thread.update({
      where: {
        id: threadId,
      },
      data: {
        name,
        messages: {
          create: [{
            role: role,
            content: content,
            userId: userId,
            companionId: companionId,
          }],
        },
      },
    });

    if (!existingThread) {
      throw new Error("Thread not updated");
    }
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[THREAD_UPDATED]", error);
    throw new Error("Thread not updated");

  }
};


export async function renameThread({
  id,
  threadName,  
  path,
}: {
  id: string,
  threadName: string,
  path?: string;
}) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!id || !threadName) {
      throw new Error('Missing required fields');
    };


    const existingThread = await prismadb.thread.update({
      where: {
        id,
      },
      data: {
        name: threadName,
      },
    });

    if (!existingThread) {
      throw new Error("Thread name not updated");
    }
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[THREAD_NAME_UPDATED]", error);
    throw new Error("Thread name not updated");

  }
};

/*export async function removeThread({
  id, 
  companionId, 
  path 
}: { 
  id: string; 
  companionId: string; 
  path?: string 
}): Promise<void> {
  try {
    const { userId } = auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!id || !companionId) {
      throw new Error('Missing required fields');
    }

    const removeThread = await prismadb.thread.delete({
      where: {
        userId,
        companionId,
        id,
      },
    });

    if (!removeThread) {
      throw new Error("Thread not removed");
    }

    try {
      const session = await zepClient.memory.getSession(id);
      console.log(session);
      if (session) {
        await zepClient.memory.delete(id);
      }
    } catch (error) {
      console.error("Got error:", error);

    }

    if (path) {
      revalidatePath(path)
    }    

  } catch (error) {
    console.log("[THREAD_REMOVED]", error);
    throw new Error("Thread not removed");

  }
};*/

/*export async function clearThreads({ 
  companionId, 
  path 
}: { 
  companionId: string; 
  path: string 
}): Promise<void> {
  try {
    const { userId } = auth()

    if (!userId || !companionId) {
      throw new Error('Missing required fields');
    }

    const threads = await getThreads(userId, companionId)
    const clearThreads = await prismadb.thread.deleteMany({
      where: {
        userId,
        companionId,
      },
    });
    if (!clearThreads) {
      throw new Error("Threads not cleared");
    }

    if (threads) {
      for (const thread of threads) {
        try {
          const session = await zepClient.memory.getSession(thread.id);
          console.log(session);
          if (session) {
            await zepClient.memory.delete(thread.id);
          } 
        } catch (error) {
          console.error("Got error:", error);
        }
      }
    }
    
    if (path) {
      revalidatePath(path)
    } 

  } catch (error) {
    console.log("[THREADS_CLEARED]", error);
    throw new Error("Threads not cleared");

  }
  return redirect(path)
}*/


async function deleteObservationByOwnerAndSession(ownerId: string, sessionId: string) {
  try {
    // Initialize the Supabase client
    const client = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_PRIVATE_KEY!,
    );
    const { data, error } = await client.rpc('delete_observations_by_owner_session', {
      owner_id: ownerId,
      session_id: sessionId,
    });

    if (error) {
      console.error('Error executing RPC:', error);
    } else {
      console.log('Deleted rows:', data);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}


export async function removeThread({
  id,
  companionId,
  path
}: {
  id: string;
  companionId: string;
  path?: string;
}): Promise<void> {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!id || !companionId) {
      throw new Error('Missing required fields');
    }

    // Perform the deletion in parallel using Promise.all
    const deleteOperations: Promise<any>[] = [];

    // Remove the thread from Prisma
    const removeThreadPromise = prismadb.thread.delete({
      where: {
        userId,
        companionId,
        id,
      },
    });

    deleteOperations.push(removeThreadPromise);

    // Remove the session from ZepClient (if exists)
    const removeSessionPromise = (async () => {
      try {
        const session = await zepClient.memory.getSession(id);
        if (session) {
          await zepClient.memory.delete(id);
        }
      } catch (error) {
        console.error("Error deleting session:", error);
      }
    })();

    deleteOperations.push(removeSessionPromise);

    // Delete observations for the userId and sessionId in Supabase
    const removeObservationsPromise = deleteObservationByOwnerAndSession(userId, id);
    deleteOperations.push(removeObservationsPromise);

    // Execute all delete operations in parallel
    await Promise.all(deleteOperations);

    if (path) {
      revalidatePath(path);
    }

  } catch (error) {
    console.log("[THREAD_REMOVED]", error);
    throw new Error("Thread not removed");
  }
}


export async function clearThreads({
  companionId,
  path,
  checkpointer
}: {
  companionId: string;
  path: string;
  checkpointer?: boolean;
}): Promise<void> {
  try {
    const { userId } = await auth();

    if (!userId || !companionId) {
      throw new Error('Missing required fields');
    }

    // Get all threads for the user and companion
    const threads = await getThreads(userId, companionId);

    // Delete all threads from the database
    const clearThreads = await prismadb.thread.deleteMany({
      where: {
        userId,
        companionId,
      },
    });

    if (!clearThreads) {
      throw new Error("Threads not cleared");
    }

    if (threads && threads.length > 0) {
      // Use Promise.all to delete threads, sessions, and observations in parallel
      const deletionPromises = threads.map(async (thread) => {
        try {
          // Get the session for the current thread
          const session = await zepClient.memory.getSession(thread.id);
          if (session) {
            // Delete the session if it exists
            await zepClient.memory.delete(thread.id);
          }

          // Delete associated observations for the thread's userId and sessionId
          await deleteObservationByOwnerAndSession(userId, thread.id);
          // Delete associated Langchain checkpoints
          if (checkpointer) {
            await deleteCheckpointsByThreadId(thread.id);
          }
        } catch (error) {
          console.error("Error deleting session or observations for thread:", thread.id, error);
        }
      });

      // Wait for all deletion operations to complete
      await Promise.all(deletionPromises);
    }

    // Revalidate the path if provided
    if (path) {
      revalidatePath(path);
    }

  } catch (error) {
    console.log("[THREADS_CLEARED]", error);
    throw new Error("Threads not cleared");
  }

  return redirect(path);
}

/*
// Function to delete observations by owner_id and session_id
async function deleteObservationByOwnerAndSession(ownerId, sessionId) {
  try {
    // Execute delete query using the Supabase query builder
    const { data, error } = await client
      .from('observations_cohere')
      .delete()
      .match({
        'metadata->>owner_id': ownerId,
        'metadata->>session_id': sessionId,
      });

    if (error) {
      console.error('Error deleting data:', error);
    } else {
      console.log('Deleted rows:', data);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}
*/