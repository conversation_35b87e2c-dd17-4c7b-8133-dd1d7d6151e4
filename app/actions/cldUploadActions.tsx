'use server'

import prismadb from "@/lib/prismadb";
import { revalidatePath } from 'next/cache'
import { auth } from "@clerk/nextjs/server"
import { v2 as cloudinary } from "cloudinary";
import { uploadImage, uploadImageAddBackground } from "@/lib/cloudinary";
import { Media, Role } from "@prisma/client";


// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

enum MediaType {
  image = 'image',
  video = 'video',
}

export async function cldUploadActions({
  companionId, imageUrl, path, sketch
}: {
  companionId: string,
  imageUrl: string,
  path: string,
  sketch?: boolean,
  }) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  let cloudinary_resp
  try {
    if (sketch) {
      //console.log("sketch is true")
      cloudinary_resp = await uploadImageAddBackground(imageUrl)
    } else {
      cloudinary_resp = await uploadImage(imageUrl)
    }
    
    if (cloudinary_resp) {
      await prismadb.companion.update({
        where: {
          id: companionId,
        },
        data: {
          /*messages: {
            create: [
              { 
                role: Role.user,
                userId,
                content: '',
                image: cloudinary_resp.secure_url
              },
            ]
          },*/
          medias: {
            create: [
              {
                userId,
                url: cloudinary_resp.secure_url,
                mediaType: MediaType.image,
                desc: "cldTransformation",
              },
            ],
          },
        },        
      });
      revalidatePath(path)    
      console.log("CloudinaryResponse: ", cloudinary_resp)
      return cloudinary_resp.secure_url

    }
    
  } catch (error) {
    console.error('[IMAGE_UPLOAD_ERROR]', error);
    throw new Error("IMAGE_UPLOAD_ERROR");
  }
}


export async function updateGalleryAction({
  companionId, imageUrl, path
}: {
  companionId: string,
  imageUrl: string,
  path: string,
  }) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  try {
    await prismadb.companion.update({
        where: {
          id: companionId,
        },
        data: {
          medias: {
            create: [
              {
                userId,
                url: imageUrl,
                mediaType: MediaType.image,
                desc: "CldUploadButton",
              },
            ],
          },
        },        
      });
      revalidatePath(path)      

  } catch (error) {
    console.error('[IMAGE_UPLOAD_ERROR]', error);
    throw new Error("IMAGE_UPLOAD_ERROR");
  }
}