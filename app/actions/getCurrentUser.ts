'use server'

import prismadb from "@/lib/prismadb";
import { currentUser as clerkCurrentUser } from '@clerk/nextjs/server'

const getCurrentUser = async () => {
  try {
    const clerkUser = await clerkCurrentUser();    

    if (!clerkUser?.emailAddresses[0]?.emailAddress) {
      return null;
    }

    const currentUser = await prismadb.persona.findUnique({
      where: {
        email: clerkUser.emailAddresses[0].emailAddress as string
      },
      cacheStrategy: {
        swr: 60,
        ttl: 30
      },
    });

    if (!currentUser) {
      return null;
    }

    return currentUser;
  } catch (error: any) {
    return null;
  }
};

export default getCurrentUser;