'use server'

import prismadb from "@/lib/prismadb";
import { currentUser } from '@clerk/nextjs/server'

const getCurrentProfile = async () => {
  try {
    const clerkUser = await currentUser();    

    if (!clerkUser?.id) {
      return null;
    }

    const currentProfile = await prismadb.profile.findUnique({
      where: {
        externalId: clerkUser.id as string
      },
      cacheStrategy: {
        swr: 60,
        ttl: 30
      },
      include: {
        admin: true
      }
    });

    if (!currentProfile) {
      return null;
    }

    return currentProfile;
  } catch (error: any) {
    return null;
  }
};

export default getCurrentProfile;