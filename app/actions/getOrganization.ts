import prismadb from "@/lib/prismadb";

//export const runtime = 'edge'

interface getOrganizationProps {
  orgId: string;
}

const getOrganization = async ({
  orgId
}: getOrganizationProps) => {
  try {
    const organization = await prismadb.organization.findUnique({
      where: {
        id:orgId
      },
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        email: true,
        website: true,
        address: true,
        phoneNumber: true,
        status: true,
        members: {
          select: {
            id: true,
            createdAt: true,
            updatedAt: true,
            orgId: true,
            user: { // This is how you select fields from the `user` inside `members`
              select: {
                id: true,
                externalId: true,
                attributes: true,
                // createdAt: true, // Uncomment if needed
                // updatedAt: true, // Uncomment if needed
                // Exclude organizationPasscode or other sensitive fields
              },
            },
          },
        },
      },
    });

    return organization
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null;
  }
}
 
export default getOrganization;