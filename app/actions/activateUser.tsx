'use server';

import prismadb from "@/lib/prismadb";
import { currentUser } from '@clerk/nextjs/server';
import { MAX_FREE_COUNTS as DEFAULT_MAX_FREE_COUNTS, API_MULTIPLIER, UNIT_AMOUNT } from "@/constants";

export const activateUser = async (otp: string) => {
  console.log("otp: ", otp)
  try {
    const user = await currentUser();
    const userId = user?.id
    const email = user?.emailAddresses[0]?.emailAddress
  
    if (!userId || !email ||!otp) {
      return null;
    }
    // Check if the OTP exists in the oneTimeAccessCode table
    const otpRecord = await prismadb.oneTimeAccessCode.findUnique({
      where: { code: otp },
      include: { organization: true }
    });

    if (!otpRecord) {
      return {
        status: 400,
        body: { message: 'Invalid OTP' }
      };
    }

    // Check if user is already allowed
    const hasUser = await prismadb.allowedUser.findUnique({
      where: { userId },
    });

    if (!hasUser) {
      // Register the new allowed user
      await prismadb.allowedUser.create({
        data: { 
          userId,
          email,
        },
      });

      // Wrap all operations in a Prisma transaction
      const result = await prismadb.$transaction(async (prisma) => {
        // Update the user's profile with the organization passcode
        const updatedProfile = await prisma.profile.update({
          where: { externalId: userId },
          data: { organizationPasscode: otpRecord.organization.organizationPasscode },
        });

        // Create free credits for the new user
        const MAX_FREE_COUNTS = otpRecord?.freeCount ?? DEFAULT_MAX_FREE_COUNTS
        const freeCreditCost: number = (MAX_FREE_COUNTS / API_MULTIPLIER) * UNIT_AMOUNT;
        const createdCredit = await prisma.creditBalance.create({
          data: {
            balance: freeCreditCost,
            apiBalance: MAX_FREE_COUNTS,
            userId,
            Transactions: {
              create: {
                count: MAX_FREE_COUNTS,
                amount: freeCreditCost,
                inputTokens: 0,
                outputTokens: 0,
                userId,
                model: 'gpt_4o',
                notes: "Free credits",
              },
            },
          },
        });

        
        // Optionally, delete or mark the OTP as used
        const deletedOtp = await prisma.oneTimeAccessCode.delete({
          where: { id: otpRecord.id }
        });

        // Return all results if successful
        return { updatedProfile, deletedOtp, createdCredit };
      });

      // Transaction success
      console.log("Transaction complete: ", result);


      return {
        status: 200,
        body: { message: 'User activated successfully' }
      };
    } else {
      return {
        status: 400,
        body: { message: 'User is already activated' }
      };
    }
  } catch (error) {
    console.error('Error activating user: ', error);
    return {
      status: 500,
      body: { message: 'Error activating user' }
    };
  }
};