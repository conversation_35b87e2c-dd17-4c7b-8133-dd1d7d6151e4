'use server'

import prismadb from "@/lib/prismadb";
import { currentUser } from '@clerk/nextjs/server'

const getUsers = async () => {
  const user = await currentUser();

  if (!user?.emailAddresses[0]?.emailAddress) {
    return [];
  }

  try {
    const users = await prismadb.persona.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      where: {
        NOT: {
          email: user?.emailAddresses[0].emailAddress
        }
      },
      cacheStrategy: {
        swr: 60,
        ttl: 30
      },
    });

    return users;
  } catch (error: any) {
    return [];
  }
};

export default getUsers;