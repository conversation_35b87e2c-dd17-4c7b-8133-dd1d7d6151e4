'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from '@clerk/nextjs/server'
import prismadb from "@/lib/prismadb"
import { GoogleGenerativeAI } from "@google/generative-ai"
import { ResumeInfo } from '@/components/resume/types'
import { nanoid } from '@/lib/utils'

export interface ResumeData extends ResumeInfo {}
  
const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY
if (!apiKey) {
  throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is not defined in environment variables')
}
const genAI = new GoogleGenerativeAI(apiKey)

const model = genAI.getGenerativeModel({
  model: "gemini-2.5-flash",
})

const generationConfig = {
  temperature: 1,
  topP: 0.95,
  topK: 64,
  maxOutputTokens: 8192,
  responseMimeType: "application/json",
}
export async function AIChatSession(PROMPT: string) {
  console.log("PROMPT ", PROMPT)
  const chat = model.startChat({
    generationConfig,
    history: [],
  })

  const res = await chat.sendMessage(PROMPT)
  console.log("res ", res)
  // Extract only the text or data you need
  return {
    candidates: res.response.candidates,
    usageMetadata: res.response.usageMetadata,
    text: await res.response.text() // Assuming text() is an async function that returns the response as a string
  }
}

export async function createNewResume(data: Partial<ResumeData>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!data.title || !data.userEmail || !data.userName) {
      throw new Error('Missing required fields');
    }

    const resume = await prismadb.userResume.create({
      data: {
        title: data.title,
        resumeId: data.resumeId || nanoid(),
        image: data.image ?? '',
        userEmail: data.userEmail || undefined,
        userName: data.userName || undefined,
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        address: data.address || '',
        jobTitle: data.jobTitle || '',
        phone: data.phone || '',
        email: data.email || '',
        summary: data.summary || '',
        themeColor: data.themeColor || "#ff6666",
        profile: { connect: { externalId: userId } },
        experiences: {
          create: data.experiences?.map(exp => ({
            title: exp.title || '',
            companyName: exp.companyName || '',
            city: exp.city || '',
            state: exp.state || '',
            startDate: exp.startDate ? new Date(exp.startDate).toISOString() : null,
            endDate: exp.endDate ? new Date(exp.endDate).toISOString() : null,
            currentlyWorking: exp.currentlyWorking || false,
            workSummary: exp.workSummary || ''
          })) || []
        },
        educations: {
          create: data.educations?.map(edu => ({
            universityName: edu.universityName || '',
            degree: edu.degree || '',
            major: edu.major || '',
            startDate: edu.startDate ? new Date(edu.startDate).toISOString() : null,
            endDate: edu.endDate ? new Date(edu.endDate).toISOString() : null,
            description: edu.description || ''
          })) || []
        },
        skills: {
          create: data.skills?.map(skill => ({
            name: skill.name || '',
            rating: skill.rating || null
          })) || []
        }
      },
    });

    if (!resume) {
      throw new Error("Resume not created");
    }

    revalidatePath('/resumes');

    return { success: true, data: resume };

  } catch (error) {
    console.error("[RESUME_CREATED]", error);
    return { success: false, error: "Resume not created" };
  }
}

export async function getUserResumes(userEmail: string) {
  try {
    const { userId } = await auth()

    if (!userId || !userEmail) {
      throw new Error('Unauthorized')
    }

    const resumes = await prismadb.userResume.findMany({
      where: { userEmail: userEmail },
      include: {
        experiences: true,
        educations: true,
        skills: true,
      }
    })

    console.log("resumes: ", resumes)
    return { success: true, data: resumes }
  } catch (error) {
    console.error('Error fetching resumes:', error)
    return { success: false, error: 'An error occurred while fetching resumes' }
  }
}


export async function updateResumeDetail(resumeId: string, data: Partial<ResumeInfo>) {
  try {
    const result = await prismadb.userResume.update({
      where: { id: resumeId },
      data: {
        title: data.title ?? undefined,
        image: data.image ?? undefined,
				userEmail: data.userEmail ?? undefined,
        userName: data.userName ?? undefined,
        firstName: data.firstName ?? undefined,
        lastName: data.lastName ?? undefined,
        address: data.address ?? undefined,
        jobTitle: data.jobTitle ?? undefined,
				phone: data.phone ?? undefined,
				email: data.email ?? undefined,
        summary: data.summary ?? undefined,
        themeColor: data.themeColor ?? undefined,
        experiences: {
          upsert: data.experiences?.map(exp => ({
            where: { id: exp.id ?? nanoid() }, // Ensure a unique identifier is used
            update: {
              title: exp.title ?? '',
              companyName: exp.companyName ?? '',
              city: exp.city ?? '',
              state: exp.state ?? '',
              startDate: exp.startDate ? new Date(exp.startDate).toISOString() : null,
              endDate: exp.endDate ? new Date(exp.endDate).toISOString() : null,
              currentlyWorking: exp.currentlyWorking ?? false,
              workSummary: exp.workSummary ?? ''
            },
            create: {
              id: exp.id ?? nanoid(),
              title: exp.title ?? '',
              companyName: exp.companyName ?? '',
              city: exp.city ?? '',
              state: exp.state ?? '',
              startDate: exp.startDate ? new Date(exp.startDate).toISOString() : null,
              endDate: exp.endDate ? new Date(exp.endDate).toISOString() : null,
              currentlyWorking: exp.currentlyWorking ?? false,
              workSummary: exp.workSummary ?? ''
            }
          })) || [],
        },
        educations: {
          upsert: data.educations?.map(edu => ({
            where: { id: edu.id ?? nanoid() },
            update: {
              universityName: edu.universityName ?? '',
              degree: edu.degree ?? '',
              major: edu.major ?? '',
              startDate: edu.startDate ? new Date(edu.startDate).toISOString() : null,
              endDate: edu.endDate ? new Date(edu.endDate).toISOString() : null,
              description: edu.description ?? ''
            },
            create: {
              id: edu.id ?? nanoid(),
              universityName: edu.universityName ?? '',
              degree: edu.degree ?? '',
              major: edu.major ?? '',
              startDate: edu.startDate ? new Date(edu.startDate).toISOString() : null,
              endDate: edu.endDate ? new Date(edu.endDate).toISOString() : null,
              description: edu.description ?? ''
            }
          })) || [],
        },
        skills: {
          upsert: data.skills?.map(skill => ({
            where: { id: skill.id ?? nanoid() },
            update: {
              name: skill.name ?? '',
              rating: skill.rating ?? null
            },
            create: {
              id: skill.id ?? nanoid(),
              name: skill.name ?? '',
              rating: skill.rating ?? null
            }
          })) || []
        }
      }
    })

    return { success: true, data: result }
  } catch (error) {
    console.error("[RESUME_UPDATE_ERROR]", error)
    return { success: false, error: "Resume not updated" }
  }
}

export async function deleteResumeById(id: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      throw new Error('Unauthorized')
    }

    await prismadb.userResume.delete({
      where: { id: id }
    })

    revalidatePath('/resumes')

    return { success: true, message: 'Resume deleted successfully' }
  } catch (error) {
    console.error('Error deleting resume:', error)
    return { success: false, error: 'An error occurred while deleting the resume' }
  }
}


export async function getResumeById(resumeId: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      throw new Error('Unauthorized')
    }

    const resume = await prismadb.userResume.findUnique({
      where: { id: resumeId },
      include: {
        experiences: true,
        educations: true,
        skills: true,
      }
    })

    return { success: true, data: resume }
  } catch (error) {
    console.error('Error fetching resume by ID:', error)
    return { success: false, error: 'An error occurred while fetching the resume' }
  }
}

