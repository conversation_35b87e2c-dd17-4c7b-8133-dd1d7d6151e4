'use server'

import { auth } from '@clerk/nextjs/server';
import prismadb from "@/lib/prismadb";
import { Companion, Persona, Thread } from '@prisma/client'

type PersonaSelectedFields = Pick<Persona, 'id' | 'name' | 'nickName' | 'age' | 'traits' | 'status' | 'email'>;

export async function fetchPersona(userId: string): Promise<PersonaSelectedFields | null> {
  const { userId: authedUserId } = await auth();

  if (!authedUserId || !userId) {
    return null; // Return null if the user is not authenticated
  }

  try {
    const persona: PersonaSelectedFields | null = await prismadb.persona.findUnique({
      where: {
        userId
      },
      select: {
        id: true,
        name: true,
        nickName: true,
        age: true,
        traits: true,
        status: true,
        email: true,
      }
    });

    if (!persona) {
      return null; // Return null if no persona is found
    }

    return persona;
  } catch (error: any) {
    console.error("Error fetching persona:", error);
    return null; // Return null in case of an error
  }
}


export async function fetchCompanions(userId: string): Promise<(Partial<Companion> & { threads: Partial<Thread>[] })[]> {
  const { userId: authedUserId } = await auth();

  if (!authedUserId) {
    throw new Error("Unauthorized");
  }

  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    const companions = await prismadb.companion.findMany({
      where: {
        threads: {
          some: {
            userId: userId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        threads: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
          where: {
            userId: userId,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    return companions;
  } catch (error: any) {
    console.error("Error fetching companions:", error);
    throw new Error("Failed to fetch companions");
  }
}