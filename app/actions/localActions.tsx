'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from '@clerk/nextjs/server';

import prismadb from "@/lib/prismadb";
import { 
  Companion, 
  Thread, 
  UserThread,
  Message, 
  ChatSummary,
  Persona, 
  Observations, 
  Todos,
  File } from '@prisma/client'


type ThreadInfo = { threadId: string; name: string } | null;


export async function getThreads(
  userId?: string | null, 
  companionId?: string | null,
  botId?: string | null,
  ) {  
  if (!userId || !companionId || !botId) {
    return []
  }

  try {
    const threads = await prismadb.userThread.findMany({
      where: {
        companionId,
        userId,
        assistantId: botId,
      },
      orderBy: {
        createdAt: 'desc'
      },
    });

    //console.log("getThreads: ", threads)
    return threads;
  } catch (error: any) {
    return [];
  }
};


export async function getThread(
  userId: string | null, 
  companionId: string | null,
  threadId: string | null
): Promise<ThreadInfo> {
  
  if (!userId || !companionId || !threadId) {
    throw new Error('Missing required fields');
  }
  
  try {
    const thread = await prismadb.userThread.findFirst({
      where: {
        threadId,
        companionId,
        userId,        
      },
    });

    if (thread) {
      return {
        threadId: thread.threadId,
        name: thread.name,
      };
    } else {
      return null;
    }
  } catch (error: any) {
    console.error("Error fetching thread:", error);
    throw new Error("Unable to fetch thread");
  }
};


export async function newThreadRecord({
    threadId,
    name,  
    companionId,
    botId,
    path,
  }: {
    threadId: string;
    name: string;
    companionId: string;
    botId: string;
    path?: string;
  }) {
    try {
      const { userId } = await auth();
  
      if (!userId) {
        return {
          error: 'Unauthorized'
        }
      }
  
      if (!threadId || !name || !companionId || !botId || !userId) {
        return {
          error: 'Missing required fields'
        }
      };
  
      const thread: Thread = await prismadb.userThread.create({
        data: {
          id: threadId,
          name,
          companionId,
          assistantId: botId,
          userId,
          threadId,
        },
      });
  
  
      if (!thread) {
        throw new Error("Thread not created");
      }
      
      if (path) {
        revalidatePath(path)
      }
      
      return thread;
  
    } catch (error) {
      console.log("[THREAD_CREATED]", error);
      throw new Error("Thread not created");
  
    }
  };

export async function renameThread({
    id,
    threadName,  
    path,
  }: {
    id: string,
    threadName: string,
    path?: string;
  }) {
    try {
      const { userId } = await auth();
  
      if (!userId) {
        return {
          error: 'Unauthorized'
        }
      }
  
      if (!id || !threadName) {
        return {
          error: 'Missing required fields'
        }
      };
  
  
      const existingThread = await prismadb.userThread.update({
        where: {
          id,
        },
        data: {
          name: threadName,
        },
      });
  
      if (!existingThread) {
        throw new Error("Thread name not updated");
      }
      
      if (path) {
        revalidatePath(path)
      }
  
    } catch (error) {
      console.log("[THREAD_NAME_UPDATED]", error);
      throw new Error("Thread name not updated");
  
    }
  };
export async function removeThread({
    id, 
    companionId, 
    path 
  }: { 
    id: string; 
    companionId: string; 
    path?: string 
  }): Promise<void> {
    try {
      const { userId } = await auth();
  
      if (!userId) {
        throw new Error('Missing required fields');
      }
  
      if (!id || !companionId) {
        throw new Error('Missing required fields');
      }
  
      const removeThread = await prismadb.userThread.delete({
        where: {
          userId,
          companionId,
          id,
        },
      });
  
      if (!removeThread) {
        throw new Error("Thread not removed");
      }
      
      if (path) {
        revalidatePath(path)
      }    
  
    } catch (error) {
      console.log("[THREAD_REMOVED]", error);
      throw new Error("Thread not removed");
  
    }
  };


export async function clearThreads({ 
    companionId, 
    botId,
    path 
  }: { 
    companionId: string;
    botId: string; 
    path: string 
  }): Promise<void> {
    try {
      const { userId } = await auth()
  
      if (!userId || !companionId || !botId) {
        throw new Error('Missing required fields');
      }
  
      const clearThreads = await prismadb.userThread.deleteMany({
        where: {
          userId,
          companionId,
          assistantId: botId,
        },
      });
  
      if (!clearThreads) {
        throw new Error("Threads not cleared");
      }
      
      if (path) {
        revalidatePath(path)
      } 
  
    } catch (error) {
      console.log("[THREADS_CLEARED]", error);
      throw new Error("Threads not cleared");
  
    }
    return redirect(path)
  }
