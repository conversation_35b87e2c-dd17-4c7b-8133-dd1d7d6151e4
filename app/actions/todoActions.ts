'use server'

import { revalidatePath } from 'next/cache'
import { auth } from "@clerk/nextjs/server"

import prismadb from "@/lib/prismadb";
import { Todos } from "@prisma/client";


export async function addTodoAction({
  companionId,
  task,
  path,
}: {
  companionId: string;
  task: string;
  path?: string;
}): Promise<Todos> {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!task || !companionId) {
      throw new Error('Missing required fields');
    }

    const todo: Todos = await prismadb.todos.create({
      data: {
        task,
        userId,
        companionId,
      },
    });

    if (path) {
      revalidatePath(path);
    }

    return todo;

  } catch (error) {
    console.log("[TODOS_ADDED]", error);
    throw new Error("Todos not added");
  }
}



export async function updateTodoAction({
    todoId,
    is_completed,
    path,
  }: {
    todoId: string;
    is_completed: boolean;
    path?: string;
  }) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!todoId || !is_completed) {
      throw new Error('Missing required fields');
    };


    const updatedTodo: Todos = await prismadb.todos.update({
      where: {
        id: todoId,
      },
      data: {
        is_completed: is_completed,
      }
    });

    if (!updatedTodo) {
      throw new Error("Todos not updated");
    }
    
    if (path) {
      revalidatePath(path)
    }

  } catch (error) {
    console.log("[TODOS_UPDATED]", error);
    throw new Error("Todos not updated");

  }
};



export async function deleteTodoAction({
    todoId,
    path,
  }: {
    todoId: string;
    path?: string;
  }) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    if (!todoId) {
      throw new Error('Missing required fields');
    };


    const deletedTodo: Todos = await prismadb.todos.delete({
      where: {
        userId,
        id: todoId,
      },
    });

    if (!deletedTodo) {
      throw new Error("Todos not deleted");
    }
    
    if (path) {
      revalidatePath(path)
    }    

  } catch (error) {
    console.log("[TODOS_DELETED]", error);
    throw new Error("Todos not deleted");

  }
};