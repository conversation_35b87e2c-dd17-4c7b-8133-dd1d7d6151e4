export const threadSummaryTool = [
  {
    type: 'function',
    function: {
      name: 'thread_summary',
      description: 'Prints a summary of the conversation.',
      inputSchema: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: 'Distinct for the conversation.'
          },
          topics: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of tags for topics discussed in this conversation, e.g. ["tech", "politics"]. Should be as specific as possible, and can overlap.'
          },
          summary: {
            type: 'string',
            description: 'High level summary of the interactions. One or two paragraphs max.'
          },
          coherence: {
            type: 'integer',
            description: 'Coherence of the conversation, 0-100 (inclusive)'
          },
          persuasion: {
            type: 'number',
            description: "Conversation's persuasion score, 0.0-1.0 (inclusive)"
          }
        },
        required: ['title', 'topics', 'summary', 'coherence', 'persuasion', 'counterpoint']
      }
    }
  }
];

export const userProfileTool = [
  {
    type: 'function',
    function: {
      name: 'user_state',
      description: 'Prints extract named entities.',
      inputSchema: {
        type: 'object',
        properties: {
          preferred_name: {
            type: 'string',
            description: "The user's name."
          },
          summary: {
            type: 'string',
            description: 'A quick summary of how the user would describe themselves.'
          },
          interests: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'Array of short (two to three word) descriptions of areas of particular interest for the user. This can be a concept, activity, or idea. Favor broad interests over specific ones.'
          },
          other_info: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: ''
          },
          relationships: {
            type: 'array',
            description: 'A list of friends, family members, colleagues, and other relationships.',
            items: {
              type: 'object',
              description: "A person's biographical details.",
              properties: {
                name: {
                  type: 'string',
                  description: 'The name of the person.'
                },
                relation: {
                  type: 'string',
                  description: 'The relation of the person to the user.'
                },
                context: {
                  type: 'string',
                  description: 'A detailed yet concise history of things the person has done with the user.'
                }
              },
              required: ['name', 'relation', 'context']
            }
          }
        },
        required: ['summary']
      }
    }
  }
];

export const coreBeliefAnalysisTool = [
  {
    type: 'function',
    function: {
      name: 'core_belief_analysis',
      description: 'Analyzes and records the core beliefs of the user as extracted from conversations.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                belief: {
                  type: 'string',
                  description: 'The belief the user has about the world, themselves, or anything else.'
                },
                why: {
                  type: 'string',
                  description: 'Why the user believes this.'
                },
                context: {
                  type: 'string',
                  description: 'The raw context from the conversation that leads you to conclude that the user believes this.'
                }
              },
              required: ['belief', 'why', 'context']
            }
          }
        },
        required: ['entries']
      }
    },
  },
  {
    type: 'function',
    function: {
      name: 'formative_event_logging',
      description: 'Logs a significant, formative event in the user\'s life and its impact on them. This tool appends the recorded event to the user\'s state, contributing to a comprehensive understanding of the user\'s experiences.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                event: {
                  type: 'string',
                  description: 'The event that occurred. Must be important enough to be formative for the user.'
                },
                impact: {
                  type: 'string',
                  description: 'How this event influenced the user.'
                }
              },
              required: ['event', 'impact']
            }
          }
        },
        required: ['entries']
      }
    }
  }
];

export const userReflectionAnalysisTools = [
  {
    type: 'function',
    function: {
      name: 'user_reflection_analysis',
      description: 'Analyzes reflections on specific activities or experiences (like stickball) and their significance to the user\'s life, along with scoring based on recency, importance, and relevance.',
      inputSchema: {
        type: 'object',
        properties: {
          entries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                content: {
                  type: 'object',
                  properties: {
                    subject: {
                      type: 'string',
                      description: 'The subject of the reflection.'
                    },
                    predicate: {
                      type: 'string',
                      description: 'The user\'s reflection on the activity or experience.'
                    },
                    object: {
                      type: 'string',
                      description: 'The impact of the activity or experience on the user\'s life.'
                    }
                  },
                  required: ['subject', 'predicate', 'object']
                },
                scores: {
                  type: 'object',
                  properties: {
                    recency: {
                      type: 'number',
                      description: 'The recency score of the reflection.'
                    },
                    importance: {
                      type: 'number',
                      description: 'The importance score of the reflection.'
                    },
                    relevance: {
                      type: 'number',
                      description: 'The relevance score of the reflection.'
                    }
                  },
                  required: ['recency', 'importance', 'relevance']
                }
              },
              required: ['content', 'scores']
            }
          }
        },
        required: ['entries']
      }
    }
  }
];
