import prismadb from "@/lib/prismadb";
import getCurrentUser from "./getCurrentUser";

const getConversationById = async (
  conversationId: string
) => {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser?.email) {
      return null;
    }
  
    const conversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId
      },
      include: {
        users: true,
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    return conversation;
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null;
  }
};

export default getConversationById;