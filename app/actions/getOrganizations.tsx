'use server'

import prismadb from "@/lib/prismadb";
import getAdmin from "@/app/actions/getAdmin";

//export const runtime = 'edge'

type getOrganizationsProps = {
  adminId: string;
}

const getOrganizations = async ({
  adminId
}: getOrganizationsProps) => {
  try {
  // Find the AdminOrganization records associated with the specific admin ID
  const adminOrganizations = await prismadb.adminOrganization.findMany({
    where: {
      adminId,
    },
    select: {
      orgId: true,
    }
  });
  
  // Extract the orgId from each AdminOrganization record
  const orgIds = adminOrganizations.map(adminOrg => adminOrg.orgId);
  
  // Find the Organization records using the extracted orgIds
  const organizations = await prismadb.organization.findMany({
    where: {
      id: {
        in: orgIds,
      },
    },
    select: {
      id: true,
      name: true,
      description: true,
      createdAt: true,
      updatedAt: true,
      email: true,
      website: true,
      address: true,
      phoneNumber: true,
      status: true,
      members: {
        select: {
          id: true,
          createdAt: true,
          updatedAt: true,
          orgId: true,
          user: { // This is how you select fields from the `user` inside `members`
            select: {
              id: true,
              externalId: true,
              attributes: true,
              // createdAt: true, // Uncomment if needed
              // updatedAt: true, // Uncomment if needed
              // Exclude organizationPasscode or other sensitive fields
            },
          },
        },
      },
    },
  });
  
  //console.log('organizations', organizations)
  return organizations;
  } catch (error: any) {
    console.log(error, 'SERVER_ERROR')
    return null;
  }
}
 
export default getOrganizations;


export type OrganizationView = {
  id: string;
  name: string;
  members: {
    id: string;
    user: {
      id: string;
      attributes: any;
    };
  }[];
};

export const getOrganizationsView = async ({
  adminId,
}: {
  adminId: string;
}): Promise<OrganizationView[]> => {
  try {
    const adminWithOrganizations = await prismadb.admin.findUnique({
      where: {
        id: adminId,
      },
      include: {
        adminOrganizations: {
          select: {
            orgId: true,
            organization: {
              select: {
                id: true,
                name: true,
                members: {
                  select: {
                    id: true,
                    user: {
                      select: {
                        id: true,
                        attributes: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    // Map the result to extract and structure only the required fields
    const organizations = adminWithOrganizations?.adminOrganizations.map(
      (adminOrg) => ({
        id: adminOrg.organization.id,
        name: adminOrg.organization.name,
        members: adminOrg.organization.members.map((member) => ({
          id: member.id,
          user: member.user,
        })),
      })
    );

    return organizations ?? [];
  } catch (error: any) {
    console.error("Error fetching organizations:", error);
    return [];
  }
};
