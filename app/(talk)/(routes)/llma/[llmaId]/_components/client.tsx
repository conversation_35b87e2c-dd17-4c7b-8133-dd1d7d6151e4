"use client";

import { useCompletion } from '@ai-sdk/react';
import { FormEvent, useState } from "react";
import { Companion, Message, Observations, Todos } from "@prisma/client";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";

import { ChatForm } from "@/components/chat-form";
import { ChatHeader } from "@/components/chat-header";
import { ChatMessages } from "@/components/chat-messages";
import { ChatMessageProps } from "@/components/chat-item";
import { ObservationModal } from '@/components/modals/ObservationModal'
import ObservationNew from '@/components/observation/observation-new';
import Character from '@/components/post/character'
import TodoList from '@/components/suggestion/TodoList'
import { Characters } from '@/components/post/config'
import type { CharacterType } from '@/lib/CharacterType'



interface LlmaClientProps {
  companion: Companion & {
    messages: Message[];
    observations: Observations[];
    todos: Todos[];
    _count: {
      messages: number;
    }
  };
};

export const LlmaClient = ({
  companion,
}: LlmaClientProps) => {
  const router = useRouter();
  const { user } = useUser();
  const [messages, setMessages] = useState<ChatMessageProps[]>(companion.messages);
  const [character, setCharacter] = useState<CharacterType>(Characters[0]);

  const observations: Observations[] = companion ? companion.observations : [];
  const todos: Todos[] = companion ? companion.todos : [];
  const {
    input,
    isLoading,
    handleInputChange,
    handleSubmit,
    setInput,
  } = useCompletion({
    api: `/api/llma/${companion.id}`,
    onFinish(_prompt, completion) {
      const aiMessage: ChatMessageProps = {
        role: "assistant",
        content: completion
      };

      setMessages((current) => [...current, aiMessage]);
      setInput("");

      router.refresh();
    },
  });

  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    const userMessage: ChatMessageProps = {
      role: "user",
      content: input
    };

    setMessages((current) => [...current, userMessage]);

    handleSubmit(e);
    
  }

  

  return (
      <div className="flex max-w-screen min-h-screen max-h-screen">
        {/* Left Panel */}
        <div className="hidden h-[100vh] lg:block md:flex w-[45%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">          
          <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
            <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
              <div className="pt-2">
                <ObservationModal companionId={companion.id} userId={user?.id!} path={`/llma/${companion.id}`} />
              </div>
              <div className="pt-2">
                My Observations
              </div>
            </div>
            <div className="mt-0">
              <ObservationNew observations={observations} />
            </div>
          </div>
        </div>        
        {/* Center Panel */}
        <div className="flex flex-col h-auto w-[100%] p-4 space-y-2">
          <ChatHeader companion={companion} />
          <div className="flex-grow overflow-y-auto">
            <ChatMessages 
              companion={companion}
              isLoading={isLoading}
              messages={messages}
            />
          </div>
          <ChatForm 
            isLoading={isLoading} 
            input={input} 
            handleInputChange={handleInputChange} 
            onSubmit={onSubmit} 
          />
          {isLoading && <Character character={character} />}
        </div>

        {/* Right Panel */}
        <div className="hidden lg:block w-[45%] flex-grow pt-[2.75rem] px-1 overflow-y-auto division">
          <div className="flex flex-col pl-2 justify-center">
          {todos.length > 0 && (
            <TodoList companionId={companion.id} todosData={todos ?? []}/>
          )}
          </div>
        </div>
      </div>
   );
}
