import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server"

import prismadb from "@/lib/prismadb";

import { LlmaClient } from "./_components/client";

//export const runtime = 'edge'

interface LlmaIdPageProps {
  params: Promise<{
    llmaId: string;
  }>
}


const LlmaIdPage = async (props: LlmaIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.llmaId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      todos: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });


  if (!companion) {
    return redirect("/");
  }


  return (
     <LlmaClient companion={companion} />
  );
}
 
export default LlmaIdPage;
