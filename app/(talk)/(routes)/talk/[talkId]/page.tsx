import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";

import prismadb from "@/lib/prismadb";
import { Persona } from "@prisma/client";

import {TalkClient } from "./_components/client";

//export const runtime = 'edge' // Disable due to oversize limit to 1M

interface TalkIdPageProps {
  params: Promise<{
    talkId: string;
  }>
}


const TalkIdPage = async (props: TalkIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.talkId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      todos: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      serviceInvitations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  });


  if (!companion) {
    return redirect("/");
  }


  return (
     <TalkClient companion={companion} foundPersona ={foundPersona} />
  );
}
 
export default TalkIdPage;