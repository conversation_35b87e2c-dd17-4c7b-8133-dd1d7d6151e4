'use client'

import axios from 'axios'
import DOMPurify from 'dompurify'
import { useUser } from "@clerk/nextjs";
import { useToast } from "@/components/ui/use-toast";
import { useProModal } from "@/hooks/use-pro-modal";
import { Companion, Message, Observations, Todos, ServiceInvitation, Persona } from "@prisma/client";
import { SyntheticEvent, useCallback, KeyboardEvent, useState, useRef, useEffect } from 'react'
import { notFound, useRouter } from 'next/navigation'
import { IChoiceGroupOption, Checkbox, Dropdown, IDropdownOption, Panel, DefaultButton, Spinner, TextField, SpinButton } from '@fluentui/react'
import { format } from 'date-fns'
import { zonedTimeToUtc } from 'date-fns-tz';
import { useStopwatch } from 'react-timer-hook'
import { v4 as uuidv4 } from 'uuid'
import { chatApi } from '@/components/post/chatApi'
import { Answer } from '@/components/post/Answer'
import { parseAnswerToHtml } from '@/components/post/AnswerParser'
import { Characters, TestMessages } from '@/components/post/config'
import { SettingsButton } from '@/components/Settings/SettingsButton'
import { ChatHeader } from "@/components/chat-header";
import { TalkMessages } from "@/components/talk-messages";
import { TalkMessageProps } from "@/components/talk-message";
import { SuggestionList } from '@/components/suggestion'
import { updatePolarAreaChart } from '@/components/post/Sentiment'
import { ObservationModal } from '@/components/modals/ObservationModal'
import ObservationNew from '@/components/observation/observation-new';
import CountdownTimer from '@/lib/countdown-timer'
import TodoList from '@/components/suggestion/TodoList'
import Character from '@/components/post/character'
import PostList from '@/components/post/post-list'
import ChatForm from '@/components/post/ChatForm'
import RadarChart from '@/components/post/RadarChart'
import PolarAreaChart from '@/components/post/PolarAreaChart'
import DynamicChoiceGroup from '@/components/post/DynamicChoiceGroup'
import { 
  Approaches,
  AskResponse,
  ChatRequest,
  SentimentData,
  PolarAreaChartType,
  PromptSuggestType,
  QuestionWithOptions
} from '@/lib/post.types'
import { CharacterType, MessageType } from '@/lib/CharacterType'
import styles from '@/components/post/Chat.module.css'
import { initializeIcons } from '@fluentui/react/lib/Icons'
initializeIcons(undefined, { disableWarnings: true })

const MicRecorder = require('mic-recorder-to-mp3')


interface TalkClientProps {
  companion: Companion & {
    messages: Message[];
    observations: Observations[];
    todos: Todos[];
    serviceInvitations: ServiceInvitation[];
    _count: {
      messages: number;
    }
  };
  foundPersona : Persona | null
};


export const TalkClient = ({
  companion, 
  foundPersona,
}: TalkClientProps) => {
  const router = useRouter()
  const proModal = useProModal()
  const { user } = useUser()
  const { toast } = useToast()
  const [image, setImage] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [task, setTask] = useState<string>('')
  const [apiInterface, setApiInterface] = useState<string>('/api/backend')
  const [route, setRoute] = useState<string>('chat_enn')
  const [prompt, setPrompt] = useState('')
  const [messages, setMessages] = useState<TalkMessageProps[]>(companion.messages);
  const [chatHistoryMessages, setChatHistoryMessages] = useState<MessageType[]>([])
  const [autoSubmit, setAutoSubmit] = useState(false)
  const [autoSpeak, setAutoSpeak] = useState(false)
  const [music, setMusic] = useState<string>()
  const [musicCaption, setMusicCaption] = useState<boolean>(false)
  const [speakContent, setSpeakContent] = useState('')
  const [useSuggest, setUseSuggest] = useState('')
  const [followupQuestion, setFollowupQuestion] = useState('')
  const [transcript, setTranscript] = useState('')
  const recorder = useRef<typeof MicRecorder>(null)
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [recording, setRecording] = useState(false)
  const { seconds, minutes, start, pause, reset } = useStopwatch({
    autoStart: false,
  })
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);
  const [approach, setApproach] = useState<Approaches>(Approaches.EnneagramTypeFull)
  const [promptTemplate, setPromptTemplate] = useState<string>('')
  const [promptTemplatePrefix, setPromptTemplatePrefix] = useState<string>('')
  const [promptTemplateSuffix, setPromptTemplateSuffix] = useState<string>(companion.seed)
  const [preemptTask, setPreemptTask] = useState<string>('')
  const [retrieveCount, setRetrieveCount] = useState<number>(5)
  const [useMaxTokens, setUseMaxTokens] = useState<number>(250)
  const [useWordLimit, setUseWordLimit] = useState<number>(10)
  const [chatModel, setChatModel] = useState<string>('gpt-4.1-mini')
  const [useTemperature, setUseTemperature] = useState<number>(0.7)
  const [useSemanticRanker, setUseSemanticRanker] = useState<boolean>(false)
  const [useSemanticCaptions, setUseSemanticCaptions] = useState<boolean>(false)
  const [excludeCategory, setExcludeCategory] = useState<string>('')
  const [useSuggestFollowupQuestions, setUseSuggestFollowupQuestions] = useState<boolean>(true)
  const [selectedChoice, setSelectedChoice] = useState('')
  const [questionWithOptions, setQuestionWithOptions] = useState<QuestionWithOptions | null>(null) 
  const [selectedOption, setSelectedOption] = useState<IChoiceGroupOption | undefined>(undefined)
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(20)
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [answers, setAnswers] = useState<any>(null)
  const [uploadedImageUrl, setUploadedImageUrl] = useState("");
  const [character, setCharacter] = useState<CharacterType>(Characters[0])
  const messageHandler = (chatHistoryMessage: MessageType) => {
    setChatHistoryMessages((chatHistoryMessages) => [...chatHistoryMessages, chatHistoryMessage])
  }

  const observations: Observations[] = companion ? companion.observations : [];
  const todos: Todos[] = companion ? companion.todos : [];


  const labels: string[] = [
    "Excited", "Delighted", "Happy", "Content", 
    "Relaxed", "Calm", "Tired", "Bored", "Depressed", 
    "Frustrated", "Angry", "Tense"]
  const backgroundColors = [
    'rgb(104, 159, 56, 0.9)',
    'rgb(255, 99, 132, 0.9)',
    'rgb(75, 192, 192, 0.9)',
    'rgb(255, 128, 171, 0.9)',
    'rgb(255, 205, 86, 0.9)',
    'rgb(54, 162, 235, 0.9)',
    'rgb(101, 31, 255, 0.9)',
    'rgb(158, 158, 158, 0.9)',
    'rgb(48, 79, 254, 0.9)',
    'rgb(0, 200, 83, 0.9)',
    'rgb(245, 0, 87, 0.9)',
    'rgb(244, 81, 30, 0.9)'
  ];
  const initialEmotionsData: PolarAreaChartType = {
    labels: labels,
    datasets: [
      {
        label: 'Emotions',
        data: [4, 5, 3, 4, 5, 3, 4, 3, 4, 5, 3, 5],
        fill: true,
        backgroundColor: backgroundColors, //'rgba(132, 255, 255, 0.4)', this is for Radar
        borderColor: 'rgb(132, 255, 255, 0.0)',
        pointBackgroundColor: 'rgb(132, 255, 255)',
        pointBorderColor: 'rgb(132, 255, 255, 0.0)',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(132, 255, 255, 0.2)'
      },
    ],
  }
  const [emotionsData, setEmotionsData] = useState<PolarAreaChartType>(initialEmotionsData)
  const [beforeData, setBeforeData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })
  const [previousData, setPreviousData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })
  const [currentData, setCurrentData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })

  let chatRuns = 0

  useEffect(() => {
    setUsePersona(foundPersona);
  }, [foundPersona ]);

  // Image upload
  const onImageUpload = (imageUrl: string) => {
    setUploadedImageUrl(imageUrl);
  };

  useEffect(() => {
    const uploadImageAndPrediction = async () => {
      if (uploadedImageUrl && user?.id) {
        setIsLoading(true);
        try {
          const body = JSON.stringify({ uploadedImageUrl });
          //const response = await fetch(`/api/image2text`, {
          const response = await fetch(`/api/sceneXplain`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body,
          })

          //const response_data = await response.json();
          //const response_data_text = response_data.text //HF
          const response_data_text = await response.text();
          console.log("response_data from image2text: ", response_data_text)


          // Update aiMessage
          const currentDate = new Date();
          const timeZone = 'Asia/Taipei';
          const utcDate = zonedTimeToUtc(currentDate, timeZone);
          const formattedDate = format(utcDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
          const createdAt = new Date(formattedDate);

          let aiMessage: TalkMessageProps = {
            role: "assistant",
            content: response_data_text,
            createdAt: createdAt,
          };
    
          setMessages((current) => [...current, aiMessage]);

          let sanitizedAnswer
          const talkStory: boolean = false
          if (talkStory) {
            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: response_data_text,
                role: "assistant",
              }
            );

            const callToActionTemplate: string =
            `You are a world class story teller,` +
            ` Your goal is to generate a short tiny story` +
            ` less than 100 words based on a story idea.` +
            `\nWhenever response, only use zh-tw` +
            `\nHere is the story idea: ${response_data_text}` +
            `\n\nStory: `;

            // Chain text2image respond to llm
            const question = response_data_text
            const messageQuestion = { type: 'question', text: question }
            messageHandler(messageQuestion)

            // Ask chatgpt questions and get answers
            const responseChatGPT = await axios.post(`/api/chatgpt`, {
              promptTemplate: callToActionTemplate,
              maxOutputTokens: 500,
              temperature: 0.8,
              question,
              chatHistoryMessages,
            })

            const answer = responseChatGPT?.data?.response

            if (answer) {
              sanitizedAnswer = DOMPurify.sanitize(parseAnswerToHtml(answer).answerHtml)
              setSpeakContent(sanitizedAnswer)            

              // Add to message list
              const messageAnswer = { type: 'answer', text: sanitizedAnswer }
              messageHandler(messageAnswer)
            }

            // Update the content of the existing aiMessage
            aiMessage = {
              ...aiMessage,
              content: sanitizedAnswer,
              createdAt: new Date(formattedDate),
            };
      
            setMessages((current) => [...current, aiMessage]);
          } else {
            sanitizedAnswer = response_data_text          
          }
          setIsLoading(false)

          // Insert data to database
          await axios.post(
            "/api/talk",
            {
              companionId: companion.id,
              content: sanitizedAnswer,
              role: "assistant",
            }
          );          

        } catch (error: any) {
          if (error?.response?.status === 403) {
            proModal.onOpen();
          } else {
            toast({
              variant: "destructive",
              description: "Something went wrong.",
              duration: 3000,
            });
            console.error(error)
          }
        } finally {
          setIsLoading(false)
          router.refresh()
        }
      }
    }

    uploadImageAndPrediction()
  }, [uploadedImageUrl])


  useEffect(() => {
    // Create instance
    recorder.current = new MicRecorder({ bitRate: 128 })
  }, [])

  // Start recording
  const startRecording = async () => {
    // stopwatch start
    reset()

    await recorder.current
      .start()
      .then(() => {
        setRecording(true)
      })
      .catch((error: string) => {
        console.error(error)
      })
  }

  // Stop recording
  const stopRecording = async () => {
    // stopwatch stop
    pause()
    // Stop recording
    await recorder.current
      .stop()
      .getMp3()
      .then(([buffer, blob]: any) => {
        // Sound file generation
        const file = new File(buffer, 'audio.mp3', {
          type: blob.type,
          lastModified: Date.now(),
        })

        setIsLoading(true)
        setAudioFile(file)
      })
      .catch((error: string) => {
        console.log(error)
        setIsLoading(false)
      })

    // Reset recording state
    setRecording(false)
  }

  useEffect(() => {
    const fn = async () => {
      try {
        if (audioFile) {
          // Send data
          const formData = new FormData()
          formData.append('file', audioFile)

          // Whisper API
          const response = await fetch(`/api/whisper`, {
            method: 'POST',
            body: formData,
          })
          const response_data = await response.json()

          // Voice recognition check
          if (response_data.transcript) {
            setTranscript(response_data.transcript)
          }
        }
      } catch (error: any) {
        if (error?.response?.status === 403) {
          proModal.onOpen();
        } else {
          toast({
            variant: "destructive",
            description: "Something went wrong.",
            duration: 3000,
          });
          console.error(error)
        }
      } finally {
        setIsLoading(false)
        setAudioFile(null)
      }
    }

    fn()
  }, [audioFile])

  useEffect(() => {
    if (autoSubmit && transcript) {
      onSubmit()
      //console.log ("Transcript received:", transcript)
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }, [transcript, autoSubmit])

  // AutoSpeak AI content
  useEffect(() => {
    if (autoSpeak && speakContent) {
      const fetchAudio = async () => {
        try {
          const responseAudio = await fetch('/api/text-to-speech', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ speakContent }),
          });

          if (responseAudio.ok) {
            // Audio playback logic...
          } else {
            console.error('Error: Failed to fetch audio');
          }
        } catch (error) {
          toast({
            variant: "destructive",
            description: "Something went wrong.",
            duration: 3000,
          });
          console.error('Error fetching audio:', error);
        }
      };

      fetchAudio();
    }
  }, [autoSpeak, speakContent])



  // Submit prompt to API
  const onSubmit = async () => {
    setIsLoading(true)
    setQuestionWithOptions(null)

    if (user?.id) {
      try {
        if (prompt || transcript || useSuggest || followupQuestion || selectedChoice) {
          const message = prompt || transcript || useSuggest || selectedChoice || followupQuestion
          const sanitizedMessage = DOMPurify.sanitize(message)

          if (!music || !musicCaption) {
            const currentDate = new Date()
            const timeZone = 'Asia/Taipei'
            const utcDate = zonedTimeToUtc(currentDate, timeZone)
        
            // Format the converted date using the imported 'format' function
            const formattedDate = format(utcDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
            const createdAt = new Date(formattedDate);
  
            // Construct User input message
            const userMessage: TalkMessageProps = {
              role: "user",
              content: sanitizedMessage,
              createdAt: createdAt,
              };
      
            setMessages((current) => [...current, userMessage]);

            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: sanitizedMessage,
                role: "user",
               }
            );
          }
    
          // Send data
          let sanitizedAnswer

          // Fetch Text to Music
          if (musicCaption) {
            try {
              setMusic(undefined)
              const response = await axios.post('/api/music', {
                userId: user.id,
                prompt: prompt
              })
              console.log(response.data.audio)
              setMusic(response.data.audio)
            } catch (error: any) {
              console.error('Error fetching music:', error)
            } finally {
              setIsLoading(false)
              router.refresh()
            }
          }

          if (apiInterface === '/api/chatgpt' && !musicCaption) {
            const question = sanitizedMessage
            const messageQuestion = { type: 'question', text: question }
            messageHandler(messageQuestion)

            // Ask chatgpt questions and get answers
            const responseChatGPT = await axios.post(apiInterface, {
              userId: user.id,
              promptTemplate: promptTemplate.length === 0 ? undefined : promptTemplate,
              maxOutputTokens: useMaxTokens,
              temperature: useTemperature,
              question,
              chatHistoryMessages,
            })

            const answer = responseChatGPT?.data?.response

            if (answer) {
              sanitizedAnswer = DOMPurify.sanitize(parseAnswerToHtml(answer).answerHtml)
              setSpeakContent(sanitizedAnswer)
              //console.log("sanitizedAnswer: ", sanitizedAnswer)

              // Add to message list
              const messageAnswer = { type: 'answer', text: sanitizedAnswer }
              messageHandler(messageAnswer)

              const currentDate = new Date();
              const timeZone = 'Asia/Taipei';
              const utcDate = zonedTimeToUtc(currentDate, timeZone);
          
              // Format the converted date using the imported 'format' function
              const formattedDate = format(utcDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
              const createdAt = new Date(formattedDate);

              // Construct AI respond message
              const aiMessage: TalkMessageProps = {
                role: "assistant",
                content: sanitizedAnswer,
                createdAt: createdAt,
              };
        
              setMessages((current) => [...current, aiMessage]);              
            }
          }
          setIsLoading(false)

              
          //const apiInterface = '/api/backend'
          if (apiInterface === '/api/backend' && !musicCaption) {
            const request: ChatRequest = {
                route: route,
                userId: user.id,
                approach: approach,
                //approach: Approaches.EnneagramType3,
                task: task,
                prompt: message,
                overrides: {
                    promptTemplate: promptTemplate.length === 0 ? undefined : promptTemplate,
                    promptTemplatePrefix: promptTemplatePrefix.length === 0 ? undefined : promptTemplatePrefix,
                    promptTemplateSuffix: promptTemplateSuffix.length === 0 ? undefined : promptTemplateSuffix,
                    preemptTask: preemptTask.length === 0 ? undefined : preemptTask,
                    excludeCategory: excludeCategory.length === 0 ? undefined : excludeCategory,
                    chatModel: chatModel,
                    top: retrieveCount,
                    wordLimit: useWordLimit,
                    temperature: useTemperature,
                    semanticCaptions: useSemanticCaptions,
                    suggestFollowupQuestions: useSuggestFollowupQuestions,
                    reflectionThreshold: useReflectionThreshold
                },
                persona: {
                    name: usePersona?.name,
                    age: usePersona?.age,
                    traits: usePersona?.traits,
                    status: usePersona?.status,
                }
            }

            console.log("request_data: ", request)
            const response_data = await chatApi(apiInterface, request)
            console.log("response_data: ", response_data)

            
            if (response_data && response_data.text && response_data.text.answer) {
              setAnswers(response_data.text)
              // Update Post data
              sanitizedAnswer = DOMPurify.sanitize(
                parseAnswerToHtml(response_data.text.answer).answerHtml
              )
              setSpeakContent(sanitizedAnswer)

              const currentDate = new Date();
              const timeZone = 'Asia/Taipei';
              const utcDate = zonedTimeToUtc(currentDate, timeZone);
          
              // Format the converted date using the imported 'format' function
              const formattedDate = format(utcDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx");
              const createdAt = new Date(formattedDate);

              // Construct AI respond message
              const aiMessage: TalkMessageProps = {
                role: "assistant",
                content: sanitizedAnswer,
                createdAt: createdAt,
              };
        
              setMessages((current) => [...current, aiMessage]);

              //console.log("sanitizedAnswer: ", sanitizedAnswer)
              //console.log("messages: ", messages)
              setIsLoading(false)
              router.refresh()
            }
              


            if (response_data && response_data.text.user_status_sum) { 
              // Insert into the Observations table
              const sanitizedUserStatusSum = DOMPurify.sanitize(
                response_data.text.user_status_sum
              );
              //console.log("sanitizedUserStatusSum: ", sanitizedUserStatusSum)

              try {
                const observations: Observations = await axios.post(
                  "/api/observation",
                  {
                    companionId: companion.id,
                    title: sanitizedUserStatusSum,
                    message: sanitizedUserStatusSum,
                    roomId: 'user_status_sum'
                  }
                );
              } catch (error) {
                toast({
                  variant: "destructive",
                  description: "Something went wrong.",
                  duration: 3000,
                });
              }
            }


            // Update User SentimentData
            if (response_data && response_data.text.user_sentiment) {
              const updateSentimentData = async() => {
                const sentimentResult = response_data.text.user_sentiment;
                await setBeforeData(previousData)

                const newData: SentimentData = {
                  emotions: labels.map((emotion) => {
                    const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion)
                    return emotionIndex !== -1 ? emotion : emotion;
                  }),
                  stars: labels.map((emotion) => {
                    const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
                    return emotionIndex !== -1 ? (sentimentResult?.stars ?? [])[emotionIndex] : 0
                  })
                }
                await setPreviousData(newData)

                const updateEmotions = async() => {
                  //const newEmotionsData: RadarChartType = updateRadarChart(beforeData, previousData, newData)
                  const newEmotionsData: PolarAreaChartType = updatePolarAreaChart(newData)
                  await setEmotionsData(newEmotionsData)
                }
                updateEmotions()
              }
              updateSentimentData() // Call the function to update the Sentiment data
              //console.log("setBeforeData:  ", beforeData)
              //console.log("setPreviousData:  ", previousData)
              //console.log("newData:  ", newData)
              //console.log("EmotionsData: ", emotionsData)
            }

            // Fetch options from the API and update the dynamicOptions state
            if (response_data?.text?.user_choiceOptions) {
              const fetchDataFromAPI = async () => {
                const receivedOptionsData = response_data.text.user_choiceOptions;
                //console.log("receivedOptionsData:", receivedOptionsData);

                if (receivedOptionsData && receivedOptionsData.length > 0) {
                  const questionWithOptions: QuestionWithOptions = receivedOptionsData[0]
                  //console.log("questionWithOptions:", questionWithOptions)
                  setQuestionWithOptions(questionWithOptions);
                }
              };
              fetchDataFromAPI(); // Call the API to fetch options
              setFollowupQuestion('') // Clear followupQuestions
            }

            if (response_data && response_data.text.user_todos) {
              const todoList = response_data.text.user_todos
              try {
                // Loop through the todo_list and insert each task into the todos table
                for (const taskText of todoList) {
                  const sanitizedTaskText = DOMPurify.sanitize(taskText);
                  await axios.post(
                    "/api/todo",
                    {
                      task: sanitizedTaskText,
                      companionId: companion.id,
                    }
                  );
                }
              } catch (error) {
                toast({
                  variant: "destructive",
                  description: "Something went wrong.",
                  duration: 3000,
                });
              }
            }

          }


          // Update to database
          if (sanitizedMessage && sanitizedAnswer && !music) {
            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: sanitizedAnswer,
                role: "assistant",
               }
            );
          }
          setIsLoading(false) // To release UI isLoading for better user experience.
          
          if (sanitizedAnswer && chatRuns % (useReflectionThreshold + 3) === 9) {
            try {
              const userMessages = messages.filter((message) => message.role === "user")
              const sentiment: { data: SentimentData } = await axios.post('/api/sentiment', {
                userMessages,
                useReflectionThreshold,
              })

              /*
              const TestsentimentResult = {
                "emotions": [
                    "Tired",
                    "Angry"
                ],
                "stars": [
                    5,
                    5
                ]
              }
              */

              const sentimentResult = sentiment.data
              //console.log("sentimentResult: ", sentimentResult)

              const updateSentimentData = async() => {
                //await setBeforeData(previousData)
  
                const newData: SentimentData = {
                  emotions: labels.map((emotion) => {
                    const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion)
                    return emotionIndex !== -1 ? emotion : emotion;
                  }),
                  stars: labels.map((emotion) => {
                    const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
                    return emotionIndex !== -1 ? (sentimentResult?.stars ?? [])[emotionIndex] : 0
                  })
                }
                //await setPreviousData(newData)
                //console.log("newData", newData)
  
                const updateEmotions = async() => {
                  //const newEmotionsData: RadarChartType = updateRadarChart(beforeData, previousData, newData)
                  const newEmotionsData: PolarAreaChartType = updatePolarAreaChart(newData)
                  await setEmotionsData(newEmotionsData)
                }
                updateEmotions()
              }
              updateSentimentData()
              
            } catch (error) {
              toast({
                variant: "destructive",
                description: "Something went wrong.",
                duration: 3000,
              });
              console.error("Error in sentiment analysis:", error);
            }
          }
        chatRuns = chatRuns + 1
        }
      } catch (error: any) {
        if (error?.response?.status === 403) {
          proModal.onOpen();
        } else {
          toast({
            variant: "destructive",
            description: "Something went wrong.",
            duration: 3000,
          });
          console.error(error)
        }
      } finally {
        setPrompt('')
        setTranscript('')
        setUseSuggest('')
        setFollowupQuestion('')
        setSelectedChoice('')
        router.refresh()        
      }
    }
    setSpeakContent('')
    setIsLoading(false)
  }

  // Prompt configurations
  const onPromptTemplateChange = (_ev?: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string) => {
      setPromptTemplate(newValue || "");
  };

  const onPromptTemplatePrefixChange = (_ev?: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string) => {
      setPromptTemplatePrefix(newValue || "");
  };

  const onPromptTemplateSuffixChange = (_ev?: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string) => {
      setPromptTemplateSuffix(newValue || "");
  };

  const onPromptTaskChange = (_ev?: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string) => {
      setPreemptTask(newValue || "");
  };

  const onRetrieveCountChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
      setRetrieveCount(parseInt(newValue || "5"));
  };

  const onUseMaxTokensChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
      setUseMaxTokens(parseInt(newValue || "250"));
  };

  const onUseWordLimitChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
      setUseWordLimit(parseInt(newValue || "10"));
  };

  const chatModelOptions = [
    { key: 'gpt-4.1-mini', text: 'GPT-3.5' },
    //{ key: 'gpt-4', text: 'GPT-4' }
  ]
  const onChatModelChange = (_event: React.FormEvent<HTMLDivElement>, item?: IDropdownOption): void => {
    if (item) {
      setChatModel(item.key.toString())
    }
  }

  const onUseTemperatureChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
    if (newValue !== undefined) {
      const parsedValue = parseFloat(newValue)
      const clampedValue = Math.min(Math.max(parsedValue, 0.0), 1.0)
      setUseTemperature(clampedValue)
    }
  }

  const onUseReflectionThresholdChange = (_ev?: React.SyntheticEvent<HTMLElement, Event>, newValue?: string) => {
      setUseReflectionThreshold(parseInt(newValue || "20"));
  }
   
  const onUseSemanticCaptionsChange = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, checked?: boolean) => {
      setUseSemanticCaptions(!!checked);
  }
   
  const onApproachChange = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, option?: IDropdownOption ) => {
      setApproach((option?.key as Approaches) || Approaches.EnneagramTypeFull);
  }

  const onUseSuggestFollowupQuestionsChange = (_ev?: React.FormEvent<HTMLElement | HTMLInputElement>, checked?: boolean) => {
      setUseSuggestFollowupQuestions(!!checked);
  }

  const onExcludeCategoryChanged = (_ev?: React.FormEvent, newValue?: string) => {
      setExcludeCategory(newValue || "");
  }

  const approaches: IDropdownOption [] = [
      {
          key: Approaches.Chat,
          text: "Free Chat"
      },
      {
          key: Approaches.Conversation,
          text: "Conversation"
      },
      {
          key: Approaches.EnneagramType2,
          text: "Enneagram-Type2"
      },
      {
          key: Approaches.EnneagramType3,
          text: "Enneagram-Type3"
      },
      {
          key: Approaches.EnneagramType4,
          text: "Enneagram-Type4"
      },
      {
          key: Approaches.EnneagramType5,
          text: "Enneagram-Type5"
      },
      {
          key: Approaches.EnneagramType7,
          text: "Enneagram-Type7"
      },
      {
          key: Approaches.EnneagramTypeFull,
          text: "Enneagram-TypeFull"
      },
  ]

  // Press Enter to submbit、Shift+Enter to next line
  const enterPress: React.KeyboardEventHandler<HTMLTextAreaElement> =
  (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key == 'Enter' && e.shiftKey == false) {
      e.preventDefault()
      onSubmit()
    }
  }

  const handleClick: React.MouseEventHandler<HTMLButtonElement> =
  (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault()
      onSubmit()
  }


  useEffect(() => {
    if (useSuggest !== '') {
      onSubmit()
    }
  }, [useSuggest])
 
  const handleSuggest = async (suggestion: PromptSuggestType) => {
    const { id, title, message, approach, task, route, apiInterface } = suggestion;
    const approachType = approach as Approaches
    await setApiInterface(suggestion.apiInterface)
    await setRoute(suggestion.route)
    await setApproach(approachType)
    //console.log("setApproach: ", approachType)
    if (task !== '') {
      await setTask(suggestion.task)
    }
    await setUseSuggest(suggestion.message)
    //await deletePromptSuggest(suggestion.id);
  }
  
  const onSuggestionClicked = async (suggestion: PromptSuggestType, callback: () => void) => {
    if (!isLoading) {
      //<SuggestionList onSuggestionClicked={(suggestion) => setPrompt(Suggestion)} />
      //<SuggestionList onSuggestionClicked={onSuggestionClicked} />
      //console.log("Suggestion.id clicked: ", suggestion.id) 
      console.log("Suggestion.value clicked: ", suggestion.message) 
      await handleSuggest(suggestion)
      callback()
    }
  }

  const onFollowupQuestionClicked = async (question: string) => {
      await setFollowupQuestion(question)
      onSubmit()
  }


  return (
    <div className="flex max-w-screen max-h-screen overflow-hidden">
      {/* Left Panel */}
      <div className="hidden  h-[100vh] lg:block md:flex w-[45%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">          
        <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
          <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
            <div className="pt-2">
              <ObservationModal companionId={companion.id} userId={user?.id!} path={`/talk/${companion.id}`} />
            </div>
            <div className="pt-2">
              My Observations
            </div>
          </div>
          <div className="mt-0">
            <ObservationNew observations={observations} />
          </div>
        </div>
        <div className="flex flex-col h-[35%] w-full px-2 justify-center items-center overflow-overlay">
          <PolarAreaChart data={emotionsData} />
        </div>
      </div>    
      {/* Center Panel */}
      <div className="flex flex-col h-[96vh] w-full max-h-screen px-4 space-y-2">
        <SettingsButton className={styles.commandsButton} onClick={() => setIsConfigPanelOpen(!isConfigPanelOpen)} />
        <ChatHeader companion={companion} />
        <div className="flex-grow overflow-y-auto">
          <TalkMessages 
            companion={companion}
            isLoading={isLoading}
            messages={messages}
          />
        </div>
        <div className="flex flex-col items-center justify-center">
          {music && (
            <audio controls className="w-[22rem] h-[2.3rem]">
              <source src={music} />
            </audio>
          )}
        </div>
        <div className="w-full h-[10%] rounded-3xl flex-cols bg-transparent max-w-[100%] sticky bottom-0">        
          <Answer
            text={answers ?? { answer: '', followupQuestions: [] }}
            onFollowupQuestionClicked={(question) => onFollowupQuestionClicked(question)}
            showFollowupQuestions={useSuggestFollowupQuestions}
          />
          <ChatForm
            onImageUpload={onImageUpload}
            setPrompt={setPrompt}
            handleClick={handleClick}
            startRecording={startRecording}
            stopRecording={stopRecording}
            isLoading={isLoading}
            prompt={prompt}
            transcript={transcript}
            recording={recording}
            minutes={minutes}
            seconds={seconds}
            musicCaption={musicCaption}
            setMusicCaption={setMusicCaption}
            autoSubmit={autoSubmit}
            setAutoSubmit={setAutoSubmit}
            autoSpeak={autoSpeak}
            setAutoSpeak={setAutoSpeak}
            enterPress={enterPress}
          />
          <Panel
              headerText="Customize Prompt"
              isOpen={isConfigPanelOpen}
              hasCloseButton
              isBlocking={false}
              onDismiss={() => setIsConfigPanelOpen(false)}
              closeButtonAriaLabel="Close"
              isFooterAtBottom={true}
          >
          <Dropdown
            className={styles.chatSettingsSeparator}
            label="Approach"
            options={approaches}
            selectedKey={approach} // Use selectedKey instead of defaultSelectedKey
            onChange={(_event, option) => {
              if (option) {
                const approachKey = option.key as Approaches;
                console.log('Selected option:', option);
                console.log('approachKey :', approachKey);

                // Set the approach state based on the selected option
                setApproach(approachKey);

                // Optionally, update other state variables based on the approachKey
                let newRoute = '';
                let newApiInterface = '';
                let newTask = '';

                switch (approachKey) {
                  case Approaches.Chat:
                    newApiInterface = '/api/chatgpt';
                    break;
                  case Approaches.Conversation:
                    newRoute = 'conversation';
                    newApiInterface = '/api/backend';
                    newTask = '';
                    break;
                  case Approaches.EnneagramType1:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask = 'Encourage me to be responsible and follow the rules.';
                    break;          
                  case Approaches.EnneagramType2:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask = 'Make connections with me and encourage me to share.';
                    break;
                  case Approaches.EnneagramType3:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Help me develop at least 3 action plans or goals that are achievable for better life changing and achievement.';
                    break;
                  case Approaches.EnneagramType4:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Help me figure out what my feelings and emotion is to my issue and why they come out.';
                    break;
                  case Approaches.EnneagramType5:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'You demonstrate a deep understanding of knowledge, engage in rational discussions, encourage exploration, and patiently support my intellectual pursuits.';
                    break;
                  case Approaches.EnneagramType6:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Help me foresee potential problems and plan a prevention strategy in place.';
                    break;
                  case Approaches.EnneagramType7:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Uplift me by encouraging me to see the positive and develop multiple actions to be happier.';
                    break;
                  case Approaches.EnneagramType8:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Build my self-confidence to control and construct a better life.';
                    break;
                  case Approaches.EnneagramType9:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Create a harmonious and peaceful atmosphere for me.';
                    break;
                  case Approaches.EnneagramTypeFull:
                    newRoute = 'chat_enn';
                    newApiInterface = '/api/backend';
                    newTask =
                      'Make connections with me and encourage me to share.';
                    break;          
                  default:
                    break;
                }

                // Update other state variables as needed
                setRoute(newRoute);
                setApiInterface(newApiInterface);
                setTask(newTask);
              }
              console.log('setApproach:', approach);
            }}
          />

              {(approach === Approaches.ReadRetrieveRead || approach === Approaches.EnneagramType2 || approach === Approaches.EnneagramType3 || approach === Approaches.EnneagramType4 || approach === Approaches.EnneagramType5 || approach === Approaches.EnneagramType7 || approach === Approaches.EnneagramTypeFull || approach === Approaches.Conversation || approach === Approaches.Chat) && (
                  <TextField
                      className={styles.chatSettingsSeparator}
                      defaultValue={promptTemplate}
                      label="Override prompt template"
                      multiline
                      autoAdjustHeight
                      onChange={onPromptTemplateChange}
                  />
              )}

              {(approach === Approaches.ReadRetrieveRead || approach === Approaches.Conversation) && (
                  <>
                      <TextField
                          className={styles.chatSettingsSeparator}
                          defaultValue={promptTemplatePrefix}
                          label="Override prompt prefix template"
                          multiline
                          autoAdjustHeight
                          onChange={onPromptTemplatePrefixChange}
                      />
                      <TextField
                          className={styles.chatSettingsSeparator}
                          defaultValue={promptTemplateSuffix}
                          label="Override prompt suffix template"
                          multiline
                          autoAdjustHeight
                          onChange={onPromptTemplateSuffixChange}
                      />
                  </>
              )}

              {(approach === Approaches.EnneagramType2 || approach === Approaches.EnneagramType3 || approach === Approaches.EnneagramType4 || approach === Approaches.EnneagramType5 || approach === Approaches.EnneagramType7 || approach === Approaches.EnneagramTypeFull || approach === Approaches.Conversation) && (
                  <TextField
                      className={styles.chatSettingsSeparator}
                      defaultValue={preemptTask}
                      label="Override prompt task"
                      multiline
                      autoAdjustHeight
                      onChange={onPromptTaskChange}
                  />
              )}
              {(approach === Approaches.Chat) && (
                  <SpinButton
                      className={styles.chatSettingsSeparator}
                      label="Max Tokens:"
                      min={20}
                      max={1500}
                      defaultValue={useMaxTokens.toString()}
                      step={5}
                      onChange={onUseMaxTokensChange}
                  />
              )}
              {(approach === Approaches.EnneagramType2 || approach === Approaches.EnneagramType3 || approach === Approaches.EnneagramType4 || approach === Approaches.EnneagramType5 || approach === Approaches.EnneagramType7 || approach === Approaches.EnneagramTypeFull || approach === Approaches.Conversation) && (
                  <SpinButton
                      className={styles.chatSettingsSeparator}
                      label="Word Limits:"
                      min={10}
                      max={1500}
                      defaultValue={useWordLimit.toString()}
                      step={5}
                      onChange={onUseWordLimitChange}
                  />
              )}
              <Dropdown
                className={styles.chatSettingsSeparator}
                label="Chat Model"
                options={chatModelOptions}
                defaultSelectedKey={chatModel}
                onChange={onChatModelChange}
              />
              <SpinButton
                  className={styles.chatSettingsSeparator}
                  label="Temperature"
                  min={0.0}
                  max={1.0}
                  defaultValue={useTemperature.toString()}
                  step={0.1}
                  onChange={onUseTemperatureChange}
              />
              {(approach === Approaches.EnneagramType2 || approach === Approaches.EnneagramType3 || approach === Approaches.EnneagramType4 || approach === Approaches.EnneagramType5 || approach === Approaches.EnneagramType7 || approach === Approaches.EnneagramTypeFull) && (
                <SpinButton
                    className={styles.chatSettingsSeparator}
                    label="Reflection Threshold"
                    min={8}
                    max={50}
                    defaultValue={useReflectionThreshold.toString()}
                    step={1}
                    onChange={onUseReflectionThresholdChange}
                />
              )}
              {(approach === Approaches.ReadRetrieveRead || approach === Approaches.Conversation) && (
                  <SpinButton
                      className={styles.chatSettingsSeparator}
                      label="Retrieve num of similarity from search"
                      min={5}
                      max={50}
                      defaultValue={retrieveCount.toString()}
                      onChange={onRetrieveCountChange}
                  />
              )}
              {(approach === Approaches.ReadRetrieveRead || approach === Approaches.Conversation) && (
                  <TextField 
                      className={styles.chatSettingsSeparator}
                      label="Exclude keywords"
                      onChange={onExcludeCategoryChanged}
                  />
              )}
              {(approach === Approaches.ReadRetrieveRead || approach === Approaches.Conversation) && (
                  <Checkbox
                      className={styles.chatSettingsSeparator}
                      checked={useSemanticCaptions}
                      label="Use query-contextual summaries instead of whole documents"
                      onChange={onUseSemanticCaptionsChange}
                      disabled={!useSemanticRanker}
                  />
              )}
              {(approach === Approaches.EnneagramType2 || approach === Approaches.EnneagramType3 || approach === Approaches.EnneagramType4 || approach === Approaches.EnneagramType5 || approach === Approaches.EnneagramType7 || approach === Approaches.EnneagramTypeFull) && (
                <Checkbox
                    className={styles.chatSettingsSeparator}
                    checked={useSuggestFollowupQuestions}
                    label="Suggest follow-up questions"
                    onChange={onUseSuggestFollowupQuestionsChange}
                />
              )}
          </Panel>
          {questionWithOptions && (
            <DynamicChoiceGroup questionWithOptions={questionWithOptions} setSelectedChoice={setSelectedChoice} onSubmit={onSubmit} />
          )}
        </div>
      </div>

      {/* Right Panel */}
      <div className="hidden lg:block w-[45%] flex-grow pt-[2.75rem] px-1 overflow-hidden">
        <div className="flex flex-col pl-2 justify-center">
        {todos.length > 0 && (
          <TodoList companionId={companion.id} todosData={todos ?? []}/>
        )}
        <CountdownTimer companion={companion} />
        {uploadedImageUrl && (
          <div>
            <img src={uploadedImageUrl} alt="Uploaded" />
          </div>
        )}
        {isLoading && (
          <div className="flex flex-col px-2 justify-center">
          <Character character={character} />
          </div>
        )}  
        </div>
      </div>
    </div>
  )
}