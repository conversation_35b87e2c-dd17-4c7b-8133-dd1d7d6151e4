"use client";

import { useCompletion } from "ai/react";
import { FormEvent, useState } from "react";
import { Companion, Message, Observations, Todos } from "@prisma/client";
import { useRouter } from "next/navigation";

import { ChatForm } from "@/components/chat-form";
import { ChatHeader } from "@/components/chat-header";
import { ChatMessages } from "@/components/chat-messages";
import { ChatMessageProps } from "@/components/chat-item";
import Character from '@/components/post/character'
import ObservationNew from '@/components/observation/observation-new';
import TodoList from '@/components/suggestion/TodoList'
import { Characters } from '@/components/post/config'
import type { CharacterType } from '@/lib/CharacterType'



interface TalkClientProps {
  companion: Companion & {
    messages: Message[];
    observations: Observations[];
    todos: Todos[];
    _count: {
      messages: number;
    }
  };
};

export const TalkClient = ({
  companion,
}: TalkClientProps) => {
  const router = useRouter();
  const [messages, setMessages] = useState<ChatMessageProps[]>(companion.messages);
  const [character, setCharacter] = useState<CharacterType>(Characters[0]);

  const observations: Observations[] = companion ? companion.observations : [];
  const todos: Todos[] = companion ? companion.todos : [];
  const {
    input,
    isLoading,
    handleInputChange,
    handleSubmit,
    setInput,
  } = useCompletion({
    api: `/api/talk/${companion.id}`,
    onFinish(_prompt, completion) {
      const aiMessage: ChatMessageProps = {
        role: "assistant",
        content: completion
      };

      setMessages((current) => [...current, aiMessage]);
      setInput("");

      router.refresh();
    },
  });

  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    const userMessage: ChatMessageProps = {
      role: "user",
      content: input
    };

    setMessages((current) => [...current, userMessage]);

    handleSubmit(e);
    
  }

  

  return (
      <div className="flex max-w-screen min-h-screen max-h-screen">
        {/* Left Panel */}
        <div className="hidden lg:block md:flex w-[45%] flex-grow items-start pt-[2.75rem] px-1 overflow-y-auto division">
          <div className="h-[60%] flex flex-col pr-2 justify-center overflow-y-auto">
            {/* <ObservationNew observations={observations} /> */}
            {observations.length > 0 && (
              <ObservationNew observations={observations} />
            )}
          </div>
          <div>
          </div>
        </div>
        
        {/* Center Panel */}
        <div className="flex flex-col h-auto w-[100%] p-4 space-y-2">
          <ChatHeader companion={companion} />
          <div className="flex-grow overflow-y-auto">
            <ChatMessages 
              companion={companion}
              isLoading={isLoading}
              messages={messages}
            />
          </div>
          <ChatForm 
            isLoading={isLoading} 
            input={input} 
            handleInputChange={handleInputChange} 
            onSubmit={onSubmit} 
          />
        </div>

        {/* Right Panel */}
        <div className="hidden lg:block w-[45%] flex-grow pt-[2.75rem] px-1 overflow-y-auto division">
          <div className="flex flex-col pl-2 justify-center">
          {todos.length > 0 && (
            <TodoList companionId={companion.id} todosData={todos ?? []}/>
          )}
          {isLoading && <Character character={character} />}
          </div>
        </div>
      </div>
   );
}