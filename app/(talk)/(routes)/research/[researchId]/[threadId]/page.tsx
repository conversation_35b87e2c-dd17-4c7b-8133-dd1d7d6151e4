import { auth } from "@clerk/nextjs/server";
import { getThread } from "@/ai/utils";
import { getThread as getThreadActions } from "@/app/actions/threadActions"
import { getUserMemories } from "@/ai/memory_service/actions";
import { getOrganizationsView } from "@/app/actions/getOrganizations";
import { v4 as uuidv4 } from 'uuid';
import ChatInterface from "@/components/langgraph/ChatInterface";

export interface CareerPageProps {
  params: Promise<{
    threadId: string
    researchId: string
  }>
}

export default async function ResearchPage(props: CareerPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const nameSpace = ["thread_summary", "user_state"]
  const [existingThread, organizations, threadData, userMemories] = await Promise.all([
    getThreadActions(userId, params.researchId, params.threadId),
    getOrganizationsView({ adminId: userId }),
    getThread(params.threadId),
    getUserMemories(userId, params.researchId, nameSpace, true)
  ]);  
  // Destructure the thread data with defaults
  const { messages = [], summary = "", memories, deep_research, research_topic, running_summary } = threadData ?? {};
  
  // Construct research messages array
  const research_messages = [];
  if (research_topic) {
    research_messages.push({
      id: uuidv4(),
      text: research_topic,
      sender: "human",
    });
  }
  if (running_summary) {
    research_messages.push({
      id: uuidv4(),
      text: running_summary,
      sender: "ai",
    });
  }
  console.log("research messages ==================>", JSON.stringify(research_messages));
  //const memoryContents = userMemories?.map((memory) => memory?.content) ?? [];
  //console.log("user memories @ page ==================>", JSON.stringify(userMemories));

  const path = `/research/${params.researchId}`;

  return (
    <div className="h-screen">
      <ChatInterface 
        existingThread={existingThread}
        assistantId={params.researchId} 
        threadId={params.threadId} 
        thread={messages.length > 0 ? messages : research_messages} 
        summary={summary}
        deepResearch={deep_research}     
        memories={userMemories}
        organizations={organizations || null}
        path={path}
      />
    </div>
  );
}
