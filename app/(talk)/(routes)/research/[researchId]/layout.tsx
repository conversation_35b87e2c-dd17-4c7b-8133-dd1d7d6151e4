import { MemoryAI } from '@/ai/memoryAI'
import HistoryContainer from '@/components/langgraph/history-container'

export interface ResearchLayoutProps {
  params: Promise<{ 
    researchId: string
  }>
}

const RootLayout = async (
  props: ResearchLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  return (
    <MemoryAI>
      <HistoryContainer companionId={params.researchId} path={`/research/${params.researchId}`} location="header" />
      {props.children}
    </MemoryAI>
   );
}
 
export default RootLayout;