import { redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { v4 as uuidv4 } from 'uuid'
import { getUserMemories } from "@/ai/memory_service/actions";
import { type UserMemory } from '@/components/langgraph/UserMemories';
import { 
  type OrganizationView, 
  getOrganizationsView 
} from "@/app/actions/getOrganizations";
import ChatInterface from "@/components/langgraph/ChatInterface";


export interface ResearchIdPageProps {
  params: Promise<{
    researchId: string
  }>
}

export default async function ResearchIdPage(props: ResearchIdPageProps) {
  const params = await props.params;
  const threadId: string = uuidv4()
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
    return redirectToSignIn();
  }

  const organizations: OrganizationView[] = await getOrganizationsView({ adminId: userId });

  const nameSpace = ["thread_summary", "user_state"]
  const userMemories = await getUserMemories(userId, params.researchId, nameSpace, true);
  const path = `/research/${params.researchId}`;

  return (
    <div className="h-screen">
      <ChatInterface
        assistantId={params.researchId} 
        threadId={threadId} 
        thread={[]} 
        summary={""}
        memories={userMemories || []}
        organizations={organizations || null}
        path={path}
      />
    </div>
  );
}