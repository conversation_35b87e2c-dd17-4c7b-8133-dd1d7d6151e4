import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { CheersClient } from "../_components/client";
import { getCompanionById, getThread } from "@/app/actions/threadActions"
import { getNotes } from "@/app/actions/noteActions"

//export const runtime = 'edge'
//export const revalidate = 0
export const preferredRegion = ['sfo1']
//export const experimental_ppr = true

export interface CheersPageProps {
  params: Promise<{
    threadId: string
    cheersId: string
  }>
}

type ThreadInfo = { id: string; name: string } | null;


export default async function CheersPage(props: CheersPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
    return redirectToSignIn();
    // redirect(`/sign-in?next=/cheers/${params.cheersId}/${params.threadId}`)
  }

  const existingThread: ThreadInfo = await getThread(
    userId, params.cheersId, params.threadId
  );

  //const companion = await getCompanionById(userId, params.cheersId, params.threadId)

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.cheersId
    },
    //cacheStrategy: {
    //  swr: 0,
    //  ttl: 0
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
          threadId: params.threadId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      chatSummaries: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
          threadId: params.threadId,
        },
        take: 5,
      },
      todos: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });


  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  return <CheersClient companion={companion} foundPersona={foundPersona} threadId={params.threadId} existingThread={existingThread} notes={notes} />
}