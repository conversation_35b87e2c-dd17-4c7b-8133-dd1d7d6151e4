import { SidebarHeader } from '@/components/talk/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
//export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
//export const revalidate = 0

export interface CheersLayoutProps {
  params: Promise<{ 
    cheersId: string
  }>
}

const CheersLayout = async (
  props: CheersLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/cheers/${params.cheersId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.cheersId} path={path} />
      {children}
    </div>
  );
}

export default CheersLayout;