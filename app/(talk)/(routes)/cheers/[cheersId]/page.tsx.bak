import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server"
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { CheersClient } from "./_components/client";

//export const runtime = 'edge'


export interface CheersPageProps {
  params: Promise<{ 
    cheersId: string
  }>
}

export default async function CheersPage({ params }: CheersPageProps) {
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
      return redirectToSignIn();
    }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.cheersId
    },
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      todos: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

  if (!companion) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  })

  if (!foundPersona) {
    return <div>Profile not found.</div>;
  }

  return <CheersClient companion={companion} foundPersona={foundPersona} />
}