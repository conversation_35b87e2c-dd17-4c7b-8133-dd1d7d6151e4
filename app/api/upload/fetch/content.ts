import { unified } from "unified";
import parse from "rehype-parse";
import rehype2remark from "rehype-remark";
import stringify from "remark-stringify";
import axios from "axios";
import pdf from "pdf-parse";
import { remove } from "unist-util-remove";
import { UnstructuredClient } from "unstructured-client";
import { URLDetailContent } from "@/app/client/fetch/url";
import { Document } from "@langchain/core/documents";
import { UnstructuredLoader } from "@langchain/community/document_loaders/fs/unstructured";
import { PartitionParameters } from "unstructured-client/sdk/models/shared";
import { PartitionRequest, PartitionResponse } from "unstructured-client/sdk/models/operations";
import { writeFile, unlink } from "fs/promises";
import * as fs from "fs";

type Element = {
  type: string;
  text: string;
  // this is purposefully loosely typed
  metadata: {
    [key: string]: unknown;
  };
};


function dataCategory(elements: Element[]): [string[], string[]] {
  const tables: string[] = [];
  const texts: string[] = [];
  for (const element of elements) {
    if (element.type === "Table") {
      //const combinedText = element.text + '\n' + element.metadata.text_as_html;
      tables.push(element.text);
      //tables.push(combinedText);
    } else if (element.type === "CompositeElement") {
      texts.push(element.text);
    }
  }
  return [texts, tables];
}

function removeCommentsAndTables() {
  return (tree: any) => {
    remove(tree, { type: "comment" });
    remove(tree, { tagName: "table" });
  };
}

async function htmlToMarkdown(html: string): Promise<string> {
  const processor = unified()
    .use(parse) // Parse the HTML
    .use(removeCommentsAndTables) // Remove comment nodes
    .use(rehype2remark as any) // Convert it to Markdown
    .use(stringify); // Stringify the Markdown

  const file = await processor.process(html);
  return String(file);
}

export async function fetchContentFromURL(
  url: string,
): Promise<URLDetailContent> {
  const response = await fetch(url);

  if (!response.ok) {
    throw new Error("Failure fetching content from provided URL");
  }

  const contentType = response.headers.get("content-type") || "";

  if (contentType.includes("text/html")) {
    const htmlContent = await response.text();
    const markdownContent = await htmlToMarkdown(htmlContent);
    return {
      url,
      content: markdownContent,
      size: htmlContent.length,
      type: "text/html",
    };
  }

  if (contentType.includes("application/pdf")) {
    const response = await axios.get(url, {
      responseType: "arraybuffer",
    });
    const pdfBuffer = response.data;
    const pdfData = await pdf(pdfBuffer);
    const result = {
      url,
      content: pdfData.text,
      size: pdfData.text.length,
      type: "application/pdf",
    } as URLDetailContent;
    return result;
  }

  throw new Error("URL provided is not a PDF or HTML document");
}

export const getPDFContentFromBuffer = async (pdfBuffer: Buffer) => {
  const data = await pdf(pdfBuffer);
  const content = data.text;
  const size = data.text.length;

  return {
    content,
    size,
    type: "application/pdf",
  };
};

// https://github.com/Unstructured-IO/unstructured-js-client
async function convertPdfToDocuments(pdf: Buffer, fileName: string): Promise<Array<Element>> {
  if (!process.env.UNSTRUCTURED_API_KEY) {
    throw new Error("Missing UNSTRUCTURED_API_KEY");
  }
  //const randomName = Math.random().toString(36).substring(7);
  //const pdfPath = `${randomName}.pdf`;
  //await writeFile(pdfPath, pdf, "binary");
  const client = new UnstructuredClient({
    security: {
      apiKeyAuth: process.env.UNSTRUCTURED_API_KEY,
    },
  });

  //const pdfData = fs.readFileSync(pdfPath);
  //console.log("pdfData: ", pdfData)

  const res = await client.general.partition({
    partitionParameters: {
      files: { content: pdf, fileName },
      strategy: "hi_res",
      encoding: "utf-8",
      hiResModelName: "yolox",
      languages: ['chi_tra', 'eng'],
      pdfInferTableStructure: true,
      skipInferTableTypes: ['jpg', 'png', 'heic'],
      chunkingStrategy: "by_title",
      multipageSections: true,
      maxCharacters: 4000,
      newAfterNChars: 3800,
      combineUnderNChars: 500,
      overlap: 25,
    }
  } as PartitionRequest);

  if (res.statusCode === 200) {
    console.log("res.elements", res.elements);
    const elements = res.elements
    if (!Array.isArray(elements)) {
      throw new Error(
        `Expected partitioning request to return an array, but got ${elements}`
      );
    }
    return elements.filter((el) => typeof el.text === "string") as Element[];
  } else {
    console.log(res.statusCode);
    throw new Error("Failed to partition PDF");
  }
}

export const getUnstructuredContentFromBuffer = async (pdfBuffer: Buffer, fileName: string) => {
  const elements = await convertPdfToDocuments(pdfBuffer, fileName);

  const dataCategories: [string[], string[]] = dataCategory(elements);
  const texts: string[] = dataCategories[0];
  const tables: string[] = dataCategories[1];
  console.log(`${tables.length} tables in the PDF file`);
  console.log(`${texts.length} texts in the PDF file`);

  const content = elements;
  const size = "";
  return {
    content,
    size,
    type: "application/pdf",
  };
};