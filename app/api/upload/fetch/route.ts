import {
  fetchContentFromURL,
  getPDFContentFromBuffer,
  getUnstructuredContentFromBuffer,
} from "./content";
import { NextResponse, NextRequest } from "next/server";
//import splitAndEmbed from "./embeddings";
import { URLDetailContent } from "@/app/client/fetch/url";

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const searchParams = new URLSearchParams(url.search);
  const site = searchParams.get("site");
  if (!site) {
    return NextResponse.json(
      { error: "Missing site parameter" },
      { status: 400 },
    );
  }

  try {
    const urlContent = await fetchContentFromURL(site);
    //urlContent.embeddings = await splitAndEmbed(urlContent.content!);
    return NextResponse.json(urlContent);
  } catch (error) {
    console.error("[Fetch]", error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 },
    );
  }
}

async function handleText(
  fileName: string,
  text: string,
): Promise<URLDetailContent> {
  //const embeddings = await splitAndEmbed(text);
  return {
    content: text,
    //embeddings: embeddings,
    url: fileName,
    size: text.length,
    type: "text/plain",
  };
}

async function handleMarkdown(
  fileName: string,
  markdown: string
): Promise<URLDetailContent> {
  return {
    content: markdown,
    url: fileName,
    size: markdown.length,
    type: "text/markdown",
  };
}

async function handlePDF(
  fileName: string,
  pdf: string,
): Promise<URLDetailContent> {
  // Convert the base64 string to binary data (a Buffer object)
  const pdfBuffer = Buffer.from(pdf, "base64");
  const pdfData = await getPDFContentFromBuffer(pdfBuffer);
  //const embeddings = await splitAndEmbed(pdfData.content);
  return {
    content: pdfData.content,
    //embeddings: embeddings,
    size: pdfData.size,
    type: "application/pdf",
    url: fileName,
  };
}

async function handleUnstructuredData(
  fileName: string,
  fileSize: number,
  pdf: string,
) {
  const pdfBuffer = Buffer.from(pdf, "base64");
  const elements = await getUnstructuredContentFromBuffer(pdfBuffer, fileName);
  console.log("pdfData: ", elements)

  return {
    content: elements,
    //embeddings: embeddings,
    size: fileSize,//pdfData?.size,
    type: "application/pdf",
    url: fileName,
  };
}

type Input = {
  fileName: string;
  pdf?: string;
  text?: string;
  markdown?: string;
};
  
export async function POST(request: NextRequest) {
  try {
    const { fileName, pdf, text, markdown }: Input = await request.json();
    if (!fileName && (!pdf && !text && !markdown)) {
      return NextResponse.json(
        {
          error:
            "filename and either text, pdf, or markdown is required in the request body",
        },
        { status: 400 },
      );
    }

    let json;
    if (pdf) {
      json = await handlePDF(fileName, pdf);
      // Use unstructure.io to process the file, the return data is  
      // json = await handleUnstructuredData(fileName, pdf);
    } else if (markdown) {
      json = await handleMarkdown(fileName, markdown);
    } else if (text) {
      json = await handleText(fileName, text);
    } else {
      throw new Error("Invalid input: No content provided");
    }

    return NextResponse.json(json);
  } catch (error) {
    console.error("[Fetch]", error);
    return NextResponse.json(
      {
        error: (error as Error).message,
      },
      {
        status: 500,
      },
    );
  }
}
  
export const runtime = "nodejs";
export const dynamic = "force-dynamic";
  