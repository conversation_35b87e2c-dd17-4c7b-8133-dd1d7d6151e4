import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { anthropic } from '@ai-sdk/anthropic';
import { auth, currentUser } from '@clerk/nextjs/server'
import {
    UIMessage, 
    streamText,
  } from "ai";
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
  
export const runtime = "edge";
export const preferredRegion = ['hnd1']


export async function POST(req: Request, props: { params: Promise<{ summaryId: string }> }) {
  const params = await props.params;
  try {
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const {
      threadId,
      existingThread,
      fileDetail,
      path,
    } = await req.json();

    if (!threadId || !fileDetail.content) {
      return new NextResponse("Missing required fields", { status: 404 });
    }

    const summarizeContext = `Summarize below context briefly in 200 words or less in zh-TW. """ ${fileDetail.content} """`

    const model = anthropic('claude-3-haiku-20240307');
  
    const res = streamText({
      model,
      prompt: summarizeContext,
      topK: 0.2,
      temperature: 1,
      maxOutputTokens: 500,
      async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
        if (fileDetail && text) {
          const fileSize = fileDetail.size;
          const fileSizeInKB = (fileSize / 1024).toFixed(2);
  
          const fileDescription = `${fileDetail.url} \n
            : ${fileDetail.type} • ${fileSizeInKB} KB`;
  
          if (existingThread) {
            await saveCompletionToDatabase(
              params.summaryId, userId, fileDescription, "user", threadId
            );
          } else {
            await newThread({
              threadId, 
              name: `${fileDetail.url}`, 
              companionId: params.summaryId, 
              content: fileDescription,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            params.summaryId, userId, `${text}`, "assistant", threadId
          )
          revalidatePath(path)
        }
      },
    });

    return res.toUIMessageStreamResponse();
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}