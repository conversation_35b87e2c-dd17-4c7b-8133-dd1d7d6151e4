import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { UIMessage } from "ai";
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
  
export const runtime = "edge";
export const preferredRegion = ['hnd1']

const { Client } = require("@octoai/client")

const client = new Client(process.env.OCTO_CLIENT_TOKEN);

export async function POST(req: Request, props: { params: Promise<{ summaryId: string }> }) {
  const params = await props.params;
  try {
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const {
      threadId,
      existingThread,
      fileDetail,
      path,
    } = await req.json();

    if (!threadId || !fileDetail.content) {
      return new NextResponse("Missing required fields", { status: 404 });
    }

    const summarizeContext = `Summarize below context briefly in 200 words or less in zh-TW. """ ${fileDetail.content} """`
    const stream = await client.chat.completions.create({
      messages: [
        {
          "role": "system",
          "content": summarizeContext
        },
      ],
      model: "mixtral-8x7b-instruct",
      presence_penalty: 0,
      temperature: 1,
      top_p: 0.9,
      stream: true,
    });
    

    let completion = '';
    //for await (const chunk of stream) {
    //  completion += chunk.choices[0].delta.content;
    //}

    const textEncoder = new TextEncoder();
    const clientStream = new ReadableStream({
      async start(controller) {
        for await (const chunk of stream) {
          if (chunk.choices[0].delta.content != null) {
            controller.enqueue(
              textEncoder.encode(
                chunk.choices[0].delta.content
              ),
            );
          completion += chunk.choices[0].delta.content;
          }
        }
        
        if (fileDetail && completion) {
          const fileSize = fileDetail.size;
          const fileSizeInKB = (fileSize / 1024).toFixed(2);

          const fileDescription = `${fileDetail.url} \n
           : ${fileDetail.type} • ${fileSizeInKB} KB`;

          if (existingThread) {
            await saveCompletionToDatabase(
              params.summaryId, userId, fileDescription, "user", threadId
            );
          } else {
            await newThread({
              threadId, 
              name: `${fileDetail.url}`, 
              companionId: params.summaryId, 
              content: fileDescription,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            params.summaryId, userId, `${completion}`, "assistant", threadId
          )
          revalidatePath(path)
        }

        controller.close();
      },
    });

    return new NextResponse(clientStream)
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}