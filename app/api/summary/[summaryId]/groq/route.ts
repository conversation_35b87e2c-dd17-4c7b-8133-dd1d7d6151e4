import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { auth, currentUser } from '@clerk/nextjs/server'
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText, UIMessage } from "ai";
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
import { codeBlock } from 'common-tags';
import { createOpenAI } from '@ai-sdk/openai';

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});
const model = groq('llama-3.3-70b-versatile');
  
export const runtime = "edge";
export const preferredRegion = ['hnd1']


export async function POST(req: Request, props: { params: Promise<{ summaryId: string }> }) {
  const params = await props.params;
  try {
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const {
      threadId,
      existingThread,
      fileDetail,
      aiLanguage,
      path,
    } = await req.json();

    if (!threadId || !fileDetail.content) {
      return new NextResponse("Missing required fields", { status: 404 });
    }

    const language = aiLanguage?.label! ?? 'English'
    const summarizeContext = codeBlock`As a psychologist, review the Enneagram profile: \n
      """ ${fileDetail.content} """ \n
      Read the table on page 2 and summarize the different personality types shown in various situations such as being alone, at work, with friends, and with family.
      Highlight that personality manifestations can differ based on mental states and are also shaped by the situational context.
      Keep your responses warm, positive, and concise, ideally under 100 words.
      Respond in ${language}.
      SUMMARY:`
    //console.log("summarizeContext", summarizeContext)
    //const summarizeContext = `Summarize below context briefly in 200 words or less in zh-TW. """ ${fileDetail.content} """`
    
    const res = streamText({
      model,
      maxOutputTokens: 2000,
      temperature: 0,
      prompt: summarizeContext,
      async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
        if (fileDetail && text) {
          const fileSize = fileDetail.size;
          const fileSizeInKB = (fileSize / 1024).toFixed(2);
          const fileDescription = `${fileDetail.url}\n : ${fileDetail.type} • ${fileSizeInKB} KB`;
            
          if (existingThread) {
            await saveCompletionToDatabase(
              params.summaryId, userId, fileDescription, "user", threadId
            );
          } else {
            await newThread({
              threadId, 
              name: `${fileDetail.url}`, 
              companionId: params.summaryId, 
              content: fileDescription,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            params.summaryId, userId, `${text}`, "assistant", threadId
          )
          revalidatePath(path)
        }
      }
    })

    return res.toTextStreamResponse();
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}