import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { tokenCounter } from "@/lib/token-counter";
import { createOpenAI } from '@ai-sdk/openai';
import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import {
  streamText
} from "ai";

import { PromptTemplate } from '@langchain/core/prompts'
import { 
  saveChatSummaryToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['sfo1']

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});

const model = groq('llama-3.3-70b-versatile');

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

My recent chat summary:
{chatSummaryHistory}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}

Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const OBSERVATION_TEMPLATE: string = `
You act as a psychologist to summarize your understanding of ` +
`my strengths, personality traits, values, motivations, and interest ` +
`based on my expressions whenever I provide insights or narratives over 200 words.\n` +
`Respond by adopting my linguistic habits and utilizing the Linguistic Inquiry and Word Count (LIWC) tool for detailed analysis.\n` +
`Assist me in gaining a deeper understanding of my emotional tendencies, cognitive complexity, social connections, and personal concerns.\n` +
`Do not offer any advice or guidance, only reflect and affirm what I have expressed.\n` +
`Ensure each response is warm and positive and keep it under 100 words.\n` + 
`Respond in {language}.\n` +
`{userName}’s conversation:\n` +
`----------` +
`{relevant_memories}\n` +
`----------` +
`Do not embellish.`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words in zh-tw, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`

const CHAT_SUMMARIZE_TEMPLATE = `
I am here to share my life experience. 
The topics may cover:
1.self-care, stress, or emotion management 
2.interpersonal or family experiences or challenges
3.career or financial statues or issues
4.others that I am interested in.

You can adopt my discourse particles and use them when responding to me.
You act as a psychologist to summarize the key messages from below conversations ` +
`in order to help the next psychologist to better understand the user ` +
`and can pick up the following conversation easily.` +
`The mandatory information you summarize is the topic I would like to discuss.` +
`The supplement information covers the experiences I share, ` +
`including the critical details as why, who, when, what, how.` + 
`Be specific, clear, and objective. Do not provide suggestions.` +
`Summarize with less than 100 words.` +  
`Respond in {language}.\n` +
`{userName}conversation:\n` +
`----------` +
`{relevant_memories}\n` +
`----------` +
`Do not embellish.`

export async function POST(req: Request, props: { params: Promise<{ summaryId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const { 
    prompt,
    aiLanguage,
    observationList,
    companionName,
    userName,
    threadId,
    chatRuns,
    useReflectionThreshold,
    latestCompletion,
    path,
} = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const messages = observationList

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!latestCompletion) {
    return new NextResponse("latest command is not specified", { status: 500 });
  }

  let systemPrompt;
  let combinedMessages: any[]
  let limitedObservations: string[] = []
  let saveCompletion: string = ''
  const language = aiLanguage?.label! ?? 'zh-TW'

  // Get the last 8 messages
  if (messages && messages.length > 0) {
    const maxItems = 3;
    const totalMessages = messages.length;

    // Ensure not to exceed the length of the observationList
    const startIndex = Math.max(0, totalMessages - maxItems);
    limitedObservations = messages.slice(startIndex, totalMessages);
  }

  if (latestCompletion.current === "getUserChatSummary") {
    const systemPromptTemplate = PromptTemplate.fromTemplate(CHAT_SUMMARIZE_TEMPLATE);
    systemPrompt = await systemPromptTemplate.format({
      userName: userName,
      language: language ?? 'zh-TW',
      relevant_memories: `${limitedObservations}`,
    })
    combinedMessages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt },
    ]
    saveCompletion = 'userChatSummary'
    console.log("getUserChatSummary")

  } else if (latestCompletion.current === "getObservation") {
    const systemPromptTemplate = PromptTemplate.fromTemplate(OBSERVATION_TEMPLATE);
    systemPrompt = await systemPromptTemplate.format({
      userName: userName,
      language: language ?? 'zh-TW',
      relevant_memories: `${limitedObservations}`,
    })
    combinedMessages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt },
    ]
    saveCompletion = 'observation'
    console.log("getObservation")
  } else {
    combinedMessages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt },
    ]
    return new NextResponse("latest command is not specified", { status: 500 });
  }

  // check if the conversation requires a function call to be made
  const res = streamText({
    model,
    system: systemPrompt,
    prompt: prompt,
    async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
      console.log("text@api/summary: ", text)
      if (saveCompletion === "observation") {
        await saveObservationToDatabase(
          params.summaryId, 
          userId, 
          //text.length > 10 ? text.substring(0, 10) + "..." : text,
          text,
          text, 
          'user_status_sum'
        )
        await tokenCounter(
          combinedMessages, 
          text, 
          1,
          'gpt-4-turbo-preview', 
          userId, 
          process.env.APP_SECRET_KEY!
        );
        revalidatePath(path)
      } else if (saveCompletion === "userChatSummary"){
        await saveChatSummaryToDatabase(
          text.length > 15 ? text.substring(0, 15) + "..." : text,
          text,
          params.summaryId,
          companionName,
          userId,
          userName,
          threadId,
        );
        await tokenCounter(
          combinedMessages, 
          text, 
          1,
          'gpt-4-turbo-preview', 
          userId, 
          process.env.APP_SECRET_KEY!
        );
        revalidatePath(path)
      }
    },
  });
  return res.toUIMessageStreamResponse();
}