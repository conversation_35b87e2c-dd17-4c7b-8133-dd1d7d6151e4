import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import prismadb from "@/lib/prismadb";
import { Todos } from "@prisma/client";


export async function PATCH(req: Request, props: { params: Promise<{ todoId: string }> }) {
  const params = await props.params;
  try {
    const body = await req.json();
    const { is_completed } = body;


    const updatedTodo: Todos = await prismadb.todos.update({
      where: {
        id: params.todoId,
        },
      data: {
        is_completed: is_completed,
      }
    });

    return NextResponse.json(updatedTodo);
  } catch (error) {
    console.log("[TODOS_PATCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};

export async function DELETE(request: Request, props: { params: Promise<{ todoId: string }> }) {
  const params = await props.params;
  try {
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const deletedTodo: Todos = await prismadb.todos.delete({
        where: {
          userId,
          id: params.todoId,
        },
      });
      

    return NextResponse.json(deletedTodo);
  } catch (error) {
    console.log("[TODOS_DELETE]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};