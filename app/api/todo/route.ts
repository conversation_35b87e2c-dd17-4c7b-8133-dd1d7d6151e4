import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import prismadb from "@/lib/prismadb";
import { Todos } from "@prisma/client";


export async function POST(req: Request) {
  try {
    const body = await req.json();
    const user = await currentUser();
    const { task, companionId } = body;

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!task || !companionId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };


     const todo: Todos = await prismadb.todos.create({
       data: {
         task,
         userId: user!.id,
         companionId: companionId
       },
     });
      
    return NextResponse.json(todo);
  } catch (error) {
    console.log("[TODOS_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};