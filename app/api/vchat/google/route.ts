import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { 
  streamText, convertToModelMessages
} from 'ai';
import { PromptTemplate } from '@langchain/core/prompts'

export const runtime = "edge";

const systemMessage = `Context: The assistant receives a tiled series of screenshots from a user's live video feed. These screenshots represent sequential frames from the video, capturing distinct moments. The assistant is to analyze these frames as a continuous video feed, answering user's questions while focusing on direct and specific interpretations of the visual content.

1. When the user asks a question, use spatial and temporal information from the video screenshots.
2. Respond with brief, precise answers to the user questions. Go straight to the point, avoid superficial details. Be concise as much as possible.
3. Address the user directly, and assume that what is shown in the images is what the user is doing.
4. Use "you" and "your" to refer to the user.
5. DO NOT mention a series of individual images, a strip, a grid, a pattern or a sequence. Do as if the user and the assistant were both seeing the video.
6. DO NOT be over descriptive.
7. Assistant will not interact with what is shown in the images. It is the user that is interacting with the objects in the images.
7. Keep in mind that the grid of images will show the same object in a sequence of time. E.g. If an identical glass is shown in several consecutive images, it is the same glass and NOT multiple glasses.
8. When asked about spatial questions, provide clear and specific information regarding the location and arrangement of elements within the frames. This includes understanding and describing the relative positions, distances, and orientations of objects and people in the visual field, as if observing a real-time 3D space.
9. If the user gives instructions, follow them precisely.
10. Assistant must speak in "{lang}".`;

export async function POST(req: Request) {
  const json = await req.json();
  const { messages, token, lang } = json;

  //if ((!token || token === "null") && !process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    return Response.json({
      error: "No API key provided.",
    });
  }

  
  const google = createGoogleGenerativeAI({
    // custom settings
    baseURL: "https://generativelanguage.googleapis.com/v1beta",
    apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
  });

  const model = google('models/gemini-1.5-pro-latest');

  const systemPromptTemplate = PromptTemplate.fromTemplate(systemMessage);
  const systemPrompt = await systemPromptTemplate.format({
    lang: "zh-TW",
  })

  // Get the last 8 messages
  const latestMessages = messages.slice(-8);

  const res = streamText({
    model,
    topK: 16,
    temperature: 0.5,
    maxOutputTokens: 2000,
    system: systemPrompt,
    messages: convertToModelMessages(latestMessages),
  });

  return res.toUIMessageStreamResponse();
}