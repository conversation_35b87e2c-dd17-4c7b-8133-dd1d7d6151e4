import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { ChatGroq } from "@langchain/groq";
import { UnstructuredLoader } from "@langchain/community/document_loaders/fs/unstructured";
import { CharacterTextSplitter, RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { loadSummarizationChain } from "langchain/chains";
import { PromptTemplate } from "@langchain/core/prompts";
import { LLMResult } from "@langchain/core/outputs";
import { getUnstructuredContent } from "./content";

import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { OpenAIEmbeddings } from "@langchain/openai";
import { CohereEmbeddings } from "@langchain/cohere";
import { v4 as uuidv4 } from 'uuid'
import { codeBlock } from 'common-tags';

export const runtime = "nodejs";

// Before running, follow set-up instructions at
// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 * https://gist.github.com/taishikato/2f53dada6ea3339ce60b0b55a672dd1b
 */

async function handleExtension(
  fileDetail: any, 
  userId: string, 
  client: any
) {
const mapTemplate = codeBlock`Write a summary of the following in 1-2 sentences:    
  "{text}"
  Respond in ${fileDetail.language}.
  SUMMARY:
  `
const summaryTemplate = codeBlock`As a psychologist, review the Enneagram profile:
  Read the table on page 2 and summarize the different personality types shown in various situations such as being alone, at work, with friends, and with family.
  "{text}"
  Highlight that personality manifestations can differ based on mental states and are also shaped by the situational context.
  Keep your responses warm, positive, and concise, ideally under 100 words.
  Respond in ${fileDetail.language}.
  SUMMARY:
  `
const summaryRefineTemplate = codeBlock`As a psychologist, review the Enneagram profile:
  We have provided an existing summary up to a certain point: {existing_answer}
  Below you find the Enneagram profile of me:
  "{text}"
  Summarize the different personality types shown in various situations such as being alone, at work, with friends, and with family.
  Highlight that personality manifestations can differ based on mental states and are also shaped by the situational context.
  Keep your responses warm, positive, and concise, ideally under 100 words.
  Respond in ${fileDetail.language}.
  SUMMARY:
  `
  const documentId: string = uuidv4()
  const extension = fileDetail.extension

  let splitter;
  if (extension === "pdf") {
     splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 50,
      separators: ['\n\n', '\n', ' ', ''] // default setting
    });
  }
  if (extension === "txt") {
    splitter = new CharacterTextSplitter({
      separator: " ",
      chunkSize: 500,
      chunkOverlap: 50,
    });
  } else {
    const language = extension === "md" ? "markdown" : "html";
    splitter = RecursiveCharacterTextSplitter.fromLanguage(language, {
      chunkSize: 500,
      chunkOverlap: 50,
    });
  }

  // Convert PDF to documents using UnstructuredLoader
  const {
    documents,
    dataCategories,
  } = await getUnstructuredContent(fileDetail, documentId);

  const model = new ChatGroq({
    modelName: 'llama-3.3-70b-versatile', //'llama3-8b-8192', 'mixtral-8x7b-32768',
    temperature: 0.0,
    callbacks: [
      {
        /*handleLLMNewToken(token: string): Promise<void> | void {
          console.log("token", token);
        },*/
        handleLLMEnd: async (output: LLMResult) => {
          const tokenUsage = output?.llmOutput?.tokenUsage
          if (output?.llmOutput?.tokenUsage) {
            console.log("model tokenUsage: ", tokenUsage)
            console.log(JSON.stringify(output, null, 2));            
          }
        },
      },
    ],
  });
  const combineModel = new ChatGroq({
    modelName: 'llama-3.3-70b-versatile', //'llama3-8b-8192', 'mixtral-8x7b-32768',
    temperature: 0.0,
    streaming: true,
    callbacks: [
      {
        /*handleLLMNewToken(token: string): Promise<void> | void {
          console.log("token", token);
        },*/
        handleLLMEnd: async (output: LLMResult) => {
          const tokenUsage = output?.llmOutput?.tokenUsage
          if (output?.llmOutput?.tokenUsage) {
            console.log("combineModel tokenUsage: ", tokenUsage)
            console.log(JSON.stringify(output, null, 2));            
          }
        },
      },
    ],
  });


  const COMBINE_MAP_PROMPT = PromptTemplate.fromTemplate(mapTemplate)
  const SUMMARY_PROMPT = PromptTemplate.fromTemplate(summaryTemplate)
  const SUMMARY_REFINE_PROMPT = PromptTemplate.fromTemplate(
    summaryRefineTemplate
  );

  const chain = loadSummarizationChain(combineModel, {
    type: "refine",
    verbose: false,
    questionPrompt: SUMMARY_PROMPT,
    refinePrompt: SUMMARY_REFINE_PROMPT,
    refineLLM: combineModel,
  });

  /*const chain = loadSummarizationChain(model, {
    type: "map_reduce",
    // The prompt passed to `llmChain`
    combineMapPrompt: COMBINE_MAP_PROMPT,
    // The prompt passed to `combineDocumentChain`
    combinePrompt: SUMMARY_PROMPT,
    combineLLM: combineModel,
  });*/
  const summaries = await chain.call({
    input_documents: documents,
    returnIntermediateSteps: false,
  });
  console.log({ summaries });

  // disable import summaryDocs to vectorStore
  const summaryDocs = await splitter.createDocuments(
    //[summaries.text], // for map_reduce
    [summaries.output_text], // for refine
    [{  
      category: "summary",
    }],
  );
  // Add metadata to each document
  /*const documentsWithMetadata = [
    ...summaryDocs.map((summaryDoc, index) => ({
      ...summaryDoc,
      document_id: documentId,
      metadata: {
        ...summaryDoc.metadata,
        index,
        id: documentId,
        source: fileDetail.name,
        user_id: userId,
      },
    })),
    ...documents.map((document, index) => ({
      ...document,
      document_id: documentId,
      metadata: {
        ...document.metadata,
        index: summaryDocs.length + index, // Update the index for documents
        id: documentId,
        source: fileDetail.name,
        user_id: userId,
      },
    })),
  ];*/
  const documentsWithMetadata = documents.map((document, index) => {
    return {
      ...document,
      document_id: documentId,
      metadata: {
        ...document.metadata,
        index,
        id: documentId,
        source: fileDetail.name,
        user_id: userId,
      },
    };
  });
  //console.log("documentsWithMetadata: ", documentsWithMetadata)
  await Promise.all([
    await client
      .from("documents_1024")
      .insert({
        id: documentId,
        filename: fileDetail.name,
        type: fileDetail.type,
        size: +fileDetail.size,
        owner_id: userId,
    }),
    await SupabaseVectorStore.fromDocuments(
      documentsWithMetadata,
      //new OpenAIEmbeddings(),
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        tableName: "document_sections_1024",
        queryName: "match_document_sections_1024",
      },
    )
  ])
}

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  };
  const { getToken } = await auth(); 
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const rateLimitResponse = await checkRateLimit(req);
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  }
  const formData = await req.formData();
  const file = formData.get("file") as File | null;
  const name = file?.name!;
  const type = formData.get("type");
  const size = formData.get("size");
  const aiLanguage = formData.get("aiLanguage");
  const path = formData.get("path")?.toString();

  // Accept these file types
  // Markdown, Text, HTML
  const extension = name ? name.split(".").pop() : undefined;
  if (extension && !["md", "txt", "pdf", "html", "doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)) {
    return NextResponse.json(
      {
        error: [
          "File type not supported.",
          "Please upload a markdown, text, pdf, doc, docx, xls, xlsx, ppt, pptx or html file.",
        ].join("\n"),
      },
      { status: 403 },
    );
  }

  const language = aiLanguage ?? 'English';

  try {
    const fileDetail = {
      name,
      file,
      type,
      extension,
      size,
      language,
    };
    const client = await createClerkSupabaseServerClient(authToken);
    await handleExtension(fileDetail, userId, client);

    if (path) {
      revalidatePath(path)      
    }

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}