// https://github.com/Unstructured-IO/unstructured-js-client
import { UnstructuredClient } from "unstructured-client";
import { PartitionParameters } from "unstructured-client/sdk/models/shared";
import { PartitionRequest, PartitionResponse } from "unstructured-client/sdk/models/operations";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatGroq } from "@langchain/groq";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumanMessage } from "@langchain/core/messages";
import { Document } from "@/lib/vectorstores/document";
import { codeBlock } from 'common-tags';
import { v4 as uuidv4 } from 'uuid';

/**
 * Represents an element returned by the Unstructured API. It has
 * properties for the element type, text content, and metadata.
 */
type Element = {
  type: string;
  element_id: string;
  text: string;
  // this is purposefully loosely typed
  metadata: {
    [key: string]: unknown;
  };
};

type ElementText = {
  element_id: string;
  text: string;
};
type ElementSummary = {
  element_id: string;
  summary: string;
};

async function dataCategory(elements: Element[]): Promise<[ElementText[], ElementText[], ElementSummary[], ElementSummary[]]> {
  const textElements: ElementText[] = [];
  const tableElements: ElementText[] = [];

  const textSummaries: ElementSummary[] = [];
  const tableSummaries: ElementSummary[] = [];

  const summary_prompt = codeBlock`"""
  Summarize the following {element_type}: 
  {context}
  """`
  const prompt = ChatPromptTemplate.fromTemplate(summary_prompt);
  const llm = new ChatGroq({model: "llama-3.3-70b-versatile", temperature: 0.0, maxOutputTokens: 1024})
  const chain = prompt.pipe(llm);

  const vision = new ChatGoogleGenerativeAI({
    model: "gemini-pro-vision",
    maxOutputTokens: 2048,
  });

  for (const element of elements) {
    const { element_id, text, type } = element;
    if (element.type === "Table") {
      const image = element.metadata?.image_base64
      if (image) {
        const input = [
          new HumanMessage({
            content: [
              {
                type: "text",
                text: "Analyzing the following image. Translate it to a markdown table format and describe the contents of the image.",
              },
              {
                type: "image_url",
                image_url: `data:image/jpeg;base64,${image}`,
              },
            ],
          }),
        ];
        const res = await vision.invoke(input);
        const summary = typeof res.content === 'string' ? res.content : JSON.stringify(res.content);
        //const combinedText = element.text + '\n' + summary;

        tableElements.push({ element_id, text });
        tableSummaries.push({ element_id, summary });
        
      } else {
        tableElements.push({ element_id, text });
      }


    } else if (element.type === "CompositeElement") {
      const res = await chain.invoke({ element_type: type, context: text });
      const summary = typeof res.content === 'string' ? res.content : JSON.stringify(res.content);
      //const combinedText = text + '\n' + summary;
      textElements.push({ element_id, text });
      textSummaries.push({ element_id, summary });
    }
  }
  return [textElements, tableElements, textSummaries, tableSummaries];
}

async function convertPdfToDocuments(file: any): Promise<Array<Element>> {
  if (!process.env.UNSTRUCTURED_API_KEY) {
    throw new Error("Missing UNSTRUCTURED_API_KEY");
  }
  
  const client = new UnstructuredClient({
    security: {
      apiKeyAuth: process.env.UNSTRUCTURED_API_KEY,
    },
  });

  const arrayBuffer = await file.file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  if (buffer) {
    const res = await client.general.partition({
      partitionParameters: {
        files: { content: buffer, fileName: file.name },
        gzUncompressedContentType: "application/pdf",
        strategy: "hi_res",
        encoding: "utf-8",
        extractImageBlockTypes: ["image", "table"],
        languages: ['chi_tra', 'eng'],
        pdfInferTableStructure: true,
        skipInferTableTypes: ["pdf"],//['jpg', 'png', 'heic'],
        chunkingStrategy: "by_title",
        multipageSections: true,
        maxCharacters: 4000,
        newAfterNChars: 3800,
        combineUnderNChars: 500,
        outputFormat: "application/json",        
      }
    } as PartitionRequest);

    if (res.statusCode === 200) {
      console.log("res.elements", res.elements);
      const elements = res.elements
      if (!Array.isArray(elements)) {
        throw new Error(
          `Expected partitioning request to return an array, but got ${elements}`
        );
      }
      return elements.filter((el) => typeof el.text === "string") as Element[];
    } else {
      console.log(res.statusCode);
      throw new Error("Failed to partition PDF");
    }
  }
  
  throw new Error("Unexpected error occurred in PDF partitioning");
}


export const getUnstructuredContent = async (file: any, documentId: string) => {
  const elements = await convertPdfToDocuments(file);

  const dataCategories: [ElementText[], ElementText[], ElementSummary[], ElementSummary[]] = await dataCategory(elements);
  const textElements: ElementText[] = dataCategories[0];
  const tableElements: ElementText[] = dataCategories[1];
  const textSummaries: ElementSummary[] = dataCategories[2];
  const tableSummaries: ElementSummary[] = dataCategories[3];
  console.log(`${tableElements.length} tables in the PDF file`);
  console.log(`${textElements.length} texts in the PDF file`);
  console.log("TextSummaries: ", tableSummaries);
  console.log("TableSummaries: ", textSummaries);

  // Iterate over textSummaries and append the summary to the original text in the elements
  for (const { element_id, summary } of textSummaries) {
    const index = elements.findIndex(element => element.element_id === element_id);
    if (index !== -1) {
      elements[index].text += '\n' + summary;
    }
  }

  // Iterate over tableSummaries and append the summary to the original text in the elements
  for (const { element_id, summary } of tableSummaries) {
    const index = elements.findIndex(element => element.element_id === element_id);
    if (index !== -1) {
      elements[index].text += '\n' + summary;
    }
  }

  const documents: Document[] = [];
  for (const element of elements) {
    const { metadata, text } = element;
    const filteredMetadata = { ...metadata };
    delete filteredMetadata.orig_elements;
    delete filteredMetadata.languages;
    delete filteredMetadata.filename;
    //delete filteredMetadata.image_base64;
    if (typeof text === "string") {
      documents.push(
        new Document({
          document_id: documentId,
          pageContent: text,
          metadata: {
            ...filteredMetadata,
            category: element.type,
          },
        })
      );
    }
  }
  console.log("documents: ", documents)

  return {
    documents,
    dataCategories,
  };
};