// https://github.com/Unstructured-IO/unstructured-js-client
import { UnstructuredClient } from "unstructured-client";
import { PartitionParameters } from "unstructured-client/sdk/models/shared";
import { PartitionRequest, PartitionResponse } from "unstructured-client/sdk/models/operations";
import { Document } from "@/lib/vectorstores/document";

/**
 * Represents an element returned by the Unstructured API. It has
 * properties for the element type, text content, and metadata.
 */
type Element = {
  type: string;
  text: string;
  // this is purposefully loosely typed
  metadata: {
    [key: string]: unknown;
  };
};


function dataCategory(elements: Element[]): [string[], string[]] {
  const tables: string[] = [];
  const texts: string[] = [];
  for (const element of elements) {
    if (element.type === "Table") {
      //const combinedText = element.text + '\n' + element.metadata.text_as_html;
      tables.push(element.text);
      //tables.push(combinedText);
    } else if (element.type === "CompositeElement") {
      texts.push(element.text);
    }
  }
  return [texts, tables];
}

async function convertPdfToDocuments(file: any): Promise<Array<Element>> {
  if (!process.env.UNSTRUCTURED_API_KEY) {
    throw new Error("Missing UNSTRUCTURED_API_KEY");
  }
  
  const client = new UnstructuredClient({
    security: {
      apiKeyAuth: process.env.UNSTRUCTURED_API_KEY,
    },
  });

  const arrayBuffer = await file.file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  if (buffer) {
    const res = await client.general.partition({
      partitionParameters: {
        files: { content: buffer, fileName: file.name },
        gzUncompressedContentType: "application/pdf",
        hiResModelName: "yolox",
        strategy: "hi_res",
        encoding: "utf-8",
        extractImageBlockTypes: ["image", "table"],
        languages: ['chi_tra'],
        //pdfInferTableStructure: true,
        skipInferTableTypes: ['jpg', 'png', 'heic'],
        chunkingStrategy: "by_title",
        multipageSections: true,
        maxCharacters: 4000,
        newAfterNChars: 3800,
        combineUnderNChars: 500,
      }
    } as PartitionRequest )

    if (res.statusCode === 200) {
      console.log("res.elements", res.elements);
      const elements = res.elements
      if (!Array.isArray(elements)) {
        throw new Error(
          `Expected partitioning request to return an array, but got ${elements}`
        );
      }
      return elements.filter((el) => typeof el.text === "string") as Element[];
    } else {
      console.log(res.statusCode);
      throw new Error("Failed to partition PDF");
    }
  }
  
  throw new Error("Unexpected error occurred in PDF partitioning");
}


export const getUnstructuredContent = async (file: any, documentId: string) => {
  const elements = await convertPdfToDocuments(file);

  const dataCategories: [string[], string[]] = dataCategory(elements);
  const texts: string[] = dataCategories[0];
  const tables: string[] = dataCategories[1];
  console.log(`${tables.length} tables in the PDF file`);
  console.log(`${texts.length} texts in the PDF file`);

  const documents: Document[] = [];
  for (const element of elements) {
    const { metadata, text } = element;
    const filteredMetadata = { ...metadata };
    delete filteredMetadata.orig_elements; // Remove the orig_elements property
    if (typeof text === "string") {
      documents.push(        
        new Document({
          document_id: documentId,
          pageContent: text,
          metadata: {
            ...filteredMetadata,
            category: element.type,
          },
        })
      );
    }
  }
  console.log("documents: ", documents)

  return {
    documents,
    dataCategories,
  };
};