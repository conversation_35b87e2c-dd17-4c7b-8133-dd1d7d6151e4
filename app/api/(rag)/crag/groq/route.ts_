import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { NextRequest, NextResponse } from "next/server";
import { 
  StreamingTextResponse 
} from 'ai';
 
import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'
import { checkCreditBalance } from "@/lib/credit-balance";
import { addTransaction } from '@/app/actions/creditsActions'
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { CohereEmbeddings } from "@langchain/cohere";
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { TaskType } from "@google/generative-ai";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";

import { StateGraphArgs } from "@langchain/langgraph";
import { Document, type DocumentInterface } from "@langchain/core/documents";
import { StructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { pull } from "langchain/hub";
import { ChatGroq } from "@langchain/groq";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { JsonOutputToolsParser } from "langchain/output_parsers";
import { END, START, StateGraph } from "@langchain/langgraph";

export const runtime = 'edge'
export const preferredRegion = ['hnd1']

const TEMPLATE = `

**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}

Question: {prompt}
`

export async function POST(req: NextRequest) {
try {
    const { userId, getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    if (!userId || !authToken) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const creditAccess = await checkCreditBalance();
    if (!creditAccess) {
      return new NextResponse(
        JSON.stringify({ 
          error: "Your credit is empty. Please purchase more credits." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }

    const json = await req.json()
    const {
      companion,
      threadId,
      existingThread,
      messages,
      chatRuns,
      previewToken,
      useReflectionThreshold,
      Persona,
      aiLanguage,
      path,
    } = json

    const language = aiLanguage?.label! ?? 'English'
    const client = await createClerkSupabaseServerClient(authToken);
    const retriever = new SupabaseHybridSearch(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        similarityK: 4,
        keywordK: 0,
        tableName: "document_sections_1024",
        similarityQueryName: "match_document_sections_1024",
        keywordQueryName: "kw_match_document_sections",
      });

    /**
     * Represents the state of our graph.
     */
    type GraphState = {
      documents: Document[];
      question: string;
      generation?: string;
    };

    const graphState: StateGraphArgs<GraphState>["channels"] = {
      documents: {
        value: (left?: Document[], right?: Document[]) =>
          right ? right : left || [],
        default: () => [],
      },
      question: {
        value: (left?: string, right?: string) => (right ? right : left || ""),
        default: () => "",
      },
      generation: {
        value: (left?: string, right?: string) => (right ? right : left),
        default: () => undefined,
      },
    };

    // Data model (create via a LangChain tool)
    const zodScore = z.object({
      binaryScore: z.enum(["yes", "no"]).describe("Relevance score 'yes' or 'no'"),
    });
    class Grade extends StructuredTool<typeof zodScore> {
      name = "grade";
      description =
        "Grade the relevance of the retrieved documents to the question. Either 'yes' or 'no'.";
      schema = zodScore;
      async _call(input: z.infer<(typeof this)["schema"]>) {
        return JSON.stringify(input);
      }
    }
    const gradeTool = new Grade();

    /**==================================================================
     * Nodes and Edges
     ==================================================================*/
    /**
     * Retrieve documents
     *
     * @param {GraphState} state The current state of the graph.
     * @param {RunnableConfig | undefined} config The configuration object for tracing.
     * @returns {Promise<GraphState>} The new state object.
    */
    async function retrieve(state: GraphState) {
      console.log("---RETRIEVE---");
      console.log(state);
      const documents = await retriever
        .withConfig({ runName: "FetchRelevantDocuments" })
        .invoke(state.question);
      return {
        documents,
      };
    }

    /**
     * Generate answer
     *
     * @param {GraphState} state The current state of the graph.
     * @param {RunnableConfig | undefined} config The configuration object for tracing.
     * @returns {Promise<GraphState>} The new state object.
     */
    async function generate(state: GraphState) {
      console.log("---GENERATE---");
      
      // Pull in the prompt
      const prompt = await pull<ChatPromptTemplate>("rlm/rag-prompt");

      const llm = new ChatGoogleGenerativeAI({
        model: "gemini-1.5-pro-latest",
        maxOutputTokens: 2048,
        streaming: true,
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
          },
        ],
      });

      // RAG Chain
      const ragChain = prompt.pipe(llm).pipe(new StringOutputParser());
      const formattedDocs = state.documents
        .map((doc: Document) => doc.pageContent)
        .join("\n\n");
      console.log("---GENERATE formattedDocs---", formattedDocs);
      const generation = await ragChain.invoke({ 
        context: formattedDocs, 
        question: state.question 
      });

      return {
        generation,
      };
    }

    /**
     * Determines whether the retrieved documents are relevant to the question.
     *
     * @param {GraphState} state The current state of the graph.
     * @param {RunnableConfig | undefined} config The configuration object for tracing.
     * @returns {Promise<GraphState>} The new state object.
     */
    async function gradeDocuments(state: GraphState) {
      console.log("---CHECK RELEVANCE---");

      const model = new ChatGroq({
        apiKey: process.env.GROQ_API_KEY,
        model: "llama-3.3-70b-versatile",
        temperature: 0,
      }); 
      /*const model = new ChatGoogleGenerativeAI({
        modelName: "gemini-pro",
        temperature: 0,
      });*/

      const parser = new JsonOutputToolsParser();
    

      // LLM with tool and enforce invocation
      const llmWithTool = model.bindTools([gradeTool], {
        tool_choice: { type: "function", function: { name: gradeTool.name } },
      });

      const prompt = ChatPromptTemplate.fromTemplate(
        `You are a grader assessing relevance of a retrieved document to a user question.
      Here is the retrieved document:
      
      {context}
      
      Here is the user question: {question}
    
      If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant.
      Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.`,
      );


      // Chain
      const chain = prompt.pipe(llmWithTool).pipe(parser);

      const filteredDocs: Array<DocumentInterface> = [];
      for await (const doc of state.documents) {
        const grade = await chain.invoke({
          context: doc.pageContent,
          question: state.question,
        });
        const { args } = grade[0];
        if (args.binaryScore === "yes") {
          console.log("---GRADE: DOCUMENT RELEVANT---");
          filteredDocs.push(doc);
        } else {
          console.log("---GRADE: DOCUMENT NOT RELEVANT---");
        }
      }

      return {
        documents: filteredDocs,
      };
    }

    /**
     * Transform the query to produce a better question.
     *
     * @param {GraphState} state The current state of the graph.
     * @param {RunnableConfig | undefined} config The configuration object for tracing.
     * @returns {Promise<GraphState>} The new state object.
     */
    async function transformQuery(state: GraphState) {
      console.log("---TRANSFORM QUERY---");
      // Pull in the prompt
      const prompt = ChatPromptTemplate.fromTemplate(
        `You are generating a question that is well optimized for semantic search retrieval.
      Look at the input and try to reason about the underlying sematic intent / meaning.
      Here is the initial question:
      \n ------- \n
      {question} 
      \n ------- \n
      Formulate an improved question: `,
      );
    
      // Grader
      const model = new ChatGroq({
        model: "llama-3.3-70b-versatile",
        temperature: 0,
      })
    
      // Prompt
      const chain = prompt.pipe(model).pipe(new StringOutputParser());
      const betterQuestion = await chain.invoke({ question: state.question });
    
      return {
        question: betterQuestion,
      };
    }

    /**
     * Web search based on the re-phrased question using Tavily API.
     *
     * @param {GraphState} state The current state of the graph.
     * @param {RunnableConfig | undefined} config The configuration object for tracing.
     * @returns {Promise<GraphState>} The new state object.
     */
    async function webSearch(state: GraphState) {
      console.log("---WEB SEARCH---");
      console.log("---state---", state);

      // Validate state.question
      if (!state.question || typeof state.question !== 'string') {
        console.error("Invalid state.question:", state.question);
        throw new Error("Invalid query for Tavily API.");
      }
      const tool = new TavilySearchResults({ maxResults: 1 })
      //const model = new ChatGroq({ model: "llama-3.3-70b-versatile" }).bindTools(tools);
      const docs = await tool.invoke({ query: state.question });
      //const docs = await model.invoke(state.question)
      console.log("---docs---", docs);

      const webResults = new Document({ pageContent: docs });
      const newDocuments = state.documents.concat(webResults);

      return {
        documents: newDocuments,
      };
    }

    /**
     * Determines whether to generate an answer, or re-generate a question.
     *
     * @param {GraphState} state The current state of the graph.
     * @returns {"transformQuery" | "generate"} Next node to call
     */
    function decideToGenerate(state: GraphState) {
      console.log("---DECIDE TO GENERATE---");
      const filteredDocs = state.documents;

      if (filteredDocs.length === 0) {
        // All documents have been filtered checkRelevance
        // We will re-generate a new query
        console.log("---DECISION: TRANSFORM QUERY---");
        return "transformQuery";
      }
      // We have relevant documents, so generate answer
      console.log("---DECISION: GENERATE---");
      return "generate";
    }


    /**==================================================================
     * Build Graph
     ==================================================================*/

    const workflow = new StateGraph<GraphState>({
      channels: graphState,
    })
    // Define the nodes
      .addNode("retrieve", retrieve)
      .addNode("gradeDocuments", gradeDocuments)
      .addNode("generate", generate)
      .addNode("transformQuery", transformQuery)
      .addNode("webSearch", webSearch)

    // Build graph
    workflow.addEdge(START, "retrieve");
    workflow.addEdge("retrieve", "gradeDocuments");
    workflow.addConditionalEdges(
      "gradeDocuments",
      decideToGenerate,
    );
    workflow.addEdge("transformQuery", "webSearch");
    workflow.addEdge("webSearch", "generate");
    workflow.addEdge("generate", END);

    // Compile
    const app = workflow.compile();

    /**==================================================================
     * Run the graph
     ==================================================================*/
    const userMessage = messages.slice(-1)
    .filter((message: { role: string }) => message.role === 'user')
    const promptMessage = userMessage[0]?.content

    const inputs = {
      question: promptMessage,
    };
    const config = { recursionLimit: 20 };

    let finalState;
    let finalGeneration: string | undefined = undefined;
    interface TokenUsage {
      completionTokens: number;
      promptTokens: number;
      totalTokens: number;
    }
    
    const sumTokenUsages = {
      completionTokens: 0,
      promptTokens: 0,
      totalTokens: 0
    };

    let tokenUsage: TokenUsage | undefined = undefined
    const textEncoder = new TextEncoder();
    const clientStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const output of await app.stream(inputs, config)) {
            for (const [key, value] of Object.entries(output)) {
              console.log(`Output from node: '${key}'`);
              console.log("JSON.stringify", JSON.stringify(value, null, 2));
              finalState = value;

              if (key === '__end__' && Array.isArray(value) && value.length > 1) {
                value.forEach(item => {            
                  const tokenUsage = item.response_metadata?.tokenUsage;
                  if (tokenUsage) {
                    //allTokenUsages.push(tokenUsage);
                    //console.log("==allTokenUsages==: ", allTokenUsages);
                    sumTokenUsages.completionTokens += tokenUsage.completionTokens;
                    sumTokenUsages.promptTokens += tokenUsage.promptTokens;
                    sumTokenUsages.totalTokens += tokenUsage.totalTokens;
                  }
                });
                const lastOutput = value[value.length - 1];
                console.log("lastOutput: ", lastOutput)
                if (lastOutput && lastOutput?.content) {
                  finalGeneration = lastOutput.content;
                } else if (lastOutput) {
                  finalGeneration = lastOutput;
                }
              } //else if (key === 'generate' && Array.isArray(value) && value.length > 0) {
                  //finalGeneration = value[0];
              //}
              
              if (finalGeneration) {
                //console.log("finalGeneration: ", finalGeneration);
                controller.enqueue(textEncoder.encode(finalGeneration)); // Enqueue the finalGeneration to the stream
                //finalGeneration = undefined; // Reset finalGeneration after enqueuing
              }
            }
          }
        } catch (error) {
          console.error(error);
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
        if (
          sumTokenUsages &&
          typeof sumTokenUsages.promptTokens === 'number' &&
          typeof sumTokenUsages.completionTokens === 'number'
        ) {
          console.log("====tokenUsages====: ", sumTokenUsages);
          await addTransaction({
            userApiCount: 1,
            promptTokens: sumTokenUsages.promptTokens,
            completionTokens: sumTokenUsages.completionTokens,
            model: 'gpt_4o',
            userId,
            notes: `Token count`,
          });
        }

        if (promptMessage && finalGeneration && finalGeneration.length > 0) {
          console.log("====saveCompletionToDatabase====");
          if (existingThread) {
            await saveCompletionToDatabase(companion?.id!, userId, promptMessage, "user", threadId);
          } else {
            const name = promptMessage.substring(0, 30);
            await newThread({
              threadId,
              name,
              companionId: companion?.id!,
              content: promptMessage,
              role: "user",
            });
          }
    
          await saveCompletionToDatabase(companion?.id!, userId, finalGeneration, "assistant", threadId);
        }
        revalidatePath(path);
        controller.close();
      }
    });

    return new StreamingTextResponse(clientStream)
  } catch (e: any) {
    console.error(e);
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}
