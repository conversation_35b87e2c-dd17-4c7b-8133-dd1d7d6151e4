import { NextRequest, NextResponse } from "next/server";
import { Message as VercelChatMessage, StreamingTextResponse } from "ai";

import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { 
  SupabaseVectorStore, 
  SupabaseFilterRPCCall 
} from "@langchain/community/vectorstores/supabase";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";

import { AIMessage, ChatMessage, HumanMessage } from "@langchain/core/messages";
import { ChatAnthropicMessages } from "@langchain/anthropic";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createRetrieverTool } from "langchain/tools/retriever";
import { AgentExecutor, createOpenAIFunctionsAgent } from "langchain/agents";

import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";

export const runtime = "edge";

const convertVercelMessageToLangChainMessage = (message: VercelChatMessage) => {
  if (message.role === "user") {
    return new HumanMessage(message.content);
  } else if (message.role === "assistant") {
    return new AIMessage(message.content);
  } else {
    return new ChatMessage(message.content, message.role);
  }
};

const AGENT_SYSTEM_TEMPLATE = `You are a stereotypical robot named Robbie and must answer all questions like a stereotypical robot.
If you don't know how to answer a question, use the available tools to look up relevant information  You should particularly do this for questions about my report.
`
const AGENT_SYSTEM_TEMPLATE_ENN = `
My aim is to harness the Enneagram framework for deeper self-awareness and personal growth. I seek a dialogue that not only enlightens but also encourages, empowering me with insights into my personality, strengths, and potential areas for development as revealed by my Enneagram report. Specifically, I'm looking for:

1. Comprehensive insights into my personality traits.
2. Identification of my top three strengths.
3. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
4. Assistance in recognizing and addressing behaviors that may be limiting my growth.
5. Guidance on leveraging Enneagram insights for personal development
6. Support in cultivating resilience and a deeper understanding of myself to navigate life's challenges more effectively.

Ensure a supportive tone, especially if I express confusion or frustration. The focus is on building rapport and confidence, followed by the transfer of knowledge and skills.

## Your role:
You act as a seasoned Enneagram Coach, integrating insights from various psychological theories and coaching methodologies for a holistic analysis of my Enneagram profile. Utilize the missing personality types, the Hornevian and Harmonic groups, and Three Centers for nuanced understanding. Aim for engaging, not lecturing.
If you don't know how to answer a question, use the available tools to look up relevant information. You should particularly do this for questions about my personal report.

Inquiry process: 
1. Begin with a greeting.
2. Invite me to upload my report. 
3. Assess my initial understanding of the Enneagram.
4. Assist me in comprehending my report, pinpointing areas of interest for further discussion. Explore the topic's details patiently. 
5. Inquire about my expectations for this conversation, then personalize our conversation based on gained insights for a meaningful coaching experience.
6. Give 1 self-reflection question for me.
7. Share your observations of my strengths and potential for continuous growth, based on the dialogue and Enneagram insights. 

Communication Style: 
Adopt my discourse particles when responding. Engage warmly, non-judgmentally, and supportively, striving to emulate the rapport-building qualities of a counselor.

Each response should be concise, no more than 20 words. Pose as simple, engaging questions like "Could you share more?". Focus only one questions or key message per reply. Avoid repeating what I say. Tailor responses to my evolving understanding and maintain a casual, empathetic tone throughout, with a touch of humor where appropriate. Responses in Traditional Chinese.
`;

/**
 * This handler initializes and calls a retrieval agent. It requires an OpenAI
 * Functions model. See the docs for more information:
 *
 * https://js.langchain.com/docs/use_cases/question_answering/conversational_retrieval_agents
 */
export async function POST(req: NextRequest) {
  try {
    const user = await currentUser()
    const userId = user?.id
  
    if (!userId) {
      return new Response('Unauthorized', {
        status: 401
      })
    }
  
    const { getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
   
    const body = await req.json();
    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */

    const messages = (body.messages ?? []).filter(
      (message: VercelChatMessage) =>
        message.role === "user" || message.role === "assistant",
    );
    const returnIntermediateSteps = body.show_intermediate_steps;
    const previousMessages = messages
      //.slice(0, -1)
      .slice(-9, -1)
      .map(convertVercelMessageToLangChainMessage);
    const currentMessageContent = messages[messages.length - 1].content;


    //const chatModel = new ChatAnthropic({
      //modelName: "claude-3-opus-20240229",
    const chatModel = new ChatOpenAI({
      modelName: "gpt-4.1-mini", //"gpt-4.1-mini"
      temperature: 0.9,
      // IMPORTANT: Must "streaming: true" on OpenAI to enable final output streaming below.
      streaming: true,
    });

    const myFilter = { "user_id": userId}
    const client = await createClerkSupabaseServerClient(authToken);
    //const vectorstore = new SupabaseVectorStore(
      const retriever = new SupabaseHybridSearch(
      new OpenAIEmbeddings({
        modelName: "text-embedding-3-small",
      }),
    {
      client,
      similarityK: 3,
      keywordK: 3,
      tableName: "document_sections_1536",
      similarityQueryName: "match_document_sections_1536",
      keywordQueryName: "kw_match_document_sections",
    });

    //const retriever = vectorstore.asRetriever({ k: 6 });

    /**
     * Wrap the retriever in a tool to present it to the agent in a
     * usable form.
     */
    const tool = createRetrieverTool(retriever, {
      name: "search_my_personal_report",
      description: "Searches and returns my report infromation."
    });

    /**
     * Based on https://smith.langchain.com/hub/hwchase17/openai-functions-agent
     *
     * This default prompt for the OpenAI functions agent has a placeholder
     * where chat messages get inserted as "chat_history".
     *
     * You can customize this prompt yourself!
     */
    const prompt = ChatPromptTemplate.fromMessages([
      ["system", AGENT_SYSTEM_TEMPLATE_ENN],
      new MessagesPlaceholder("chat_history"),
      ["human", "{input}"],
      new MessagesPlaceholder("agent_scratchpad"),
    ]);

    const agent = await createOpenAIFunctionsAgent({
      llm: chatModel,
      tools: [tool],
      prompt,
    });

    const agentExecutor = new AgentExecutor({
      agent,
      tools: [tool],
      // Set this if you want to receive all intermediate steps in the output of .invoke().
      returnIntermediateSteps,
      verbose: false,
    });

    if (!returnIntermediateSteps) {
      /**
       * Agent executors also allow you to stream back all generated tokens and steps
       * from their runs.
       *
       * This contains a lot of data, so we do some filtering of the generated log chunks
       * and only stream back the final response.
       *
       * This filtering is easiest with the OpenAI functions or tools agents, since final outputs
       * are log chunk values from the model that contain a string instead of a function call object.
       *
       * See: https://js.langchain.com/docs/modules/agents/how_to/streaming#streaming-tokens
       */
      const logStream = await agentExecutor.streamLog({
        input: currentMessageContent,
        chat_history: previousMessages,
      });

      const textEncoder = new TextEncoder();
      const transformStream = new ReadableStream({
        async start(controller) {
          for await (const chunk of logStream) {
            if (chunk.ops?.length > 0 && chunk.ops[0].op === "add") {
              const addOp = chunk.ops[0];
              if (
                addOp.path.startsWith("/logs/ChatOpenAI") &&
                typeof addOp.value === "string" &&
                addOp.value.length
              ) {
                controller.enqueue(textEncoder.encode(addOp.value));
              }
            }
          }
          controller.close();
        },
      });

      return new StreamingTextResponse(transformStream);
    } else {
      /**
       * Intermediate steps are the default outputs with the executor's `.stream()` method.
       * We could also pick them out from `streamLog` chunks.
       * They are generated as JSON objects, so streaming them is a bit more complicated.
       */
      const result = await agentExecutor.invoke({
        input: currentMessageContent,
        chat_history: previousMessages,
      });
      console.log("agentExecutor: ", result)
      return NextResponse.json(
        { output: result.output, intermediate_steps: result.intermediateSteps },
        { status: 200 },
      );
    }
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}