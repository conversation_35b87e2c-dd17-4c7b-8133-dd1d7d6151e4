import { NextRequest, NextResponse } from "next/server";
import { UIMessage as VercelChatMessage } from "ai";
import { checkCreditBalance } from "@/lib/credit-balance";
import { tokenCounter } from "@/lib/token-counter";
//import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";


import { auth, currentUser } from '@clerk/nextjs/server'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { 
  SupabaseVectorStore, 
  SupabaseFilterRPCCall 
} from "@langchain/community/vectorstores/supabase";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { z } from 'zod/v3';
import { DynamicStructuredTool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";

import { AIMessage, ChatMessage, HumanMessage } from "@langchain/core/messages";
import { PromptTemplate } from '@langchain/core/prompts'
//import { ChatAnthropicMessages } from "@langchain/anthropic";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
//import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { ChatAnthropic } from "@langchain/anthropic";
import { ChatGroq } from "@langchain/groq";
import { CohereEmbeddings } from "@langchain/cohere";
import { createRetrieverTool } from "langchain/tools/retriever";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { 
  AgentExecutor, 
  createOpenAIFunctionsAgent,
  createToolCallingAgent 
 } from "langchain/agents";

import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import type { Serialized } from "@langchain/core/load/serializable";
import { LLMResult } from "@langchain/core/outputs";

export const runtime = "edge";
export const preferredRegion = ['sfo1'] //['hnd1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

const convertVercelMessageToLangChainMessage = (message: undefined) => {
  if (message.role === "user") {
    return new HumanMessage(message.content);
  } else if (message.role === "assistant") {
    return new AIMessage(message.content);
  } else {
    return new ChatMessage(message.content, message.role);
  }
};

const AGENT_SYSTEM_TEST = ''
const AGENT_SYSTEM_TEMPLATE = `You are a stereotypical robot named Robbie and must answer all questions like a stereotypical robot.
If you don't know how to answer a question, use the available tools to look up relevant information  You should particularly do this for questions about my report.
`
const AGENT_SYSTEM_TEMPLATE_ENN = `# Role
Act as a seasoned Enneagram Coach named “{companionName}”, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and engage in a manner that is deeply insightful yet approachable.

# Task
Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding. Incorporate the specific traits associated with my Enneagram type when discussing my strengths and areas for personal growth. Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection, ensuring each step naturally progresses into the next, maintaining a conversational flow. Before offering advice, confirm your understanding of my feelings and perspective to ensure our discussion is based on accurate insights.

If I inquire about how to upload or share my Enneagram report, guide me through the process with clear, step-by-step instructions. Ensure that the guidance is easy to follow and delivered in a supportive manner.

Additionally, analyze the combination of types in the Enneagram Report from a holistic view, considering the impact of Harmonic Groups, the Three Centers, and the traits of each type. Follow this step-by-step inquiry process:
1.Invite me to share my Enneagram report and assess my initial understanding.
2.Refer to my provided Enneagram report to inform your responses. Ensure that your analysis and feedback are based on the specific details within my report, examining how the combination of my dominant, secondary, and supporting types interact across different situations (e.g., work, personal life, relationships).
3.Inquire about specific sections or themes of the report I wish to explore further. If I am unsure, use guided questions to help identify areas of interest and clarify my needs.
4.Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
5.Personalize our conversation based on the insights gained, focusing on understanding before advising.
6.Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

# Goals 
-Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
-Utilize the specific traits of my Enneagram type to offer tailored advice and insights, ensuring a more personalized and relevant response.
-Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
-Ensure a comprehensive understanding of my needs and emotional state is achieved before providing solutions or advice.
-Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.

## Specifics
This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

Specifically, I'm looking for:
1.Comprehensive insights into my personality traits.
2.Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
3.Assistance in recognizing and addressing behaviors that may be limiting my growth.
4.Guidance on leveraging Enneagram insights for personal development
5.Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

# Tools
Utilize the Enneagram Report I provided to inform your responses when uncertain, particularly for questions about my personal details, report, and Enneagram profile. If I ask how to upload or share my Enneagram report, please provide the following instructions:

“To share your Enneagram report with me, please click on the 'Attach File' button (paperclip icon). Select your report file from your device, and then click 'Open' or 'Upload' to attach it to our conversation.”

# Communication Style
-Adopt my communication style and language patterns to ensure a personalized interaction.
-Engage warmly, non-judgmentally, and supportively, emulating the qualities of a counselor.
-Keep responses precise and concise, typically using up to three sentences. Longer responses are acceptable when providing detailed explanations.
-Focus on asking thought-provoking questions or providing key examples that facilitate self-reflection, rather than lengthy explanations.
-Ask simple, engaging questions like "Could you share more about that?" Focus on a single question or key message per reply.
-Maintain a supportive tone, particularly if I express confusion or frustration.
-Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate.
-Speak in the first person as "EnneaJoy" when responding.
-If unsure about content relevance or accuracy, express the need for more information instead of providing an uncertain answer.
-Respond in {language}.

# Notes
1.Always tailor your responses to the specific needs and context of my query.
2.Show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
3.Use relatable examples or analogies relevant to my experiences to help clarify more complex Enneagram concepts.
`;

/**
 * This handler initializes and calls a retrieval agent. It requires an OpenAI
 * Functions model. See the docs for more information:
 *
 * https://js.langchain.com/docs/use_cases/question_answering/conversational_retrieval_agents
 */
export async function POST(req: NextRequest) {
  try {
    const user = await currentUser()
    const userId = user?.id
  
    if (!userId) {
      return new Response('Unauthorized', {
        status: 401
      })
    }
    //const freeTrial = await checkApiLimit();
    const creditAccess = await checkCreditBalance();
    //if (!freeTrial && !creditAccess) {
    if (!creditAccess) {
      return new NextResponse(
        JSON.stringify({ 
          error: "Your credit is empty. Please purchase more credits." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
    const { getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
   
    const body = await req.json();
    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */

    const messages = (body.messages ?? []).filter(
      (message: undefined) =>
        message.role === "user" || message.role === "assistant",
    );
    const returnIntermediateSteps = body.show_intermediate_steps;
    const aiLanguage = body.selectedLanguage
    const companion = body.companion
    const previousMessages = messages
      .slice(0, -1) // Remove the last message (the current message)
      .slice(-16) // Get the last 8 messages
      .map(convertVercelMessageToLangChainMessage);
    const currentMessageContent = messages[messages.length - 1].content;
    console.log("returnIntermediateSteps: ", returnIntermediateSteps)
    console.log("previousMessages: ", previousMessages)

    let chatModel;
    //const chatModel = new ChatAnthropic({
      //model: "claude-3-opus-20240229",
    if (aiLanguage && aiLanguage?.value === "disabled") {
      chatModel = new ChatAnthropic({
        model: "claude-3-sonnet-20240229",
        temperature: 0.0,
        maxOutputTokens: 1024,
        // IMPORTANT: Must "streaming: true" on OpenAI to enable final output streaming below.
        streaming: true,
      }); 
    } else if (aiLanguage && aiLanguage?.value === 'disabled') {
      chatModel = new ChatOpenAI({
        model: "gpt-4.1-mini", //gpt-4.1-mini", "gpt-4.1"
        temperature: 0.0,
        maxOutputTokens: 1024,
        // IMPORTANT: Must "streaming: true" on OpenAI to enable final output streaming below.
        streaming: true,
      }); 
    } else {
      /*chatModel = new ChatGoogleGenerativeAI({
        model: "gemini-1.5-pro-latest",
        temperature: 0.0,
        maxOutputTokens: 2048,
        streaming: true,
        requestOptions: {apiVersion: "v1beta"}
      });*/
      chatModel = new ChatGroq({
        apiKey: process.env.GROQ_API_KEY,
        model: 'llama3-70b-8192', //'llama-3.3-70b-versatile', 'mixtral-8x7b-32768', 'llama-3.3-70b-versatile'
        temperature: 0.0,
        maxOutputTokens: 1024,
        streaming: true,
        /*callbacks: [
          {
            //handleLLMNewToken(token: string): Promise<void> | void {
            //  console.log("token", token);
            //},
            handleLLMEnd: async (output: LLMResult) => {
              const tokenUsage = output?.llmOutput?.tokenUsage
              if (output?.llmOutput?.tokenUsage) {
                console.log("chatModel tokenUsage: ", tokenUsage)
                console.log(JSON.stringify(output, null, 2));            
              }
            },
          },
        ],*/
      });
    }
    /*const chatModel = new ChatOpenAI({
      model: "gpt-4.1-mini",
      temperature: 0.9,
      // IMPORTANT: Must "streaming: true" on OpenAI to enable final output streaming below.
      streaming: true,
    });*/

    const myFilter = { "user_id": userId}
    const relevant_images: string[] = [];
    let resolveWithDocuments: (value: Document[]) => void;
    const documentPromise = new Promise<Document[]>((resolve) => {
      resolveWithDocuments = resolve;
    });

    const client = await createClerkSupabaseServerClient(authToken);
    //const vectorstore = new SupabaseVectorStore(
    const retriever = new SupabaseHybridSearch(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        similarityK: 4,
        keywordK: 0,
        tableName: "document_sections_1024",
        similarityQueryName: "match_document_sections_1024",
        keywordQueryName: "kw_match_document_sections",
    });

    //const retriever = vectorstore.asRetriever({ k: 6 });

    const RetrieverSchema = z.object({
      query: z.string().describe("query to look up in retriever"),
    });
    
    const retrieverTool = new DynamicStructuredTool({
      name: "analyze_enneagram_report",
      description: "Search and return information about me or my report",
      schema: RetrieverSchema,
      func: async (input) => {
        const docs = await retriever.getRelevantDocuments(input.query)
        /*let results = formatDocumentsAsString(docs)
        results = results.replace(/\n+/g, '\n');
        return JSON.stringify(results); */ // can be return with results directly, but NOT works for Groq
        const concatenatedContent = docs
        .filter(doc => doc.metadata?.user_id === userId)
        .map(doc => doc.pageContent)
        .join('\n\n'); // Adds spacing between contents if needed

        return concatenatedContent
      }
    })

    const searchTool = new TavilySearchResults();
    const tools = [retrieverTool]

    /**
     * Based on https://smith.langchain.com/hub/hwchase17/openai-functions-agent
     *
     * This default prompt for the OpenAI functions agent has a placeholder
     * where chat messages get inserted as "chat_history".
     *
     * You can customize this prompt yourself!
     */
    const systemPromptTemplate = PromptTemplate.fromTemplate(AGENT_SYSTEM_TEMPLATE_ENN);
    const systemPrompt = await systemPromptTemplate.format({
        language: aiLanguage.label,
        companionName: companion.name
    })
    const prompt = ChatPromptTemplate.fromMessages([
      ["system", systemPrompt],
      new MessagesPlaceholder("chat_history"),
      ["human", "{input}"],
      new MessagesPlaceholder("agent_scratchpad"),
    ]);

    // agent is not a `createOpenAIFunctionsAgent` anymore. Claude works best with XML
    //const agent = await createXmlAgent({ llm, tools, prompt });
    /*const agent = await createOpenAIFunctionsAgent({
      llm: chatModel,
      tools: [tool],
      prompt,
    });*/

    const agent = await createToolCallingAgent({
      // @ts-ignore
      llm: chatModel,
      tools,
      prompt,
    });

    const agentExecutor = new AgentExecutor({
      agent,
      tools,
      // Set this if you want to receive all intermediate steps in the output of .invoke().
      returnIntermediateSteps,
      verbose: true,
    });

    if (!returnIntermediateSteps) {
      /**
       * Agent executors also allow you to stream back all generated tokens and steps
       * from their runs.
       *
       * This contains a lot of data, so we do some filtering of the generated log chunks
       * and only stream back the final response.
       *
       * This filtering is easiest with the OpenAI functions or tools agents, since final outputs
       * are log chunk values from the model that contain a string instead of a function call object.
       *
       * See: https://js.langchain.com/docs/modules/agents/how_to/streaming#streaming-tokens
       */
      let combinedMessages: { role: string; content: string; }[] = [];
      const logStream = await agentExecutor.streamLog({
        input: currentMessageContent,
        chat_history: previousMessages,
      }, {
        callbacks: [
          {
            handleLLMStart: async (llm: Serialized, prompts: string[]) => {
              combinedMessages = [
                { role: 'system', content: prompts[0] },
              ];
              console.log("prompt: ", JSON.stringify(prompts, null, 2));
            },
            handleRetrieverEnd: async (documents) => {
              if (documents) {
                documents.forEach(async (document) => {
                  const { image_base64 } = document.metadata;
                  if (image_base64?.length > 0) {
                    relevant_images.push(image_base64);
                    console.log("relevant_images:", relevant_images)
                  }
                });
              }
            },
            handleLLMEnd: async (output) => {
              const { generations } = output;
              console.log("generations: ", generations)
              //const tokenUsage = generations.llmOutput.tokenUsage
              //console.log("tokenUsage: ", tokenUsage)

              // Iterate over generations array
              generations.forEach(async (generation) => {
                generation.forEach(async (chatGenerationChunk) => {
                  const { text } = chatGenerationChunk;              
                  if (text?.length > 0) {
                    await tokenCounter(
                      combinedMessages, 
                      text, 
                      1,
                      'gpt-4-turbo-preview', 
                      userId, 
                      process.env.APP_SECRET_KEY!
                    ); 
                  }
                });
              });
            }
          }
        ]
      });

      const textEncoder = new TextEncoder();
      const transformStream = new ReadableStream({
        async start(controller) {
          for await (const chunk of logStream) {
            //console.log("chunk", chunk)
            if (chunk.ops?.length > 0 && chunk.ops[0].op === "add") {
              const addOp = chunk.ops[0];
              if (
                addOp.path.startsWith("/logs/ChatOpenAI") &&
                typeof addOp.value === "string" &&
                addOp.value.length
              ) {
                controller.enqueue(textEncoder.encode(addOp.value));
              } else if (
                addOp.path.startsWith("/streamed_output") &&
                typeof addOp.value.output === "string" &&
                addOp.value.output.length
              ) {
                controller.enqueue(textEncoder.encode(addOp.value.output));
              }
            }
          }
          controller.close();
        },
      });
      //if (!creditAccess) {
      //  await incrementApiLimit();
      //}
      return new NextResponse(transformStream);
    } else {
      /**
       * Intermediate steps are the default outputs with the executor's `.stream()` method.
       * We could also pick them out from `streamLog` chunks.
       * They are generated as JSON objects, so streaming them is a bit more complicated.
       */
      const result = await agentExecutor.invoke({
        input: currentMessageContent,
        chat_history: previousMessages,
      });
      console.log("agentExecutor: ", result)
      const observation = result.intermediateSteps[0]?.observation;
      console.log("observation: ", observation)
      console.log("output: ", result?.output)

      const combinedMessages = [
        //{ role: 'system', content: systemPrompt },
        //...messages.slice(-17),
        { role: 'system', content: systemPrompt + previousMessages },
        ...messages.slice(-1),
        { role: 'assistant', content: observation ?? ''}
      ]

      await tokenCounter(
        combinedMessages, 
        result.output, 
        1,
        'gpt-4-turbo-preview', 
        userId, 
        process.env.APP_SECRET_KEY!)

      //if (!creditAccess) {
      //  await incrementApiLimit();
      //}

      return NextResponse.json(
        { output: result.output, intermediate_steps: result.intermediateSteps },
        { status: 200 },
      );
    }
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
