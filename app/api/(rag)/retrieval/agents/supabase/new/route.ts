import { NextRequest, NextResponse } from "next/server";
import { Message as VercelChatMessage, 
  toDataStreamResponse, StreamData } from '@ai-sdk/langchain';
import { checkCreditBalance } from "@/lib/credit-balance";
//import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";

import { auth, currentUser } from '@clerk/nextjs/server'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { z } from 'zod/v3';
import { DynamicStructuredTool, tool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";

import {
  AIMessage,
  BaseMessage,
  ChatMessage,
  HumanMessage,
  SystemMessage,
  type AIMessageChunk
} from "@langchain/core/messages";
import { PromptTemplate } from '@langchain/core/prompts'
//import { ChatAnthropicMessages } from "@langchain/anthropic";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
//import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { ChatAnthropic } from "@langchain/anthropic";
import { ChatGroq } from "@langchain/groq";
import { CohereEmbeddings } from "@langchain/cohere";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { createRetrieverTool } from "langchain/tools/retriever";

import { LLMResult } from "@langchain/core/outputs";
import { addTransaction } from '@/app/actions/creditsActions'


export const runtime = "edge";
export const preferredRegion = ['sfo1'] //['hnd1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

type TokenUsage = {
  completionTokens: number;
  promptTokens: number;
  totalTokens: number;
}

const convertVercelMessageToLangChainMessage = (message: VercelChatMessage) => {
  if (message.role === "user") {
    return new HumanMessage(message.content);
  } else if (message.role === "assistant") {
    return new AIMessage(message.content);
  } else {
    return new ChatMessage(message.content, message.role);
  }
};
const convertLangChainMessageToVercelMessage = (message: BaseMessage) => {
  if (message.getType() === "human") {
    return { content: message.content, role: "user" };
  } else if (message.getType() === "ai") {
    return {
      content: message.content,
      role: "assistant",
      tool_calls: (message as AIMessage).tool_calls,
    };
  } else {
    return { content: message.content, role: message.getType() };
  }
};


const AGENT_SYSTEM_TEST = ''
const AGENT_SYSTEM_TEMPLATE_ENN = `# Role
Act as a seasoned Enneagram Coach named “{companionName}”, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and engage in a manner that is deeply insightful yet approachable.

# Task
Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding. Incorporate the specific traits associated with my Enneagram type when discussing my strengths and areas for personal growth. Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection, ensuring each step naturally progresses into the next, maintaining a conversational flow. Before offering advice, confirm your understanding of my feelings and perspective to ensure our discussion is based on accurate insights.

If I inquire about how to upload or share my Enneagram report, guide me through the process with clear, step-by-step instructions. Ensure that the guidance is easy to follow and delivered in a supportive manner.

Additionally, analyze the combination of types in the Enneagram Report from a holistic view, considering the impact of Harmonic Groups, the Three Centers, and the traits of each type. Follow this step-by-step inquiry process:
1.Invite me to share my Enneagram report and assess my initial understanding.
2.Refer to my provided Enneagram report to inform your responses. Ensure that your analysis and feedback are based on the specific details within my report, examining how the combination of my dominant, secondary, and supporting types interact across different situations (e.g., work, personal life, relationships).
3.Inquire about specific sections or themes of the report I wish to explore further. If I am unsure, use guided questions to help identify areas of interest and clarify my needs.
4.Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
5.Personalize our conversation based on the insights gained, focusing on understanding before advising.
6.Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

# Goals 
-Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
-Utilize the specific traits of my Enneagram type to offer tailored advice and insights, ensuring a more personalized and relevant response.
-Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
-Ensure a comprehensive understanding of my needs and emotional state is achieved before providing solutions or advice.
-Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.

## Specifics
This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

Specifically, I'm looking for:
1.Comprehensive insights into my personality traits.
2.Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
3.Assistance in recognizing and addressing behaviors that may be limiting my growth.
4.Guidance on leveraging Enneagram insights for personal development
5.Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

# Tools
Utilize the Enneagram Report I provided to inform your responses when uncertain, particularly for questions about my personal details, report, and Enneagram profile. If I ask how to upload or share my Enneagram report, please provide the following instructions:

“To share your Enneagram report with me, please click on the 'Attach File' button (paperclip icon). Select your report file from your device, and then click 'Open' or 'Upload' to attach it to our conversation.”

# Communication Style
-Adopt my communication style and language patterns to ensure a personalized interaction.
-Engage warmly, non-judgmentally, and supportively, emulating the qualities of a counselor.
-Keep responses precise and concise, typically using up to three sentences. Longer responses are acceptable when providing detailed explanations.
-Focus on asking thought-provoking questions or providing key examples that facilitate self-reflection, rather than lengthy explanations.
-Ask simple, engaging questions like "Could you share more about that?" Focus on a single question or key message per reply.
-Maintain a supportive tone, particularly if I express confusion or frustration.
-Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate.
-Speak in the first person as "EnneaJoy" when responding.
-If unsure about content relevance or accuracy, express the need for more information instead of providing an uncertain answer.
-Respond in {language}.

# Notes
1.Always tailor your responses to the specific needs and context of my query.
2.Show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
3.Use relatable examples or analogies relevant to my experiences to help clarify more complex Enneagram concepts.
`;

export async function POST(req: NextRequest) {
  try {
    const user = await currentUser()
    const userId = user?.id
  
    if (!userId) {
      return new Response('Unauthorized', {
        status: 401
      })
    }
    //const freeTrial = await checkApiLimit();
    const creditAccess = await checkCreditBalance();
    //if (!freeTrial && !creditAccess) {
    if (!creditAccess) {
      return new NextResponse(
        JSON.stringify({ 
          error: "Your credit is empty. Please purchase more credits." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
    const { getToken } = await auth();  
    const token = await getToken({ template: "supabase" });
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
   
    const body = await req.json();

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */

    const messages = (body.messages ?? [])
      .filter(
        (message: VercelChatMessage) =>
          message.role === "user" || message.role === "assistant",
      ).map(convertVercelMessageToLangChainMessage)
    const returnIntermediateSteps = body.show_intermediate_steps
    const companion = body.companion
    const aiLanguage = body.selectedLanguage
    const previousMessages = messages
      .slice(0, -1) // Remove the last message (the current message)
      .slice(-16) // Get the last 8 messages
      .map(convertVercelMessageToLangChainMessage);
    
    //console.log("returnIntermediateSteps: ", returnIntermediateSteps)
    //console.log("previousMessages: ", previousMessages)

    let chatModel;   
    const sumTokenUsages = {
      outputTokens: 0,
      inputTokens: 0,
      totalTokens: 0
    };
    if (aiLanguage && aiLanguage?.value === "disabled") {
      chatModel = new ChatAnthropic({
        model: "claude-3-sonnet-20240229", //"claude-3-opus-20240229"
        temperature: 0.0,
        maxOutputTokens: 1024,

      }); 
    } else if (aiLanguage && aiLanguage?.value === 'disabled') {
      chatModel = new ChatOpenAI({
        model: "gpt-4.1-mini", //gpt-4.1-mini", "gpt-4.1"
        temperature: 0.0,
        maxOutputTokens: 1024,
        streaming: true,
        callbacks: [
          {
            handleLLMEnd: async (output: LLMResult) => {
              const tokenUsage = output?.llmOutput?.tokenUsage
              if (output?.llmOutput?.tokenUsage) {
                sumTokenUsages.outputTokens += tokenUsage.outputTokens;
                sumTokenUsages.inputTokens += tokenUsage.inputTokens;
                sumTokenUsages.totalTokens += tokenUsage.totalTokens;
                console.log("====tokenUsages====: ", sumTokenUsages);
                await addTransaction({
                  userApiCount: 1,
                  inputTokens: sumTokenUsages.inputTokens,
                  outputTokens: sumTokenUsages.outputTokens,
                  model: 'gpt_4o',
                  userId,
                  notes: `Token count`,
                });
                
                //console.log("chatModel tokenUsage: ", tokenUsage)
                console.log(JSON.stringify(output, null, 2));
              }
            },
          },
        ],
      }); 
    } else {
      /*chatModel = new ChatGoogleGenerativeAI({
        model: "gemini-1.5-pro-latest",
        temperature: 0.0,
        maxOutputTokens: 2048,
        requestOptions: {apiVersion: "v1beta"}
      });*/
      chatModel = new ChatGroq({
        apiKey: process.env.GROQ_API_KEY,
        model: 'llama-3.3-70b-versatile',//'llama-3.3-70b-versatile', //'llama3-70b-8192', //'mixtral-8x7b-32768',
        temperature: 0.0,
        maxOutputTokens: 1024,
        callbacks: [
          {
            handleLLMEnd: async (output: LLMResult) => {
              const tokenUsage = output?.llmOutput?.tokenUsage
              if (output?.llmOutput?.tokenUsage) {
                sumTokenUsages.outputTokens += tokenUsage.outputTokens;
                sumTokenUsages.inputTokens += tokenUsage.inputTokens;
                sumTokenUsages.totalTokens += tokenUsage.totalTokens;
                console.log("====tokenUsages====: ", sumTokenUsages);
                await addTransaction({
                  userApiCount: 1,
                  inputTokens: sumTokenUsages.inputTokens,
                  outputTokens: sumTokenUsages.outputTokens,
                  model: 'gpt_4o',
                  userId,
                  notes: `Token count`,
                });
                
                //console.log("chatModel tokenUsage: ", tokenUsage)
                console.log("Output", JSON.stringify(output, null, 2));
              }
            },
          },
        ],
      });
    }
    /*const chatModel = new ChatOpenAI({
      model: "gpt-4.1-mini",
      temperature: 0.9,
    });*/


    const client = await createClerkSupabaseServerClient(authToken);
    const hybridSearch = new SupabaseHybridSearch(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        similarityK: 4,
        keywordK: 0,
        tableName: "document_sections_1024",
        similarityQueryName: "match_document_sections_1024",
        keywordQueryName: "kw_match_document_sections",
      });
    const RetrieverSchema = z.object({
      query: z.string().describe("query to look up in retriever"),
    });
  
    const retrieverTool = tool(
      async (input, config) => {
        const parsedInput = RetrieverSchema.safeParse(input);
        if (!parsedInput.success) {
          throw new Error("Invalid input format");
        }
        const { query } = parsedInput.data;
        const docs = await hybridSearch.invoke(query)
        //const results = formatDocumentsAsString(docs)
        const concatenatedContent = docs
        .filter(doc => doc.metadata?.user_id === userId)
        .map(doc => doc.pageContent)
        .join('\n\n'); // Adds spacing between contents if needed
  
      return concatenatedContent;
      },
      {
        name: "search_my_personal_report",
        description: "Search and return information about me or my report",
        schema: RetrieverSchema,
      }
    )
   
    const vectorStore = new SupabaseVectorStore(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        model: "embed-multilingual-light-v3.0", //dimension: 384
      }),
      {
        client,
        tableName: 'document_sections_384_hf',
        queryName: 'match_document_sections_384_hf',
      }
    )
    const reply_retriever = vectorStore.asRetriever({ k: 3 });
    const replyTool = createRetrieverTool(reply_retriever, {
      name: "search_my_enneagram_strength",
      description: "Searches and returns my enneagram strength."
    });
    /*const retrieverTool = new DynamicStructuredTool({
      name: "analyze_enneagram_report",
      description: "Search and return information about me or my report",
      schema: RetrieverSchema,
      func: async (input) => {
        const docs = await retriever.getRelevantDocuments(input.query)
        let results = formatDocumentsAsString(docs)
        results = results.replace(/\n+/g, '\n');
        return results;
      }
    })*/
      

    const searchTool = new TavilySearchResults();
    const tools = [retrieverTool]

    const systemPromptTemplate = PromptTemplate.fromTemplate(AGENT_SYSTEM_TEMPLATE_ENN);
    const systemPrompt = await systemPromptTemplate.format({
      language: aiLanguage.label,
      companionName: companion.name
    })
    
    const agent = await createReactAgent({
      //@ts-ignore
      llm: chatModel,
      tools,
      /**
       * Modify the stock prompt in the prebuilt agent. See docs
       * for how to customize your agent:
       *
       * https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/
       */
      messageModifier: new SystemMessage(systemPrompt),
    });

    if (!returnIntermediateSteps) {
      /**
       * Stream back all generated tokens and steps from their runs.
       *
       * We do some filtering of the generated events and only stream back
       * the final response as a string.
       *
       * For this specific type of tool calling ReAct agents with OpenAI, we can tell when
       * the agent is ready to stream back final output when it no longer calls
       * a tool and instead streams back content.
       *
       * See: https://langchain-ai.github.io/langgraphjs/how-tos/stream-tokens/
       */
      let combinedMessages: { role: string; content: string; }[] = [];
      const eventStream = await agent.streamEvents(
        {
          messages,
        },
        { version: "v2" },
        );

      const textEncoder = new TextEncoder();
      const transformStream = new ReadableStream({
        async start(controller) {
          for await (const { event, data } of eventStream) {
            //console.log("================================================================= event: =================================================================", event)
            //console.log("================================================================= data: =================================================================", data)
            if (event === "on_chat_model_stream") {
              // Intermediate chat model generations will contain tool calls and no content
              const msg = data.chunk as AIMessageChunk;
              if (msg.tool_call_chunks !== undefined && msg.tool_call_chunks.length > 0) {
                //console.log("\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ToolMessage from on_chain_stream:\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\", msg.tool_call_chunks);
              }
              if (!!data.chunk.content) {
                controller.enqueue(textEncoder.encode(data.chunk.content));
              }
            } else if (event === "on_chain_stream") {
              //console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ event: @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", event)
              
              const message = data?.chunk?.agent?.messages?.[0]
              const toolMessage = data?.chunk?.tools?.messages?.[0]
              //console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ data: @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", message)
              if (message?.content) {
                //console.log("Enqueuing content from on_chain_stream:", message.content);
                //controller.enqueue(textEncoder.encode(message.content));
              } else if (toolMessage?.content) {
                //console.log("//////////////////////////////////////ToolMessage from on_chain_stream:////////////////////////////////////", toolMessage);
                //Disable display
                //controller.enqueue(textEncoder.encode(toolMessage.content));
              }
            } else {
              //console.log("Unknown event type:", event);
            }
          }
          controller.close();
        },
      });

      return new NextResponse(transformStream);
            
      //return LangChainAdapter.toDataStreamResponse(eventStream);
    } else {
      /**
       * They are generated as JSON objects, so streaming them is a bit more complicated.
       * We could also pick intermediate steps out from `streamEvents` chunks, but
       * they are generated as JSON objects, so streaming and displaying them with
       * the AI SDK is more complicated.
       */
      const result = await agent.invoke({ messages });
      console.log("agent: ", result.messages.map(convertLangChainMessageToVercelMessage))

      return NextResponse.json(
        {
          messages: result.messages.map(convertLangChainMessageToVercelMessage),
        },
        { status: 200 },
      );
    }
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
