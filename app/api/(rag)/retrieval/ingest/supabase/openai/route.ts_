import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { CharacterTextSplitter, RecursiveCharacterTextSplitter } from "langchain/text_splitter";

import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { OpenAIEmbeddings } from "@langchain/openai";
import { URLDetailContent } from "@/app/client/fetch/url";
import { v4 as uuidv4 } from 'uuid'

export const runtime = "edge";

// Before running, follow set-up instructions at
// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 */

async function handleExtension(
  extension: string, 
  fileDetail: URLDetailContent, 
  userId: string, 
  client: any
) {
  const documentId: string = uuidv4()
  console.log("documentId: ", documentId)
  const res = await client
  .from("documents_1536")
  .insert({
    id: documentId,
    filename: fileDetail.url,
    type: fileDetail.type,
    size: fileDetail.size,
    owner_id: userId,
  });
  if (res.error) {
    throw new Error(
      `Error creating document: ${res.error.message} ${res.status} ${res.statusText}`
    );
  }
  let returnedIds: string[] = [];
  if (res.data) {
    returnedIds = returnedIds.concat(res.data.map((row: { id: string }) => row.id));
    console.log("res.data: ", res.data)
  }

  let splitter;

  if (extension === "pdf") {
     splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 256,
      chunkOverlap: 20,
      separators: ['\n\n', '\n', ' ', ''] // default setting
    });
  }
  if (extension === "txt") {
    splitter = new CharacterTextSplitter({
      separator: " ",
      chunkSize: 256,
      chunkOverlap: 20,
    });
  } else {
    const language = extension === "md" ? "markdown" : "html";
    splitter = RecursiveCharacterTextSplitter.fromLanguage(language, {
      chunkSize: 256,
      chunkOverlap: 20,
    });
  }

  const docsContent: string = fileDetail?.content!
  const splitDocuments = await splitter.createDocuments(
    [docsContent],
    [{  
      id: documentId,
      source: fileDetail.url,
      user_id: userId,
    }],
  );
  //console.log("splitDocuments: ", splitDocuments)

  // Add metadata to each document
  const documentsWithMetadata = splitDocuments.map((document, index) => {
    return {
      ...document,
      document_id: documentId,
      metadata: {
        ...document.metadata,
        //id: documentId,
        //source: fileDetail.url,
        //user_id: userId,
        index,
      },
    };
  });
  //console.log("documentsWithMetadata: ", documentsWithMetadata)
  const vectorstore = await SupabaseVectorStore.fromDocuments(
    documentsWithMetadata,
    new OpenAIEmbeddings({
      modelName: "text-embedding-3-small",
    }),
    {
      client,
      tableName: "document_sections_1536",
      queryName: "match_document_sections_1536",
    },
  );
}

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  };
  const { getToken } = await auth(); 
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const rateLimitResponse = await checkRateLimit(req);
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  }
  const {
    fileDetail,
  } = await req.json();
  // Get file extension
  const extension = fileDetail.url.split(".").pop();

  // Accept these file types
  // Markdown, Text, HTML
  if (!["md", "txt", "pdf", "html", "doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)) {
    return NextResponse.json(
      {
        error: [
          "File type not supported.",
          "Please upload a markdown, text, pdf, doc, docx, xls, xlsx, ppt, pptx or html file.",
        ].join("\n"),
      },
      { status: 403 },
    );
  }

  try {
    const client = await createClerkSupabaseServerClient(authToken);
    await handleExtension(extension, fileDetail, userId, client);

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}