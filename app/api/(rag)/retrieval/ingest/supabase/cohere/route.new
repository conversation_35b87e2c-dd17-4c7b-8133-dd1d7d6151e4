import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { UnstructuredLoader } from "langchain/document_loaders/fs/unstructured";
import { CharacterTextSplitter, RecursiveCharacterTextSplitter } from "langchain/text_splitter";

import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { OpenAIEmbeddings } from "@langchain/openai";
import { CohereEmbeddings } from "@langchain/cohere";
import { URLDetailContent } from "@/app/client/fetch/url";
import { v4 as uuidv4 } from 'uuid'

export const runtime = "edge";

// Before running, follow set-up instructions at
// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 * https://gist.github.com/taishikato/2f53dada6ea3339ce60b0b55a672dd1b
 */

async function handleExtension(
  extension: string, 
  fileDetail: URLDetailContent, 
  fileSummary: string,
  userId: string, 
  client: any
) {
  const documentId: string = uuidv4()
  const pdfBuffer = Buffer.from(fileDetail.content, "base64");

  // Convert PDF to documents using UnstructuredLoader
  const loader = new UnstructuredLoader(pdfBuffer, {
    apiKey: process.env.UNSTRUCTURED_API_KEY,
    strategy: "hi_res",
  });
  const documents = await loader.load();

  let splitter;
  if (extension === "pdf") {
     splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 500,
      chunkOverlap: 50,
      separators: ['\n\n', '\n', ' ', ''] // default setting
    });
  }
  if (extension === "txt") {
    splitter = new CharacterTextSplitter({
      separator: " ",
      chunkSize: 500,
      chunkOverlap: 50,
    });
  } else {
    const language = extension === "md" ? "markdown" : "html";
    splitter = RecursiveCharacterTextSplitter.fromLanguage(language, {
      chunkSize: 500,
      chunkOverlap: 50,
    });
  }

  const docsContent: string = documents
  const combinedDocs = fileSummary! + docsContent;
  const splitDocuments = await splitter.createDocuments(
    [combinedDocs],
    [{  
      id: documentId,
      source: fileDetail.url,
      user_id: userId,
    }],
  );
  console.log("splitDocuments: ", splitDocuments)

  // Add metadata to each document
  const documentsWithMetadata = splitDocuments.map((document, index) => {
    return {
      ...document,
      document_id: documentId,
      metadata: {
        ...document.metadata,
        //id: documentId,
        //source: fileDetail.url,
        //user_id: userId,
        index,
      },
    };
  });
  //console.log("documentsWithMetadata: ", documentsWithMetadata)
  await Promise.all([
    await client
      .from("documents_1024")
      .insert({
        id: documentId,
        filename: fileDetail.url,
        type: fileDetail.type,
        size: fileDetail.size,
        owner_id: userId,
    }),
    await SupabaseVectorStore.fromDocuments(
      documentsWithMetadata,
      //new OpenAIEmbeddings(),
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        tableName: "document_sections_1024",
        queryName: "match_document_sections_1024",
      },
    )
  ])
}

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  };
  const { getToken } = await auth(); 
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const rateLimitResponse = await checkRateLimit(req);  
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  } 
  const {
    fileDetail,
    fileSummary,
    path,
  } = await req.json();
  // Get file extension
  const extension = fileDetail.url.split(".").pop();

  // Accept these file types
  // Markdown, Text, HTML
  if (!["md", "txt", "pdf", "html", "doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)) {
    return NextResponse.json(
      {
        error: [
          "File type not supported.",
          "Please upload a markdown, text, pdf, doc, docx, xls, xlsx, ppt, pptx or html file.",
        ].join("\n"),
      },
      { status: 403 },
    );
  }

  try {
    const client = await createClerkSupabaseServerClient(authToken);
    await handleExtension(extension, fileDetail, fileSummary, userId, client);

    revalidatePath(path)
    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}