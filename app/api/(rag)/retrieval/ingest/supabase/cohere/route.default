import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";

import { createClient } from "@supabase/supabase-js";
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase"
import { OpenAIEmbeddings } from "@langchain/openai";
import { CohereEmbeddings } from "@langchain/cohere";

export const runtime = "edge";

// Before running, follow set-up instructions at
// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 */
export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }
  const {
    fileDetail,
  } = await req.json();
  const docsContent = fileDetail.content
  const source = fileDetail.ur

  if (process.env.NEXT_PUBLIC_DEMO === "true") {
    return NextResponse.json(
      {
        error: [
          "Ingest is not supported in demo mode.",
          "Please set up your own version of the repo here: https://github.com/langchain-ai/langchain-nextjs-template",
        ].join("\n"),
      },
      { status: 403 },
    );
  }

  try {
    const client = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_PRIVATE_KEY!,
    );

    //const textSplitter = RecursiveCharacterTextSplitter.fromLanguage("markdown", {
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 500,
      chunkOverlap: 50,
      separators: ['\n\n', '\n', ' ', ''] // default setting
    });

    const splitDocuments = await textSplitter.createDocuments([docsContent]);
    //console.log("splitDocuments: ", splitDocuments)

    // Add metadata to each document
    const documentsWithMetadata = splitDocuments.map((document, index) => {
      return {
        ...document,
        metadata: {
          ...document.metadata,
          source: source,
          user_id: userId,
          index,
        },
      };
    });
    console.log("documentsWithMetadata: ", documentsWithMetadata)

    const vectorstore = await SupabaseVectorStore.fromDocuments(
      documentsWithMetadata,
      //new OpenAIEmbeddings(),
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        tableName: "documents_cohere",
        queryName: "match_documents_cohere",
      },
    );

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}