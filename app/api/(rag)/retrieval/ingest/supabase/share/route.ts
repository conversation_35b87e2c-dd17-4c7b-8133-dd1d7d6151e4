import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { checkCreditBalance } from "@/lib/credit-balance";
//import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { CharacterTextSplitter, RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { ContextualCompressionRetriever } from "langchain/retrievers/contextual_compression";
import { LLMChainExtractor } from "langchain/retrievers/document_compressors/chain_extract";

import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { OpenAIEmbeddings } from "@langchain/openai";
import { CohereEmbeddings } from "@langchain/cohere";
import { HuggingFaceTransformersEmbeddings } from '@/lib/embeddings/huggingfaceTransformer';
import { URLDetailContent } from "@/app/client/fetch/url";
import { v4 as uuidv4 } from 'uuid'

export const runtime = "edge";

// Before running, follow set-up instructions at
// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 * https://gist.github.com/taishikato/2f53dada6ea3339ce60b0b55a672dd1b
 */

async function handleExtension(
  extension: string, 
  fileDetail: URLDetailContent, 
  userId: string, 
  client: any
) {
  const documentId: string = uuidv4()
  let splitDocuments;
  const docsContent: string = fileDetail?.content!

  // Use the custom splitter for markdown files
  const splitSections = splitTextByEnneagramType(docsContent);
  
  // Convert the split sections into document format expected by the pipeline
  splitDocuments = splitSections.map((section, index) => {
    return {
      pageContent: section,
      metadata: {
        id: documentId,
        source: fileDetail.url,
        user_id: userId,
        index,
      },
    };
  });
    
  // Add metadata to each document
  const documentsWithMetadata = splitDocuments.map((document, index) => {
    return {
      ...document,
      document_id: documentId,
      metadata: {
        ...document.metadata,
        index,
      },
    };
  });
  //console.log("documentsWithMetadata: ", documentsWithMetadata)
  await Promise.all([
    await client
      .from("documents_384_hf")
      .insert({
        id: documentId,
        filename: fileDetail.url,
        type: fileDetail.type,
        size: fileDetail.size,
        owner_id: userId,
    }),
    await SupabaseVectorStore.fromDocuments(
      documentsWithMetadata,
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        model: "embed-multilingual-light-v3.0", //dimension: 384
      }),
      {
        client,
        tableName: "document_sections_384_hf",
        queryName: "match_document_sections_384_hf",
      },
    ),
    await client.from("document_384_hf_owners").insert({
      document_id: documentId,
      owner_id: userId,
    }),
  ])
}

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  };
  //const freeTrial = await checkApiLimit();
  const creditAccess = await checkCreditBalance();
  //if (!freeTrial && !creditAccess) {
  if (!creditAccess) {  
    return new NextResponse(
      JSON.stringify({ 
        error: "Your credit is empty. Please purchase more credits." 
      }), 
      { 
        status: 403,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }
  const { getToken } = await auth(); 
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const rateLimitResponse = await checkRateLimit(req);
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  }
  const {
    fileDetail,
    path,
  } = await req.json();
  // Get file extension
  const extension = fileDetail.url.split(".").pop();

  // Accept these file types
  // Markdown, Text, HTML
  if (!["md", "txt", "html"].includes(extension)) {
    return NextResponse.json(
      {
        error: [
          "File type not supported.",
          "Please upload a markdown, text or html file.",
        ].join("\n"),
      },
      { status: 403 },
    );
  }

  try {
    const client = await createClerkSupabaseServerClient(authToken);
    await handleExtension(extension, fileDetail, userId, client);

    revalidatePath(path)
    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}

// Function to split text based on regex pattern
function splitTextByEnneagramType(text: string): string[] {
  // Define the regex pattern to match each Enneagram type table
  const pattern = /\| Enneagram Type \d+ Strengths\s*\|[\s\S]+?(?=\| Enneagram Type \d+ Strengths|\s*$)/g;
  
  // Use match to extract all the sections matching the pattern
  const matches = text.match(pattern);

  // Return an array of matched strings, or an empty array if no matches are found
  return matches ? matches : [];
}
