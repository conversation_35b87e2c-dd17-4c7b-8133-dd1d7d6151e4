import { NextRequest, NextResponse } from "next/server";
import { Message as VercelChatMessage, StreamingTextResponse } from "ai";

import { auth } from '@clerk/nextjs/server'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { createClient } from "@supabase/supabase-js";

import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { CohereEmbeddings } from "@langchain/cohere";
import { PromptTemplate } from "@langchain/core/prompts";
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { Document } from "@langchain/core/documents";
import { RunnableSequence } from "@langchain/core/runnables";
import {
  BytesOutputParser,
  StringOutputParser,
} from "@langchain/core/output_parsers";

export const runtime = "edge";

//const combineDocumentsFn = (docs: Document[]) => {
//  const serializedDocs = docs.map((doc) => doc.pageContent);
//  return serializedDocs.join("\n\n");
//};


const formatVercelMessages = (chatHistory: VercelChatMessage[]) => {
  const formattedDialogueTurns = chatHistory.map((message) => {
    if (message.role === "user") {
      return `Human: ${message.content}`;
    } else if (message.role === "assistant") {
      return `Assistant: ${message.content}`;
    } else {
      return `${message.role}: ${message.content}`;
    }
  });
  return formattedDialogueTurns.join("\n");
};

const CONDENSE_QUESTION_TEMPLATE = `Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question, in its original language.

<chat_history>
  {chat_history}
</chat_history>

Follow Up Input: {question}
Standalone question:`;
const condenseQuestionPrompt = PromptTemplate.fromTemplate(
  CONDENSE_QUESTION_TEMPLATE,
);

const ANSWER_TEMPLATE = `
My aim is to harness the Enneagram framework for deeper self-awareness and personal growth. I seek a dialogue that not only enlightens but also encourages, empowering me with insights into my personality, strengths, and potential areas for development as revealed by my Enneagram report. Specifically, I'm looking for:

1. Comprehensive insights into my personality traits.
2. Identification of my top three strengths.
3. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
4. Assistance in recognizing and addressing behaviors that may be limiting my growth.
5. Guidance on leveraging Enneagram insights for personal development
6. Support in cultivating resilience and a deeper understanding of myself to navigate life's challenges more effectively.

Ensure a supportive tone, especially if I express confusion or frustration. The focus is on building rapport and confidence, followed by the transfer of knowledge and skills.

## Your role:
You act as a seasoned Enneagram Coach, integrating insights from various psychological theories and coaching methodologies for a holistic analysis of my Enneagram profile. Utilize the missing personality types, the Hornevian and Harmonic groups, and Three Centers for nuanced understanding. Aim for engaging, not lecturing.

1. Begin with a greeting.
2. Invite me to upload my report. 
3. Assess my initial understanding of the Enneagram.
4. Assist me in comprehending my report, pinpointing areas of interest for further discussion. Explore the topic's details patiently. 
5. Inquire about my expectations for this conversation, then personalize our conversation based on gained insights for a meaningful coaching experience.
6. Give 1 self-reflection question for me.
7. Share your observations of my strengths and potential for continuous growth, based on the dialogue and Enneagram insights. 

Communication Style: 
Adopt my discourse particles when responding. Engage warmly, non-judgmentally, and supportively, striving to emulate the rapport-building qualities of a counselor.

Each response should be concise, no more than 20 words. Pose as simple, engaging questions like "Could you share more?". Focus only one questions or key message per reply. Avoid repeating what I say. Tailor responses to my evolving understanding and maintain a casual, empathetic tone throughout, with a touch of humor where appropriate. Responses in Traditional Chinese.


Answer the question based only on the following context and chat history:
<context>
  {context}
</context>

<chat_history>
  {chat_history}
</chat_history>

Question: {question}
`;
const answerPrompt = PromptTemplate.fromTemplate(ANSWER_TEMPLATE);

/**
 * This handler initializes and calls a retrieval chain. It composes the chain using
 * LangChain Expression Language. See the docs for more information:
 *
 * https://js.langchain.com/docs/guides/expression_language/cookbook#conversational-retrieval-chain
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }
    
    const messages = body.messages ?? [];
    const previousMessages = messages.slice(0, -1);
    const currentMessageContent = messages[messages.length - 1].content;


    const model = new ChatAnthropic({
        modelName: "claude-3-opus-20240229",
        temperature: 0.2,
      });

    const client = createClient(
        process.env.SUPABASE_URL!,
        process.env.SUPABASE_PRIVATE_KEY!,
    );
    const vectorstore = new SupabaseVectorStore(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
    {
      client,
      tableName: "documents_cohere",
      queryName: "match_documents_cohere",
    });

    /**
     * We use LangChain Expression Language to compose two chains.
     * To learn more, see the guide here:
     *
     * https://js.langchain.com/docs/guides/expression_language/cookbook
     *
     * You can also use the "createRetrievalChain" method with a
     * "historyAwareRetriever" to get something prebaked.
     */
    const standaloneQuestionChain = RunnableSequence.from([
      condenseQuestionPrompt,
      model,
      new StringOutputParser(),
    ]);

    let resolveWithDocuments: (value: Document[]) => void;
    const documentPromise = new Promise<Document[]>((resolve) => {
      resolveWithDocuments = resolve;
    });

    const retriever = vectorstore.asRetriever({
      callbacks: [
        {
          handleRetrieverEnd(documents_cohere) {
            resolveWithDocuments(documents_cohere);
          },
        },
      ],
    });

    const combineDocumentsFn = (docs: Document[]) => {
      //const filteredDocs = docs.filter((doc) => doc.metadata.user_id === userId);
      //const serializedDocs = filteredDocs.map((doc) => doc.pageContent);
      const serializedDocs = docs.map((doc) => doc.pageContent);
      return serializedDocs.join("\n\n");
    };

    const retrievalChain = retriever.pipe(combineDocumentsFn);

    const answerChain = RunnableSequence.from([
      {
        context: RunnableSequence.from([
          (input) => input.question,
          retrievalChain,
        ]),
        chat_history: (input) => input.chat_history,
        question: (input) => input.question,
      },
      answerPrompt,
      model,
    ]);

    const conversationalRetrievalQAChain = RunnableSequence.from([
      {
        question: standaloneQuestionChain,
        chat_history: (input) => input.chat_history,
      },
      answerChain,
      new BytesOutputParser(),
    ]);

    const stream = await conversationalRetrievalQAChain.stream({
      question: currentMessageContent,
      chat_history: formatVercelMessages(previousMessages),
    });

    const documents_cohere = await documentPromise;
    const serializedSources = Buffer.from(
      JSON.stringify(
        documents_cohere.map((doc) => {
          return {
            pageContent: doc.pageContent.slice(0, 50) + "...",
            metadata: doc.metadata,
          };
        }),
      ),
    ).toString("base64");

    return new StreamingTextResponse(stream, {
      headers: {
        "x-message-index": (previousMessages.length + 1).toString(),
        "x-sources": serializedSources,
      },
    });
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}