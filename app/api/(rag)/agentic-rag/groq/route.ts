import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { NextRequest, NextResponse } from "next/server";
import { 
  UIMessage as VercelChatMessage,
} from 'ai';
import { checkCreditBalance } from "@/lib/credit-balance";
//import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { addTransaction } from '@/app/actions/creditsActions'
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
 
import { PromptTemplate } from '@langchain/core/prompts'
import { auth } from "@clerk/nextjs/server";
import { currentUser } from '@clerk/nextjs/server'
import { nanoid } from '@/lib/utils'

//import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
//import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";
import { RunnableSequence } from "@langchain/core/runnables";
import { VectorStoreRetrieverMemory } from "langchain/memory";
import { MemoryVectorStore } from "langchain/vectorstores/memory";

import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { CohereEmbeddings } from "@langchain/cohere";
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { TaskType } from "@google/generative-ai";
import { type DocumentInterface, Document } from "@langchain/core/documents";
import { z } from 'zod/v3';
import { zodToJsonSchema } from "zod-to-json-schema";
import { pull } from "langchain/hub";
import { createRetrieverTool } from "langchain/tools/retriever";
import { ToolExecutor } from "@langchain/langgraph/prebuilt";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatGroq } from "@langchain/groq";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { 
  convertToOpenAIFunction, 
  convertToOpenAITool } from "@langchain/core/utils/function_calling";
import { START, END } from "@langchain/langgraph";
import {
  BaseMessage, FunctionMessage, 
  HumanMessage, AIMessage, ChatMessage, } from "@langchain/core/messages";
import { MessageGraph } from "@langchain/langgraph";

export const runtime = 'edge'
export const preferredRegion = ['hnd1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

const TEMPLATE = `

**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}

Question: {prompt}
`

//const urls = [
//  "https://lilianweng.github.io/posts/2023-06-23-agent/",
//  "https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/",
//  "https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/",
//];

//const docs = await Promise.all(urls.map((url) => new CheerioWebBaseLoader(url).load()));
//const docsList = docs.flat();

//const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 250, chunkOverlap: 0 });
//const docSplits = await textSplitter.splitDocuments(docsList);

// Add to vectorDB
//const vectorStore = await Chroma.fromDocuments(docSplits, new OpenAIEmbeddings(), {
//  collectionName: "crag-rag-chroma",
//});
 
// Usage, Query docs from existing collection //
/*const vectorStore = await Chroma.fromExistingCollection(
  new CohereEmbedding(),
  { collectionName: "crag-rag-chroma" }
);*/

//const retriever = vectorStore.asRetriever();

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id
  const { getToken } = await auth();  
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  if (!userId || !authToken) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  try {
    //const freeTrial = await checkApiLimit();
    const creditAccess = await checkCreditBalance();
    //if (!freeTrial && !creditAccess) {
    if (!creditAccess) {
      return new NextResponse(
        JSON.stringify({ 
          error: "Your credit is empty. Please purchase more credits." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }

    const json = await req.json()
    const {
      companion,
      threadId,
      existingThread,
      messages,
      chatRuns,
      previewToken,
      useReflectionThreshold,
      Persona,
      aiLanguage,
      path,
    } = json

    const language = aiLanguage?.label! ?? 'English'
    const client = await createClerkSupabaseServerClient(authToken);
    const retriever = new SupabaseHybridSearch(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        similarityK: 4,
        keywordK: 0,
        tableName: "document_sections_1024",
        similarityQueryName: "match_document_sections_1024",
        keywordQueryName: "kw_match_document_sections",
      });
    /*const client = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_PRIVATE_KEY!,
    );
    const vectorStore = new SupabaseVectorStore(
      //new OpenAIEmbeddings({
      //  modelName: "text-embedding-3-small",
      //}),
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        tableName: "documents_cohere",
        queryName: "match_documents_cohere",
        //filter: { owner_id: "userId" },
      });

    //const retriever = vectorStore.asRetriever({ k: 3, filter });
    const retriever = vectorStore.asRetriever({ k: 3 });*/
    console.log("retriever: ", retriever)

    const tool = createRetrieverTool(
      retriever,
      {
        name: "retrieve_my_report",
        description: "Search and return information about me or my report."
      }
    );
    const tools = [tool];

    // @ts-ignore
    const toolExecutor = new ToolExecutor({ tools });

    /**==================================================================
     * Nodes and Edges
     ==================================================================*/
    /**
     * Decides whether the agent should retrieve more information or end the process.
     * This function checks the last message in the state for a function call. If a function call is
     * present, the process continues to retrieve information. Otherwise, it ends the process.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @returns {string} - A decision to either "continue" the retrieval process or "end" it.
     */
    function shouldRetrieve(state: Array<BaseMessage>) {
      console.log("---DECIDE TO RETRIEVE---");
      console.log("state", state);
      const lastMessage = state[state.length - 1];
      console.log("---DECIDE TO RETRIEVE---", lastMessage);
      console.log("---TOOL_CALLS---", lastMessage.additional_kwargs.tool_calls); // Add this line

      // If there is no function call then we finish.
      //if (!lastMessage.additional_kwargs.function_call) {
      if (!lastMessage.additional_kwargs.tool_calls) {
        console.log("---DECISION: DO NOT RETRIEVE / DONE---");
        return "end";
      }
      console.log("---DECISION: RETRIEVE---");
      return "continue";
    };

    /**
     * Determines whether the Agent should continue based on the relevance of retrieved documents.
     * This function checks if the last message in the conversation is of type FunctionMessage, indicating
     * that document retrieval has been performed. It then evaluates the relevance of these documents to the user's
     * initial question using a predefined model and output parser. If the documents are relevant, the conversation
     * is considered complete. Otherwise, the retrieval process is continued.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function gradeDocuments(state: Array<BaseMessage>) {
      console.log("---GET RELEVANCE---");
      // Output
      const output = zodToJsonSchema(z.object({
        binaryScore: z.string().describe("Relevance score 'yes' or 'no'"),
      }));
      const tool = {
        type: "function" as const,
        function: {
          name: "give_relevance_score",
          description: "Give a relevance score to the retrieved documents.",
          inputSchema: output,
        }
      }

      const prompt = ChatPromptTemplate.fromTemplate(`You are a grader assessing relevance of retrieved docs to a user question.
      Here are the retrieved docs:
      \n ------- \n
      {context} 
      \n ------- \n
      Here is the user question: {question}
      If the content of the docs are relevant to the users question, score them as relevant.
      Give a binary score 'yes' or 'no' score to indicate whether the docs are relevant to the question.
      Yes: The docs are relevant to the question.
      No: The docs are not relevant to the question.`);

      //const model = new ChatOpenAI({
      //  modelName: "gpt-4.1",
      //  temperature: 0,
      const model = new ChatGroq({
        model: "llama-3.3-70b-versatile",
        temperature: 0,
      }).bind({
        tools: [tool],
        tool_choice: tool,
      });

      const chain = prompt.pipe(model);
      console.log("state: ", state)
      const lastMessage = state[state.length - 1];

      const score = await chain.invoke({
        question: state[0].content as string,
        context: lastMessage.content as string,
      });
      console.log("score: ", score)
      return [score];
    }

    /**
     * Check the relevance of the previous LLM tool call.
     * 
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @returns {string} - A directive to either "yes" or "no" based on the relevance of the documents.
     */
    function checkRelevance(state: Array<BaseMessage>) {
      console.log("---CHECK RELEVANCE---");
      const lastMessage = state[state.length - 1];
      console.log("state: ", state)
      console.log("lastMessage: ", lastMessage)
      const toolCalls = lastMessage.additional_kwargs.tool_calls
      if (!toolCalls) {
        throw new Error("Last message was not a function message");
      }
      const parsedArgs = JSON.parse(toolCalls[0].function.arguments);
      
      if (parsedArgs.binaryScore === "yes") {
        console.log("---DECISION: DOCS RELEVANT---");
        return "yes";
      }
      console.log("---DECISION: DOCS NOT RELEVANT---");
      return "no";
    }

    // Nodes

    /**
     * Invokes the agent model to generate a response based on the current state.
     * This function calls the agent model to generate a response to the current conversation state.
     * The response is added to the state's messages.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function agent(state: Array<BaseMessage>) {
      console.log("---CALL AGENT---");
      //const functions = tools.map((tool) => convertToOpenAIFunction(tool));
      const gradeToolOai = tools.map((tool) => convertToOpenAITool(tool));
      console.log("---gradeToolOai---", gradeToolOai);
      
      /*const llm = new ChatOpenAI({
        modelName: "gpt-4.1",
        temperature: 0,
        streaming: true,
      }).bind({
        functions,
      });*/

      const model = new ChatGroq({
        modelName: "llama-3.3-70b-versatile",
        temperature: 0,
      }).bind({
        tools: gradeToolOai,
        tool_choice: 'auto'//gradeToolOai[0],
      })

      const response = await model.invoke(state);
      // We can return just the response because it will be appended to the state.
      return [response];
    };

    /**
     * Executes a tool based on the last message's function call.
     * This function is responsible for executing a tool invocation based on the function call
     * specified in the last message. The result from the tool execution is added to the conversation
     * state as a new message.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function retrieve(state: Array<BaseMessage>) {
      console.log("---EXECUTE RETRIEVAL---");
      // Based on the continue condition
      // we know the last message involves a function call.
      const lastMessage = state[state.length - 1];
      const action = {
        //tool: lastMessage.additional_kwargs.function_call?.name ?? "",
        //toolInput: JSON.parse(lastMessage.additional_kwargs.function_call?.arguments ?? "{}"),
        tool: lastMessage.additional_kwargs.tool_calls?.[0]?.function.name ?? "",
        toolInput: JSON.parse(lastMessage.additional_kwargs.tool_calls?.[0]?.function?.arguments ?? "{}"),
      };
      // We call the tool_executor and get back a response.
      const response = await toolExecutor.invoke(action);
      // We use the response to create a FunctionMessage.
      const functionMessage = new FunctionMessage({
        name: action.tool,
        content: response,
      });
      console.log("functionMessage: ", functionMessage)
      return [functionMessage];
    }

    /**
     * Transform the query to produce a better question.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function rewrite(state: Array<BaseMessage>) {
      console.log("---TRANSFORM QUERY---");
      const question = state[0].content as string;
      const prompt = ChatPromptTemplate.fromTemplate(`Look at the input and try to reason about the underlying semantic intent / meaning. \n 
      Here is the initial question:
      \n ------- \n
      {question} 
      \n ------- \n
      Formulate an improved question:`);

      // Grader
      //const model = new ChatOpenAI({
      //  modelName: "gpt-4.1",
      //  temperature: 0,
      //  streaming: true,
      //});
      const model = new ChatGroq({
        modelName: "llama-3.3-70b-versatile",
        temperature: 0,
      })
      const response = await prompt.pipe(model).invoke({ question });
      console.log("improved question response: ", response)
      return [response];
    }

    /**
     * Generate answer
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function generate(state: Array<BaseMessage>) {
      console.log("---GENERATE---");
      const question = state[0].content as string;
      console.log("---GENERATE---question---", question);
      const sendLastMessage = state[state.length - 2];
      console.log("---GENERATE---sendLastMessage---", sendLastMessage);
      const docs = sendLastMessage.content as string;
      console.log("---GENERATE---docs---", docs);

      const formatVercelMessages = (chatHistory: undefined[]) => {
        const formattedDialogueTurns = chatHistory.map((message) => {
          if (message.role === "user") {
            return `Human: ${message.content}`;
          } else if (message.role === "assistant") {
            return `Assistant: ${message.content}`;
          } else {
            return `${message.role}: ${message.content}`;
          }
        });
        return formattedDialogueTurns.join("\n");
      };
      
      //const prompt = await pull<ChatPromptTemplate>("rlm/rag-prompt");
      const prompt = ChatPromptTemplate.fromTemplate(`# Role
      Act as a seasoned Enneagram Coach, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and ensure the dialogue is insightful and supportive.
      
      # Goals 
      - Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
      - Utilize open-ended questions to help me explore my core needs and underlying fears or beliefs.
      - Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
      - Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.
      
      
      # Task
      Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding.
      Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection.
      Follow this step-by-step inquiry process:
      
      1. Invite me to share my Enneagram report and assess my initial understanding.
      2. Help me understand my report, highlighting areas for deeper exploration.
      3. Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
      4. Personalize our conversation based on the insights gained, focusing on understanding before advising.
      5. Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.
      
      ## Specifics
      This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.
      
      Specifically, I'm looking for:
      1. Comprehensive insights into my personality traits.
      2. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
      3. Assistance in recognizing and addressing behaviors that may be limiting my growth.
      4. Guidance on leveraging Enneagram insights for personal development
      5. Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively
      
      # Tools
      Always use available tools to research relevant information when uncertain, particularly for questions about my personal details, report, and Enneagram profile.
      
      # Communication Style
      Adopt my discourse particles when responding. Engage warmly and supportively, emulating the qualities of a counselor.
      Keep responses concise, using up to three sentences unless more detail is requested. Ask simple, engaging questions like "Could you share more about that?". Focus on a single question or key message per reply. Maintain a supportive tone, particularly if I express confusion or frustration.
      
      Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate. Always speak in the first person as an Enneagram Coach. If unsure about the content's relevance or accuracy, express the need for more information instead of providing an uncertain answer.
      Responses in {language}.
      
      # Notes
      - Be concise and response in 100 words unless I ask for details.
      - Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
      - Please use relatable examples or analogies to help clarify more complex Enneagram concepts.

      Question: {question} \n
      My Report: {context} \n
      Chat History: {chat_history} \n
      `);
      //console.log("prompt: ", prompt)
      
      const previousMessages = messages
      .slice(0, -1) // Remove the last message (the current message)
      .slice(-16) // Get the last 8 messages

      //console.log("previousMessages: ", formatVercelMessages(previousMessages))

      /*const llm = new ChatOpenAI({
        modelName: "gpt-4.1-mini",
        temperature: 0,
        streaming: true,
      });*/
      const llm = new ChatGoogleGenerativeAI({
        model: "gemini-1.5-pro-latest",
        maxOutputTokens: 2048,
        streaming: true,
        //requestOptions: {apiVersion: "v1beta"}
      });
      const model = new ChatGroq({
        modelName: "llama-3.3-70b-versatile",
        temperature: 0,
        maxOutputTokens: 1024,
        streaming: true,
      })

      /*const vectorStore = new MemoryVectorStore(
        new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }));
      const memory = new VectorStoreRetrieverMemory({
        // 1 is how many documents to return, you might want to return more, eg. 4
        vectorStoreRetriever: vectorStore.asRetriever(
          1,
          (doc) => 
            doc.metadata?.userId === userId &&
            doc.metadata?.threadId === threadId &&
            doc.metadata?.companionId === companion?.id!
        ),
        memoryKey: "history",
        metadata: { threadId, userId, companionId: companion?.id! },
      });

      const result = await memory.loadMemoryVariables({prompt: question})
      console.log("loadMemory: ",
        result.history
      );
      const ragChain = RunnableSequence.from([
        memory,
        prompt,
        llm,
        new StringOutputParser(),
      ]);*/
      const ragChain = prompt.pipe(model).pipe(new StringOutputParser());
      const response = await ragChain.invoke({
        context: docs,
        question,
        language,
        //history: result?.history ?? '',
        chat_history: formatVercelMessages(previousMessages)
      });
      //console.log("response: ", response)

      /*await memory.saveContext(
        { input: question },
        { output: response }
      );*/

      return [new AIMessage(response)];
    }

    /**==================================================================
     * Build Graph
     ==================================================================*/
    // Define a new graph
    const workflow = new MessageGraph()
    // Define the nodes which we'll cycle between.
      .addNode("agent", agent)
      .addNode("retrieve", retrieve)
      .addNode("gradeDocuments", gradeDocuments)
      .addNode("rewrite", rewrite)
      .addNode("generate", generate)

    // Call agent node to decide to retrieve or not
    workflow.addEdge(START, "agent")

    // Decide whether to retrieve
    workflow.addConditionalEdges(
      "agent",
      // Assess agent decision
      shouldRetrieve,
      {
        // Call tool node
        continue: "retrieve",
        end: END,
      }
    );

    workflow.addEdge("retrieve", "gradeDocuments")

    // Edges taken after the `action` node is called.
    workflow.addConditionalEdges(
      "gradeDocuments",
      // Assess agent decision
      checkRelevance,
      {
        // Call tool node
        yes: "generate",
        no: "rewrite" , // placeholder
      }
    );

    workflow.addEdge("generate", END);
    workflow.addEdge("rewrite", "agent");

    // Compile
    const app = workflow.compile();

    /**==================================================================
     * Run the graph
     ==================================================================*/
    const userMessage = messages.slice(-1)
      .filter((message: { role: string }) => message.role === 'user')
    const promptMessage = userMessage[0]?.content
    //console.log("promptMessage@chat: ", promptMessage)

    const inputs = [new HumanMessage(promptMessage)];
    let finalState;
    let finalGeneration: string | undefined = undefined;
    interface TokenUsage {
      completionTokens: number;
      promptTokens: number;
      totalTokens: number;
    }
    
    const sumTokenUsages = {
      outputTokens: 0,
      inputTokens: 0,
      totalTokens: 0
    };
    //const allTokenUsages: TokenUsage[] = [];
    let tokenUsage: LanguageModelUsage | undefined = undefined
    const textEncoder = new TextEncoder();
    const clientStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const output of await app.stream(inputs)) {
            for (const [key, value] of Object.entries(output)) {
              console.log(`Output from node: '${key}'`);
              console.log("JSON.stringify", JSON.stringify(value, null, 2));
              finalState = value;

              if (key === '__end__' && Array.isArray(value) && value.length > 1) {
                value.forEach(item => {            
                  const tokenUsage = item.response_metadata?.tokenUsage;
                  if (tokenUsage) {
                    //allTokenUsages.push(tokenUsage);
                    //console.log("==allTokenUsages==: ", allTokenUsages);
                    sumTokenUsages.outputTokens += tokenUsage.outputTokens;
                    sumTokenUsages.inputTokens += tokenUsage.inputTokens;
                    sumTokenUsages.totalTokens += tokenUsage.totalTokens;
                  }
                });
                const lastOutput = value[value.length - 1];
                console.log("lastOutput: ", lastOutput)
                if (lastOutput && lastOutput?.content) {
                  finalGeneration = lastOutput.content;
                } else if (lastOutput) {
                  finalGeneration = lastOutput;
                }
              } //else if (key === 'generate' && Array.isArray(value) && value.length > 0) {
                  //finalGeneration = value[0];
              //}
              
              if (finalGeneration) {
                //console.log("finalGeneration: ", finalGeneration);
                controller.enqueue(textEncoder.encode(finalGeneration)); // Enqueue the finalGeneration to the stream
                //finalGeneration = undefined; // Reset finalGeneration after enqueuing
              }
            }
          }
        } catch (error) {
          console.error(error);
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }
        if (
          sumTokenUsages &&
          typeof sumTokenUsages.inputTokens === 'number' &&
          typeof sumTokenUsages.outputTokens === 'number'
        ) {
          console.log("====tokenUsages====: ", sumTokenUsages);
          await addTransaction({
            userApiCount: 1,
            inputTokens: sumTokenUsages.inputTokens,
            outputTokens: sumTokenUsages.outputTokens,
            model: 'gpt_4o',
            userId,
            notes: `Token count`,
          });
        }

        if (promptMessage && finalGeneration && finalGeneration.length > 0) {
          console.log("====saveCompletionToDatabase====");
          if (existingThread) {
            await saveCompletionToDatabase(companion?.id!, userId, promptMessage, "user", threadId);
          } else {
            const name = promptMessage.substring(0, 30);
            await newThread({
              threadId,
              name,
              companionId: companion?.id!,
              content: promptMessage,
              role: "user",
            });
          }
    
          await saveCompletionToDatabase(companion?.id!, userId, finalGeneration, "assistant", threadId);
        }
        revalidatePath(path);
        controller.close();
      }
    });
    //if (!creditAccess) {
    //  await incrementApiLimit();
    //}
    return new NextResponse(clientStream)
  } catch (e: any) {
    console.error(e);
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}
