import { revalidatePath } from 'next/cache'
import { NextRequest, NextResponse } from "next/server";
import { 
  UIMessage as VercelChatMessage,
  streamText,
} from 'ai';
import { checkCreditBalance } from "@/lib/credit-balance";
//import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { addTransaction } from '@/app/actions/creditsActions'
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";
 
import { auth, currentUser } from "@clerk/nextjs/server";

//import { createClient } from "@supabase/supabase-js";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@langchain/community/vectorstores/supabase";

import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { CohereEmbeddings } from "@langchain/cohere";
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { CacheBackedEmbeddings } from "langchain/embeddings/cache_backed";
import { InMemoryStore } from "@langchain/core/stores";
import { TaskType } from "@google/generative-ai";
import type { RunnableConfig } from "@langchain/core/runnables";
import { type DocumentInterface, Document } from "@langchain/core/documents";
import { z } from 'zod/v3';
import { zodToJsonSchema } from "zod-to-json-schema";
import { createRetrieverTool } from "langchain/tools/retriever";
import { tool } from "@langchain/core/tools";
import { formatDocumentsAsString } from "langchain/util/document";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { ChatGroq } from "@langchain/groq";
import { ChatOpenAI } from "@langchain/openai";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { StringOutputParser } from "@langchain/core/output_parsers";

import { START, END, Annotation, 
  StateGraph, MessageGraph, MemorySaver} from "@langchain/langgraph";
import {
  BaseMessage, ToolMessage, AIMessage,
  HumanMessage } from "@langchain/core/messages";
import zepClient, { zepMemory, formatChatSummaries, formatChatFacts } from "@/lib/zepClient"


export const runtime = 'edge'
export const preferredRegion = ['sfo1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

const TEMPLATE = `Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate.
Responses in {language}.

# Notes
- Be concise unless I ask for suggestions or details.
- Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.

Question: {question} \n
My Report: {context} \n
Chat History: {chat_history} \n
Previous Chat Fact: {chat_fact} \n
Previous Chat Summary: {chat_summary} \n
`

const AGENT_SYSTEM_TEMPLATE_ENN = `# Role
Act as a seasoned Enneagram Coach, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and ensure the dialogue is insightful and supportive.

# Goals 
- Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
- Utilize open-ended questions to help me explore my core needs and underlying fears or beliefs.
- Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
- Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.

# Task
Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding.
Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection.
Follow this step-by-step inquiry process:

1. Help me understand my report, highlighting areas for deeper exploration.
2. Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
3. Personalize our conversation based on the insights gained, focusing on understanding before advising.
4. Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

## Specifics
This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

Specifically, I'm looking for:
1. Comprehensive insights into my personality traits.
2. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
3. Assistance in recognizing and addressing behaviors that may be limiting my growth.
4. Guidance on leveraging Enneagram insights for personal development
5. Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

# Communication Style
Adopt my discourse particles when responding. Engage warmly and supportively, emulating the qualities of a counselor.
Keep responses in concise. Ask simple, engaging questions like "Could you share more about that?". Focus on a single question or key message per reply. Maintain a supportive tone, particularly if I express confusion or frustration.

Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate. Always speak in the first person as an Enneagram Coach. If unsure about the content's relevance or accuracy, express the need for more information instead of providing an uncertain answer.
Responses in {language}.

# Notes
- Be concise unless I ask for suggestions or details.
- Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
- Please use relatable examples or analogies to help clarify more complex Enneagram concepts.

Question: {question} \n
My Report: {context} \n
Chat History: {chat_history} \n
Previous Chat Fact: {chat_fact} \n
Previous Chat Summary: {chat_summary} \n
`

//const urls = [
//  "https://lilianweng.github.io/posts/2023-06-23-agent/",
//  "https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/",
//  "https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/",
//];

//const docs = await Promise.all(urls.map((url) => new CheerioWebBaseLoader(url).load()));
//const docsList = docs.flat();

//const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 250, chunkOverlap: 0 });
//const docSplits = await textSplitter.splitDocuments(docsList);

// Add to vectorDB
//const vectorStore = await Chroma.fromDocuments(docSplits, new OpenAIEmbeddings(), {
//  collectionName: "crag-rag-chroma",
//});
 
// Usage, Query docs from existing collection //
/*const vectorStore = await Chroma.fromExistingCollection(
  new CohereEmbedding(),
  { collectionName: "crag-rag-chroma" }
);*/

//const retriever = vectorStore.asRetriever();
const inMemoryStore = new InMemoryStore();
const underlyingEmbeddings = new CohereEmbeddings({
  apiKey: process.env.COHERE_API_KEY,
  model: "embed-multilingual-light-v3.0", //dimension: 384
})
const cacheBackedEmbeddings = CacheBackedEmbeddings.fromBytesStore(
  underlyingEmbeddings,
  inMemoryStore,
  {
    namespace: underlyingEmbeddings.model,
  }
);

const searchTool = new TavilySearchResults({ maxResults: 1 });

export async function POST(req: NextRequest) {
  const { userId, getToken } = await auth();  
  const user = await currentUser();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  if (!userId || !authToken) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  try {
    //const freeTrial = await checkApiLimit();
    const creditAccess = await checkCreditBalance();
    //if (!freeTrial && !creditAccess) {
    if (!creditAccess) {
      return new NextResponse(
        JSON.stringify({ 
          error: "Your credit is empty. Please purchase more credits." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }

    const json = await req.json()
    const {
      companion,
      threadId,
      existingThread,
      messages,
      chatRuns,
      previewToken,
      observationList,
      useReflectionThreshold,
      persona,
      aiLanguage,
      path,
    } = json

    const language = aiLanguage?.label! ?? 'English'
    const chatMessages = messages

    const GraphState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        value: (x, y) => x.concat(y),
        default: () => [],
      }),
      confirmed: Annotation<boolean | undefined>(),
    })

    // Many keys logged with hashed values
    const keys = [];
    for await (const key of inMemoryStore.yieldKeys()) {
      keys.push(key);
    }

    console.log("====================================InMemoryStore====================================", keys.slice(0, 5));

    const client = await createClerkSupabaseServerClient(authToken);
    const hybridSearch = new SupabaseHybridSearch(
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        similarityK: 4,
        keywordK: 0,
        tableName: "document_sections_1024",
        similarityQueryName: "match_document_sections_1024",
        keywordQueryName: "kw_match_document_sections",
      });
    /*const client = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_PRIVATE_KEY!,
    );
    const vectorStore = new SupabaseVectorStore(
      //new OpenAIEmbeddings({
      //  modelName: "text-embedding-3-small",
      //}),
      new CohereEmbeddings({
        apiKey: process.env.COHERE_API_KEY,
        // @ts-ignore
        model: "embed-multilingual-v3.0", //dimension: 1024
      }),
      {
        client,
        tableName: "documents_cohere",
        queryName: "match_documents_cohere",
        //filter: { owner_id: "userId" },
      });

    //const retriever = vectorStore.asRetriever({ k: 3, filter });
    const retriever = vectorStore.asRetriever({ k: 3 });*/
    //console.log("retriever: ", retriever)
    const RetrieverSchema = z.object({
      query: z.string().describe("query to look up in retriever"),
    });
    
    const retrieverTool = tool(
      async (input, config) => {
        const parsedInput = RetrieverSchema.safeParse(input);
        if (!parsedInput.success) {
          throw new Error("Invalid input format");
        }
        const { query } = parsedInput.data;
        const docs = await hybridSearch.invoke(query)
        const results = formatDocumentsAsString(docs)
        return results
      },
      {
        name: "search_my_personal_report",
        description: "Search and return information about me or my report",
        schema: RetrieverSchema,
      }
    )

    const vectorStore = new SupabaseVectorStore(
      cacheBackedEmbeddings,
      {
        client,
        tableName: 'document_sections_384_hf',
        queryName: 'match_document_sections_384_hf',
      }
    )
    const reply_retriever = vectorStore.asRetriever({ k: 3 });
    const replyTool = createRetrieverTool(reply_retriever, {
      name: "search_my_enneagram_strength",
      description: "Searches and returns my enneagram strength."
    });

    const retrieverTools = [retrieverTool, replyTool, searchTool];
    //console.log("retrieverTools: ", retrieverTools)

    const toolNode = new ToolNode<typeof GraphState.State>(retrieverTools);
    /**==================================================================
     * Nodes and Edges
     ==================================================================*/
    /**
     * Decides whether the agent should retrieve more information or end the process.
     * This function checks the last message in the state for a function call. If a function call is
     * present, the process continues to retrieve information. Otherwise, it ends the process.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @returns {string} - A decision to either "continue" the retrieval process or "end" it.
     */
    function shouldRetrieve(state: typeof GraphState.State): string {
      const { messages } = state;
      console.log("---DECIDE TO RETRIEVE---");
      //console.log("state", state);
      const lastMessage = messages[messages.length - 1];
      //console.log("---DECIDE TO RETRIEVE---", lastMessage);

      if ("tool_calls" in lastMessage && Array.isArray(lastMessage.tool_calls) && lastMessage.tool_calls.length) {
        console.log("---DECISION: RETRIEVE---");
        return "continue";
      }
      // If there are no tool calls then we finish.
      return "end";
    };

    /**
     * Determines whether the Agent should continue based on the relevance of retrieved documents.
     * This function checks if the last message in the conversation is of type FunctionMessage, indicating
     * that document retrieval has been performed. It then evaluates the relevance of these documents to the user's
     * initial question using a predefined model and output parser. If the documents are relevant, the conversation
     * is considered complete. Otherwise, the retrieval process is continued.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function gradeDocuments(state: typeof GraphState.State): Promise<Partial<typeof GraphState.State>> {
      console.log("---GET RELEVANCE---");
      // Output
      const { messages } = state;
      const tool = {
        name: "give_relevance_score",
        description: "Give a relevance score to the retrieved documents.",
        schema: z.object({
          binaryScore: z.string().describe("Relevance score 'yes' or 'no'"),
        })
      }

      const prompt = ChatPromptTemplate.fromTemplate(
        `You are a grader assessing relevance of retrieved docs to a user question.
      Here are the retrieved docs:
      \n ------- \n
      {context} 
      \n ------- \n
      Here is the user question: {question}
      If the content of the docs are relevant to the users question, score them as relevant.
      Give a binary score 'yes' or 'no' score to indicate whether the docs are relevant to the question.
      Yes: The docs are relevant to the question.
      No: The docs are not relevant to the question.`,
      );

      const model = new ChatGroq({
        model: "llama3-groq-70b-8192-tool-use-preview",
        temperature: 0,
      }).bindTools([tool], {
        tool_choice: "auto",
      });

      const chain = prompt.pipe(model);
      //console.log("state: ", state)
      const lastMessage = messages[messages.length - 1];

      const score = await chain.invoke({
        question: messages[0].content as string,
        context: lastMessage.content as string,
      });
      //console.log("score: ", score)
      return {
        messages: [score]
      };
    }

    /**
     * Check the relevance of the previous LLM tool call.
     * 
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @returns {string} - A directive to either "yes" or "no" based on the relevance of the documents.
     */
    function checkRelevance(state: typeof GraphState.State): string {
      console.log("---CHECK RELEVANCE---");
      const { messages } = state;
      const lastMessage = messages[messages.length - 1];
      if (!("tool_calls" in lastMessage)) {
        throw new Error("The 'checkRelevance' node requires the most recent message to contain tool calls.")
      }
      //console.log("state: ", state)
      //console.log("lastMessage: ", lastMessage)
      const toolCalls = (lastMessage as AIMessage).tool_calls;
      if (!toolCalls || !toolCalls.length) {
        throw new Error("Last message was not a function message");
      }

      if (toolCalls[0].args.binaryScore === "yes") {
        console.log("---DECISION: DOCS RELEVANT---");
        return "yes";
      }
      console.log("---DECISION: DOCS NOT RELEVANT---");
      return "no";
    }

    // Nodes

    /**
     * Invokes the agent model to generate a response based on the current state.
     * This function calls the agent model to generate a response to the current conversation state.
     * The response is added to the state's messages.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function agent(state: typeof GraphState.State, config?: RunnableConfig): Promise<Partial<typeof GraphState.State>> {
      console.log("---CALL AGENT---");
      const { messages } = state;
      // Find the AIMessage which contains the `give_relevance_score` tool call,
      // and remove it if it exists. This is because the agent does not need to know
      // the relevance score.
      const filteredMessages = messages.filter((message) => {
        if ("tool_calls" in message && Array.isArray(message.tool_calls) && message.tool_calls.length > 0) {
          return message.tool_calls[0].name !== "give_relevance_score";
        }
        return true;
      });
      //console.log("---STATE---", state);
      
      const model = new ChatGroq({
        model: "llama-3.3-70b-versatile",
        temperature: 0,
      }).bindTools(retrieverTools)

      const response = await model.invoke(filteredMessages);
      // We can return just the response because it will be appended to the state.
      //console.log("---AGENT RESPONSE---", response);
      return { messages: [response] };
    };

    /**
     * Transform the query to produce a better question.
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function rewrite(state: typeof GraphState.State): Promise<Partial<typeof GraphState.State>> {
      console.log("---TRANSFORM QUERY---");
      const { messages } = state;
      const question = messages[0].content as string;
      const prompt = ChatPromptTemplate.fromTemplate(`Look at the input and try to reason about the underlying semantic intent / meaning. \n 
      Here is the initial question:
      \n ------- \n
      {question} 
      \n ------- \n
      Formulate an improved question:`);

      // Grader
      //const model = new ChatOpenAI({
      //  modelName: "gpt-4.1",
      //  temperature: 0,
      //  streaming: true,
      //});
      const model = new ChatGroq({
        modelName: "llama-3.3-70b-versatile",
        temperature: 0,
      })
      const response = await prompt.pipe(model).invoke({ question });
      //console.log("improved question response: ", response)
      return { messages: [response] }
    }

    /**
     * Generate answer
     * @param {Array<BaseMessage>} state - The current state of the agent, including all messages.
     * @param {RunnableConfig | undefined} config - The configuration for the runnable.
     * @returns {Array<BaseMessage>} - The updated state with the new message added to the list of messages.
     */
    async function generate(state: typeof GraphState.State): Promise<Partial<typeof GraphState.State>> {
      console.log("---GENERATE---");
      const { messages } = state;
      console.log("---GENERATE---messages---", messages);
      const question = messages[0].content as string;
      //console.log("---GENERATE---question---", question);
      // Extract the most recent ToolMessage
      const lastToolMessage = messages.slice().reverse().find((msg) => msg.getType() === "tool");
      if (!lastToolMessage) {
        throw new Error("No tool message found in the conversation history");
      }
      //console.log("---GENERATE---lastToolMessage---", lastToolMessage);
      const docs = lastToolMessage.content as string;
      console.log("---GENERATE---docs---", docs);
      
      const formatVercelMessages = (chatHistory: undefined[]) => {
        const formattedDialogueTurns = chatHistory.map((message) => {
          if (message.role === "user") {
            return `Human: ${message.content}`;
          } else if (message.role === "assistant") {
            return `Assistant: ${message.content}`;
          } else {
            return `${message.role}: ${message.content}`;
          }
        });
        return formattedDialogueTurns.join("\n");
      };
      const previousMessages = chatMessages
      .slice(0, -1) // Remove the last message (the current message)
      .slice(-6) // Get the last 3 messages

      let prompt
      if (lastToolMessage?.name === "tavily_search_results_json") {
        prompt = ChatPromptTemplate.fromTemplate(TEMPLATE);
      } else {
        prompt = ChatPromptTemplate.fromTemplate(AGENT_SYSTEM_TEMPLATE_ENN);
      }

      //console.log("@@@@@@@@@@@@@@@@@@@@prompt@@@@@@@@@@@@@@@@@@@@@@@@@@@@: ", prompt)

      /*const llm = new ChatOpenAI({
        model: "gpt-4.1-mini",
        temperature: 0,
        streaming: true,
      });*/
      const sambanova = new ChatOpenAI({
        configuration: {
          baseURL: "https://api.sambanova.ai/v1/",
          apiKey: process.env.SAMBANOVA_API_KEY,
        },
        streaming: true,
        model: "Meta-Llama-3.1-70B-Instruct",
      })
      const llm = new ChatGoogleGenerativeAI({
        model: "gemini-2.5-flash",
        maxOutputTokens: 2048,
        streaming: true,
      });
      const model = new ChatGroq({
        model: "llama-3.3-70b-versatile",
        temperature: 0,
        maxOutputTokens: 1024,
        streaming: true,
      })
    
      const { 
        prevMemoryFacts, 
        prevMemorySummaries 
      } = (await zepMemory(
        companion?.id!,
        threadId!,
        userId!,
        question,
      )) ?? { prevMemoryFacts: [], prevMemorySummaries: [] }; 
      //console.log("ChatHistory", formatVercelMessages(previousMessages))
         
      const ragChain = prompt.pipe(model).pipe(new StringOutputParser());
      const response = await ragChain.invoke({
        context: docs,
        question,
        language,
        chat_history: formatVercelMessages(previousMessages),
        chat_fact: formatChatFacts(prevMemoryFacts),
        chat_summary: formatChatSummaries(prevMemorySummaries)
      });
      await zepClient.memory.add(threadId, {
        messages: [
          //{ role: "user", roleType: "user", content: question },
          { role: "assistant", roleType: "assistant", content: response }
        ]
      });
      //console.log("response: ", response)

      return { messages: [new AIMessage(response)] }
    }

    async function normalChat(state: typeof GraphState.State): Promise<Partial<typeof GraphState.State>> {
      console.log("---CHAT---");
      const { messages } = state;
      console.log("---CHAT---messages---", messages);
      const question = messages[0].content as string;
      console.log("---CHAT---question---", question);
      
      const formatVercelMessages = (chatHistory: undefined[]) => {
        const formattedDialogueTurns = chatHistory.map((message) => {
          if (message.role === "user") {
            return `Human: ${message.content}`;
          } else if (message.role === "assistant") {
            return `Assistant: ${message.content}`;
          } else {
            return `${message.role}: ${message.content}`;
          }
        });
        return formattedDialogueTurns.join("\n");
      };
      const previousMessages = chatMessages
      .slice(0, -1) // Remove the last message (the current message)
      .slice(-6) // Get the last 3 messages

      const prompt = ChatPromptTemplate.fromTemplate(`# Role
        Act as a seasoned Enneagram Coach, blending insights from various psychological theories and coaching methods, including reality therapy and the integral coaching method. Provide a holistic analysis of my Enneagram profile and ensure the dialogue is insightful and supportive.

        # Goals 
        - Actively listen to my thoughts and feelings, offering validation and support as needed before moving to problem-solving.
        - Utilize open-ended questions to help me explore my core needs and underlying fears or beliefs.
        - Facilitate a deeper understanding of my primary strengths and blind spots, particularly how they influence my behavior in various settings.
        - Foster a space where I feel understood and supported, paving the way for identifying strategies for personal growth and emotional well-being based on my Enneagram type.


        # Task
        Employ advanced listening skills to demonstrate comprehension and tailor your responses based on my level of understanding.
        Focus on being an attentive listener, facilitating an engaging interaction rather than a didactic one. Use powerful questions to encourage self-reflection.
        Follow this step-by-step inquiry process:

        1. Help me understand my report, highlighting areas for deeper exploration.
        2. Discuss the core topics in detail, with an emphasis on understanding my perspective and feelings.
        3. Personalize our conversation based on the insights gained, focusing on understanding before advising.
        4. Reflect on my strengths and areas for potential growth, ensuring understanding and validation of my feelings precede any advice.

        ## Specifics
        This task is critical for deeper self-awareness and personal growth using the Enneagram. Provide a thorough analysis of my report, empowering me with insights into my personality, strengths, and areas for development.

        Specifically, I'm looking for:
        1. Comprehensive insights into my personality traits.
        2. Exploration of how my traits play out in various life aspects, such as alone time, work, friendships, and family.
        3. Assistance in recognizing and addressing behaviors that may be limiting my growth.
        4. Guidance on leveraging Enneagram insights for personal development
        5. Support in cultivating resilience and a deeper self-understanding to navigate life's challenges effectively

        # Communication Style
        Adopt my discourse particles when responding. Engage warmly and supportively, emulating the qualities of a counselor.
        Keep responses in concise. Ask simple, engaging questions like "Could you share more about that?". Focus on a single question or key message per reply. Maintain a supportive tone, particularly if I express confusion or frustration.

        Tailor responses to my evolving understanding, maintaining a casual, empathetic tone with a touch of humor where appropriate. Always speak in the first person as an Enneagram Coach. If unsure about the content's relevance or accuracy, express the need for more information instead of providing an uncertain answer.
        Responses in {language}.

        # Notes
        - Be concise unless I ask for suggestions or details.
        - Always show empathy and validate my feelings as part of your response. Understanding my emotional state is as important as understanding my words.
        - Please use relatable examples or analogies to help clarify more complex Enneagram concepts.
  
        Question: {question} \n
        Chat History: {chat_history} \n
        Previous Chat Summary: {chat_summary} \n
      `);

      const llm = new ChatGoogleGenerativeAI({
        model: "gemini-1.5-pro",
        maxOutputTokens: 2048,
        //streaming: true,
        verbose: true,
      });
      const model = new ChatGroq({
        model: "llama-3.3-70b-versatile",
        temperature: 0,
        maxOutputTokens: 1024,
        streaming: true,
      })

      const { 
        prevMemoryFacts, 
        prevMemorySummaries 
      } = (await zepMemory(
        companion?.id!,
        threadId!,
        userId!,
        question,
      )) ?? { prevMemoryFacts: [], prevMemorySummaries: [] }; 
    
      const chatChain = prompt.pipe(model).pipe(new StringOutputParser());
      const response = await chatChain.invoke({
        question,
        language,
        chat_history: formatVercelMessages(previousMessages),
        chat_fact: formatChatFacts(prevMemoryFacts),
        chat_summary: formatChatSummaries(prevMemorySummaries),
      });
      await zepClient.memory.add(threadId, {
        messages: [
          //{ role: "user", roleType: "user", content: question },
          { role: "assistant", roleType: "assistant", content: response }
        ]
      });

      //console.log(`STATE from generate: '${state}'`);

      return { messages: [new AIMessage(response)] }
    }

    /**==================================================================
     * Build Graph
     ==================================================================*/
    // Define a new graph
    const workflow = new StateGraph(
      { stateSchema: GraphState, },
    )
      .addNode("agent", agent)
      .addNode("retrieve", toolNode)
      //.addNode("gradeDocuments", gradeDocuments)
      //.addNode("rewrite", rewrite)
      .addNode("generate", generate)
      .addNode("normalChat", normalChat)
  
    // Call agent node to decide to retrieve or not
    .addEdge(START, "agent")
    // Decide whether to retrieve
    .addConditionalEdges(
      "agent",
      // Assess agent decision
      shouldRetrieve,
      {
        // Call tool node
        continue: "retrieve",
        end: "normalChat",
      }
    )
  
    //.addEdge("retrieve", "gradeDocuments")
    .addEdge("retrieve", "generate")
  
    // Edges taken after the `action` node is called.
    /*.addConditionalEdges(
      "gradeDocuments",
      // Assess agent decision
      checkRelevance,
      {
        // Call tool node
        yes: "generate",
        no: "rewrite" , // placeholder
      }
    )*/
  
    .addEdge("generate", END)
    .addEdge("normalChat", END)
    //.addEdge("rewrite", "normalChat")

    // Compile
    //const app = workflow.compile({ checkpointer: new MemorySaver(), interruptBefore: ["retrieve"] });
    const app = workflow.compile({ checkpointer: new MemorySaver() });

    /**==================================================================
     * Run the graph
     ==================================================================*/
    const userMessage = messages.slice(-1)
      .filter((message: { role: string }) => message.role === 'user')
    const promptMessage = userMessage[0]?.content
    //console.log("promptMessage@chat: ", promptMessage)

    const config = { recursionLimit: 30, configurable: { thread_id: threadId } };
    const inputs = {
      messages: [
        new HumanMessage(
          promptMessage,
        ),
      ],
    };

    let finalState;
    let finalGeneration: string | undefined = undefined;
    type TokenUsage = {
      completionTokens: number;
      promptTokens: number;
      totalTokens: number;
    }
    
    const sumTokenUsages = {
      outputTokens: 0,
      inputTokens: 0,
      totalTokens: 0
    };
    //const allTokenUsages: TokenUsage[] = [];
    let tokenUsage: LanguageModelUsage | undefined = undefined
    const textEncoder = new TextEncoder();

    const clientStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const output of await app.stream(inputs, config)) {
            for (const [key, value] of Object.entries(output)) {
              const lastMsg = output[key].messages[output[key].messages.length - 1];
              console.log("lastMsg", JSON.stringify(lastMsg, null, 2));
              console.log(`Output from node: '${key}'`);
              console.log("JSON.stringify", JSON.stringify(value, null, 2));
              finalState = key;
              console.log("======================finalState======================", finalState);

              if (value && typeof value === 'object' && 'messages' in value && Array.isArray(value.messages) && value.messages.length > 0) {
                value.messages.forEach((item: any) => {
                  const tokenUsage = item.response_metadata?.tokenUsage;
                  if (tokenUsage) {
                    sumTokenUsages.outputTokens += tokenUsage.outputTokens;
                    sumTokenUsages.inputTokens += tokenUsage.inputTokens;
                    sumTokenUsages.totalTokens += tokenUsage.totalTokens;
                  }
                });

                const lastOutput = value.messages[value.messages.length - 1];
                console.log("lastOutput: ", lastOutput);

                if ((finalState == "generate" && lastOutput && lastOutput?.content) || 
                   (finalState == "normalChat" && lastOutput && lastOutput?.content)){
                  finalGeneration = lastOutput.content;
                  console.log("======================finalGeneration======================", finalGeneration);
                }

                if (finalGeneration) {
                  controller.enqueue(textEncoder.encode(finalGeneration)); // Enqueue the finalGeneration to the stream
                }
              }
            }
          }

          const state = await app.getState(config)
          console.log("state.next", state.next)
        } catch (error) {
          console.error(error);
          return NextResponse.json({ error: "Internal server error" }, { status: 500 });
        }

        let addTransactionPromise;
        let saveCompletionPromise;
        
        if (
          sumTokenUsages &&
          typeof sumTokenUsages.inputTokens === 'number' &&
          typeof sumTokenUsages.outputTokens === 'number'
        ) {
          console.log("====tokenUsages====: ", sumTokenUsages);
          addTransactionPromise = addTransaction({
            userApiCount: 1,
            inputTokens: sumTokenUsages.inputTokens,
            outputTokens: sumTokenUsages.outputTokens,
            model: 'gpt_4o',
            userId,
            notes: `Token count`,
          });
        }
        
        if (promptMessage && finalGeneration && finalGeneration.length > 0) {
          console.log("====saveCompletionToDatabase====");
          
          let threadOperation;
          if (existingThread) {
            threadOperation = saveCompletionToDatabase(companion?.id!, userId, promptMessage, "user", threadId);
          } else {
            const name = promptMessage.substring(0, 30);
            threadOperation = newThread({
              threadId,
              name,
              companionId: companion?.id!,
              content: promptMessage,
              role: "user",
              path
            });
          }
        
          saveCompletionPromise = threadOperation.then(() =>
            saveCompletionToDatabase(companion?.id!, userId, finalGeneration!, "assistant", threadId)
          );
        }
        
        // Run the promises in parallel if both promises exist
        try {
          const promises = [];
          if (addTransactionPromise) promises.push(addTransactionPromise);
          if (saveCompletionPromise) promises.push(saveCompletionPromise);
        
          if (promises.length > 0) {
            await Promise.all(promises);
            console.log("Both addTransaction and saveCompletionToDatabase completed.");
          }
        } catch (error) {
          console.error("Error in parallel operations:", error);
        }
        
        revalidatePath(path);
        finalGeneration = undefined; // Reset finalGeneration after enqueuing
        controller.close();
        
      }
    });

    return new NextResponse(clientStream)
  } catch (e: any) {
    console.error(e);
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}
