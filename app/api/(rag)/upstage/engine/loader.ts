import { checkRequiredEnvVars } from "./shared.mjs";

// Define the known Upstage categories for type safety
type UpstageCategory = 
  | 'table' 
  | 'figure'
  | 'chart'
  | 'heading'
  | 'header'
  | 'footer'
  | 'caption'
  | 'paragraph'
  | 'equation'
  | 'list'
  | 'index'
  | 'footnote';

// Type for Upstage element content
interface UpstageElementContent {
  text: string;
  html: string;
  markdown: string;
}

interface UpstageElement {
  id: number;
  category: UpstageCategory;
  page: number;
  content: UpstageElementContent;
  coordinates?: {
    x: number;
    y: number;
  }[];
  base64_encoding?: string;
}

interface UpstageResponse {
  apiVersion: string;
  modelVersion: string;
  elements: UpstageElement[];
  content: {
    text: string;
    html: string;
    markdown: string;
  };
  usage: {
    pages: number;
  };
}

export type Language = string;
export type ResultType = string;

export async function ensureUserDir(userId: string): Promise<string> {
  // Implementation similar to your existing code
  return `tmp/${userId}`;
}

export async function getUpstageDocuments(
  file: Blob,
  fileName: string,
) {
  checkRequiredEnvVars();
  
  const formData = new FormData();
  formData.append('document', file, fileName);
  formData.append('ocr', 'auto');
  formData.append('coordinates', 'true');
  formData.append('output_formats', JSON.stringify(['markdown']));
  formData.append('model', 'document-parse');
  formData.append('base64_encoding', JSON.stringify(['equation']));

  const response = await fetch('https://api.upstage.ai/v1/document-ai/document-parse', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.UPSTAGE_API_KEY}`,
    },
    body: formData
  });

  if (!response.ok) {
    throw new Error(`Upstage API error: ${response.statusText}`);
  }

  const result = await response.json() as UpstageResponse;
  console.log("Upstage document parse result: ", result)
  
  // Transform the Upstage response to match your document format
  return result
}