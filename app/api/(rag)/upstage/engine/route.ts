import { NextResponse } from 'next/server';
import { auth } from "@clerk/nextjs/server";
import { unlink } from "fs/promises";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { Language, getUpstageDocuments, ensureUserDir } from "./loader";
import { checkRequiredEnvVars } from "./shared.mjs";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { v4 as uuidv4 } from 'uuid'

type SupportedTextSplitterLanguages = 
  | "markdown" 
  | "html" 
  | "cpp" 
  | "go" 
  | "java" 
  | "js" 
  | "php" 
  | "proto" 
  | "python" 
  | "rst" 
  | "ruby" 
  | "rust" 
  | "scala" 
  | "swift" 
  | "latex" 
  | "sol"
// Define the known Upstage categories for type safety
type UpstageCategory = 
  | 'table' 
  | 'figure'
  | 'chart'
  | 'heading'
  | 'header'
  | 'footer'
  | 'caption'
  | 'paragraph'
  | 'equation'
  | 'list'
  | 'index'
  | 'footnote';

// Type for Upstage element content
interface UpstageElementContent {
  text: string;
  html: string;
  markdown: string;
}

interface UpstageElement {
  id: number;
  category: UpstageCategory;
  page: number;
  content: UpstageElementContent;
  coordinates?: {
    x: number;
    y: number;
  }[];
  base64_encoding?: string;
}

export async function POST(req: Request) {
  try {
    const { getToken, userId } = await auth(); 
    const token = await getToken({ template: "supabase" });
    if (!token || !userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    checkRequiredEnvVars();
    
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const formData = await req.formData();
    const file = formData.get('file') as Blob;
    const name = formData.get('name') as string;
    const type = formData.get('type') as string;
    const size = parseInt(formData.get('size') as string, 10);
    const language = formData.get('language') as Language;
    const path = formData.get('path') as string;

    if (!file || !name) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    const extension = name.split(".").pop();
    if (!extension) {
      return NextResponse.json({ error: "Invalid file extension" }, { status: 400 });
    }

    const client = await createClerkSupabaseServerClient(authToken);
    const userDir = await ensureUserDir(userId)
    const randomName = Math.random().toString(36).substring(7);
    const filePath = `${userDir}/${randomName}.${extension}`;

    const upstageResult = await getUpstageDocuments(file, name);

    if (!upstageResult) {
      console.error("No content returned from Upstage Parse");
      return NextResponse.json({ error: "No content returned from parser" }, { status: 500 });
    }

    // Function to split and process the whole document based on content type
    async function processWholeDocument(contentType: SupportedTextSplitterLanguages, content: string, documentId: string) {
      // Configure the splitter for HTML or Markdown
      const splitter = RecursiveCharacterTextSplitter.fromLanguage(contentType, {
        chunkSize: 500,
        chunkOverlap: 50,
        keepSeparator: true, // Keep tags (for html) or formatting (for markdown)
      });

      const splitDocuments = await splitter.createDocuments(
        [content],  // Entire content (HTML or Markdown)
        [{ 
          id: documentId,
          source: name,
          user_id: userId,
        }]
      );
      console.log("splitDocuments", splitDocuments)

      // Add metadata to each split document
      return splitDocuments.map((doc, index) => ({
        ...doc,
        document_id: documentId,
        metadata: {
          ...doc.metadata,
          index,  // Track the chunk index
          contentType,  // Specify 'html' or 'markdown'
          parser: 'upstage',
        },
      }));
    }

    // Function to handle splitting and processing based on content type
    async function processElementContent(
      contentType: SupportedTextSplitterLanguages, 
      content: string, 
      elementMetadata: Record<string, unknown>, 
      documentId: string
    ) {
      // Configure splitter based on content type (html or markdown)
      const splitter = RecursiveCharacterTextSplitter.fromLanguage(contentType, {
        chunkSize: 500,
        chunkOverlap: 50,
        keepSeparator: true, // Keep tags (for html) or formatting (for markdown)
      });

      const splitDocuments = await splitter.createDocuments(
        [content], // Content could be html or markdown
        [elementMetadata]
      );

      // Add metadata to each document
      return splitDocuments.map((doc, index) => ({
        ...doc,
        document_id: documentId,
        metadata: {
          ...doc.metadata,
          index,
          contentType,  // Specify whether it's 'html' or 'markdown'
          parser: 'upstage',
          originalFormat: extension,
          category: elementMetadata.category,
        },
      }));
    }

    // Process each element from Upstage response
    /*const documentId = uuidv4();
    const processedDocs = await Promise.all(upstageResult.elements.map(async (element) => {
      const elementMetadata = {
        id: documentId,
        source: name,
        user_id: userId,
        category: element.category,
        page: element.page,
      };

      const result = [];

      // Check for html content and process
      if (element.content.html) {
        const htmlProcessedDocs = await processElementContent(
          "html", 
          element.content.html, 
          //upstageResult.content.html, // Use the whole HTML content here
          elementMetadata, 
          documentId
        );
        result.push(...htmlProcessedDocs);
      }

      // Check for markdown content and process
      if (element.content.markdown) {
        const markdownProcessedDocs = await processElementContent(
          "markdown", 
          element.content.markdown, 
          //upstageResult.content.markdown,
          elementMetadata, 
          documentId
        );
        result.push(...markdownProcessedDocs);
      }

      return result;
    }));

    const flattenedDocs = processedDocs.flat();*/

    // Step 1: Generate a unique document ID
    const documentId = uuidv4();

    // Step 2: Insert the main document metadata into 'documents_1024'
    await client
      .from("documents_1024")
      .insert({
        id: documentId,
        filename: name,
        type: type,
        size: +size,
        owner_id: userId,
      });

    // Step 3: Process the whole document (either HTML or Markdown)
    let processedDocs = [];
    let docsContent = []
    if (upstageResult.content?.html) {
      // Process the whole HTML document
      processedDocs = await processWholeDocument(
        "html",
        upstageResult.content.html,  // The entire HTML content
        documentId
      );
      docsContent = [upstageResult.content?.html]
    } else if (upstageResult.content?.markdown) {
      // Process the whole Markdown document
      processedDocs = await processWholeDocument(
        "markdown",
        upstageResult.content.markdown,  // The entire Markdown content
        documentId
      );
      docsContent = [upstageResult.content?.markdown]
    } else {
      console.error("No supported content (HTML or Markdown) found in the document.");
      return NextResponse.json({ error: "No supported content found" }, { status: 400 });
    }

    // Step 4: Insert the processed sections into 'document_sections_1024'
    const cohereEmbed = new CohereEmbeddings({
      apiKey: process.env.COHERE_API_KEY,
      model: "embed-multilingual-v3.0",
    });

    try {
      await SupabaseVectorStore.fromDocuments(
        processedDocs,
        cohereEmbed,
        {
          client,
          tableName: "document_sections_1024",
          queryName: "match_document_sections_1024",
        }
      );
      console.log('Documents successfully saved to vector store.');
    } catch (error) {
      console.error('Error saving documents to vector store:', error);
    }

    if (path) {
      revalidatePath(path)      
    }
    //await unlink(filePath);

    return NextResponse.json({ message: "File processed successfully", docsContent });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}