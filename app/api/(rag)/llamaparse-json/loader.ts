import { readdir, writeFile, unlink, mkdir } from "fs/promises";
import { existsSync } from "fs";
import { createMessageContent } from "@llamaindex/core/response-synthesizers";
import {
  Document,
  ImageNode,
  LlamaParseReader,
  PromptTemplate,
  Groq,
  Gemini,
  GEMINI_MODEL 
} from "llamaindex"
import path from 'path';

export type ResultType = "text" | "markdown" | "json";
export const DATA_DIR = '/tmp';

export async function getDocuments(
  userDir: string,
  filePath: string,
  file: Blob,
  fileExtension: string,
  parsingInstruction: string,
  language: Language,
  resultType: ResultType
) {
  // Write the file to the user-specific directory
  const fileBuffer = Buffer.from(await file.arrayBuffer());
  //await writeFile(filePath, fileBuffer, "binary");
  await writeFile(filePath, new Uint8Array(fileBuffer));

  const reader = new LlamaParseReader({ 
    parsingInstruction,
    language,
    resultType 
  });

  const jsonObjs = await reader.loadJson(filePath);
  // Access the first "pages" (=a single parsed file) object in the array
  const jsonList = jsonObjs[0]["pages"];

  const textDocs = getTextDocs(jsonList);
  const imageTextDocs = await getImageTextDocs(jsonObjs, reader);
  const documents = [...textDocs, ...imageTextDocs];

  return documents
}

// Ensure the user-specific directory exists
export async function ensureUserDir(userId: string) {
  const userDir = path.join(DATA_DIR, userId);
  if (!existsSync(userDir)) {
    await mkdir(userDir, { recursive: true });
  } else {
    await cleanFolder(userDir);
  }
  return userDir;
}

// Function to clean all files inside a folder
async function cleanFolder(folderPath: string) {
  try {
    // Check if the folder exists
    if (!existsSync(folderPath)) {
      //console.log(`Folder ${folderPath} does not exist.`);
      return;
    }

    // Read all files in the folder
    const files = await readdir(folderPath);

    // Iterate over each file and delete it
    for (const file of files) {
      const filePath = `${folderPath}/${file}`;
      await unlink(filePath);
      //console.log(`Deleted file: ${filePath}`);
    }

    //console.log(`All files in folder ${folderPath} have been deleted.`);
  } catch (error) {
    console.error(`Error cleaning folder ${folderPath}: ${error}`);
  }
}

// Extract and assign text and page number from jsonList, return an array of Document objects
function getTextDocs(jsonList: { text: string; page: number }[]): Document[] {
  return jsonList.map(
    (page) => new Document({ text: page.text, metadata: { page: page.page } }),
  );
}
// Download all images from jsonObjs, send them to OpenAI API to get alt text, return an array of Document objects
async function getImageTextDocs(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  jsonObjs: Record<string, any>[],
  reader: LlamaParseReader,
): Promise<Document[]> {
  const llm = new Groq({
    model: "llama-3.3-70b-versatile",
    temperature: 0.2,
    maxOutputTokens: 1000,
  });
  const gemini = new Gemini({
    model: GEMINI_MODEL.GEMINI_PRO_1_5_LATEST,
    temperature: 0.2,
  });
  const imageDicts = await reader.getImages(jsonObjs, "images");
  const imageDocs = [];

  for (const imageDict of imageDicts) {
    const imageDoc = new ImageNode({ image: imageDict.path });
    const prompt = new PromptTemplate({
      template: `Describe the image as alt text`,
    });
    const message = await createMessageContent(prompt, [imageDoc]);

    const response = await llm.complete({
      prompt: message,
    });

    /*const response = await gemini.chat({
      messages: [
        { content: 
          "Describe the image as alt text", role: "system" },
      ],
      ...message
    });*/

    console.log("response====>", response)
    const doc = new Document({
      text: response.text, //Groq
      //text: response.message.content, //Gemini
      metadata: { path: imageDict.path },
    });
    imageDocs.push(doc);
  }

  return imageDocs;
}

export type Language =
  | "abq"
  | "ady"
  | "af"
  | "ang"
  | "ar"
  | "as"
  | "ava"
  | "az"
  | "be"
  | "bg"
  | "bh"
  | "bho"
  | "bn"
  | "bs"
  | "ch_sim"
  | "ch_tra"
  | "che"
  | "cs"
  | "cy"
  | "da"
  | "dar"
  | "de"
  | "en"
  | "es"
  | "et"
  | "fa"
  | "fr"
  | "ga"
  | "gom"
  | "hi"
  | "hr"
  | "hu"
  | "id"
  | "inh"
  | "is"
  | "it"
  | "ja"
  | "kbd"
  | "kn"
  | "ko"
  | "ku"
  | "la"
  | "lbe"
  | "lez"
  | "lt"
  | "lv"
  | "mah"
  | "mai"
  | "mi"
  | "mn"
  | "mr"
  | "ms"
  | "mt"
  | "ne"
  | "new"
  | "nl"
  | "no"
  | "oc"
  | "pi"
  | "pl"
  | "pt"
  | "ro"
  | "ru"
  | "rs_cyrillic"
  | "rs_latin"
  | "sck"
  | "sk"
  | "sl"
  | "sq"
  | "sv"
  | "sw"
  | "ta"
  | "tab"
  | "te"
  | "th"
  | "tjk"
  | "tl"
  | "tr"
  | "ug"
  | "uk"
  | "ur"
  | "uz"
  | "vi";

