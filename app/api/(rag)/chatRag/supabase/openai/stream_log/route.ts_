import { NextRequest, NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'
import { auth } from "@clerk/nextjs/server";
import { checkRateLimit } from '@/lib/ratelimitUtil';

import type { Document } from "@langchain/core/documents";

import {
  Runnable,
  RunnableSequence,
  RunnableMap,
  RunnableBranch,
  RunnableLambda,
} from "@langchain/core/runnables";
import { HumanMessage, AIMessage, BaseMessage } from "@langchain/core/messages";
import { ChatAnthropicMessages } from "@langchain/anthropic";
import { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { ChatGroq } from "@langchain/groq";

import { ChatFireworks } from "@langchain/community/chat_models/fireworks";
import { StringOutputParser } from "@langchain/core/output_parsers";
import {
  PromptTemplate,
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";

import weaviate, { ApiKey } from "weaviate-ts-client";
import { WeaviateStore } from "@langchain/weaviate";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { 
  SupabaseVectorStore, 
  SupabaseFilterRPCCall 
} from "@langchain/community/vectorstores/supabase";
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase";


export const runtime = "edge";
export const preferredRegion = ['hnd1']

const RESPONSE_TEMPLATE = `You act as a seasoned Enneagram Coach, tasked to answer any question about my profile.
Integrating insights from various psychological theories and coaching methodologies for a holistic analysis of my Enneagram profile. 
Utilize the missing personality types, the Hornevian and Harmonic groups, and Three Centers for nuanced understanding. 
Aim for engaging, not lecturing.
Using the provided context, answer the user's question to the best of your ability using the resources provided.
Generate a comprehensive and informative answer (but no more than 80 words) for a given question based solely on the provided search results (URL and content).
You must only use information from the provided search results.
Use an unbiased and journalistic tone.
Combine search results together into a coherent answer.
Do not repeat text.
Cite search results using [\${{number}}] notation.
Only cite the most relevant results that answer the question accurately.
Place these citations at the end of the sentence or paragraph that reference them - do not put them all at the end.
If different results refer to different entities within the same name, write separate answers for each entity.
If there is nothing in the context relevant to the question at hand, just say "Hmm, I'm not sure." Don't try to make up an answer.

You should use bullet points in your answer for readability
Put citations where they apply rather than putting them all at the end.

Anything between the following \`context\`  html blocks is retrieved from a knowledge bank, not part of the conversation with the user.

<context>
{context}
<context/>

REMEMBER: If there is no relevant information within the context, just say "Hmm, I'm not sure." Don't try to make up an answer.
Anything between the preceding 'context' html blocks is retrieved from a knowledge bank, not part of the conversation with the user.`;

const REPHRASE_TEMPLATE = `Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.

Chat History:
{chat_history}
Follow Up Input: {question}
Standalone Question:`;

type RetrievalChainInput = {
  chat_history: string;
  question: string;
};

const getRetriever = async (authToken: any, filter: any, funcFilterA: any) => {
  if (
    !process.env.NEXT_PUBLIC_SUPABASE_URL ||
    !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  ) {
    throw new Error(
      "SUPABASE_API_KEY and SUPABASE_URL environment variables must be set",
    );
  }
  const client = await createClerkSupabaseServerClient(authToken);

  const embeddings = new OpenAIEmbeddings({
    modelName: "text-embedding-3-small",
  })
  /*const vectorstore = new SupabaseVectorStore(
    embeddings,
  {
    client,
    tableName: "document_sections_1024",
    queryName: "match_document_sections_1024",
    filter: funcFilterA
  });
  return vectorstore.asRetriever({ k: 6, filter: funcFilterA });*/

  const retriever = new SupabaseHybridSearch(embeddings, {
    client,
    metadata: filter,
    similarityK: 3,
    keywordK: 3,
    tableName: "document_sections_1536",
    similarityQueryName: "match_document_sections_1536",
    keywordQueryName: "kw_match_document_sections",
  });
  return retriever
};


const createRetrieverChain = (llm: any, retriever: Runnable) => {
  // Small speed/accuracy optimization: no need to rephrase the first question
  // since there shouldn't be any meta-references to prior chat history
  const CONDENSE_QUESTION_PROMPT =
    PromptTemplate.fromTemplate(REPHRASE_TEMPLATE);
  const condenseQuestionChain = RunnableSequence.from([
    CONDENSE_QUESTION_PROMPT,
    llm,
    new StringOutputParser(),
  ]).withConfig({
    runName: "CondenseQuestion",
  });
  const hasHistoryCheckFn = RunnableLambda.from(
    (input: RetrievalChainInput) => input.chat_history.length > 0,
  ).withConfig({ runName: "HasChatHistoryCheck" });
  const conversationChain = condenseQuestionChain.pipe(retriever).withConfig({
    runName: "RetrievalChainWithHistory",
  });
  const basicRetrievalChain = RunnableLambda.from(
    (input: RetrievalChainInput) => input.question,
  )
    .withConfig({
      runName: "Itemgetter:question",
    })
    .pipe(retriever)
    .withConfig({ runName: "RetrievalChainWithNoHistory" });

  return RunnableBranch.from([
    [hasHistoryCheckFn, conversationChain],
    basicRetrievalChain,
  ]).withConfig({
    runName: "FindDocs",
  });
};

const formatDocs = (docs: Document[]) => {
  return docs
    .map((doc, i) => `<doc id='${i}'>${doc.pageContent}</doc>`)
    .join("\n");
};

const formatChatHistoryAsString = (history: BaseMessage[]) => {
  return history
    .map((message) => `${message.getType()}: ${message.content}`)
    .join("\n");
};

const serializeHistory = (input: any) => {
  const chatHistory = input.chat_history || [];
  const convertedChatHistory = [];
  for (const message of chatHistory) {
    if (message.human !== undefined) {
      convertedChatHistory.push(new HumanMessage({ content: message.human }));
    }
    if (message["ai"] !== undefined) {
      convertedChatHistory.push(new AIMessage({ content: message.ai }));
    }
  }
  return convertedChatHistory;
};

const createChain = (llm: any, retriever: Runnable) => {
  const retrieverChain = createRetrieverChain(llm, retriever);
  console.log("retrieverChain: ", retrieverChain)
  const context = RunnableMap.from({
    context: RunnableSequence.from([
      ({ question, chat_history }) => ({
        question,
        chat_history: formatChatHistoryAsString(chat_history),
      }),
      retrieverChain,
      RunnableLambda.from(formatDocs).withConfig({
        runName: "FormatDocumentChunks",
      }),
    ]),
    question: RunnableLambda.from(
      (input: RetrievalChainInput) => input.question,
    ).withConfig({
      runName: "Itemgetter:question",
    }),
    chat_history: RunnableLambda.from(
      (input: RetrievalChainInput) => input.chat_history,
    ).withConfig({
      runName: "Itemgetter:chat_history",
    }),
  }).withConfig({ tags: ["RetrieveDocs"] });
  const prompt = ChatPromptTemplate.fromMessages([
    ["system", RESPONSE_TEMPLATE],
    new MessagesPlaceholder("chat_history"),
    ["human", "{question}"],
  ]);

  const responseSynthesizerChain = RunnableSequence.from([
    prompt,
    llm,
    new StringOutputParser(),
  ]).withConfig({
    tags: ["GenerateResponse"],
  });
  return RunnableSequence.from([
    {
      question: RunnableLambda.from(
        (input: RetrievalChainInput) => input.question,
      ).withConfig({
        runName: "Itemgetter:question",
      }),
      chat_history: RunnableLambda.from(serializeHistory).withConfig({
        runName: "SerializeHistory",
      }),
    },
    context,
    responseSynthesizerChain,
  ]);
};

export async function POST(req: NextRequest) {
  const user = await currentUser()
  const userId = user?.id

  if (!userId) {
    return new Response('Unauthorized', {
      status: 401
    })
  }

  const { getToken } = await auth();  
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const rateLimitResponse = await checkRateLimit(req);
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  }

  try {
    const body = await req.json();
    const input = body.input;
    const config = body.config;
    console.log("config: ", config)

    let llm;
    if (config.configurable.llm === "openai_gpt_3_5_turbo") {
      llm = new ChatOpenAI({
        modelName: "gpt-4.1-mini",
        temperature: 0,
      });
    } else if (config.configurable.llm === "fireworks_mixtral") {
      llm = new ChatFireworks({
        modelName: "accounts/fireworks/models/mixtral-8x7b-instruct",
        temperature: 0,
      });
    } else if (config.configurable.llm === "claude-3-opus"){
      llm = new ChatAnthropic({
        modelName: "claude-3-opus-********",
        temperature: 0,
      });
    } else if (config.configurable.llm === "groq_mixtral"){
      llm = new ChatGroq({
        apiKey: process.env.GROQ_API_KEY,
        modelName: "mixtral-8x7b-32768",
        temperature: 0,
      });
    } else if (config.configurable.llm === 'openrouter') {
      llm = new ChatOpenAI(
        {
          modelName: "gryphe/mythomist-7b",
          temperature: 0.8,
          maxTokens: 800,
          streaming: true,
          openAIApiKey: process.env.OPENROUTER_API_KEY,
        },
        {
          basePath: "https://openrouter.ai/api/v1",
        }
      )
    } else {
      throw new Error(
        "Invalid LLM option passed. Must be 'openai' or 'mixtral'. Received: " +
          config.llm,
      );
    }

    /*const filter = {
      "$and": [
        {
          "user_id": {
            "$eq": userId,
          },
        },
        {
          "source": { 
            "$eq": "749878af-47fc-bdb7-1c27d0cea79a.pdf",
          },
        },
      ],
    };*/

    const myFilter = { "user_id": userId}
    const funcFilterA: SupabaseFilterRPCCall = (rpc) =>
      rpc
        //.filter("metadata->>source", "eq", "Mocked up report TW.pdf")
        .filter("metadata->>source", "eq", "749878af-5981-47fc-bdb7-1c27d0cea79a.pdf");
    const retriever = await getRetriever(authToken, myFilter, funcFilterA);
    const answerChain = createChain(llm, retriever);

    /**
     * Narrows streamed log output down to final output and the FindDocs tagged chain to
     * selectively stream back sources.
     *
     * You can use .stream() to create a ReadableStream with just the final output which
     * you can pass directly to the Response as well:
     * https://js.langchain.com/docs/expression_language/interface#stream
     */
    const stream = answerChain.streamLog(input, config, {
      includeNames: body.includeNames,
    });

    // Only return a selection of output to the frontend
    const textEncoder = new TextEncoder();
    const clientStream = new ReadableStream({
      async start(controller) {
        for await (const chunk of stream) {
          controller.enqueue(
            textEncoder.encode(
              "event: data\ndata: " + JSON.stringify(chunk) + "\n\n",
            ),
          );
        }
        controller.enqueue(textEncoder.encode("event: end\n\n"));
        controller.close();
      },
    });

    return new Response(clientStream, {
      headers: { "Content-Type": "text/event-stream" },
    });
  } catch (e: any) {
    console.error(e);
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}