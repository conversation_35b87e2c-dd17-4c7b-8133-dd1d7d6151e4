import { deleteOldThreads } from '@/utils/deleteOldThreads';
import type { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return new Response('Unauthorized', {
        status: 401,
      });
    }
    // Perform the health check when the cron job runs
    // await checkHealthStatus();

    const options = {
      neonDatabaseUrl: process.env.NEON_DATABASE_URL!,
      tableName: 'threads',
      ageInYears: 1,
    };

    const deletedCount = await deleteOldThreads(options);

    console.log(`Deleted ${deletedCount} old threads at ${new Date().toISOString()}.`);
    return Response.json({
      data: `Deleted ${deletedCount} old threads at ${new Date().toISOString()}.`,
    });

  } catch (error: any) {
    console.error('Error deleting old threads:', error);
    return Response.json(
      {
        error: error.message,
      },
      { status: 500 }
    );
  }
}

// Function to check health status
const checkHealthStatus = async () => {
  try {
    const response = await fetch(
      'https://comfymindsapi.onrender.com/healthcheck',
      {
        cache: 'no-store',
      },
    );

    if (response.ok) {
      console.log('Backend API is healthy');
      // Perform actions when the backend is healthy
    } else {
      console.error('Backend API is not healthy');
      // Perform actions when the backend is not healthy
    }
  } catch (error) {
    console.error('Error occurred while checking health status:', error);
    // Perform actions when an error occurs during health check
  }
};