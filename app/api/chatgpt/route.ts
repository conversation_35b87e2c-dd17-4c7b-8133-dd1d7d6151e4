import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from 'next/server';
import { newMessageType, RoleType } from '@/lib/CharacterType';
import OpenAI from 'openai';
import GPT3Tokenizer from 'gpt3-tokenizer';

import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";

const configurationMap = new Map(); // Map to store configurations for each user
const MAX_TOKEN_COUNT = 3000;

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const { 
      promptTemplate, 
      maxOutputTokens, 
      temperature, 
      question, 
      chatHistoryMessages 
    } = await req.json();

    //const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    
    /*if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }*/

    let configuration = configurationMap.get(userId); // Retrieve configuration for the user

    if (!configuration) {
      
      configurationMap.set(userId, configuration); // Store the configuration for the user
    }

    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      }); // Create OpenAIApi instance with the user-specific configuration

    // Create a message list
    const newMessages: newMessageType[] = [
      { role: "user", content: question },
    ];

    // Configure GPT3Tokenizer
    const tokenizer = new GPT3Tokenizer({ type: 'gpt3' });

    // Initialize token count
    let count = 0;

    if (chatHistoryMessages.length) {
      // Add new chatHistoryMessages to the history
      for (const data of chatHistoryMessages.slice().reverse()) {
        let role: RoleType;
        // Set the role
        if (data.type === 'question') {
          role = "user";
        } else {
          role = "assistant";
        }

        // Get the token count
        const encoded = tokenizer.encode(data.text);

        // Calculate the count
        const newCount = count + encoded.text.length;

        // Check if the count exceeds MAX_TOKEN_COUNT
        if (newCount > MAX_TOKEN_COUNT) {
          // Remove the oldest chatHistoryMessages until the count is within the limit
          while (newMessages.length > 1 && count + encoded.text.length > MAX_TOKEN_COUNT) {
            const oldestMessage = newMessages.shift();

            if (oldestMessage) {
              const oldestEncoded = tokenizer.encode(oldestMessage.content);
              count -= oldestEncoded.text.length;
            }
          }
        }

        // Update the count
        count = newCount;

        // Add to the history
        newMessages.push({ role, content: data.text });

        // Stop adding to the history if the count exceeds MAX_TOKEN_COUNT
        if (count > MAX_TOKEN_COUNT) {
          break;
        }
      }
    }

    // Reverse the messages to be in chronological order
    newMessages.reverse();

    // Add a system message to the beginning of the array
    let system_message_prompt;
    if (promptTemplate) {
      if (configuration.promptTemplate !== promptTemplate) {
        // If promptTemplate is different from the previous one, reset the message history
        configuration.promptTemplate = promptTemplate;
        newMessages.length = 0; // Reset the message history
      }
      system_message_prompt = promptTemplate;
    } else {
      system_message_prompt =
        'Your name is 咪咪. You ACT as an Relationship Expert. you answer every response with emojis after the words and you like to use emojis to shorten the response instead of words. Please answer briefly and only use zh-tw';
    }

    newMessages.unshift({
      role: "assistant",
      content: system_message_prompt,
    });

    // Call the function to generate chat completion
    const completion = await generateChatCompletion(openai, newMessages, maxOutputTokens, temperature);

    // Get the response message
    const message = completion.choices[0].message?.content;

    if (!isPro) {
      await incrementApiLimit();
    }
    

    return NextResponse.json({ response: message });
  } catch (error) {
    console.log('error', error);
    return NextResponse.error();
  }
}

async function generateChatCompletion(openai: OpenAI, newMessages: newMessageType[], maxTokens?: number, temperature?: number) {
  if (maxTokens === undefined) {
    maxTokens = 250 
  }
  if (temperature === undefined) {
    temperature = 0.7 
  }

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4.1-mini',
      messages: newMessages,
      max_tokens: maxTokens,
      temperature: temperature,
    });

    return completion;
  } catch (error) {
    // Handle error
    console.error('Failed to generate chat completion:', error);
    throw error;
  }
}
