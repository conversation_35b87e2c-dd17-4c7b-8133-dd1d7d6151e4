import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import prismadb from '@/lib/prismadb';

export async function GET(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const contracts = await prismadb.contractAnalysis.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
    //console.log("contracts: ====>", contracts)

    return NextResponse.json(contracts);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch contracts' }, { status: 500 });
  }
}