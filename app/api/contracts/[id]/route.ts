import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import prismadb from '@/lib/prismadb';

export async function GET(
  req: NextRequest, 
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const contract = await prismadb.contractAnalysis.findFirst({
      where: { 
        id: params.id,
        userId 
      },
      include: {
        risks: true,
        opportunities: true,
      }
    });

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 });
    }

    return NextResponse.json(contract);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch contract' }, { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest, 
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const contract = await prismadb.contractAnalysis.findFirst({
      where: {
        id: params.id,
        userId
      }
    });

    if (!contract) {
      return NextResponse.json({ error: 'Contract not found' }, { status: 404 });
    }

    try {
      await prismadb.$transaction([
        prismadb.contractAnalysis.delete({
          where: {
            id: params.id,
            userId,
          },
        }),
        prismadb.vectorStore.deleteMany({
          where: {
            documentId: params.id,
          },
        }),
      ]);      
      return NextResponse.json({ message: 'Contract deleted successfully' }, { status: 200 });
    } catch (error) {
      console.error('Error deleting records:', error);
      return NextResponse.json({ error: 'Failed to delete contract' }, { status: 500 });
    }
  
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to delete contract' }, { status: 500 });
  }
}