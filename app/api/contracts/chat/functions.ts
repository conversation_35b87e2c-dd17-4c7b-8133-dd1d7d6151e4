import { neon } from '@neondatabase/serverless';
import prismadb from '@/lib/prismadb';
import { v4 as uuidv4 } from 'uuid';
import { getEmbedding } from "@/lib/cohere";
import { RecursiveCharacterTextSplitter } from "@langchain/textsplitters";

const queryFunction = neon(`${process.env.NEON_DATABASE_URL!!}`);
const CHUNK_SIZE = 500;
const BATCH_SIZE = 30; 
const EMBEDDING_DIMENSION = 384; // Cohere embedding dimension
const MODEL_NAME = "embed-multilingual-light-v3.0";

export const createResource = async (
  userId: string,
  contractId: string,
  content: string,
  filePath: string
) => {
  try {
    if (!!userId || !contractId || !content) {
      throw new Error("contractId and content are required.");
    }

    // Step 1: Split the content into manageable chunks
    const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: CHUNK_SIZE });
    const chunks = await textSplitter.splitText(content);

    if (chunks.length === 0) {
      console.log(`No valid chunks to process for Contract ID: ${contractId}`);
      return;
    }

    // Step 2: Generate embeddings in batches
    for (let i = 0; i < chunks.length; i += BATCH_SIZE) {
      const batchChunks = chunks.slice(i, i + BATCH_SIZE);
      const embeddings = (await getEmbedding(batchChunks, MODEL_NAME)) as number[][];

      // Step 3: Construct and execute upsert SQL queries
      const upsertQueries = batchChunks.map((chunk, index) => {
        const embedding = embeddings[index];
        const metadata = JSON.stringify({ userId, contractId }); // Metadata for each chunk
        const cleanedContent = chunk.replace(/'/g, "''"); // Escape single quotes for SQL

        return `
          INSERT INTO "VectorStore" 
            ("id", "documentId", "documentType", "content", "embedding", "metadata", "createdAt")
          VALUES 
            ('${uuidv4()}', '${contractId}', 'ContractAnalysis', '${cleanedContent}', '[${embedding}]'::vector(${EMBEDDING_DIMENSION}), '${metadata}', NOW())
          ON CONFLICT ("id") DO UPDATE 
            SET "embedding" = '[${embedding}]'::vector(${EMBEDDING_DIMENSION}),
                "content" = '${cleanedContent}',
                "metadata" = '${metadata}';
        `;
      });

      // Step 3: Execute queries batch
      const results = await Promise.allSettled(
        upsertQueries.map(async (query) => {
          try {
            await queryFunction(query);
            console.log(`Successfully upserted chunk for Contract ID: ${contractId}`);
          } catch (error) {
            console.error(`Error upserting chunk for Contract ID: ${contractId}`, error);
          }
        })
      );
      
      // Log summary of results
      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          console.log(`Chunk ${index + 1} upserted successfully.`);
        } else {
          console.error(`Failed to upsert chunk ${index + 1}:`, result.reason);
        }
      });
    }

    // Step 4: Update the userFeedback field in the ContractAnalysis table
    const userFeedback = {
      chunks: chunks.map((chunk, index) => ({
        chunkId: uuidv4(),
        content: chunk,
      })),
    };

    await prismadb.contractAnalysis.update({
      where: { id: contractId },
      data: {
        userFeedback,
      },
    });

    return { success: true };
  } catch (error) {
    console.error(`Error processing contract embeddings for Contract ID: ${contractId}`, error);
    return { success: false, error: error.message || "Unknown error" };
  }
};


// Generate a single embedding for a user query
export const generateEmbedding = async (value: string): Promise<number[]> => {
  //console.log("simivaluelarGuides", value)
  const input = value.replace(/\n/g, " ");
  const embedding = await getEmbedding(input, MODEL_NAME); // Assuming this returns a single embedding
  //console.log("embedding", embedding)
  return embedding as number[];
};

// Find relevant content based on query embedding
export const findRelevantContent = async (
  userQuery: string,
  contractId: string,
  filePath: string,
): Promise<Array<{ content: string; similarity: number }>> => {
  try {
    const userQueryEmbedded = await generateEmbedding(userQuery);
    
    const similarityQuery = `
      SELECT
        "content",
        1 - (embedding <=> $1::vector(${EMBEDDING_DIMENSION})) AS similarity
      FROM "VectorStore"
      WHERE "documentId" = $2 
      AND "filePath" = $3
      AND 1 - (embedding <=> $1::vector(${EMBEDDING_DIMENSION})) > 0.3
      ORDER BY similarity DESC
      LIMIT 4;
    `;

    const similarGuides = await queryFunction(similarityQuery, [
      `[${userQueryEmbedded.join(',')}]`,
      contractId,
      filePath,
    ]);

    return similarGuides.map((row: any) => ({
      content: row.content,
      similarity: parseFloat(row.similarity),
    }));
  } catch (error) {
    console.error("Error finding relevant content:", error);
    return [];
  }
};