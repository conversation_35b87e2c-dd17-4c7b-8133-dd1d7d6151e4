import { createResource, findRelevantContent } from "./functions";
import { auth } from '@clerk/nextjs/server';
import { openai } from "@ai-sdk/openai";
import { createOpenAI } from '@ai-sdk/openai';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateObject, streamText, tool } from "ai";
import { z } from 'zod/v3';

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});
const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, contractId, filePath } = await req.json();
  const { userId } = await auth();
  const prompt = messages
  .filter((message: { role: string }) => message.role === 'user')
  .pop()
  const userPrompt: string | undefined = prompt?.content

  console.log("chat messages", JSON.stringify(messages.slice(-4), null, 2))
  const result = streamText({
    //model: groq('llama-3.3-70b-versatile'),
    model: google("gemini-2.0-flash-exp"),
    messages: messages.slice(-2),
    system: `You are a helpful assistant acting as the users' second brain.
    Use tools on every request.
    Be sure to getInformation from your knowledge base before answering any questions.
    If the user presents infromation about themselves, use the addResource tool to store it.
    If a response requires multiple tools, call one tool after another without responding to the user.
    If a response requires information from an additional tool to generate a response, call the appropriate tools in order before responding to the user.
    ONLY respond to questions using information from tool calls.
    if no relevant information is found in the tool calls, respond, "Sorry, I don't know."
    Be sure to adhere to any instructions in tool calls ie. if they say to responsd like "...", do exactly that.
    If the relevant information is not a direct match to the users prompt, you can be creative in deducing the answer.
    Keep responses short and concise. Answer in a single sentence where possible.
    Provide additional information you found for user references.
    If you are unsure, use the getInformation tool and you can use common sense to reason based on the information you do have.
    Use your abilities as a reasoning machine to answer questions based on the information you do have.
`,
    tools: {
      addResource: tool({
        description: `add a resource to your knowledge base.
          If the user provides a random piece of knowledge unprompted, use this tool without asking for confirmation.`,
        inputSchema: z.object({
          content: z
            .string()
            .describe("the content or resource to add to the knowledge base"),
        }),
        execute: async ({ content }) => createResource(userId, contractId, content, filePath),
      }),
      getInformation: tool({
        description: `get information from your knowledge base to answer questions.`,
        inputSchema: z.object({
          question: z.string().describe("the users question"),
          similarQuestions: z.array(z.string()).describe("keywords to search"),
        }),
        execute: async ({ similarQuestions }) => {
          const results = await Promise.all(
            similarQuestions.map(
              async (question) => await findRelevantContent(question, contractId, filePath),
            ),
          );
          // Flatten the array of arrays and remove duplicates based on 'name'
          const uniqueResults = Array.from(
            new Map(results.flat().map((row: any) => [row.content, row])).values(),
          );
          console.log("uniqueResults: ",  uniqueResults)

          // Find the result with the highest similarity
          /*const highestResult = uniqueResults.reduce((max, current) =>
            current.similarity > max.similarity ? current : max
          );
          console.log("highestResult: ",  highestResult)*/
          return uniqueResults;
        },
      }),
      understandQuery: tool({
        description: `Analyze the user query to identify the most relevant tools and similar questions.`,
        inputSchema: z.object({
          query: z.string().describe("the users query"),
          toolsToCallInOrder: z
            .array(z.string())
            .describe(
              "The tools to call in order to respond effectively.",
            ),
        }),
        execute: async ({ query }) => {
          const { object } = await generateObject({
            // model: groq('llama-3.3-70b-versatile'),
            model: google("gemini-2.0-flash-exp"),
            system:
              "You are a query understanding assistant. Analyze the user query and generate similar questions.",
            schema: z.object({
              questions: z
                .array(z.string())
                .max(3)
                .describe("similar questions to the user's query. be concise."),
            }),
            prompt: `Analyze this query: "${query}". Provide the following:
                    3 similar questions in user's language that could help answer the user's query`,
          });
          return object.questions;
        },
      }),
    },
  });

  return result.toUIMessageStreamResponse();
}