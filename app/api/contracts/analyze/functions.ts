import { neon } from '@neondatabase/serverless';
import { getEmbedding } from "@/lib/cohere";
import { RecursiveCharacterTextSplitter } from "@langchain/textsplitters";
import { v4 as uuidv4 } from "uuid";

const queryFunction = neon(`${process.env.NEON_DATABASE_URL!!}`);

const cleanContent = (input: string | string[] | undefined | null): string => {
  if (Array.isArray(input)) {
    input = input.join(' ');
  }
  if (typeof input !== 'string') {
    console.error("Invalid input for cleanContent:", input);
    return '';
  }
  return input
    .trim() // Remove leading/trailing spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces/newlines with a single space
    .replace(/'/g, "''"); // Escape single quotes for safety
};
// Helper function to check if the content is valid (non-empty)
const isValidContent = (content: string): boolean => {
  return content.trim().length > 0;
};

type UpdateEmbeddingsParams = {
  tableName: string;
  embeddingColumn?: string;
  dimension?: number;
  model?: string;
  batchSize?: number;
  filter?: Record<string, string | number>;
  filePath: string
}

export const updateEmbeddings = async ({
  tableName,
  embeddingColumn = "embedding",
  dimension = 384,
  model = "embed-multilingual-light-v3.0",
  batchSize = 30,
  filter = {},
  filePath,
}: UpdateEmbeddingsParams): Promise<{ success: boolean; error?: any }> => {
  try {
    // Prepare metadata for upsert (only userId)
    const contractId = filter?.id!
    if (!contractId) return { success: false, error: "" }
    const metadata = {
      userId: filter.userId,
      contractId: filter.id
    }
    // Generate WHERE conditions from filter
    const filterConditions = Object.entries(filter)
      .map(([key, value]) => (typeof value === "string" ? `"${key}" = '${value}'` : `"${key}" = ${value}`))
      .join(" AND ");

    // Construct WHERE clause
    const whereClause = filterConditions ? `${filterConditions}` : "";
    const quotedTableName = `"${tableName}"`;

    // Fetch records with missing embeddings
    const missingEmbeddingsQuery = `
      SELECT id, "userId", "contractText", "summary", "recommendations", "keyClauses", "legalCompliance", 
         "negotiationPoints", "contractDuration", "terminationConditions", "specificClauses", 
         "overallScore", "performanceMetrics", "intellectualPropertyClauses", "customFields", 
         "language", "contractType", "financialTerms"
      FROM ${quotedTableName}
      WHERE ${whereClause};
    `;

    console.log("missingEmgeddingsQuery", missingEmbeddingsQuery)
    const missingEmbeddings = await queryFunction(missingEmbeddingsQuery);
    
    if (!missingEmbeddings.length) {
      console.log(`No records to process in table: ${tableName}`);
      return { success: false, error: "" };
    }
    console.log(`Found ${missingEmbeddings.length} records to process in ${tableName}`);

    const compensationQuery = `
      SELECT * 
      FROM "CompensationStructure"
      WHERE "contractId" = $1;
    `;
    const compensation = await queryFunction(compensationQuery, [contractId]);
    const risksQuery = `
      SELECT * 
      FROM "Risk"
      WHERE "contractId" = $1;
    `;
    const risks = await queryFunction(risksQuery, [contractId]);
    const opportunitiesQuery = `
      SELECT * 
      FROM "Opportunity"
      WHERE "contractId" = $1;
    `;
    const opportunities = await queryFunction(opportunitiesQuery, [contractId]);
    

    // Initialize the text splitter for chunking the contract text
    const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 500 });

    // Process records in batches
    for (let i = 0; i < missingEmbeddings.length; i += batchSize) {
      const batchRecords = missingEmbeddings.slice(i, i + batchSize);

      // Prepare the content for embedding (fields that are not empty)
      const fields = [
        'summary',
        'recommendations',
        'keyClauses',
        'negotiationPoints',
        'performanceMetrics'
      ];

      // Process each record in the batch
      const updatePromises = batchRecords.map(async (record) => {
        const { id, contractText } = record;

        // First handle contractText (split it into chunks)
        const contractChunks = await textSplitter.splitText(contractText);

        // Generate embeddings for the contract chunks in batches
        const chunkEmbeddings: any[] = [];
        for (let j = 0; j < contractChunks.length; j += batchSize) {
          const chunkBatch = contractChunks.slice(j, j + batchSize);
          const chunkBatchEmbeddings = await getEmbedding(chunkBatch, model);
          chunkEmbeddings.push(...chunkBatchEmbeddings);
        }

        // Prepare embeddings for other fields (no splitting)
        const fieldContents = fields.map(field => {
          let content = record[field];
            
          if (Array.isArray(content)) {
            content = content.join(' ');
          }
          // Check if content is empty (trim and check length)
          if (!content.trim()) {
            return null; // Skip this field if the content is empty
          }
          const fieldContent = `${field.toUpperCase()}: ${content}`;
          
          return {
            fieldName: field,
            content: cleanContent(fieldContent)
          };
        }).filter(Boolean);

        // Check if there are any valid fields to process
        if (fieldContents.length === 0) {
          console.log(`No valid fields for contract ID: ${id}`);
          return; // Skip processing if no valid fields
        }
          
        // Generate embeddings for each of the other fields in batches
        const fieldEmbeddings: any = [];
        for (let j = 0; j < fieldContents.length; j += batchSize) {
          const fieldBatch = fieldContents.slice(j, j + batchSize);
          const fieldBatchEmbeddings = await getEmbedding(fieldBatch.map(f => f?.content!), model);
          fieldEmbeddings.push(...fieldBatchEmbeddings);
        }

        // Upsert contract text chunks with embeddings
        const upsertChunkPromises = contractChunks.map(async (chunk, chunkIndex) => {
          try {
            const chunkEmbeddingVector = chunkEmbeddings[chunkIndex];

            const insertQuery = `
              INSERT INTO "VectorStore" 
                ("id", "documentId", "documentType", "content", "embedding", "metadata", "filePath", "createdAt")
              VALUES 
                ('${uuidv4()}', '${id}', 'ContractAnalysis', '${chunk}', '[${chunkEmbeddingVector}]'::vector(${dimension}),
                '${JSON.stringify(metadata)}', '${filePath}', NOW())
              ON CONFLICT ("id") DO UPDATE
                SET "embedding" = '[${chunkEmbeddingVector}]'::vector(${dimension});
            `;

            //console.log("Insert fields Query===============>", insertQuery)

            await queryFunction(insertQuery);
            console.log(`Upserted chunk embedding for contract ID: ${id}`);
          } catch (error) {
            console.error(`Failed to upsert chunk embedding for contract ID: ${id}`, error);
          }
        });

        // Wait for all chunk upserts to complete
        await Promise.all(upsertChunkPromises);

        // Now handle the embeddings for other fields (summary, recommendations, etc.)
        const upsertFieldPromises = fieldContents.map(async (field, fieldIndex) => {
          try {
            const embeddingVector = fieldEmbeddings[fieldIndex];

            // Construct the insert query for each field
            const insertQuery = `
              INSERT INTO "VectorStore" 
                ("id", "documentId", "documentType", "content", "embedding", "metadata", "filePath", "createdAt")
              VALUES 
                ('${uuidv4()}', '${id}', 'ContractAnalysis', '${field?.content!}', '[${embeddingVector}]'::vector(${dimension}),
                '${JSON.stringify(metadata)}', '${filePath}', NOW())
              ON CONFLICT ("id") DO UPDATE
                SET "embedding" = '[${embeddingVector}]'::vector(${dimension});
            `;

            await queryFunction(insertQuery);
            console.log(`Upserted embedding for field: ${field?.fieldName!} of contract ID: ${id}`);
          } catch (error) {
            console.error(`Failed to upsert embedding for field: ${field?.fieldName!} of contract ID: ${id}`, error);
          }
        });

        await Promise.all(upsertFieldPromises);
      });
      await Promise.all(updatePromises);

      
      /**
       * Handle Risks
       **/ 
      const riskContents = risks.map(r => 
        `RISKS: ${r.risk || ''} ${r.explanation || ''} ${r.severity || ''}`
      );
  
      // Filter out invalid content (empty content)      
      const validRiskContents = riskContents.filter(isValidContent);

      // Only process valid risk contents for embeddings
      if (validRiskContents.length > 0) {
        const riskEmbeddings = await getEmbedding(validRiskContents.map(cleanContent), model);
        
        const upsertRiskPromises = risks.map(async (risk, index) => {
          const content = `RISKS: ${risk.risk || ''} ${risk.explanation || ''} ${risk.severity || ''}`;
          
          // Skip empty content
          if (isValidContent(content)) {
            try {
              const embedding = riskEmbeddings[index];
              const upsertQuery = `
                INSERT INTO "VectorStore" 
                  ("id", "documentId", "documentType", "content", "embedding", "metadata", "filePath", "createdAt")
                VALUES 
                  ('${uuidv4()}', '${risk.contractId}', 'ContractAnalysis', '${cleanContent(content)}', '[${embedding}]'::vector(${dimension}),
                  '${JSON.stringify(metadata)}', '${filePath}', NOW())
                ON CONFLICT ("id") DO UPDATE
                  SET "embedding" = '[${embedding}]'::vector(${dimension});
              `;
            
              await queryFunction(upsertQuery);
              console.log(`Successfully upserted embedding for Risk ID: ${risk.id}`);
            } catch (error) {
              console.error(`Failed to upsert embedding for Risk ID: ${risk.id}`, error);
            }
          } else {
            console.log(`Skipping empty risk content for Risk ID: ${risk.id}`);
          }
        });
      
        await Promise.all(upsertRiskPromises);
      } else {
        console.log('No valid risk content to process.');
      }

      /**
       * Handle Opportunities
       **/ 
      const opportunityContents = opportunities.map(o => 
        `OPPORTUNITIES: ${o.opportunity || ''} ${o.explanation || ''} ${o.impact || ''}`
      );
      
      // Filter out invalid content (empty content)
      const validOpportunityContents = opportunityContents.filter(isValidContent);
      
      // Only process valid opportunity contents for embeddings
      if (validOpportunityContents.length > 0) {
        const opportunityEmbeddings = await getEmbedding(validOpportunityContents.map(cleanContent), model);
      
        const upsertOpportunityPromises = opportunities.map(async (opportunity, index) => {
          const content = `OPPORTUNITIES: ${opportunity.opportunity || ''} ${opportunity.explanation || ''} ${opportunity.impact || ''}`;
          
          // Skip empty content
          if (isValidContent(content)) {
            try {
              const embedding = opportunityEmbeddings[index];
              const upsertQuery = `
                INSERT INTO "VectorStore" 
                  ("id", "documentId", "documentType", "content", "embedding", "metadata", "filePath", "createdAt")
                VALUES 
                  ('${uuidv4()}', '${opportunity.contractId}', 'ContractAnalysis', '${cleanContent(content)}', '[${embedding}]'::vector(${dimension}),
                  '${JSON.stringify(metadata)}', '${filePath}', NOW())
                ON CONFLICT ("id") DO UPDATE
                  SET "embedding" = '[${embedding}]'::vector(${dimension});
              `;
            
              await queryFunction(upsertQuery);
              console.log(`Successfully upserted embedding for Opportunity ID: ${opportunity.id}`);
            } catch (error) {
              console.error(`Failed to upsert embedding for Opportunity ID: ${opportunity.id}`, error);
            }
          } else {
            console.log(`Skipping empty opportunity content for Opportunity ID: ${opportunity.id}`);
          }
        });
      
        await Promise.all(upsertOpportunityPromises);
      }
      
      /**
       * Handle compensation
       **/ 
      const compensationContents = compensation.map((comp) => {
        const content = `COMPENSATIONS: ${comp.baseSalary || ''} ${comp.bonuses || ''} ${comp.equity || ''} ${comp.otherBenefits || ''}`;
        return content;
      });
      
      // Filter out invalid content (empty content)
      const validCompensationContents = compensationContents.filter(isValidContent);
      
      // Only process valid compensation contents for embeddings
      if (validCompensationContents.length > 0) {
        const compensationEmbeddings = await getEmbedding(validCompensationContents.map(cleanContent), model);
        
        const upsertCompensationPromises = compensation.map(async (comp, index) => {
          const content = `COMPENSATIONS: ${comp.baseSalary || ''} ${comp.bonuses || ''} ${comp.equity || ''} ${comp.otherBenefits || ''}`;
          
          // Skip empty content
          if (isValidContent(content)) {
            try {
              const embedding = compensationEmbeddings[index];
              const upsertQuery = `
                INSERT INTO "VectorStore" 
                  ("id", "documentId", "documentType", "content", "embedding", "metadata", "filePath", "createdAt")
                VALUES 
                  ('${uuidv4()}', '${comp.contractId}', 'ContractAnalysis', '${cleanContent(content)}', '[${embedding}]'::vector(${dimension}),
                  '${JSON.stringify(metadata)}', '${filePath}', NOW())
                ON CONFLICT ("id") DO UPDATE
                  SET "embedding" = '[${embedding}]'::vector(${dimension});
              `;
              await queryFunction(upsertQuery);
            } catch (error) {
              console.error(`Failed to upsert embedding for Opportunity ID: ${comp.id}`, error);
            }
          } else {
            console.log(`Skipping empty compensation content for contractId: ${comp.contractId}`);
          }
        });

        await Promise.all(upsertCompensationPromises);
    } else {
        console.log('No valid compensation content to process.');
      }

    }
    return { success: true }
  } catch (error) {
    console.error(`Error updating embeddings in table: ${tableName}`, error);
    return { success: false, error };
  }
};