import Replicate from 'replicate'
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";



export async function POST(
    req: Request
  ) {
    try {
      const { userId } = await auth();
      const body = await req.json();
      const {
        prompt,
        negative,
        count,
        height,
        width,
        scale,
        steps,
        seed,
        genImage_API } = body
  
      if (!userId) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
  
    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
        return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    let image = null
    if (genImage_API === 'stable_diffusion') {
      const apiToken = process.env.REPLICATE_API_TOKEN;
  
      if (!apiToken) {
        return new NextResponse("Replicate API token not configured.", { status: 500 });
      }

      const replicate = new Replicate({
        auth: apiToken,
      })

      const output = await replicate.run(
        "stability-ai/stable-diffusion:ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
        {
          input: {
            prompt: prompt,
            negative_prompt: negative,
            height: height,
            width: width,
            num_outputs: count,
            num_inference_steps: steps,
            guideance_scale: scale,
            scheduler: "DPMSolverMultistep",
            seed: seed[0]
          },
        },
      )
      image = output 
      console.log('Response from API:', image) 
    } else {
      throw new Error(`Invalid value for genImage_API: ${genImage_API}`)
    }

    if (!isPro) {
        await incrementApiLimit();
      }
    
    return NextResponse.json({ image })
  } catch (error) {
    console.log('[IMAGE_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}