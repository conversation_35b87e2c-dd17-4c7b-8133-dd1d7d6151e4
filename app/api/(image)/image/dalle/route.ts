import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import OpenAI from "openai";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";

export const runtime = 'edge'
export const preferredRegion = ['sfo1']

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    const body = await req.formData(); // Extract formData from the calling party

    console.log("formData: ", body)

    const mode = body.get("mode");
    const image = body.get("image");
    const mask = body.get("mask");
    const prompt = body.get("prompt");
    const amount = body.get("n") as string;
    const size = body.get("size");
    const count = amount !== null ? parseInt(amount, 10) : 1

    console.log("DalleApi: mode =", mode);
    console.log("DalleApi: image =", image);
    console.log("DalleApi: mask =", mask);
    console.log("DalleApi: count =", count);
    console.log("DalleApi: size =", size);
    console.log("DalleApi: amount =", amount);

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    console.log("mode: ", mode)
    if (mode === "edit" || mode === "contEdit") {
      // Convert image and mask to base64-encoded strings if they are not null
      let imageBase64: string | null = null;
      let maskBase64: string | null = null;

      imageBase64 = image as string;
      maskBase64 = mask as string;
      console.log("image && mask")

      if (!imageBase64 || !maskBase64) {
        // Handle the case where image or mask is null
        return new NextResponse("Image or mask is missing.", { status: 400 });
      }
    
      
      const response = await openai.images.edit({
          image: imageBase64 as any,
          mask: maskBase64 as any,
          prompt: prompt as any,
          n: count as any,
          size: size as any,
      })
      console.log("response.data: ", response.data)
      return NextResponse.json(response.data);


    } else if (mode === "generate") {
      //Mark out if in test
      /*const response = await openai.images.generate({
          prompt: prompt as any,
          n: count as any,
          size: size as any,
      });
      *///

      console.log("text to image")

      interface MyResponse {
        data: { url: string }[]; 
      }

      const response: MyResponse = {
        data: [
          {
            url: 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1697603962/qnyru0csohqmibirtlqb.png'
          }
        ]
      }
      console.log("response.data: ", response.data)
      return NextResponse.json(response.data);
    }
    

  } catch (error) {
    console.error('[IMAGE_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};
