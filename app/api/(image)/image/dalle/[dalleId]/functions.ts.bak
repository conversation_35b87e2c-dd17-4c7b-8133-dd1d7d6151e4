import type { CompletionCreateParams } from 'openai/resources/chat';
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export const functions = [
  {
    name: 'extractImageDescription',
    description:
      'Get the image or picture description from the body of the input text that the user expects to generate a new image.',
    parameters: {
      type: 'object',
      properties: {
        imageDesc: {
          type: 'string',
          description: 'The description of the image prompt.'
        },
        imageStyles: {
          type: 'array',
          items: {
            type: 'string',
            enum: ['Minimalism', 'Retro', 'Vintage', 'Geometric', 'Vector', 'Flat art'],
          },
          description: 'The description of the image style, If not found, randomly select one style for each image, Ex: Minimalism, Retro, Vintage, Geometric, Vector, Flat art.',
        },
      },
      required: ['imageDesc', 'imageStyles'],
    },
  },
];

async function generateImage(
  imageDesc: string,
  imageStyles: string[],
  additionalArgs: {
    mode: string,
    image: string,
    mask: string,
    size: string,
    count: number,
    data: any
  }
) {
  try {

    if (imageDesc && imageStyles.length > 0) {
        console.log("mode: ", additionalArgs.mode)
        if (additionalArgs.mode === "edit" || additionalArgs.mode === "contEdit") {
            // Convert image and mask to base64-encoded strings if they are not null
            let imageBase64: string | null = null;
            let maskBase64: string | null = null;

            imageBase64 = additionalArgs.image as string;
            maskBase64 = additionalArgs.mask as string;
            console.log("image && mask")

            if (!imageBase64 || !maskBase64) {
                // Handle the case where image or mask is null
                throw new Error("Image or mask is missing.");
            }            
            
            const response = await openai.images.edit({
                image: imageBase64 as any,
                mask: maskBase64 as any,
                prompt: imageDesc, 
                n: additionalArgs.count as any,
                size: additionalArgs.size as any,
            })
            console.log("response.data: ", response.data)
            return response.data[0].url


        } else if (additionalArgs.mode === "generate") {
            //Mark out if in test
            const latestThreeStyles = imageStyles.slice(-3);
            const responses = await Promise.all(latestThreeStyles.map(style =>
              openai.images.generate({
                prompt: imageDesc,
                n: additionalArgs.count as any,
                size: additionalArgs.size as any,
              })
            ));
            console.log("text to image")

            return responses.map(response => response.data[0].url);
            //
        }
    } 
    return [];

  } catch (error) {
    console.error("Error generating image:", error);
    throw error; // Rethrow the error to be caught by the calling function
  }
}

async function extractImageDescription(
  imageDesc: string,
  imageStyles: string[],
  additionalArgs: {
    mode: string,
    image: string,
    mask: string,
    size: string,
    count: number,
    data: any,
  }
) {
  try {
    const { mode, image, mask, size, count, data } = additionalArgs;
    const generatedImages: string | (string | undefined)[] | undefined = await generateImage(imageDesc, imageStyles, additionalArgs);

    // Check if generatedImages is defined and an array
    if (generatedImages && Array.isArray(generatedImages)) {
      if (!generatedImages.every(Boolean)) {
        // Handle the case where generated images contain undefined values
        throw new Error("Failed to generate some images.");
      }
    } else {
      // Handle the case where generatedImages is not an array or is undefined
      throw new Error("No images generated or unexpected data structure received.");
    }
    
    const imageUrls: string[] = generatedImages.filter(Boolean) as string[]

    return {
      imageUrls,
    };
  } catch (error) {
    console.error("Error extracting image description:", error);
    throw error;
  }
}

export async function runFunction(name: string, args: any) {
  try {
    switch (name) {
      case "extractImageDescription":
        const { imageDesc, imageStyles, mode, image, mask, size, count, data } = args;
        const additionalArgs = { mode, image, mask, size, count, data };
        return await extractImageDescription(imageDesc, imageStyles, additionalArgs);
      default:
        return null;
    }
  } catch (error) {
    console.error("Error running function:", error);
    return null;
  }
}