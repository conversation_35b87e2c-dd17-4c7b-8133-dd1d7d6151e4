import axios from "axios";
import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import {
  OpenAIStream,
  StreamingTextResponse,
  experimental_StreamData,
} from "ai";
import prismadb from "@/lib/prismadb";
import { PromptTemplate } from '@langchain/core/prompts'
import { functions, runFunction } from "./functions";
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['sfo1']
export const dynamic = 'force-dynamic'; // no caching

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
Your name is {companionName}
{companionInstructions}
`

enum MediaType {
  image = 'image',
  video = 'video',
}

export async function POST(
  req: Request,
  { params }: { params: { dalleId: string } }
  ) {
  /*if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(50, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }*/

  const body = await req.formData(); // Extract formData from the calling party

  //console.log("formData@api/image/dalle: ", body)

  const mode = body.get("mode");
  const image = body.get("image");
  const mask = body.get("mask");
  const messages = body.get("prompt");  //need to replace the prompt to messages
  const amount = body.get("n") as string;
  const size = body.get("size");
  const count = amount !== null ? parseInt(amount, 10) : 1

  const user = await currentUser()
  const userId = user?.id
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }

  if (!mode || !image || !mask || !messages || !amount || !size || !count ) {
    return new NextResponse("Messages not found", { status: 404 });
  }

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: "Dalle", //companion.name,
    //userName: user.firstName || '',
    companionInstructions: "",//companion.instructions,
    //companionSeed: companion.seed
  })


  // Get the last 8 messages
  const latestMessages = messages.slice(-9);

  const combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element


  console.log("combinedMessages: ", combinedMessages)

    // check if the conversation requires a function call to be made
    const initialResponse = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: combinedMessages,
        stream: true,
        functions,
        function_call: "auto",
    });

    let imageUrls: string[] = []

    const data = new experimental_StreamData();
    const funcStream = OpenAIStream(initialResponse, {
      onStart: async () => {
        await saveCompletionToDatabase(
            params.dalleId, userId, prompt.content, "user"
      )},
      onCompletion: async () => {
        if (imageUrls) {
          const updatePrismadb = async (imageUrls: string[]) => {
            for (const imageUrl of imageUrls) {
              try {
                const { data: cloudinary_resp } = await axios.post('/api/visionUpload', {imageUrl})
                console.log("cloudinary_resp@api/image/vision", cloudinary_resp)
                  await prismadb.companion.update({
                    where: {
                      id: params.dalleId,
                    },
                    data: {
                      medias: {
                        create: [
                          {
                            userId,
                            url: imageUrl, //cloudinary_resp.uploadedImageUrl,
                            mediaType: MediaType.image,
                            desc: "DallE",
                          },
                        ],
                      },
                      messages: {
                        create: [
                          {
                            image: imageUrl, //cloudinary_resp.uploadedImageUrl,
                            content: "",
                            role: "assistant",
                            userId,
                          },
                        ],
                      },
                    },
                  })
              } catch (error) {
                console.error('Error updating prismadb:', error)
              }
            }
          }
        }
      },
      onFinal: async (completion: string) => {
        await saveCompletionToDatabase(
          params.dalleId, userId, completion, "assistant"        
        )

        data.close();
      },
      experimental_onFunctionCall: async (
        { name, arguments: args },
        createFunctionCallMessages,
        ) => {
        const { mode, image, mask, size, count, data } = args;
        const additionalArgs = { mode, image, mask, size, count, data };
        const result = await runFunction(name, {...additionalArgs });
        console.log("result: ", result)
        
        if (result !== null) {
          if ('imageUrls' in result!) {
            const resultImageUrls = (result as { imageUrls: string[] }).imageUrls;
            imageUrls = resultImageUrls;
            console.log("imageUrls: ", imageUrls)

          }
        }
        
        // Construct prompt for image caption.
        const currentMessage = {
            role: 'user', content: "what's in this image? Response in zh-TW",
        }

        // Construct content array based on the number of imageUrls
        const contentArray = [
            { type: "text", text: currentMessage.content },
            ...imageUrls.map((imageUrl) => ({
                type: "image_url",
                image_url: {
                "url": imageUrl,
                "detail": "low"
            },
            })),
        ];
  
        const newMessages = [
            ...latestMessages,
            {
            ...currentMessage,
            content: contentArray,
            },
        ];

        console.log("newMessages: ", newMessages)
        return openai.chat.completions.create({
          model: "gpt-4.1",
          stream: true,
          max_tokens: 300,
          messages: newMessages,
        });
      },
    });
    return new StreamingTextResponse(funcStream, {}, data);

}