
import { streamText } from "ai";
import { createOpenAI } from "@ai-sdk/openai";
import { PromptTemplate } from '@langchain/core/prompts';
import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { MemoryManager } from "@/lib/memory";
import { rateLimit } from "@/lib/rate-limit";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { saveCompletionToDatabase } from "@/lib/databaseUtils";
import { MdOutlineFireExtinguisher } from "react-icons/md";

const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

//export const runtime = 'edge'
export const preferredRegion = ['sfo1']


const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
Your name is {companionName}
My name is {userName}

{companionInstructions}

Below are relevant details about {companionName}'s past and the conversation you are in.
{relevantHistory}

Current conversation:
{recentChatHistory}
`

export async function POST(request: Request, props: { params: Promise<{ promptId: string }> }) {
  const params = await props.params;
  try {
    const { 
      messages,
      companionId,
      companionNickName,
      companionInstructions,
      companionSeed
    } = await request.json();
    const user = await currentUser();

    if (!user || !user.firstName || !user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    /*const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }
    
    const identifier = request.url + "-" + user.id;
    const { success } = await rateLimit(identifier);

    if (!success) {
      return new NextResponse("Rate limit exceeded", { status: 429 });
    }*/

    /*const companion = await saveCompletionToDatabase(
      params.promptId, user.id, prompt, "user"
    );

    if (!companion) {
      return new NextResponse("Companion not found", { status: 404 });
    }*/

    //console.log("messages: ", messages)

    const name = companionId;
    const companion_file_name = name + ".txt";

    const companionKey = {
      companionName: name!,
      userId: user.id,
      modelName: "gpt-4.1-mini",
    };
    const memoryManager = await MemoryManager.getInstance();

    const records = await memoryManager.readLatestHistory(companionKey);
    if (records.length === 0) {
      await memoryManager.seedChatHistory(companionSeed, "\n\n", companionKey);
    }

    // Query Pinecone
    const recentChatHistory = await memoryManager.readLatestHistory(companionKey);
    //console.log("RecentChatHistory: ", recentChatHistory)

    // Right now the preamble is included in the similarity search, but that
    // shouldn't be an issue

    const similarDocs = await memoryManager.vectorSearch(
      recentChatHistory,
      companion_file_name
    );

    let relevantHistory = "";
    if (!!similarDocs && similarDocs.length !== 0) {
      relevantHistory = similarDocs.map((doc) => doc.pageContent).join("\n");      
    }
    console.log("Relevant History: ", relevantHistory)
  
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    const systemPrompt = await systemPromptTemplate.format({
      companionName: companionNickName,
      userName: user.firstName,
      companionInstructions: "companionInstructions",
      recentChatHistory: "recentChatHistory",
      relevantHistory: "relevantHistory",
    })

    // Get the last 8 messages
    const latestMessages = messages.slice(-2);

    const combinedMessages = [
      { role: 'system', content: systemPrompt },
      ...latestMessages,
    ]

    const prompt = combinedMessages
      .filter(message => message.role === 'user')
      .pop() //retrieve the last element

    const result = streamText({
      model: openai('gpt-4.1-mini'),
      messages: combinedMessages,
      async onFinish({ text }) {
                await saveCompletionToDatabase(
          params.promptId, user.id, prompt.content, "user"
        );
        await saveCompletionToDatabase(
          params.promptId, user.id, text, "assistant"
        );
        await memoryManager.writeToHistory(user.firstName + ": " + prompt.content + "\n" + companionNickName + ": " + text, companionKey);
      },
    })

    /*if (!isPro) {
      await incrementApiLimit();
    }*/


    return result.toUIMessageStreamResponse();

  } catch (error) {
    return new NextResponse("Internal Error", { status: 500 });
  }
};