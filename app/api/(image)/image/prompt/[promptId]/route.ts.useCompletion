
import OpenAI from 'openai';
import { OpenAIStream, StreamingTextResponse } from "ai";
import { PromptTemplate } from '@langchain/core/prompts';
import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import { MemoryManager } from "@/lib/memory";
import { rateLimit } from "@/lib/rate-limit";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { saveCompletionToDatabase } from "@/lib/databaseUtils";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

//export const runtime = 'edge'


const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
My name is {userName}

{companionInstructions}

Below are relevant details about {companionName}'s past and the conversation you are in.
{relevantHistory}

Current conversation:
{recentChatHistory}
`;

export async function POST(
  request: Request,
  { params }: { params: { promptId: string } }
) {
  try {
    const { 
      prompt, 
      companionId, 
      companionNickName, 
      companionInstructions, 
      companionSeed 
    } = await request.json();
    const user = await currentUser();

    if (!user || !user.firstName || !user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }
    
    /*const identifier = request.url + "-" + user.id;
    const { success } = await rateLimit(identifier);

    if (!success) {
      return new NextResponse("Rate limit exceeded", { status: 429 });
    }*/

    /*const companion = await saveCompletionToDatabase(
      params.promptId, user.id, prompt, "user"
    );

    if (!companion) {
      return new NextResponse("Companion not found", { status: 404 });
    }*/

    const name = companionId;
    const companion_file_name = name + ".txt";

    const companionKey = {
      companionName: name!,
      userId: user.id,
      modelName: "gpt-4.1-mini",
    };
    const memoryManager = await MemoryManager.getInstance();

    const records = await memoryManager.readLatestHistory(companionKey);
    if (records.length === 0) {
      await memoryManager.seedChatHistory(companionSeed, "\n\n", companionKey);
    }
    //await memoryManager.writeToHistory(user.firstName + ": " + prompt + "\n", companionKey);

    // Query Pinecone
    const recentChatHistory = await memoryManager.readLatestHistory(companionKey);
    console.log("RecentChatHistory: ", recentChatHistory)

    // Right now the preamble is included in the similarity search, but that
    // shouldn't be an issue

    /*const similarDocs = await memoryManager.vectorSearch(
      recentChatHistory,
      companion_file_name
    );

    let relevantHistory = "";
    if (!!similarDocs && similarDocs.length !== 0) {
      relevantHistory = similarDocs.map((doc) => doc.pageContent).join("\n");      
    }
    console.log("Relevant History: ", relevantHistory)*/
  
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    const systemPrompt = await systemPromptTemplate.format({
      companionName: companionNickName,
      userName: user.firstName,
      companionInstructions: "companionInstructions",
      recentChatHistory: "recentChatHistory",
      relevantHistory: "relevantHistory",
    });


    const response = await openai.chat.completions.create({
      model: 'gpt-4.1-mini',
      stream: true,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
    });
    
    const stream = OpenAIStream(response, {
      onStart: async () => {
        await saveCompletionToDatabase(
          params.promptId, user.id, prompt, "user"
        );
        //await memoryManager.writeToHistory(user.firstName + ": " + prompt, companionKey);
      },
      onToken: async (token: string) => {
        console.log(token);
      },
      onCompletion: async (completion: string) => {        
        await saveCompletionToDatabase(
          params.promptId, user.id, completion, "assistant"
        );
        await memoryManager.writeToHistory(user.firstName + ": " + prompt + "\n" + companionNickName + ": " + completion, companionKey);
      },
    });

  
   
    if (!isPro) {
      await incrementApiLimit();
    }
    

    return new StreamingTextResponse(stream);
  } catch (error) {
    return new NextResponse("Internal Error", { status: 500 });
  }
};