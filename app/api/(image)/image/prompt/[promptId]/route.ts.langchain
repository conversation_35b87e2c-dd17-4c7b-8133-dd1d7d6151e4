"@langchain/core/runnables"
import { StreamingTextResponse, LangChainStream } from "ai";
import { ChatOpenAI } from "@langchain/openai";
import {
  RunnableSequence,
  RunnablePassthrough,
} from "langchain/schema/runnable";
import { BytesOutputParser, StringOutputParser } from 'langchain/schema/output_parser';
import { PromptTemplate } from '@langchain/core/prompts';
import { auth, currentUser } from "@clerk/nextjs/server";
import { CallbackManager } from "langchain/callbacks";
import { NextResponse } from "next/server";

import { MemoryManager } from "@/lib/memory";
import { rateLimit } from "@/lib/rate-limit";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { saveCompletionToDatabase } from "@/lib/databaseUtils";

//export const runtime = 'edge'

type ChainInput = {
  question: string;
  companionName: string;
  userFirstName: string;
  companionInstructions: string;
  relevantHistory: string;
  recentChatHistory: string;
};


const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.

{companionInstructions}

Below are relevant details about {companionName}'s past and the conversation you are in.
{relevantHistory}

Current conversation:
{recentChatHistory}
 
{userFirstName}: {question}
`;

export async function POST(
  request: Request,
  { params }: { params: { promptId: string } }
) {
  try {
    const { prompt } = await request.json();
    const user = await currentUser();

    if (!user || !user.firstName || !user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }
    
    const identifier = request.url + "-" + user.id;
    const { success } = await rateLimit(identifier);

    if (!success) {
      return new NextResponse("Rate limit exceeded", { status: 429 });
    }

    const companion = await saveCompletionToDatabase(
      params.promptId, user.id, prompt, "user"
    );

    if (!companion) {
      return new NextResponse("Companion not found", { status: 404 });
    }

    const name = companion.id;
    const companion_file_name = name + ".txt";

    const companionKey = {
      companionName: name!,
      userId: user.id,
      modelName: "gpt-4.1-mini",
    };
    const memoryManager = await MemoryManager.getInstance();

    const records = await memoryManager.readLatestHistory(companionKey);
    if (records.length === 0) {
      await memoryManager.seedChatHistory(companion.seed, "\n\n", companionKey);
    }
    await memoryManager.writeToHistory(user.firstName + ": " + prompt + "\n", companionKey);

    // Query Pinecone

    const recentChatHistory = await memoryManager.readLatestHistory(companionKey);
    console.log("RecentChatHistory: ", recentChatHistory)

    // Right now the preamble is included in the similarity search, but that
    // shouldn't be an issue

    const similarDocs = await memoryManager.vectorSearch(
      recentChatHistory,
      companion_file_name
    );

    let relevantHistory = "";
    if (!!similarDocs && similarDocs.length !== 0) {
      relevantHistory = similarDocs.map((doc) => doc.pageContent).join("\n");      
    }
    console.log("Relevant History: ", relevantHistory)
    const { handlers } = LangChainStream();

    const imagePromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    const model = new ChatOpenAI({
        modelName: "gpt-4.1-mini",
        temperature: 0.8,
        streaming: false,
        callbackManager: CallbackManager.fromHandlers(handlers),
      });

    // Turn verbose on for debugging
    //model.verbose = true;

    /**
    * Chat models stream message chunks rather than bytes, so this
    * output parser handles serialization and encoding.
    */
    //const outputParser = new BytesOutputParser();
    const outputParser = new StringOutputParser();

    const chain = RunnableSequence.from([
      {
        question: (input: ChainInput) => input.question,
        companionName: (input: ChainInput) => input.companionName,
        userFirstName: (input: ChainInput) =>  input.userFirstName,
        companionInstructions: (input: ChainInput) =>  input.companionInstructions,
        recentChatHistory: (input: ChainInput) =>  input.recentChatHistory,
        relevantHistory: (input: ChainInput) =>  input.relevantHistory,
      },
      imagePromptTemplate,
      model,
      outputParser,
    ]);

    //const stream = await chain.stream({
    const resp = await chain.invoke({
        question: prompt,
        companionName: companion.name,
        userFirstName: user.firstName,
        companionInstructions: companion.instructions,
        recentChatHistory: recentChatHistory,
        relevantHistory: relevantHistory,
      }
    );
    
    const cleaned = resp.replaceAll(",", "");
    const chunks = cleaned.split("\n");
    const response = chunks[0];

    await memoryManager.writeToHistory(companion.name + ": " + response.trim(), companionKey);
    var Readable = require("stream").Readable;

    let s = new Readable();
    s.push(response);
    s.push(null);
    console.log("response: ", response)
    
    if (response !== undefined && response.length > 1) {
      memoryManager.writeToHistory(companion.name + ": " + response.trim(), companionKey);

      await saveCompletionToDatabase(
        params.promptId, user.id, response.trim(), "assistant"
      );
    }
    
   
    if (!isPro) {
      await incrementApiLimit();
    }
    

    return new StreamingTextResponse(s);
  } catch (error) {
    return new NextResponse("Internal Error", { status: 500 });
  }
};