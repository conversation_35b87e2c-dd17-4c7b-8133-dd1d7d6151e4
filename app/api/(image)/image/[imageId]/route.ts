import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import OpenAI from "openai";

import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";



const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export async function POST(
  req: Request
) {
  try {
    const { userId } = await auth();
    const body = await req.json();
    const { prompt, amount = 1, resolution = "512x512" } = body;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!prompt) {
      return new NextResponse("Prompt is required", { status: 400 });
    }

    if (!amount) {
      return new NextResponse("Amount is required", { status: 400 });
    }

    if (!resolution) {
      return new NextResponse("Resolution is required", { status: 400 });
    }

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    /*const response = await openai.images.generate({
      prompt,
      n: parseInt(amount, 10),
      size: resolution,
      response_format: "b64_json"
    });*/

    interface MyResponse {
      data: { url: string }[]; 
    }

    const response: MyResponse = {
      data: [
        {
          url: 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1696580405/out-2_zwmma7.png'
        }
      ]
    }
    console.log("response.data: ", response.data)

    if (!isPro) {
      await incrementApiLimit();
    }

    /*const image: any = response.data[0].b64_json;
    const buffer = Buffer.from(image, "base64");
    const file: any = buffer;
    file.name = "image.png";

    const  variation_resp = await openai.images.createVariation({
      image: file, 
      n: 1, 
      size: resolution, 
      response_format: "b64_json"
    });

    const variation: any = variation_resp.data[0].b64_json
    console.log('[IMAGE_VARIATION]', variation);*/

    return NextResponse.json(response.data);
  } catch (error) {
    console.log('[IMAGE_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};