import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';
import OpenAI from "openai";
import { selectRandomStyles } from '@/lib/selectRandomStyles'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

type Style = 'Minimalism' | 'Retro' | 'Vintage' | 'Geometric' | 'Vector' | 'Flat art';
const styles: Style[] = ['Minimalism', 'Retro', 'Vintage', 'Geometric', 'Vector', 'Flat art'];

export const functions: ChatCompletionCreateParams.Function[] = [
  {
    name: 'extractImageDescription',
    description:
      'Get the image or picture description from the body of the input text that the user expects to generate a new image.',
    inputSchema: {
      type: 'object',
      properties: {
        imageDesc: {
          type: 'string',
          description: 'The description of the image prompt.'
        },
        numberOfImage: {
          type: 'array',
          items: {
            type: 'number',
            enum: [1, 2, 3, 4],
          },
          description: 'The number of image to generate.',
        },
        imageStyles: {
          type: 'array',
          items: {
            type: 'string',
            enum: ['Minimalism', 'Retro', 'Vintage', 'Geometric', 'Vector', 'Flat art'],
          },
          description: 'The description of the image style.',
        },
      },
      required: ['imageDesc', 'numberOfImage', 'imageStyles'],
    },
  },
];

async function generateImage(numberOfImage: number, imageDesc: string, imageStyles: string[]) {
  try {
    let listOfImageStyles: string[] = [];

    // When user have specify the image style and the number of image
    if (imageStyles && imageStyles.length > 0 && numberOfImage && numberOfImage > 0) {
      const numberOfStylesToSlice = Math.min(numberOfImage, 3); // limit to max=3
      listOfImageStyles = imageStyles.slice(-numberOfStylesToSlice);

    // User have specify the number of image but not image style
    } else if (numberOfImage && numberOfImage > 0 ) {
      listOfImageStyles = selectRandomStyles(styles, Math.min(numberOfImage, 3)); // limit to max=3

    // User only describe the image to generate
    } else {
      listOfImageStyles = selectRandomStyles(styles, 1); // default to 1
    }

    if (imageDesc && listOfImageStyles.length > 0) {
      /*const responses = await Promise.all(listOfImageStyles.map(style =>
        openai.images.generate({
          prompt: `${imageDesc} with style ${style}`, 
          n: 1,
          size: "512x512",
        })
      ));

      return responses.map(response => response.data[0].url);*/

      // Test usage //
      interface MyResponse {
        data: { url: string }[]; 
      }

      const response: MyResponse = {
        data: [
          {
            url: process.env.IMAGE_PROVIDER ?? 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1700058646/Intake_stywil.png'
          },
          {
            url: 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1697603962/qnyru0csohqmibirtlqb.png'
          }
        ]
      }

      return response.data.map(item => item.url);
      // Test usage //  
      
    }
  
    return [];

  } catch (error) {
    console.error("Error generating image:", error);
    throw error; // Rethrow the error to be caught by the calling function
  }
}

async function extractImageDescription(numberOfImage: number, imageDesc: string, imageStyles: string[]) {
  try {
    const generatedImages: (string | undefined)[] = await generateImage(numberOfImage, imageDesc, imageStyles);
    const imageUrls: string[] = generatedImages.filter(Boolean) as string[]

    return {
      imageUrls,
    };
  } catch (error) {
    console.error("Error extracting image description:", error);
    throw error;
  }
}

export async function runFunction(name: string, args: any) {
  try {
    switch (name) {
      case "extractImageDescription":
        if (args.imageDesc) {
          return await extractImageDescription(args.numberOfImage[0], args.imageDesc, args.imageStyles);
        } else {
          return null
        }      
      default:
        return null;
    }
  } catch (error) {
    console.error("Error running function:", error);
    return null;
  }
}