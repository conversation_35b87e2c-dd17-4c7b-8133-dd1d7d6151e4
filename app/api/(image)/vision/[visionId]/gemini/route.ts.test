//import axios from "axios";
import { kv } from "@vercel/kv";
import { revalidatePath } from 'next/cache'
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import {
  Message,
  OpenAIStream,
  GoogleGenerativeAIStream,
  StreamingTextResponse,
  experimental_StreamData,
} from "ai";
import prismadb from "@/lib/prismadb";
import { Role } from "@prisma/client";
import { PromptTemplate } from '@langchain/core/prompts'
import { tools, functions, runFunction } from "./functions";
import { 
  saveCompletionToDatabase,
  saveVisionMessageToDatabase,
} from "@/lib/databaseUtils";
import { v4 as uuidv4 } from 'uuid'
import { buildGoogleGenAIPrompt } from '@/lib/buildGoogleGenAIPrompt'
import { apiBaseUrl } from "@/constants";


export const runtime = "edge";
export const preferredRegion = ['hnd1']
//export const dynamic = 'force-dynamic'; // no caching

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});


const genAI = new GoogleGenerativeAI(
    process.env.GOOGLE_API_KEY || ''
  );

const safetySettings = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
];

//const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
//Your name is {companionName}
const TEMPLATE = `
{companionInstructions}

Your name is {companionName}
{companionSeed}
`

enum MediaType {
  image = 'image',
  video = 'video',
}


async function imageUrlToGenerativePart(url: string, mimeType: string) {
  try {
    const response: Response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image. Status: ${response.status} ${response.statusText}`);
    }
    const blob = await response.arrayBuffer();
    const imageBase64 = Buffer.from(blob).toString('base64');
    return {
      inlineData: {
        data: imageBase64,
        mimeType,
      },
    };
  } catch (error) {
    console.error(error);
    throw new Error('Failed to fetch or process image data');
  }
}

export async function POST(
  req: Request,
  { params }: { params: { visionId: string } }
  ) {
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(50, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  //const formData = await req.formData();
  const {
    chatRuns,
    companion,
    messages,
    data: imageToVisionData, //rename to avoid conflict.
    function_call } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const path = `/vision/${params.visionId}`
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  // req.json() and req.formData(), use either one, cannot retrieve twice in the req body.
  /*if (formData) {
    try {
      // Access the formData from req.body
      console.log("formData: ", formData)

      const mode = formData.get("mode");
      const image = formData.get("image");
      const mask = formData.get("mask");
      const prompt = formData.get("prompt");
      const amount = formData.get("n") as string;
      const size = formData.get("size");
      const count = amount !== null ? parseInt(amount, 10) : 1

      console.log("VisionApi: mode =", mode);
      console.log("VisionApi: image =", image);
      console.log("VisionApi: mask =", mask);
      console.log("VisionApi: count =", count);
      console.log("VisionApi: size =", size);

      if (mode === "edit" || mode === "contEdit") {
        // Convert image and mask to base64-encoded strings if they are not null
        let imageBase64: string | null = null;
        let maskBase64: string | null = null;
    
        imageBase64 = image as string;
        maskBase64 = mask as string;
        console.log("image && mask")
    
        if (!imageBase64 || !maskBase64) {
          // Handle the case where image or mask is null
          return new NextResponse("Image or mask is missing.", { status: 400 });
        }
      
        
        const response = await openai.images.edit({
            image: imageBase64 as any,
            mask: maskBase64 as any,
            prompt: prompt as any,
            n: 1,
            size: "512x512",
        })
        console.log("response.data: ", response.data)
        return NextResponse.json(response.data);
    
      } else if (mode === "generate") {
        // Mark out if in test
        const response = await openai.images.generate({
            prompt: prompt.content as any,
            n: 1,
            size: "512x512",
        });
        //
    
        console.log("text to image")
    
        // For Test
        interface MyResponse {
          data: { url: string }[]; 
        }
    
        const response: MyResponse = {
          data: [
            {
              url: 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1697603962/qnyru0csohqmibirtlqb.png'
            }
          ]
        }
        console.log("response.data: ", response.data)
        return NextResponse.json(response.data);
      }
      //


    } catch (error) {
      console.error("Error fetching formData:", error);
      return new NextResponse("formData not found", { status: 404 });
    }
  }*/

  if (chatRuns <= 1 || !chatRuns) {
    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫助嗎？`
    async () => {
      if (greetingMessage) {
        await saveCompletionToDatabase(
          params.visionId, userId, greetingMessage, "assistant"
        );
      }
    }

    return new NextResponse(greetingMessage)
  }

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: companion.name,
    //userName: user.firstName || '',
    companionInstructions: "", //companion.instructions,
    companionSeed: "" //companion.seed
  })

  // Get the last 8 messages
  const latestMessages = messages.slice(-2);

  const combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages.slice(-1)
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element

  //const prompt = messages[messages.length - 1];


  if (imageToVisionData && imageToVisionData.chatWithVisionImageUrl.length > 0) {  
    // Ask OpenAI for a streaming chat completion given the prompt
    /*const imageWithMessage = prompt.content + '\n' + `![image](${imageToVisionData.chatWithVisionImageUrl})`;
    latestMessages[latestMessages.length - 1] = {
      role: 'user',
      content: imageWithMessage
    };*/

    const userMessage = messages.slice(-1)
      .filter((message: { role: string }) => message.role === 'user')
    console.log("userMessage@cheers: ", userMessage)

    let imageUrl = imageToVisionData.chatWithVisionImageUrl;

    if (!imageUrl || typeof imageUrl !== 'string') {
      imageUrl = ''; // Assign an empty string as a fallback
    }
      
    // Check if imageUrl is a non-empty string before calling imageUrlToBase64
    if (imageUrl.trim() !== '') {
      try {
        let newPrompt: string = ''
        if (imageUrl.length > 0) {
          newPrompt = prompt.content + '\n' + 
          `![image](${imageUrl})`;
          
        } else {
          newPrompt = prompt.content
        }
        
        await saveCompletionToDatabase(
          params.visionId, userId, newPrompt, "user"
        );

        
        const imageParts = [
          imageUrlToGenerativePart(imageUrl, "image/png"), 
        ];

        //const promptMessage = userMessage[0]?.content
        const promptMessage = prompt?.content
        //const promptMessage = "What's in the picture?";

        const geminiProVisoinConfig = { 
            temperature: 0.4, 
            topP: 1, 
            topK: 32, 
            maxOutputTokens: 4096 
        };

        const result = await genAI
          .getGenerativeModel({ 
            model: 'gemini-pro-vision',
            generationConfig: geminiProVisoinConfig,
            safetySettings,
        })
          .generateContentStream([
            promptMessage, 
            ...imageParts
        ]);

        let text = '';
        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          console.log("chunkText", chunkText);
          text += chunkText;
        }


        
        // Convert the response into a friendly text-stream
        // Nextjs AI package not yet support text respond from imageCpation
        /*const stream = GoogleGenerativeAIStream(result, {
          onStart: async () => {
            let newPrompt: string = ''
            if (imageToVisionData && imageToVisionData.chatWithVisionImageUrl.length > 0) {
                newPrompt = prompt.content + '\n' + 
                `![image](${imageToVisionData.chatWithVisionImageUrl})`;
                
            } else {
                newPrompt = prompt.content
            }
            
            await saveCompletionToDatabase(
                params.visionId, userId, newPrompt, "user"
            );
          },
          onCompletion: async (completion: string) => {
            await saveCompletionToDatabase(
                params.visionId, userId, completion, "assistant"
            );
          },
        })*/

        await saveCompletionToDatabase(
            params.visionId, userId, text, "assistant"
        );

        revalidatePath(path)
        // Respond with the stream
        //return new StreamingTextResponse(stream);
        return new NextResponse(text)
      } catch (err) {
        console.error(err);
        throw new Error('Failed to fetch or process image data');
      }
    }
  }

  // Not yet support
  //const generate_image_tool = Tool(
  //  function_declarations=[generate_image_func],
  //)

  try {
    const func_request = {
        contents: [
        { role: 'user', parts: [{ text: prompt.content }] }
        ],
        tools: tools,
    };

    const response = await genAI
        .getGenerativeModel({ 
        model: 'gemini-pro',
        generationConfig: {"temperature": 0},
        safetySettings,
        })
        .generateContent(func_request);

    const func_response = await response;
    
    //const func_response = response.candidates[0].content.parts[0]
    console.log("func_response from GeminiPro",func_response)
    
    console.log("combinedMessages: ", combinedMessages)
    console.log("prompt: ", func_response)

    //return new NextResponse(result)
  } catch (err) {
    console.error(err);
    throw new Error('Failed to fetch or process image data');
  }

  // check if the conversation requires a function call to be made
  const initialResponse = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: latestMessages, //combinedMessages,
      stream: true,
      functions,
      function_call: "auto",
  });

  let imageUrls: string[] = []
  
  const data = new experimental_StreamData();
  const funcStream = OpenAIStream(initialResponse, {
    experimental_onFunctionCall: async (
      { name, arguments: args },
      createFunctionCallMessages,
      ) => {
      const result = await runFunction(name, args);
      console.log("result: ", result)
      
      if (result !== null) {
        if ('imageUrls' in result!) {
          const resultImageUrls = (result as { imageUrls: string[] }).imageUrls;
          imageUrls = resultImageUrls;
          console.log("imageUrls: ", imageUrls)
      
          // Construct content array based on the number of imageUrls

          const imageParts = await Promise.all(
            imageUrls.map(async (imageUrl) => 
            await imageUrlToGenerativePart(imageUrl, 'image/png'))
          );

          console.log("imageParts", imageParts)

          const geminiProVisoinConfig = { 
            temperature: 0.4, 
            topP: 1, 
            topK: 32, 
            maxOutputTokens: 4096 
          };

          const imageCaptionResult = await genAI
            .getGenerativeModel({ 
              model: 'gemini-pro-vision',
              generationConfig: geminiProVisoinConfig,
              safetySettings,
          })
            .generateContentStream([
              "請描述這兩張圖的差別", 
              ...imageParts
          ]);

          let text = '';
          for await (const chunk of imageCaptionResult.stream) {
            const chunkText = chunk.text();
            console.log("chunkText", chunkText);
            text += chunkText;
          }


          const newMessages = [
            { role: 'user', content: `請根據以下圖片描述，猜測我在想什麼?\n ${text}` },
          ]

          /*const contentArray = [
            { type: "text", text: "what's in this image? Response in zh-TW" },
              ...imageUrls.map((imageUrl) => ({
                type: "image_url",
                image_url: {
                  "url": imageUrl,
                  "detail": "low"
                },
            })),
          ];

          //const funcMessages = createFunctionCallMessages(result)
          const funcMessages = imageUrls.map((imageUrl) => ({
            role: 'assistant',
            content: imageUrl,
          }));

          const newMessages = [
              ...latestMessages,
              {
              role: 'user',
              //...prompt,
              content: contentArray,
              },
          ];

          console.log("newMessages: ", newMessages)
          return openai.chat.completions.create({
            model: "gpt-4.1",
            stream: true,
            max_tokens: 500,
            messages: newMessages,
          });

          return await genAI
          .getGenerativeModel({ 
            model: 'gemini-pro',
            generationConfig,
            safetySettings,
          })
          .generateContentStream(buildGoogleGenAIPrompt(newCombinedMessages));*/


          return openai.chat.completions.create({
            model: "gpt-4.1-mini",
            stream: true,
            messages: [...latestMessages, ...newMessages ],
          });
        }
      }
    },
    onStart: async () => {
      await saveCompletionToDatabase(
          params.visionId, userId, prompt.content, "user"
    )},
    onCompletion: async (completion: string) => {      
      console.log("imageUrls@onCompletion: ", imageUrls)
      if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
        data.append({
          type: 'numOfImage',
          content: '',
          url: '',
          numOfImage: imageUrls.length
        });

        for (const imageUrl of imageUrls) {
          try {
            console.log("imageUrl@onFinal", imageUrl)

            const imageUrlMessage = {
              id: uuidv4(),
              userId,
              role: Role.assistant,
              content: '',
              image: imageUrl,
              companionId: companion.id, 
            };
          
            const completionMessage = await prismadb.message.create({
              data: imageUrlMessage,
              select: { id: true },
            });

            console.log("imageUrlMessage@route/visionId : " ,imageUrlMessage)
            if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
              if (imageUrlMessage) {
                data.append({
                  type: 'image',
                  content: imageUrlMessage.id,
                  url: imageUrl,              
                });
                console.log("imageUrlMessage@vision/route", data)
              }
            }

            console.log("data@route/vision: ", data)

            /*
            const response = await fetch(`${apiBaseUrl}/visionUpload`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ imageUrl }),
            });
          
            if (response.ok) {
              const cloudinary_resp = await response.json();
              // Handle the cloudinary_resp data here
            } else {
              // Handle the error if the request fails
              throw new Error('Failed to fetch data from the server');
            }*/
          } catch (error) {
            console.error('Error updating prismadb:', error)
          }
        }
      }
    },
    onFinal: async (completion: string) => {
      console.log("completion@api/image/vision", completion)
      const messageData = {
        id: uuidv4(),
        userId,
        role: Role.assistant,
        content: completion,
        companionId: companion.id, 
      };
  
      const completionMessage = await prismadb.message.create({
        data: messageData,
        select: { id: true },
      });

      console.log("completionMessage@route/visionId : " ,completionMessage)
      if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
        if (completionMessage) {
          data.append({
            type: 'visionId',
            content: completionMessage.id,
            url: "",
          });


          console.log("completionMessage@vision/route", data)
        }
      }
      data.close();
      revalidatePath(path)
    },
    experimental_streamData: true,
    
  });
  
  return new StreamingTextResponse(funcStream, {}, data);
}