import getCurrentUser from "@/app/actions/getCurrentUser";
import { NextResponse } from "next/server";

import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";

export async function POST(
  request: Request,
) {
  try {
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      userId,
      isGroup,
      members,
      name
    } = body;

    console.log("<EMAIL>: ", userId)

    if (!currentUser?.userId || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 400 });
    }

    if (isGroup && (!members || members.length < 2 || !name)) {
      return new NextResponse('Invalid data', { status: 400 });
    }

    if (isGroup) {
      const newConversation = await prismadb.conversation.create({
        data: {
          name,
          isGroup,
          users: {
            connect: [
              ...members.map((member: { value: string }) => ({  
                userId: member.value 
              })),
              {
                userId: currentUser.userId
              }
            ]
          }
        },
        include: {
          users: true,
        }
      });


       // Update all connections with new conversation
      newConversation.users.forEach((user) => {
        if (user.email) {
          pusherServer.trigger(user.email, 'conversation:new', newConversation);
          console.log("user.email", user.email)
          console.log("pusherServer.trigger(user.email, 'conversation:new')", newConversation)
        }
      });

      return NextResponse.json(newConversation);
    }

    const existingConversations = await prismadb.conversation.findMany({
      where: {
        AND: [
          {
            users: {
              some: {
                userId: currentUser.userId
              }
            }
          },
          {
            users: {
              some: {
                userId: userId
              }
            }
          },
          {
            OR: [
              { isGroup: false },
              { isGroup: null }
            ]
          } 
        ]
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    const singleConversation = existingConversations[0];

    console.log("existingConversations@conversations/route: ", existingConversations)
    console.log("singleConversation@conversations/route: ", singleConversation)

    if (singleConversation) {
      return NextResponse.json(singleConversation);
    }

    const newConversation = await prismadb.conversation.create({
      data: {
        users: {
          connect: [
            {
              userId: currentUser.userId
            },
            {
              userId: userId
            }
          ]
        }
      },
      include: {
        users: true
      }
    });

    console.log("newConversation: ", newConversation)

    // Update all connections with new conversation
    newConversation.users.map((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:new', newConversation);
        console.log("user.email", user.email)
        console.log("pusherServer.trigger(user.email, 'conversation:new', newConversation)")
      }
    });

    return NextResponse.json(newConversation)
  } catch (error) {
    console.log("error: ", error)
    return new NextResponse('Internal Error', { status: 500 });
  }
}