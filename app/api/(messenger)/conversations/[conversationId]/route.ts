import getCurrentUser from "@/app/actions/getCurrentUser";
import { NextResponse } from "next/server";

import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";

interface IParams {
  conversationId?: string;
}

export async function DELETE(request: Request, props: { params: Promise<IParams> }) {
  const params = await props.params;
  try {
    const { conversationId } = params;
    const currentUser = await getCurrentUser();

    if (!currentUser?.id) {
      return NextResponse.json(null);
    }

    const existingConversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId
      },
      include: {
        users: true,
        messages: {
          include: {
            seen: true
          }
        }
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    if (!existingConversation) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    // Disconnect the conversation from persona's conversations lists
    const disconnectPromises = existingConversation.users.map(async (user) => {
      await prismadb.persona.update({
        where: {
          userId: user.userId,
        },
        data: {
          conversations: {
            disconnect: {
              id: existingConversation.id,
            },
          },
          seenMessages: {
            disconnect: existingConversation.messages.map((message) => ({ id: message.id })),
          },
        },
      });
    });
    
    await Promise.all(disconnectPromises);

    console.log("existingConversation", existingConversation)

    const deletedConversation = await prismadb.conversation.delete({
      where: {
        id: conversationId,
      },
    });

    console.log("deletedConversation", deletedConversation)
    



    existingConversation.users.forEach((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:remove', existingConversation);
        //console.log("user.email", user.email)
        //console.log("pusherServer.trigger(user.email, 'conversation:remove', existingConversation)")
      }
    });

    return NextResponse.json(deletedConversation)
  } catch (error) {
    return NextResponse.json(null);
  }
}


export async function PATCH(request: Request, props: { params: Promise<IParams> }) {
  const params = await props.params;
  try {
    const { conversationId } = params;
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      isGroup,
      members,
      name
    } = body;

    if (!currentUser?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (isGroup && (!members || !name)) {
      return new NextResponse('Invalid data', { status: 400 });
    }

    const existingConversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId,
      },
      include: {
        users: true,
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    if (!existingConversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }


    // Add the new users to the conversation
    const updatedConversation = await prismadb.conversation.update({
      where: {
        id: conversationId,
      },
      data: {
        users: {
          connect: [
            ...members.map((member: { value: string }) => ({  
              userId: member.value 
            })),
            {
              userId: currentUser.userId
            }
          ]
        },
      },
      include: {
        users: true,
      },
    });

    // Update all connections with new conversation
    updatedConversation.users.forEach((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:new', updatedConversation);
        //console.log("user.email", user.email)
        //console.log("pusherServer.trigger(user.email, 'conversation:new', updatedConversation)")
      }
    });

    return NextResponse.json(updatedConversation, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}