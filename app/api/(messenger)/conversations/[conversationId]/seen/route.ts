import { NextResponse } from "next/server";

//Use getCurrentUser
import getCurrentUser from "@/app/actions/getCurrentUser";
import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";

interface IParams {
  conversationId?: string;
}

export async function POST(request: Request, props: { params: Promise<IParams> }) {
  const params = await props.params;
  try {
    const body = await request.json();
    const {
      companionUserId,
    } = body;
    const currentUser = await getCurrentUser();
    const {
      conversationId
    } = params;

    
    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Find existing conversation
    const conversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId,
      },
      include: {
        messages: {
          include: {
            seen: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        users: true,
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    if (!conversation) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    // Find last message
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    console.log("conversatione@seen: ", conversation)
    console.log("lastMessage@seen: ", lastMessage)
    if (!lastMessage) {
      return NextResponse.json(conversation);
    }
    
    // Update "seen" information for the last message
    //const seenUsers = lastMessage.seen.map((s) => s.userId);
    // Initialize seenUsers with unique user IDs from lastMessage.seen
    let seenUsers = Array.from(new Set(lastMessage.seen.map((s) => s.userId)));

    if (!seenUsers.includes(currentUser.userId)) {
      // Add the current user to seenUsers
      seenUsers.push(currentUser.userId);
    }
    console.log("seenUsers@seen: ", seenUsers)

    const aiUserId = companionUserId || "3cfca034-5831-489c-a004-949be0b33dc1"
    if (companionUserId && !seenUsers.includes(aiUserId)) {
      // Add the ai user to the list of users who have seen the message
      //seenUsers.push(currentUser.userId);
      //MeMe
      seenUsers.push(aiUserId);
    }

    console.log("seenUsers.push@seen: ", seenUsers)

    // Update the message to connect the current user to "seen"
    const updatedMessage = await prismadb.message.update({
      where: {
        id: lastMessage.id,
      },
      include: {
        sender: true,
        seen: true,
      },
      data: {
        seen: {
          connect: seenUsers.map((userId) => ({
            userId,
          })),
        },
      },
    });

    console.log("updatedMessage@seen: ", updatedMessage)
    // Update all connections with new seen
    await pusherServer.trigger(currentUser.email, 'conversation:update', {
      id: conversationId,
      messages: [updatedMessage]
    });

    // If user has already seen the message, no need to go further
    if (lastMessage.seen.some((item) => item.userId === currentUser.userId)) {
      return NextResponse.json(conversation);
    }

    // Update last message seen
    await pusherServer.trigger(conversationId!, 'message:update', updatedMessage);

    return new NextResponse('Success');
  } catch (error) {
    console.log(error, 'ERROR_MESSAGES_SEEN')
    return new NextResponse('Error', { status: 500 });
  }
}