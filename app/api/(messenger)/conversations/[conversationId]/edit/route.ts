import { NextResponse } from "next/server";

import getCurrentUser from "@/app/actions/getCurrentUser";
import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";


interface IParams {
    conversationId?: string;
  }

export async function PATCH(request: Request, props: { params: Promise<IParams> }) {
  const params = await props.params;
  try {
    const { conversationId } = params;
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const { isGroup, members, name } = body;

    if (!currentUser?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (isGroup && (!members || !name)) {
      return new NextResponse('Invalid data', { status: 400 });
    }

    console.log("members@route/remove", members)
    const existingConversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId,
      },
      include: {
        users: true,
        messages: true,
      },
      //cacheStrategy: {
      //  swr: 60,
      //  ttl: 30
      //},
    });

    if (!existingConversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Remove the specified users from the conversation
    const updatedConversation = await prismadb.conversation.update({
      where: {
        id: conversationId,
      },
      data: {
        users: {
          disconnect: [
            ...members.map((member: { userId: string }) => ({  
              userId: member.userId 
            })),
          ]
        },
      },
      include: {
        users: true,
      },
    })

    console.log("updatedConversation@api/edit :", updatedConversation)

    // Delete seenMessages in Persona associated with the removed users
    const disconnectPromises = existingConversation.users.map(async (user) => {
      await prismadb.persona.update({
        where: {
          userId: user.userId,
        },
        data: {
          seenMessages: {
            disconnect: existingConversation.messages.map((message) => ({
              id: message.id,
            })),
          },
        },
      });
    });
    
    await Promise.all(disconnectPromises);

    await prismadb.message.deleteMany({
      where: {
        userId: {
          in: members.map((member: { userId: string }) => member.userId),
        },
        conversationId: conversationId,
      },
    });
    
    // Check if there are any users left in the conversation
    if (updatedConversation.users.length === 0) {
      // If no users are left, delete the conversation
      await prismadb.conversation.delete({
        where: {
          id: conversationId,
        },
      });
    }

    // Update all connections with the updated conversation
    updatedConversation.users.forEach((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:remove', updatedConversation);
      }
    });

    return NextResponse.json(updatedConversation, { status: 200 });
  } catch (error) {
    console.error(error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

