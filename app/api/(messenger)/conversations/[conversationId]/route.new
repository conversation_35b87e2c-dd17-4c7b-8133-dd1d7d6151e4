import getCurrentUser from "@/app/actions/getCurrentUser";
import { NextResponse } from "next/server";

import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";

interface IParams {
  conversationId?: string;
}

export async function DELETE(
  request: Request,
  { params }: { params: IParams }
) {
  try {
    const { conversationId } = params;
    const currentUser = await getCurrentUser();

    if (!currentUser?.id) {
      return NextResponse.json(null);
    }

    const existingConversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId
      },
      include: {
        users: true
      }
    });

    if (!existingConversation) {
      return new NextResponse('Invalid ID', { status: 400 });
    }

    const deletedConversation = await prismadb.conversation.deleteMany({
      where: {
        id: conversationId,
        users: {
          some: {
            personaUserId: currentUser.userId
          }
        }
      },
    });

    existingConversation.users.forEach((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:remove', existingConversation);
        //console.log("user.email", user.email)
        //console.log("pusherServer.trigger(user.email, 'conversation:remove', existingConversation)")
      }
    });

    return NextResponse.json(deletedConversation)
  } catch (error) {
    return NextResponse.json(null);
  }
}


export async function PATCH(
  request: Request,
  { params }: { params: IParams }
) {
  try {
    const { conversationId } = params;
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      isGroup,
      members,
      name
    } = body;

    if (!currentUser?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (isGroup && (!members || !name)) {
      return new NextResponse('Invalid data', { status: 400 });
    }

    const existingConversation = await prismadb.conversation.findUnique({
      where: {
        id: conversationId,
      },
      include: {
        users: true,
      },
    });

    if (!existingConversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }


    // Add the new users to the conversation
    const updatedConversation = await prismadb.conversation.update({
      where: {
        id: conversationId,
      },
      data: {
        users: {
          connect: [
            ...members.map((member: { value: string }) => ({  
              userId: member.value 
            })),
            {
              userId: currentUser.userId
            }
          ]
        },
      },
      include: {
        users: true,
      },
    });

    // Update all connections with new conversation
    updatedConversation.users.forEach((user) => {
      if (user.email) {
        pusherServer.trigger(user.email, 'conversation:new', updatedConversation);
        //console.log("user.email", user.email)
        //console.log("pusherServer.trigger(user.email, 'conversation:new', updatedConversation)")
      }
    });

    return NextResponse.json(updatedConversation, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}