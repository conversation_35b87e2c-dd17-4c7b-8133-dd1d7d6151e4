import { NextResponse } from "next/server";

import { createOpenAI } from '@ai-sdk/openai';
import { streamText, convertToModelMessages } from 'ai';
import getCurrentUser from "@/app/actions/getCurrentUser";
import getCompanion from "@/app/actions/getCompanion";
import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";
import { Role } from "@prisma/client";
import { PromptTemplate } from '@langchain/core/prompts';


const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});

const model = groq('llama-3.3-70b-versatile');

const TEMPLATE = `Use {companionName}: prefix for who is speaking.
Your name is {companionName}
My name is {userName}

{instructions}
{companionSeed}
`

export async function POST(request: Request, props: { params: Promise<{ conversationId: string }> }) {
  const params = await props.params;
  try {
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      allowJoin,
      messages,
      image,
      companionId,
      companionUserId,
      companionName,
    } = body;

    if (!currentUser?.id || !currentUser.name || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    
    console.log("companionId@/api/messages", companionId)
    console.log("messages@/api/messages", messages)

    // Get the last 8 messages
    const latestMessages = messages.slice(-9);
    const prompt = latestMessages
      .filter((message: { role: string }) => message.role === 'user')
      .pop() //retrieve the last element    
    console.log("prompt@api/messages: ", prompt)
    //const filterPrompt = prompt.content.includes(':') ? prompt.content.split(':').slice(1).join(':').trim() : prompt.content

    if (companionId && companionUserId && allowJoin) {
      const companion = await getCompanion({companionId});
    // MiMi userId
      const aiUserId = companionUserId || "3cfca034-5831-489c-a004-949be0b33dc1"

      const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
      const systemPrompt = await systemPromptTemplate.format({
        companionName: companionName ?? "雙桓",
        userName: currentUser.name,
        instructions: companion?.instructions! ?? "You are a terse bot in a group chat responding to q's.",
        companionSeed: companion?.seed!
      })

      const combinedMessages = [
        { role: 'system', content: systemPrompt },
        ...latestMessages,
      ]
      console.log("combineMessages@api/messages: ", combinedMessages)

      const res = streamText({
        model,
        system: systemPrompt,
        messages: convertToModelMessages(latestMessages),
        async onChunk() {
          const newMessage = await prismadb.message.create({
            include: {
              seen: true,
              sender: true
            },
            data: {
              role: Role.user,
              content: prompt.content,
              image: image,
              
              conversation: {
                connect: { id: params.conversationId }
              },
              sender: {
                connect: { userId: currentUser.userId }
              },
              seen: {
                connect: {
                  userId: currentUser.userId
                }
              },
              companion: {
                connect: { id: companionId },
              },
            }
          });
      
          const updatedConversation = await prismadb.conversation.update({
            where: {
              id: params.conversationId
            },
            data: {
              lastMessageAt: new Date(),
              messages: {
                connect: {
                  id: newMessage.id
                }
              }
            },
            include: {
              users: true,
              messages: {
                include: {
                  seen: true
                }
              }
            }
          });
  
          await pusherServer.trigger(params.conversationId, 'messages:new', newMessage);
  
          const lastMessage = updatedConversation.messages[updatedConversation.messages.length - 1];
  
          updatedConversation.users.map((user) => {
            pusherServer.trigger(user.email!, 'conversation:update', {
              id: params.conversationId,
              messages: [lastMessage]
            });
          });
          
        },
        async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
          console.log("text@api/messages" ,text);
          const newMessage = await prismadb.message.create({
            include: {
              seen: true,
              sender: true
            },
            data: {
              role: Role.assistant,
              content: text,
              image: image,
              
              conversation: {
                connect: { id: params.conversationId }
              },
              sender: {
                connect: { userId: aiUserId }
              },
              seen: {
                connect: {
                  userId: aiUserId
                }
              },
              companion: {
                connect: { id: companionId },
              },
            }
          });
      
          const updatedConversation = await prismadb.conversation.update({
            where: {
              id: params.conversationId
            },
            data: {
              lastMessageAt: new Date(),
              messages: {
                connect: {
                  id: newMessage.id
                }
              }
            },
            include: {
              users: true,
              messages: {
                include: {
                  seen: true
                }
              }
            }
          });
  
          await pusherServer.trigger(params.conversationId, 'messages:new', newMessage);
  
          const lastMessage = updatedConversation.messages[updatedConversation.messages.length - 1];
  
          updatedConversation.users.map((user) => {
            pusherServer.trigger(user.email!, 'conversation:update', {
              id: params.conversationId,
              messages: [lastMessage]
            });
          });
        },
      });
      
      return res.toUIMessageStreamResponse();
    } else {
      // for conversation without ai companion
      const defaultCompanionId = '30eca685-21ee-46ce-b808-70f62d6bc4fe'
      const newMessage = await prismadb.message.create({
        include: {
          seen: true,
          sender: true
        },
        data: {
          role: Role.user,
          content: prompt.content,
          image: image,
          
          conversation: {
            connect: { id: params.conversationId }
          },
          sender: {
            connect: { userId: currentUser.userId }
          },
          seen: {
            connect: {
              userId: currentUser.userId
            }
          },
          companion: {
            connect: { id: defaultCompanionId },
          },
        }
      });

      const updatedConversation = await prismadb.conversation.update({
        where: {
          id: params.conversationId
        },
        data: {
          lastMessageAt: new Date(),
          messages: {
            connect: {
              id: newMessage.id
            }
          }
        },
        include: {
          users: true,
          messages: {
            include: {
              seen: true
            }
          }
        }
      });

      await pusherServer.trigger(params.conversationId, 'messages:new', newMessage);

      const lastMessage = updatedConversation.messages[updatedConversation.messages.length - 1];

      updatedConversation.users.map((user) => {
        pusherServer.trigger(user.email!, 'conversation:update', {
          id: params.conversationId,
          messages: [lastMessage]
        });
      });

      return NextResponse.json(newMessage)
    }
  } catch (error) {
    console.log(error, 'ERROR_MESSAGES')
    return new NextResponse('Error', { status: 500 });
  }
}