import { NextResponse } from "next/server";

import OpenAI from 'openai';
import { OpenAIStream, StreamingTextResponse } from "ai";
import getCurrentUser from "@/app/actions/getCurrentUser";
import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";
import { Role } from "@prisma/client";
import { PromptTemplate } from '@langchain/core/prompts';


const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
Your name is {companionName}
My name is {userName}

{companionInstructions}

Below are relevant details about {companionName}'s past and the conversation you are in.
{relevantHistory}

Current conversation:
{recentChatHistory}
`

export async function POST(
  request: Request,
  { params }: { params: { conversationId: string } }
) {
  try {
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      messages,
      image,
    } = body;

    if (!currentUser?.id || !currentUser.name || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the last 8 messages
    const latestMessages = messages.slice(-2);
    const combinedMessages = [
        { role: 'system', content: 'systemPrompt' },
        ...latestMessages,
      ]

    console.log("combineMessages@api/messages: ", combinedMessages)


    const prompt = combinedMessages
        .filter(message => message.role === 'user')
        .pop() //retrieve the last element
    console.log("prompt@api/messages: ", prompt)

    const companionId = '0f54e57c-02b0-411a-84b7-6a5ef5a32323'
    
    const newMessage = await prismadb.message.create({
      include: {
        seen: true,
        sender: true
      },
      data: {
        role: Role.user,
        content: prompt.content,
        image: image,
        
        conversation: {
          connect: { id: params.conversationId }
        },
        sender: {
          connect: { userId: currentUser.userId }
        },
        seen: {
          connect: {
            userId: currentUser.userId
          }
        },
        companion: {
          connect: { id: companionId },
        },
      }
    });

    
    const updatedConversation = await prismadb.conversation.update({
      where: {
        id: params.conversationId
      },
      data: {
        lastMessageAt: new Date(),
        messages: {
          connect: {
            id: newMessage.id
          }
        }
      },
      include: {
        users: true,
        messages: {
          include: {
            seen: true
          }
        }
      }
    });

    await pusherServer.trigger(params.conversationId, 'messages:new', newMessage);

    const lastMessage = updatedConversation.messages[updatedConversation.messages.length - 1];

    updatedConversation.users.map((user) => {
      pusherServer.trigger(user.email!, 'conversation:update', {
        id: params.conversationId,
        messages: [lastMessage]
      });
    });

    return NextResponse.json(newMessage)
  } catch (error) {
    console.log(error, 'ERROR_MESSAGES')
    return new NextResponse('Error', { status: 500 });
  }
}