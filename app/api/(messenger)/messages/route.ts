import { NextResponse } from "next/server";

import getCurrentUser from "@/app/actions/getCurrentUser";
import { pusherServer } from '@/lib/pusher'
import prismadb from "@/lib/prismadb";
import { Role } from "@prisma/client";


export async function POST(
  request: Request,
) {
  try {
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      message,
      image,
      conversationId
    } = body;

    if (!currentUser?.id || !currentUser?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const companionId = '0f54e57c-02b0-411a-84b7-6a5ef5a32323'
    console.log("message@/api/messages", message)
    
    const newMessage = await prismadb.message.create({
      include: {
        seen: true,
        sender: true
      },
      data: {
        role: Role.user,
        content: message,
        image: image,
        
        conversation: {
          connect: { id: conversationId }
        },
        sender: {
          connect: { userId: currentUser.userId }
        },
        seen: {
          connect: {
            userId: currentUser.userId
          }
        },
        companion: {
          connect: { id: companionId },
        },
      }
    });

    
    const updatedConversation = await prismadb.conversation.update({
      where: {
        id: conversationId
      },
      data: {
        lastMessageAt: new Date(),
        messages: {
          connect: {
            id: newMessage.id
          }
        }
      },
      include: {
        users: true,
        messages: {
          include: {
            seen: true
          }
        }
      }
    });

    await pusherServer.trigger(conversationId, 'messages:new', newMessage);

    const lastMessage = updatedConversation.messages[updatedConversation.messages.length - 1];

    updatedConversation.users.map((user) => {
      pusherServer.trigger(user.email!, 'conversation:update', {
        id: conversationId,
        messages: [lastMessage]
      });
    });

    return NextResponse.json(newMessage)
  } catch (error) {
    console.log(error, 'ERROR_MESSAGES')
    return new NextResponse('Error', { status: 500 });
  }
}