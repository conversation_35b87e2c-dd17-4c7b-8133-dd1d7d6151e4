import { NextResponse } from "next/server"

import getCurrentUser from "@/app/actions/getCurrentUser";
import prismadb from "@/lib/prismadb";

export async function POST(
  request: Request,
) {
  try {
    const currentUser = await getCurrentUser();
    const body = await request.json();
    const {
      name,
      image,
    } = body;

    if (!currentUser?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const updatedUser = await prismadb.persona.update({
      where: {
        id: currentUser.userId
      },
      data: {
        image: image,
        name: name
      },
    });

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.log(error, 'ERROR_MESSAGES')
    return new NextResponse('Error', { status: 500 });
  }
}