import { Embedding } from "@/app/client/fetch/url";
import {
  DATASOURCES_CHUNK_OVERLAP,
  DATASOURCES_CHUNK_SIZE,
} from "@/scripts/constants.mjs";
import {
  Document,
  MetadataMode,
  SentenceSplitter,
  SimpleNodeParser,
  OpenAIEmbedding,
  VectorStoreIndex,
  serviceContextFromDefaults,
} from "llamaindex";
import { HuggingFaceEmbedding } from "llamaindex/embeddings/HuggingFaceEmbedding"
export default async function splitAndEmbed(
  document: string,
): Promise<Embedding[]> {
  const openaiEmbedModel = new OpenAIEmbedding({model: "text-embedding-3-small",});
  const huggingFaceEmbeds = new HuggingFaceEmbedding({
    //apiKey: process.env.HUGGINGFACE_API_TOKEN
  });
  const nodeParser = new SimpleNodeParser({
    chunkSize: DATASOURCES_CHUNK_SIZE,
    chunkOverlap: DATASOURCES_CHUNK_OVERLAP,
  });
  const nodes = nodeParser.getNodesFromDocuments([
    new Document({ text: document }),
  ]);
  const texts = nodes.map((node) => node.getContent(MetadataMode.EMBED));
  const embeddings = await huggingFaceEmbeds.getTextEmbeddingsBatch(texts);

  return nodes.map((node, i) => ({
    text: node.getContent(MetadataMode.NONE),
    embedding: embeddings[i],
  }));
}