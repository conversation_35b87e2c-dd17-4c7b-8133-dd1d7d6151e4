import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@clerk/nextjs/server";
import axios, { AxiosResponse } from 'axios';

export async function POST(req: NextRequest) {
try {
  const { userId } = await auth();
  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const apiKey = process.env.LLAMA_CLOUD_API_KEY;
  if (!apiKey) {
    throw new Error('LLAMA_CLOUD_API_KEY is not set');
  }

  const { jobId } = await req.json();

  if (!jobId) {
    return new NextResponse("JobId is missing in the request body", { status: 422 });
  }

  let config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `https://api.cloud.llamaindex.ai/api/parsing/job/${jobId}/result/markdown`,
    headers: { 
      'Accept': 'application/json', 
      'Authorization': `Bearer ${apiKey}`
    }
  };
  
  // Send the request to llamaindex API
  return axios(config)
    .then((response: AxiosResponse) => {
      return new NextResponse(JSON.stringify(response.data), { status: 200 });
    })
    .catch((error) => {
      console.error(error);
      return new NextResponse("Error checking job status", { status: 500 });
    });
  } catch (error) {
    console.error("Error processing request:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
