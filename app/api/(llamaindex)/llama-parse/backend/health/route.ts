import { NextRequest, NextResponse } from 'next/server';


export async function POST(req: Request) {
  try {
    const response = await fetch(
      `https://comfymindsapi.onrender.com/health`, 
      {
        method: "GET",
      }
    );
    if (response.ok) {
      const status = response.status;
      return new NextResponse(JSON.stringify({ status }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } else {
      console.error('Error response from server:', response.status);
      return new NextResponse('Something went wrong', {
        status: 500,
      });
    }    
  } catch (error) {
    console.error('Error:', error);
    return new NextResponse('Something went wrong', {
      status: 500,
    });
  }
}
