import { NextRequest, NextResponse } from 'next/server';


export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const health = await fetch(
      //`http://192.168.0.30:8000/health`,
      `https://comfymindsapi.onrender.com/health`, 
      {
        method: "GET",
      }
    );
    if (health.ok) {
      const response = await fetch(
        //`http://192.168.0.30:8000/llama_parse/create_job`,
        `https://comfymindsapi.onrender.com/llama_parse/create_job`, 
        {
          method: "POST",
          body: formData,
        }
      );

      if (response.ok) {
        const { job_id } = await response.json();
        console.log('job_id:', job_id);
        
        return new NextResponse(JSON.stringify({ job_id }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } else {
        console.error('Error response from server:', response.status);
        return new NextResponse('Something went wrong', {
          status: 500,
        });
      }
    } else {
      return new NextResponse(
        JSON.stringify({ 
          error: "Server is busy, please retry it later." 
        }), 
        { 
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }

  } catch (error) {
    console.error('Error:', error);
    return new NextResponse('Something went wrong', {
      status: 500,
    });
  }
}
