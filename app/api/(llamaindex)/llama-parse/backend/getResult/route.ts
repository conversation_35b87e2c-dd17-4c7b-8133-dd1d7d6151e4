import { NextRequest, NextResponse } from 'next/server';

type ResponseItem = {
  id_: string;
  text: string;
}

export async function POST(req: Request) {
  try {
    const formData = await req.formData();

    const response = await fetch(
      `http://192.168.0.30:8000/llama_parse/upload_file`, 
      {
        method: "POST",
        body: formData,
      }
    );

    if (response.ok) {
      const responseData: ResponseItem[] = await response.json();
      console.log('responseData: ', responseData);
      
      // Extracting id and text properties from each object in responseData array
      const jobResponse = responseData.map(item => ({
        id: item.id_,
        text: item.text
      }));
      
      return new NextResponse(JSON.stringify({ jobResponse }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } else {
      console.error('Error response from server:', response.status);
      return new NextResponse('Something went wrong', {
        status: 500,
      });
    }

  } catch (error) {
    console.error('Error:', error);
    return new NextResponse('Something went wrong', {
      status: 500,
    });
  }
}
