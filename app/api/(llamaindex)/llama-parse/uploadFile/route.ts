import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@clerk/nextjs/server";
import https from 'https';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    const { userId } = await auth();
    const apiKey = process.env.LLAMA_CLOUD_API_KEY;
    
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    
    if (!apiKey) {
      throw new Error('LLAMA_CLOUD_API_KEY is not set');
    }

    const formData = await req.formData();
    const file = formData.get('file') as Blob;
    const name = formData.get('name') as string;
    const type = formData.get('type') as string;
    const language = formData.get('language') as string;
    const parsing_instruction = formData.get('parsing_instruction') as string;

    // Convert Blob to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const fields = {
      language,
      parsing_instruction
    };
    const boundary = `----WebKitFormBoundary7MA4YWxkTrZu0gW`;
    const postData = generateMultipartBody(fields, buffer, name, type, boundary);

    const options = {
      method: 'POST',
      hostname: 'api.cloud.llamaindex.ai',
      path: '/api/parsing/upload',
      headers: {
        'Content-Type': `multipart/form-data`,
        'Accept': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      maxRedirects: 20
    };
    
    return new Promise<Response>((resolve, reject) => {
      const req = https.request(options, (res) => {
        let chunks: Uint8Array[] = [];
      
        res.on("data", (chunk) => {
          chunks.push(chunk);
        });
      
        res.on("end", () => {
          const body = Buffer.concat(chunks);
          console.log(body.toString());
          const jsonResponse = JSON.parse(body.toString());
          const { id, status } = jsonResponse;
          if (id && status) {
            // Send the id and status back to the client
            resolve(new NextResponse(JSON.stringify({ id, status }), { status: res.statusCode, headers: { 'Content-Type': 'application/json' } }));
          } else {
            // If id or status is missing, handle the error
            reject(new NextResponse("Invalid response from server", { status: 500 }));
          }
        });
      
        res.on("error", (error) => {
          console.error(error);
          reject(new NextResponse("Internal Server Error", { status: 500 }));
        });
      });
      
      req.on('error', (error: Error) => {
        console.error(error);
        reject(new NextResponse("Internal Server Error", { status: 500 }));
      });
      req.setHeader('content-type', 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW');
      // Write the Buffer containing the file content to the request body
      req.write(postData);
      req.end();
    });
  } catch (error) {
    console.error("Error processing request:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

function generateMultipartBody(fields: Record<string, string | string[]>, fileBuffer: Buffer, fileName: string, fileType: string, boundary: string) {
  let postData = '';

  // Add file part
  postData += `--${boundary}\r\n`;
  postData += `Content-Disposition: form-data; name="file"; filename="${fileName}"\r\n`;
  postData += `Content-Type: ${fileType}\r\n\r\n`;
  postData += fileBuffer.toString('binary');
  postData += `\r\n`;
  
  // Add other fields
  for (const [fieldName, fieldValue] of Object.entries(fields)) {
    if (Array.isArray(fieldValue)) {
      // If fieldValue is an array, convert it to a comma-separated string
      postData += `--${boundary}\r\n`;
      postData += `Content-Disposition: form-data; name="${fieldName}"\r\n\r\n`;
      postData += `${fieldValue.join(', ')}\r\n`; // Convert array to string and append to postData
    } else {
      // If fieldValue is a string, treat it as a single value
      postData += `--${boundary}\r\n`;
      postData += `Content-Disposition: form-data; name="${fieldName}"\r\n\r\n`;
      postData += `${fieldValue}\r\n`;
    }
  }

  // Add closing boundary
  postData += `--${boundary}--\r\n`;

  return postData;
}
