import { NextResponse } from 'next/server';
import axios from 'axios';
import { auth } from "@clerk/nextjs/server";
import { unlink } from "fs/promises";
import { revalidatePath } from 'next/cache'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { Language, ResultType, getDocuments, ensureUserDir } from "../loader.ts";
import { checkRequiredEnvVars } from "../shared.mjs";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseVectorStore } from "@/lib/vectorstores/supabase"
import { CohereEmbeddings } from "@langchain/cohere";
import { CharacterTextSplitter, RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { Document } from "@/lib/vectorstores/document"


export async function POST(req: Request) {
  try {
    const { getToken, userId } = await auth(); 
    const token = await getToken({ template: "supabase" });
    if (!token || !userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    const authToken = token ? { Authorization: `Bearer ${token}` } : null;
    checkRequiredEnvVars();
    
    const rateLimitResponse = await checkRateLimit(req);
    if (rateLimitResponse) {
      // If rateLimitResponse is not null, return it directly to the client
      return rateLimitResponse;
    }

    const formData = await req.formData();
    const file = formData.get('file') as Blob;
    const name = formData.get('name') as string;
    const type = formData.get('type') as string;
    const size = parseInt(formData.get('size') as string, 10);
    const language = formData.get('language') as Language;
    const parsing_instruction = formData.get('parsing_instruction') as string;
    const result_type = formData.get('result_type') as ResultType;
    const path = formData.get('path') as string;

    // Accept these file types
    // Markdown, Text, HTML
    const extension = name ? name.split(".").pop() : undefined;
    if (extension === undefined) {
      return NextResponse.json(
        {
          error: "File extension is missing or invalid. Please provide a file with a valid extension (md, txt, pdf, html).",
        },
        { status: 400 }
      );
    }
    if (extension && !["md", "txt", "pdf", "html", "doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)) {
      return NextResponse.json(
        {
          error: [
            "File type not supported.",
            "Please upload a markdown, text, pdf, doc, docx, xls, xlsx, ppt, pptx or html file.",
          ].join("\n"),
        },
        { status: 403 },
      );
    }

    const client = await createClerkSupabaseServerClient(authToken);

    // Ensure the user-specific directory exists
    const userDir = await ensureUserDir(userId)
    const randomName = Math.random().toString(36).substring(7);
    const filePath = `${userDir}/${randomName}.${extension}`;
    
    // Write the file to the user-specific directory
    /*const fileBuffer = Buffer.from(await file.arrayBuffer());
    await writeFile(filePath, fileBuffer, "binary");*/

    const docsContent = await handleLlamaParseToVectorStore(
      userDir,
      filePath,
      file,
      name,
      extension,
      type,
      size,
      language,
      parsing_instruction,
      result_type,
      userId,
      client
    );

    //console.log("return docsContent: ", docsContent)
    if (!docsContent || docsContent.length === 0) {
      console.error("No content returned from handleLlamaParseToVectorStore");
    }
    
    if (path) {
      revalidatePath(path)      
    }
    await unlink(filePath);

    return NextResponse.json({ message: "File processed successfully", docsContent });
  } catch (e: any) {
    console.error(e)
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}

async function handleLlamaParseToVectorStore(
  userDir: string,
  filePath: string,
  file: Blob,
  name: string, 
  extension: string,
  type: string, 
  size: number,
  language: Language, 
  parsing_instruction: string, 
  result_type: ResultType,
  userId: string,
  client: any,
) {

  const documents = await getDocuments(
    userDir,
    filePath,
    file,
    extension,
    parsing_instruction,
    language,
    result_type,
  );
  //console.log("LlamaParse documents: ", documents)

  // Extract text from documents
  const docsContent = documents.map(doc => doc.text);
  //console.log("Extracted texts: ", docsContent);

  if (!docsContent || docsContent.length === 0 || docsContent.some(text => !text)) {
    console.error("No text content extracted from documents");
    return [];
  }

  for (const document of documents) {
    const documentId = document.id_;
    const response = await axios.post(
      "http://127.0.0.1:8000/api/semantic-chunkers",
      {
        documents: document.text
      },
      {
        params: {
          document_id: documentId
        }
      }
    );

    console.log("response: ", response)

    const splitDocuments = response.data.map((chunk: any) => new Document({
      pageContent: chunk.pageContent,
      metadata: {
        ...chunk.metadata,
        index: chunk.metadata.index,
      },
      document_id: chunk.document_id,
    }));

    await Promise.all([
      client
        .from("documents_1024")
        .insert({
          id: documentId,
          filename: name,
          type: type,
          size: +size,
          owner_id: userId,
      }),
      SupabaseVectorStore.fromDocuments(
        splitDocuments,
        // new OpenAIEmbeddings(),
        new CohereEmbeddings({
          apiKey: process.env.COHERE_API_KEY,
          // @ts-ignore
          model: "embed-multilingual-v3.0", // dimension: 1024
        }),
        {
          client,
          tableName: "document_sections_1024",
          queryName: "match_document_sections_1024",
        },
      )
    ]);
  }
  return docsContent
}
