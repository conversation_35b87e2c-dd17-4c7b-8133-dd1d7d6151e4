import { readdir, writeFile, unlink, mkdir } from "fs/promises";
import { existsSync } from "fs";
import { SimpleDirectoryReader } from "@llamaindex/edge/readers/SimpleDirectoryReader";
import { LlamaParseReader } from "@llamaindex/edge/readers/LlamaParseReader";
import path from 'path';

export type ResultType = "text" | "markdown" | "json";
export const DATA_DIR = '/tmp';

export async function getDocuments(
  userDir: string,
  filePath: string,
  file: Blob,
  fileExtension: string,
  parsingInstruction: string,
  language: Language,
  resultType: ResultType
) {
  // Write the file to the user-specific directory
  const fileBuffer = Buffer.from(await file.arrayBuffer());
  //await writeFile(filePath, fileBuffer, "binary");
  await writeFile(filePath, new Uint8Array(fileBuffer));

  const reader = new SimpleDirectoryReader();
  const fileExtToReader: { [key: string]: LlamaParseReader } = {};
  fileExtToReader[fileExtension] = new LlamaParseReader({ 
    parsingInstruction,
    language,
    resultType 
  });

  const overrideReader = new LlamaParseReader({
    language,
    resultType,
    parsingInstruction: 'Extract the table from content'
  })

  // Load files using LlamaParseReader
  return await reader.loadData({
    directoryPath: userDir,
    fileExtToReader,
    //overrideReader
  });
}

// Ensure the user-specific directory exists
export async function ensureUserDir(userId: string) {
  const userDir = path.join(DATA_DIR, userId);
  if (!existsSync(userDir)) {
    await mkdir(userDir, { recursive: true });
  } else {
    await cleanFolder(userDir);
  }
  return userDir;
}

// Function to clean all files inside a folder
async function cleanFolder(folderPath: string) {
  try {
    // Check if the folder exists
    if (!existsSync(folderPath)) {
      //console.log(`Folder ${folderPath} does not exist.`);
      return;
    }

    // Read all files in the folder
    const files = await readdir(folderPath);

    // Iterate over each file and delete it
    for (const file of files) {
      const filePath = `${folderPath}/${file}`;
      await unlink(filePath);
      //console.log(`Deleted file: ${filePath}`);
    }

    //console.log(`All files in folder ${folderPath} have been deleted.`);
  } catch (error) {
    console.error(`Error cleaning folder ${folderPath}: ${error}`);
  }
}

export type Language =
  | "abq"
  | "ady"
  | "af"
  | "ang"
  | "ar"
  | "as"
  | "ava"
  | "az"
  | "be"
  | "bg"
  | "bh"
  | "bho"
  | "bn"
  | "bs"
  | "ch_sim"
  | "ch_tra"
  | "che"
  | "cs"
  | "cy"
  | "da"
  | "dar"
  | "de"
  | "en"
  | "es"
  | "et"
  | "fa"
  | "fr"
  | "ga"
  | "gom"
  | "hi"
  | "hr"
  | "hu"
  | "id"
  | "inh"
  | "is"
  | "it"
  | "ja"
  | "kbd"
  | "kn"
  | "ko"
  | "ku"
  | "la"
  | "lbe"
  | "lez"
  | "lt"
  | "lv"
  | "mah"
  | "mai"
  | "mi"
  | "mn"
  | "mr"
  | "ms"
  | "mt"
  | "ne"
  | "new"
  | "nl"
  | "no"
  | "oc"
  | "pi"
  | "pl"
  | "pt"
  | "ro"
  | "ru"
  | "rs_cyrillic"
  | "rs_latin"
  | "sck"
  | "sk"
  | "sl"
  | "sq"
  | "sv"
  | "sw"
  | "ta"
  | "tab"
  | "te"
  | "th"
  | "tjk"
  | "tl"
  | "tr"
  | "ug"
  | "uk"
  | "ur"
  | "uz"
  | "vi";

  /*export async function getDocuments(
  userDir: string, 
  parsingInstruction: string, 
  language: Language, 
  resultType: ResultType
) {
  const reader = new SimpleDirectoryReader();
  // Load PDFs using LlamaParseReader
  return await reader.loadData({
    directoryPath: userDir,
    fileExtToReader: {
      pdf: new LlamaParseReader({ 
        parsingInstruction,
        language,
        resultType,
        //invalidateCache: true
      }),
    },
  });
}*/