import { put } from "@vercel/blob";
import { NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server'
import { checkRateLimit } from '@/lib/ratelimitUtil';
import { URLDetail } from "@/app/client/fetch/url";

export async function POST(request: Request): Promise<NextResponse> {
  const { userId } = await auth();
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }
  const rateLimitResponse = await checkRateLimit(request);
  if (rateLimitResponse) {
    // If rateLimitResponse is not null, return it directly to the client
    return rateLimitResponse;
  }
  const { searchParams } = new URL(request.url);
  const filename = searchParams.get("filename");
  if (!filename || !request.body) {
    return NextResponse.json(
      { error: "Missing filename URL parameter or request body" },
      { status: 400 },
    );
  }

  try {
    // Convert the request body (stream) to an ArrayBuffer to calculate the size
    const arrayBuffer = await request.arrayBuffer(); 
    const size = arrayBuffer.byteLength;  // Get the size of the file in bytes

    // Upload the file to Vercel Blob storage
    const blob = await put(filename, arrayBuffer, {
      access: "public",
    });
    //const blob = await put(filename, request.body, {
    //  access: "public",
    //});

    const json = {
      type: blob.contentType as URLDetail["type"],
      url: blob.url,
      size: size,
    };

    return NextResponse.json<URLDetail>(json);
  } catch (error) {
    console.error("[Upload]", error);
    return NextResponse.json(
      {
        error: (error as Error).message,
      },
      {
        status: 500,
      },
    );
  }
}
