import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { 
    UIMessage, 
    streamText
  } from 'ai';
 
import { 
    buildGoogleGenAIPrompt,
    buildGoogleGenAIHistory,
} from '@/lib/buildGoogleGenAIPrompt'
import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'
import { 
    saveChatSummaryToDatabase,
  } from "@/lib/databaseUtils";


//export const runtime = 'edge'
export const preferredRegion = ['hnd1']


const TEMPLATE = `
My name is {userName}
{companionInstructions}

Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}
`
const genAI = new GoogleGenerativeAI(
  process.env.GOOGLE_API_KEY || ''
);
const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_API_KEY
});

const generationConfig = {
    stopSequences: ["red"],
    temperature: 0.3,
    topP: 0.1,
    topK: 16,
  };
  
  const safetySettings = [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
  ];


  export async function POST(req: Request, props: { params: Promise<{ intakeId: string }> }) {
    const params = await props.params;
    const { 
      prompt, 
      chatHistoryMessages,
      companionName,
      userName,
      threadId,
      path,
  } = await req.json();
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      return new Response('Unauthorized', {
          status: 401
        })
    }

    const latestMessagesWithoutLast  = chatHistoryMessages.slice(0, -1)
    console.log("chatHistoryMessages: ", latestMessagesWithoutLast)

    const promptMessage = `
  I am a college student and seeking a counselor at school. Please be mindful of my situation, understanding my challenges,
  ${prompt}`



    const model = genAI.getGenerativeModel({ 
      model: "gemini-pro",
      generationConfig,
      //safetySettings,
    });

    const res = streamText({
      model: google('gemini-pro'),
      messages: chatHistoryMessages,
      maxOutputTokens: 2000,      
      async onFinish({ text }) {
        await saveChatSummaryToDatabase(
          text.substring(0, 100),
          text,
          params.intakeId,
          companionName,
          userId,
          userName,
          threadId,
        );

        revalidatePath(path)
      }
    })


    // Respond with the stream
    return res.toUIMessageStreamResponse();
  }