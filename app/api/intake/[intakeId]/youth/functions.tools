import { Tool } from 'ai';

const WebURL = process.env.PUBLISH_URL

export const extractUserInfoTool: Tool[] = [
  {
    type: 'function',
    function: {
      name: 'extractUserInfo',
      description:
        'Get the user background information from the body of the input text',
      parameters: {
        type: 'object',
        properties: {
          userName: {
            type: 'string',
            description: 'Name of the user.'
          },
          userGender: {
            type: 'string',
            enum: ['Man', 'Woman', 'Boy', 'Girl', 'Neutral'],
            description: 'Gender identity of the user. Such as <PERSON>, <PERSON>, Boy, Girl, Neutral'
          },
          userAge: {
            type: 'number',
            description: 'Age of the user.'
          },
          currentRole: {
            type: 'string',
            description: 'The current role of the user.'
          },
        },
        required: ['userName', 'userGender', 'userAge', 'currentRole'],
      },
    }
  },
]

export const extractUserExpectationTool: Tool[] = [
  {
    type: 'function',
    function: {
      name: "extractUserExpectation",
      description:
        'Get the user recent life events from the body of the input text.',
      parameters: {
        type: 'object',
        properties: {
          expectation: {
            type: 'string',
            description: 'User recent life events.',
          },
        },
        required: ['expectation'],
      },
    },
  },
];

export const extractAiSuggestionTool: Tool[] = [
  {
    type: 'function',
    function: {
      name: "extractUserExpectation",
      description:
        'Get the recent life event topic from the body of the input text.',
      parameters: {
        type: 'object',
        properties: {
          expectation: {
            type: 'string',
            description: 'User recent life event topic.',
          },
        },
        required: ['expectation'],
      },
    },
  },
  {
    type: 'function',
    function: {
      name: "extractAiSuggestion",
      description:
        'Get the type of recommended AI from the body of the input text.',
      parameters: {
        type: 'object',
        properties: {
          aiType: {
            type: 'string',
            enum: ['Sn', 'E2', 'E3', 'E4', 'E5', 'E7'],
            description:
              'Get the type of recommended AI, such as Sn, E2, E3, E4, E5, E7.',
          },
        },
        required: ['aiType'],
      },
    },
  },
];


export const detectChatStateTool: Tool[] = [
  {
    type: 'function',
    function: {
      name: 'detectConversationStep',
      description:
        'Given the assistant response message, determine which "conversation progress" we are in',
      parameters: {
        type: 'object',
        properties: {
          conversationStep: {
            type: 'string',
            enum: ['IntermediateStep', 'FinalStep'],
            description: 'Given the assistant response message, determine which "conversation progress" we are in.'
          },
        },
        required: ['conversationStep'],
      },
    },
  }
]

export const extractUserChatSummaryTool: Tool[] = [
  /*{
    type: 'function',
    function: {
      name: 'extractUserInfo',
      description:
        'Get the user background information from the body of the input text',
      parameters: {
        type: 'object',
        properties: {
          userName: {
            type: 'string',
            description: 'Name of the user.'
          },
          userGender: {
            type: 'string',
            enum: ['Man', 'Woman', 'Boy', 'Girl', 'Neutral'],
            description: 'Gender identity of the user. Such as Man, Woman, Boy, Girl, Neutral'
          },
          userAge: {
            type: 'number',
            description: 'Age of the user.'
          },
          currentRole: {
            type: 'string',
            description: 'The current role of the user.'
          },
        },
        required: ['userName', 'userGender', 'userAge', 'currentRole'],
      },
    },
  },*/
  {
    type: 'function',
    function: {
      name: "extractUserChatSummary",
      description:
      "Summarize user's major life events in 20 words from all our conversations.",
      parameters: {
        type: 'object',
        properties: {
          chatSummary: {
            type: 'string',
            description: "Summarize user's major life events in 20 words from all our conversations.",
          },
        },
        required: ['chatSummary'],
      },
    },
  },
];


async function extractUserInfo(userName: string, userGender: string, userAge: number, currentRole: string) {
  return {
    userInfo: [
      {
        userName,
        userGender,
        userAge,
        currentRole,
      }
    ]
  };
}

async function extractUserExpectation(expectation: string) {
  return {
    userExpectation: [
      {
        expectation,
      }
    ]
  };
}

async function detectConversationStep(conversationStep: string) {
  return {
    conversationState: [
      {
        conversationStep,
      }
    ]
  };
}

async function extractUserChatSummary(chatSummary: string) {
  return {
    userChatSummary: [
      {
        chatSummary,
      }
    ]
  };
}


async function assignTarget(personaAge: number) {
  let target = "";
  
  if (personaAge >= 10 && personaAge <= 17) {
    target = "Child";
  } else if (personaAge >= 18 && personaAge <= 38) {
    target = "Youth";
  } else if (personaAge >= 39 && personaAge <= 98) {
    target = "Adult";
  } else {
    target = "Youth";
  }

  return target;
}


async function extractAiSuggestion(aiType: string, personaAge: number) {
  console.log("personaAge: ", personaAge)
  const target: string = await assignTarget(personaAge);
  console.log("target: ", target)
  const aiCompanion = `${aiType}_${target}`;
  console.log("aiCompanion", aiCompanion)
  let companionUrl: string;
  let aiName: string;

  switch (aiCompanion) {
    case 'NVC':
      aiName = "人際溝通";
      companionUrl = `[人際溝通](https://${WebURL}/cheers/e31f14b1-5595-4819-83ac-3756db2482a5)`
      break;
    case 'E4_Adult': //怡瑾
      aiName = "怡瑾";
      companionUrl = `[怡瑾](https://${WebURL}/cheers/fdab7860-443f-465e-914d-5710dfb622a0)`
      break;
    case 'E7_Adult': //樂泰
      aiName = "樂泰";
      companionUrl = `[樂泰](https://${WebURL}/cheers/bc1a0598-eb33-42e1-a506-5dcad719b3a3)`
      break;
    case 'E3_Adult': //力行
      aiName = "力行";
      companionUrl = `[力行](https://${WebURL}/cheers/ac49cda3-ed5a-42df-9016-c6b22ac3a998)`
      break;
    case 'Sn_Adult': //心誼
      aiName = "心誼";
      companionUrl = `[心誼](https://${WebURL}/cheers/4c0071fd-67ff-4add-9251-9cbcc508f621)`
      break;
    case 'E5_Adult': //哲明
      aiName = "哲明";
      companionUrl = `[哲明](https://${WebURL}/cheers/426853f6-a355-4a7e-93ee-ec41d32e14d1)`
      break;
    case 'E2_Adult': //暖陽
      aiName = "暖陽";
      companionUrl = `[暖陽](https://${WebURL}/cheers/37792f0a-3222-42bd-a2aa-b6df93ee280a)`
      break;
    case 'Sn_Youth': //Isa
      aiName = "Isa";
      companionUrl = `[Isa](https://${WebURL}/cheers/73eed7cc-7caf-4050-bd79-45f511f9482d)`
      break;
    case 'E4_Youth': //Fay
      aiName = "Fay";
      companionUrl = `[Fay](https://${WebURL}/cheers/7e013b17-ef4b-4718-b0bd-d79ebaddd1d1)`
      break;
    case 'E5_Youth': //Ethan
      aiName = "Ethan";
      companionUrl = `[Ethan](https://${WebURL}/cheers/7e128820-36c7-4f99-baf1-c50aa23d1e6c)`
      break;
    case 'E2_Youth': //Grace
      aiName = "Grace";
      companionUrl = `[Grace](https://${WebURL}/cheers/49a1b79d-51fa-4e72-9a2c-a86bef2b77e2)`
      break;
    case 'E7_Youth': //Joyce
      aiName = "Joyce";
      companionUrl = `[Joyce](https://${WebURL}/cheers/aebf986c-d038-46da-bc43-4eb265ace1f2)`
      break;
    case 'E3_Youth': //Max
      aiName = "Max";
      companionUrl = `[Max](https://${WebURL}/cheers/ded2ca7f-7f17-4223-92bd-1a35c6c82337)`
      break;
    case 'E5_Child': //小智
      aiName = "小智";
      companionUrl = `[小智](https://${WebURL}/cheers/66339db3-adf2-4c25-9cdf-351d40a72f0a)`
      break;
    case 'E7_Child': //小樂
      aiName = "小樂";
      companionUrl = `[小樂](https://${WebURL}/cheers/8f97bb15-6477-4cae-bd1b-fef57ae46295)`
      break;
    case 'Sn_Child': //小淨
      aiName = "小淨";
      companionUrl = `[小淨](https://${WebURL}/cheers/b04f8f7a-101e-406f-ba26-7d26ab9d7632)`
      break;
    case 'E3_Child': //阿力
      aiName = "阿力";
      companionUrl = `[阿力](https://${WebURL}/cheers/38ef071f-27b5-4548-9bb5-f96d2f436a6e)`
      break;
    case 'E4_Child': //小蝶
      aiName = "小蝶";
      companionUrl = `[小蝶](https://${WebURL}/cheers/c639567d-fb84-46af-be5d-c2cc138c8244)`
      break;
    case 'E2_Child': //暖暖
      aiName = "暖暖";
      companionUrl = `[暖暖](https://${WebURL}/cheers/04a10ef7-7e06-4eab-9ed8-0aeb98482d40)`
      break;
    default:
      // Handle the case for an unknown ai_name or set a default URL
      aiName = "Unknown";
      companionUrl = "/";
      break;
  }

  return {
    aiRecommendation: [
      {
        aiName,
        companionUrl,
      },
    ],
  };
}



export async function runFunction(name: string, argumentsName: any, personaAge: number) {
  try {
    const args = JSON.parse(argumentsName);
    if (Object.keys(args).length === 0) {
      console.log('Arguments object is empty, skipping runFunction');
      return null;
    }
    
    console.log("argumentsName: ", argumentsName)
    console.log("args: ",args)
    switch (name) {
      case "extractUserInfo":
        return await extractUserInfo(args.userName, args.userGender, args.userAge, args.currentRole);
      case "extractUserExpectation":
        return await extractUserExpectation(args.expectation);
      case "extractAiSuggestion":
        if (typeof personaAge === 'number') {
          return await extractAiSuggestion(args.aiType, personaAge);
        } else {
          console.log('Persona age not available, skipping extractAiSuggestion');
          return null;
        }
      case "extractUserChatSummary":
        return await extractUserChatSummary(args.chatSummary);
      case "detectConversationStep":
        return await detectConversationStep(args.conversationStep);
      default:
        return null;
    }
  } catch (error) {
    console.error("Error running function:", error);
    return null;
  }
}