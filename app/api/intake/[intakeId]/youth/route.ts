import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import {
  OpenAIStream,
  StreamingTextResponse,
  StreamData,
} from "ai";
import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';
import { PromptTemplate } from '@langchain/core/prompts'
import { 
  extractUserInfoFunc,
  extractAiSuggestionFunc,
  detectChatStateFunc,
  extractUserChatSummaryFunc, 
  runFunction } from "./functions";
import { 
  getThread,
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  savePersonaToDatabase,
  saveUserExpectationToDatabase,
  saveChatSummaryToDatabase,
  saveIsVisitedToDatabase
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['hnd1']

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface Message {
  role: string;
  content: string;
}

const USER_EXPECTATION_TEMPLATE = `
Your name is {companionName}
Role: Act as an intake counselor to gather insights into my information. Create a safe space for me to share my experiences.

Goal: Delve into the details of my recent life events (who, when, what, where), aiming to uncover my deeper needs. Engage in this exploration patiently, aiming for a meaningful dialogue.

Communication Style:
Adopt my discourse particles and use them when responding to me. 
Engage with me warmly, empathetically, non-judgmentally, and supportively, striving to build rapport as a human counselor would.

Each response should be concise, no more than 10 words, and posed as simple.
Probe my thoughts and feelings slowly, and gently. Avoid simply repeating what I say. 
Respond in Traditional Chinese.
`

const SUMMARIZE_TEMPLATE: string = 
`Summarize user's major life event topics` +
` with no more than 20 words in zh-tw` +
` DO NOT repeating what I've said.` +
` DO NOT include irrelevant words and redundant topics.

  Life event history:
  {chatSummaryHistory}
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words in zh-tw, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`

const GREETING_TEMPLATE = `
Firstly, ask what I need for help today.
Respond in zh-TW.
`;

const TEMPLATE = `
{companionInstructions}
Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.

**Collected info:**
-Name: {userName}
-Age: {userAge}
-Gender: {userGender}
-Current Role: {userCurrentRole}
`

const TEMPLATE_Phase2 = `
**Provide better responses considering the following information about me:**
I'm a young adult seeking for the counselors’ help, and you’re playing the intake counselor role to understand my situation and needs. 
Once you understand my needs, you will recommend me a suitable AI. 
Please be mindful of my situation, understanding my challenges, and providing the emotional or academic support when I may need.

My Persona:
-Name: {userName}
-Age: {userAge}
-Gender: {userGender}
-Traits: {userCurrentRole}
-Status: {userStatus}

My recent chat summary:
{chatSummaryHistory}

**This is how I would like you to response:**
You name is {companionName}, Act as an intake counselor, understand my thoughts, feelings and needs. DO not provide any therapy info. 
Goal: Understand my core issues without providing therapy or solutions.
Role: Act as an intake counselor to gather insights into my information. Create a safe space for me to share my experiences.

Inquiry process:
1.Delve into the details of my recent life events (who, when, what, where), aiming to uncover my deeper needs. Engage in this exploration patiently, aiming for a meaningful dialogue. 
2.Encourage me to share more and offer comfort if I express negative emotions.
3.Recommend an AI from the six [AI types] available based on my needs.
4.Closing: Express appreciation for my willingness to share and check if need further assistance during this conversation.

AI types: E2 for care and love. E3 for goal setting. E4 for self-reflection. E5 for problem-solving. E7 for developing a positive mindset. Sn if none of the above fits.

Communication Style:
Adopt my discourse particles and use them when responding to me. Engage with me warmly, empathetically, non-judgmentally, and supportively, striving to build rapport as a human counselor would.

Each response should be concise, no more than 10 words, and posed as simple, engaging questions like "Could you share more?". Ask questions to understand my perspective, not need to provide solutions. Probe my thoughts and feelings slowly, and gently. Avoid simply repeating what I say. Respond in Traditional Chinese.
`

const FUNC_TEMPLATE = `Extract the desired information from the following passage.
Only extract the properties mentioned in the {functionName} function.
Passage:`;

let chatModel: string = 'gpt-4.1-mini';

export async function POST(req: Request, props: { params: Promise<{ intakeId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(300, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const { 
    chatRuns, 
    companion, 
    threadId, 
    existingThread, 
    chatSummaryList,
    useReflectionThreshold,
    persona, 
    messages, 
    function_call 
  } = await req.json();
  const user = await currentUser()
  const userId = user?.id
  //const userId = (await currentUser())?.id
  const path = `(root)/(routes)/intake/${params.intakeId}/${threadId}`

  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }
  if (!companion || !persona) {
    return new NextResponse("Companion or Persona not found", { status: 404 });
  }

  let systemPrompt;
  let functions: ChatCompletionCreateParams.Function[];
  let functionCall = "none"
  let refreshMessages
  let chatSummaryHistory: string | string[]
  let keyActions = false
  const personaAge = persona.age
  console.log("isVisited@route/intake: ", persona.isVisited)

  if (chatSummaryList && chatSummaryList.length > 0) {
    chatSummaryHistory = chatSummaryList
  } else {
    chatSummaryHistory = ''
  }

  if (!existingThread && (chatRuns <= 1 || !chatRuns)) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns

    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫助嗎？`

    return new NextResponse(greetingMessage);

  } else if (
    persona.age >=10 &&
    persona.isVisited === true &&
    persona.currentRole !== null &&
    persona.currentRole.length > 0 &&
    persona.gender !== null &&
    persona.gender.length > 0
    ) {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE_Phase2);
    systemPrompt = await systemPromptTemplate.format({
      companionName: companion.name,
      userName: persona?.nickName ?? user.firstName,
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      userStatus: persona?.status ?? "",
      chatSummaryHistory: `${chatSummaryHistory}`,
      //companionSeed: companion.seed, // here we use the seed for the 2nd phase's system prompt
    })
    chatModel = 'gpt-4.1-mini' // 'gpt-4.1', 'gpt-4.1-mini'
    functions = extractAiSuggestionFunc
    functionCall = "auto"
    refreshMessages = 2
  } else {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    systemPrompt = await systemPromptTemplate.format({
      //companionName: companion.name,
      userName: persona?.nickName ?? "",
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      companionInstructions: companion.instructions,
    })
    chatModel = 'gpt-4.1-mini'
    functions = extractUserInfoFunc
    functionCall = "auto"
    refreshMessages = chatRuns
  }


  if (persona.isVisited === true && 
    messages.length >= useReflectionThreshold && 
    chatRuns % (useReflectionThreshold) === 0)
  {
    chatModel = 'gpt-4.1-mini'
    functions = extractUserChatSummaryFunc
    functionCall = "auto"
  }

  /*
  // Get the last 8 messages
  let latestMessages: Message[]

  if (chatRuns <= 1 || !chatRuns) {
    latestMessages = messages.slice(-1);
  } else {
    // Get the last 8 messages
    const maxMessages = 17
    let numberOfMessagesToSlice = chatRuns
    numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages, refreshMessages);
    latestMessages = messages.slice(-numberOfMessagesToSlice);
  }
  */

  const latestMessages = messages.slice(-17)

  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element

  const userPrompt: string | undefined = prompt?.content
  /*const combinedFuncMessages = [
    { role: 'system', content: systemFuncPrompt },
    ...latestMessages,
  ]*/

  console.log("combinedMessages: ", combinedMessages)
  // check if the conversation requires a function call to be made

  const initialResponse = await openai.chat.completions.create({
    model: chatModel,
    messages: combinedMessages,
    stream: true,
    functions,
    function_call: functionCall as any,
  })

  /* FIXME(@ai-sdk-upgrade-v5): The `StreamData` type has been removed. Please manually migrate following https://ai-sdk.dev/docs/migration-guides/migration-guide-5-0#stream-data-removal */
  const data = new StreamData();
  const funcStream = OpenAIStream(initialResponse, {
    onStart: async () => {
      if (userPrompt) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.intakeId, userId, userPrompt, "user", threadId
          );
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.intakeId, 
            content: userPrompt,
            role: "user", 
          });
        }
      }
    },
    onFinal: async (completion: string) => {
      await saveCompletionToDatabase(
        params.intakeId, userId, completion, "assistant", threadId
      );
      data.close()
      
      revalidatePath(path, 'page');
    },
    experimental_onFunctionCall: async (
      { name, arguments: args },
      createFunctionCallMessages,
    ) => {
      const result = await runFunction(name, args, personaAge);
      console.log("result: ", result)
      //const result = await runFunction(name, { ...args, personaAge });
      /*const defaultPersonaData = {
        currentRole: persona?.currentRole,
        name: persona?.newame,
        nickName: persona?.nickName,
        gender: persona?.gender,
        age: persona.age,
        traits: persona?.traits,
        status: persona?.status,
        isVisited: persona?.isVisited
      };*/
      if (user?.id && result !== null) {
        if ('userInfo' in result!) {
          const {
            userName,
            userGender,
            userAge,
            currentRole,
          } = (
            result as { userInfo: {
              userName: string
              userGender: string
              userAge: number
              currentRole: string   
            }[]
          }).userInfo[0];
          // Handle userInfo data here
          await savePersonaToDatabase(
            user!.id,
            currentRole,
            userName,
            userGender,
            userAge,
            true
          );
          
          const personaData = {
            ...persona,
            currentRole,
            nickName: userName,
            gender: userGender,
            age: userAge,
            status: "",
            traits: "",
            isVisited: true,
          };

          data.append(personaData)

          const newMessages = createFunctionCallMessages(result);
          console.log("newMessages: ", newMessages)

          return openai.chat.completions.create({
            messages: [...combinedMessages, ...newMessages],
            model: "gpt-4.1-mini",
            stream: true,
            functions,
            function_call: 'auto',
          });

        } else if ('userExpectation' in result!) {
          const { expectation } = (result as {
            userExpectation: {
              expectation: string
            }[]
          }).userExpectation[0];
          // Handle userExpectation data here
          console.log("userExpectation: ", expectation)

          await saveUserExpectationToDatabase(
            user!.id,
            expectation
          );
          const personaData = {
            ...persona,
            status: expectation,
            traits: "",
          };

          data.append(personaData)

          const systemPromptTemplate = PromptTemplate.fromTemplate(USER_EXPECTATION_TEMPLATE);
          systemPrompt = await systemPromptTemplate.format({
            companionName: companion.name,
          })

          combinedMessages = [
            { role: 'system', content: systemPrompt },
            ...latestMessages,
          ]

          const newMessages = createFunctionCallMessages(result);
          console.log("newMessages: ", newMessages)

          return openai.chat.completions.create({
            messages: [...combinedMessages, ...newMessages],
            model: "gpt-4.1-mini",
            stream: true,
            functions,
            function_call: 'auto',
          });

        } else if ('aiRecommendation' in result!) {
          const { aiName,  companionUrl } = (result as {
            aiRecommendation: {
              aiName: string
              companionUrl: string
            }[]
          }).aiRecommendation[0];
          // Handle aiRecommendation data here
          console.log("aiRecommendation: ", aiName + companionUrl)
          await saveIsVisitedToDatabase(
            user!.id,
            true
          );

          combinedMessages = [
            { role: 'system', content: "Provide recommended AI with markdown link to user" },
            ...latestMessages,
          ]

          const newMessages = createFunctionCallMessages(result);
          console.log("newMessages: ", newMessages)

          return openai.chat.completions.create({
            messages: [...combinedMessages, ...newMessages],
            model: "gpt-4.1-mini",
            stream: true,
            functions,
            function_call: 'auto',
          });

        } else if ('conversationState' in result!) {
          const { conversationStep } = (result as {
            conversationState: {
              conversationStep: string
            }[]
          }).conversationState[0];
          console.log("conversationState: ", conversationStep)
          if (conversationStep === 'FinalStep') {            
            const systemPromptTemplate = PromptTemplate.fromTemplate(SUMMARIZE_END_TEMPLATE);
            systemPrompt = await systemPromptTemplate.format({
            })

            combinedMessages = [
              { role: 'system', content: systemPrompt },
              ...latestMessages,
            ]

            const newMessages = [
              { role: 'user', content: "Summarize Key Actions for me" }
            ]

            keyActions = true

            return openai.chat.completions.create({
              messages: [...combinedMessages, ...newMessages],
              model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
              stream: true,
            });
          } else {
            return openai.chat.completions.create({
              messages: [...combinedMessages],
              model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
              stream: true,
            });
          }
        } else if ('userChatSummary' in result!) {
          const { chatSummary } = (result as {
            userChatSummary: {
              chatSummary: string
            }[]
          }).userChatSummary[0];
          // Handle userChatSummary data here
          console.log("userChatSummary: ", chatSummary)

          await saveChatSummaryToDatabase(
            chatSummary.substring(0, 100),
            chatSummary,
            params.intakeId,
            companion.name,
            user!.id,
            persona?.nickName ?? user.firstName,
            threadId,
          );

          const newMessages = createFunctionCallMessages(result);
          console.log("newMessages: ", newMessages)

          const systemPromptTemplate = PromptTemplate.fromTemplate(SUMMARIZE_TEMPLATE);
          systemPrompt = await systemPromptTemplate.format({
            chatSummaryHistory: `${chatSummaryHistory}`,
          })

          combinedMessages = [
            { role: 'system', content: systemPrompt },
            ...latestMessages,
          ]

          return openai.chat.completions.create({
            messages: [...combinedMessages, ...newMessages],
            model: "gpt-4.1-mini",
            stream: true,
            functions,
            function_call: 'auto',
          });
        }
      }
    },
    experimental_streamData: true,
  });
  return new StreamingTextResponse(funcStream, {}, data);
}