import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import {
  OpenAIStream,
  StreamingTextResponse,
  StreamData,
} from "ai";
import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';
import { PromptTemplate } from '@langchain/core/prompts'
import { 
  extractUserInfoFunc, 
  allFunctions, 
  runFunction } from "./functions";
  import { 
    getThread,
    newThread, 
  } from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  savePersonaToDatabase,
  saveUserExpectationToDatabase,
  saveIsVisitedToDatabase
} from "@/lib/databaseUtils";
import { v4 as uuidv4 } from 'uuid'


export const runtime = "edge";
export const preferredRegion = ['hnd1']

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface Message {
  role: string;
  content: string;
}

const USER_EXPECTATION_TEMPLATE = `
Your name is {companionName}
Learn from my discourse particles and use them when responding to me. Be warm, patient, and empathetic. 

**Goal:** 
Listen and Understand my expectations from today's conversation.

**Step-by-Step to Achieve Goal:**
1.Begin with a warm greeting and listen to me.
2.Listen and understand my expectations.

**Interaction Guidelines:**
Focus on one key message or question in each response. 
Be concise, offering longer guidance only when requested. 
Respond in zh-TW. 
Gauge my emotions. 
Use a casual tone and emojis only when I'm in a good mood and showing humor.
Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.
REJECT me nicely if my question is beyond the above topic.
`

const GREETING_TEMPLATE = `
Firstly, ask what I need for help today.
Respond in zh-TW.
`;

const TEMPLATE = `
Your name is {companionName}
{companionInstructions}

**Collected info:**
Name: {userName}
Age: {userAge}
Gender: {userGender}
Current Role: {userCurrentRole}
`

const TEMPLATE_Phase2 = `
Your name is {companionName}
{companionSeed}
Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.

My Persona:
Name: {userName}
Age: {userAge}
Gender: {userGender}
Traits: {userCurrentRole}
Status: {userStatus}
`

const FUNC_TEMPLATE = `Extract the desired information from the following passage.
Only extract the properties mentioned in the {functionName} function.
Passage:`;

let chatModel: string = 'gpt-4.1-mini';

export async function POST(req: Request, props: { params: Promise<{ intakeId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(300, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const { chatRuns, companion, threadId, existingThread, persona, messages, function_call } = await req.json();
  const user = await currentUser()
  const userId = user?.id
  //const userId = (await currentUser())?.id
  const path = `(root)/(routes)/intake/${params.intakeId}/${threadId}`

  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }
  if (!companion || !persona) {
    return new NextResponse("Companion or Persona not found", { status: 404 });
  }

  let systemPrompt;
  let functions: ChatCompletionCreateParams.Function[]
  let refreshMessages
  const personaAge = persona.age
  console.log("isVisited@route/intake: ", persona.isVisited)


  if (chatRuns <= 1 || !chatRuns) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns

    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫助嗎？`

    const delay = 3000; // milliseconds

    const delayedResponse = new Promise<NextResponse>((resolve) => {
      setTimeout(() => {
        resolve(new NextResponse(greetingMessage));
      }, delay);
    });

    return new NextResponse(greetingMessage);

  } else if (
    persona.age >=10 &&
    persona.isVisited === true &&
    persona.currentRole !== null &&
    persona.currentRole.length > 0 &&
    persona.gender !== null &&
    persona.gender.length > 0
    ) {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE_Phase2);
    systemPrompt = await systemPromptTemplate.format({
      companionName: companion.name,
      userName: persona?.nickName ?? user.firstName,
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      userStatus: persona?.status ?? "",
      companionSeed: companion.seed, // here we use the seed for the 2nd phase's system prompt
    })
    chatModel = 'gpt-4.1-mini'
    functions = allFunctions
    refreshMessages = 2
  } else {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    systemPrompt = await systemPromptTemplate.format({
      companionName: companion.name,
      userName: persona?.nickName ?? "",
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      companionInstructions: companion.instructions,
    })
    chatModel = 'gpt-4.1-mini'
    functions = extractUserInfoFunc
    refreshMessages = chatRuns
  }


  /*const systemFuncPromptTemplate = PromptTemplate.fromTemplate(FUNC_TEMPLATE);
  const systemFuncPrompt = await systemFuncPromptTemplate .format({
    functionName: "'extractUserInfo', 'extractAiName', 'extractUserExpectation'"
  })*/

  // Get the last 8 messages
  let latestMessages: Message[]

  if (chatRuns <= 1 || !chatRuns) {
    latestMessages = messages.slice(-1);
  } else {
    // Get the last 8 messages
    const maxMessages = 17
    let numberOfMessagesToSlice = chatRuns
    numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages, refreshMessages);
    latestMessages = messages.slice(-numberOfMessagesToSlice);
  }

  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element

  const userPrompt: string | undefined = prompt?.content
  /*const combinedFuncMessages = [
    { role: 'system', content: systemFuncPrompt },
    ...latestMessages,
  ]*/

  console.log("combinedMessages: ", combinedMessages)
  // use regular chat model for initial conversation
  if (chatRuns <= 1 || messages.length >= 30) {
    const response = await openai.chat.completions.create({
      model: 'gpt-4.1-mini',
      messages: combinedMessages,
      temperature: 0.7,
      stream: true
    })

    const stream = OpenAIStream(response, {
      onStart: async () => {
        if (userPrompt) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.intakeId, userId, userPrompt, "user", threadId
            );
          } else {
            const name = userPrompt.substring(0, 30);
            await newThread({
              threadId, 
              name, 
              companionId: params.intakeId, 
              content: userPrompt,
              role: "user", 
            });
          }
        }
      },
      onCompletion: async (completion: string) => {
        await saveCompletionToDatabase(
          params.intakeId, userId, completion, "assistant", threadId
        );
      },
    })
    return new StreamingTextResponse(stream);
  } else {
    // check if the conversation requires a function call to be made
    const initialResponse = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: combinedMessages,
      stream: true,
      functions,
      function_call: "auto",
    });

    /* FIXME(@ai-sdk-upgrade-v5): The `StreamData` type has been removed. Please manually migrate following https://ai-sdk.dev/docs/migration-guides/migration-guide-5-0#stream-data-removal */
    const data = new StreamData();
    const funcStream = OpenAIStream(initialResponse, {
      onStart: async () => {
        if (userPrompt) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.intakeId, userId, userPrompt, "user", threadId
            );
          } else {
            const name = userPrompt.substring(0, 30);
            await newThread({
              threadId, 
              name, 
              companionId: params.intakeId, 
              content: userPrompt,
              role: "user", 
            });
          }
        }
      },
      onFinal: async (completion: string) => {
        await saveCompletionToDatabase(
          params.intakeId, userId, completion, "assistant", threadId
        );
        data.close()

        revalidatePath(path, 'page');
      },
      experimental_onFunctionCall: async (
        { name, arguments: args },
        createFunctionCallMessages,
      ) => {
        const result = await runFunction(name, args, personaAge);
        console.log("result: ", result)
        /*const defaultPersonaData = {
          currentRole: persona?.currentRole,
          name: persona?.newame,
          nickName: persona?.nickName,
          gender: persona?.gender,
          age: persona.age,
          traits: persona?.traits,
          status: persona?.status,
          isVisited: persona?.isVisited
        };*/
        if (user?.id && result !== null) {
          if ('userInfo' in result!) {
            const {
              userName,
              userGender,
              userAge,
              currentRole,
            } = (
              result as { userInfo: {
                userName: string
                userGender: string
                userAge: number
                currentRole: string   
              }[]
            }).userInfo[0];
            // Handle userInfo data here
            await savePersonaToDatabase(
              user!.id,
              currentRole,
              userName,
              userGender,
              userAge,
              true
            );
            
            const personaData = {
              ...persona,
              currentRole,
              nickName: userName,
              gender: userGender,
              age: userAge,
              status: "",
              traits: "",
              isVisited: true,
            };

            data.append(personaData)
          } else if ('userExpectation' in result!) {
            const { expectation } = (result as {
              userExpectation: {
                expectation: string
              }[]
            }).userExpectation[0];
            // Handle userExpectation data here
            console.log("userExpectation: ", expectation)

            await saveUserExpectationToDatabase(
              user!.id,
              expectation
            );
            const personaData = {
              ...persona,
              status: expectation,
              traits: "",
            };

            data.append(personaData)

            const systemPromptTemplate = PromptTemplate.fromTemplate(USER_EXPECTATION_TEMPLATE);
            systemPrompt = await systemPromptTemplate.format({
              companionName: companion.name,
            })

            combinedMessages = [
              { role: 'system', content: systemPrompt },
              ...latestMessages,
            ]

          } else if ('aiRecommendation' in result!) {
            const { aiName,  companionUrl } = (result as {
              aiRecommendation: {
                aiName: string
                companionUrl: string
              }[]
            }).aiRecommendation[0];
            // Handle aiRecommendation data here
            console.log("aiRecommendation: ", aiName + companionUrl)
            await saveIsVisitedToDatabase(
              user!.id,
              true
            );
          }
        }

        const newMessages = createFunctionCallMessages(result);
        console.log("newMessages: ", newMessages)

        return openai.chat.completions.create({
          model: chatModel,
          stream: true,
          messages: [...combinedMessages, ...newMessages ],
        });
      },
      experimental_streamData: true,
    });
    return new StreamingTextResponse(funcStream, {}, data);
  }
}