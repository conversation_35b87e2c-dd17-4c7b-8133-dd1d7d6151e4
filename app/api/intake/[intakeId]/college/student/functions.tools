import { Tool } from 'ai';

const WebURL = process.env.PUBLISH_URL

export const extractUserInfoTools: Tool[] = [
  {
    type: 'function',
    function: {
      name: 'extractUserInfo',
      description:
        'Get the user background information from the body of the input text',
      parameters: {
        type: 'object',
        properties: {
          userName: {
            type: 'string',
            description: 'Name of the user.'
          },
          userGender: {
            type: 'string',
            enum: ['Man', 'Woman', 'Boy', 'Girl', 'Neutral'],
            description: 'Gender identity of the user. Such as Man, Woman, Boy, Girl, Neutral'
          },
          userAge: {
            type: 'number',
            description: 'Age of the user.'
          },
          currentRole: {
            type: 'string',
            description: 'The current role of the user.'
          },
        },
        required: ['userName', 'userGender', 'userAge', 'currentRole'],
      },
    }
  },
]

export const allFunctionsTools: Tool[] = [
  /*{
    type: 'function',
    function: {
      name: 'extractUserInfo',
      description:
        'Get the user background information from the body of the input text',
      parameters: {
        type: 'object',
        properties: {
          userName: {
            type: 'string',
            description: 'Name of the user.'
          },
          userGender: {
            type: 'string',
            enum: ['Man', 'Woman', 'Boy', 'Girl', 'Neutral'],
            description: 'Gender identity of the user. Such as Man, Woman, Boy, Girl, Neutral'
          },
          userAge: {
            type: 'number',
            description: 'Age of the user.'
          },
          currentRole: {
            type: 'string',
            description: 'The current role of the user.'
          },
        },
        required: ['userName', 'userGender', 'userAge', 'currentRole'],
      },
    },
  },*/
  {
    type: 'function',
    function: {
      name: "extractUserChatSummary",
      description:
        'Summarize and get the user background information from all our conversations.',
      parameters: {
        type: 'object',
        properties: {
          chatSummary: {
            type: 'string',
            description: 'Summarize and get the user background info from all our conversations.',
          },
        },
        required: ['chatSummary'],
      },
    },
  },
];


async function extractUserInfo(userName: string, userGender: string, userAge: number, currentRole: string) {
  return {
    userInfo: [
      {
        userName,
        userGender,
        userAge,
        currentRole,
      }
    ]
  };
}


async function extractUserChatSummary(chatSummary: string) {
  return {
    userChatSummary: [
      {
        chatSummary,
      }
    ]
  };
}


  


export async function runFunction(name: string, argumentsName: any, personaAge: number) {
  try {
    const args = JSON.parse(argumentsName);
    if (Object.keys(args).length === 0) {
      console.log('Arguments object is empty, skipping runFunction');
      return null;
    }
    
    console.log("argumentsName: ", argumentsName)
    console.log("args: ",args)
    switch (name) {
      case "extractUserInfo":
        return await extractUserInfo(args.userName, args.userGender, args.userAge, args.currentRole);
      case "extractUserChatSummary":
        return await extractUserChatSummary(args.chatSummary);
      default:
        return null;
    }
  } catch (error) {
    console.error("Error running function:", error);
    return null;
  }
}