import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import {
  OpenAIStream,
  StreamingTextResponse,
  Tool,
  ToolCallPayload,
  experimental_StreamData,
} from "ai";
import { PromptTemplate } from '@langchain/core/prompts'
import { 
  extractUserInfoTools, 
  allFunctionsTools, 
  runFunction } from "./functions";
import { 
  getThread,
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  savePersonaToDatabase,
  saveChatSummaryToDatabase,
  saveIsVisitedToDatabase
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['hnd1']


const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface Message {
  role: string;
  content: string;
}

const USER_EXPECTATION_TEMPLATE = `
Your name is {companionName}
Learn from my discourse particles and use them when responding to me. Be warm, patient, and empathetic. 

**Goal:** 
Listen and Understand my expectations from today's conversation.

**Step-by-Step to Achieve Goal:**
1.Begin with a warm greeting and listen to me.
2.Listen and understand my expectations.

**Interaction Guidelines:**
Focus on one key message or question in each response. 
Be concise, offering longer guidance only when requested. 
Respond in Traditional Chinese (zh-TW). 
Gauge my emotions. 
Use a casual tone and emojis only when I'm in a good mood and showing humor.
Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.
REJECT me nicely if my question is beyond the above topic.


`

const GREETING_TEMPLATE = `
Firstly, ask what I need for help today.
Respond in zh-TW.
`;

const TEMPLATE = `
You name is {companionName}
{companionInstructions}
Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.
Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.

**Collected info:**
-Name: {userName}
-Age: {userAge}
-Gender: {userGender}
-Current Role: {userCurrentRole}
`

const TEMPLATE_Phase2 = `
**Provide better responses considering the following information about me:**
I am a college student and seeking a counselor at school.
Please be mindful of my situation, understanding my challenges and needs, 
providing the emotional or academic support when I may need it.
My Persona:
-Name: {userName}
-Age: {userAge}
-Gender: {userGender}
-Traits: {userCurrentRole}
-Status: {userStatus}

My recent chat summary:
{chatSummaryHistory}


**This is how I would like you to response:**
You name is {companionName}, Act as an intake counselor, understand my thoughts, feelings and needs. DO not provide any therapy info. 
Goal: Inquire my core issues
Inquiry process:
1. Begin with explaining the purpose of data collection
2. Explore the details of my presenting issues, including recent life events (who, when, what, where), and try to discover my deeper needs. Explore the topic's details patiently over more than 15 exchanges. Encourage me to share more, and comfort me if I am in negative emotion. 
3. Check if any additional info is required.
4. Summarize the background information I provided and confirm with me the accuracy.
5. Closing: Appreciate my willingness to share and inform me that an appointment will be scheduled.

Communication Style:
Adopt my discourse particles and use them when responding to me. Engage with me warmly, empathetically, non-judgmentally, and supportively, striving to emulate the rapport-building qualities of a human counselor.
Each response should be concise, no more than 15 words, and posed as simple, engaging questions like "Could you share more?". Ask questions to understand my perspective, not need to provide solutions. Probe my thoughts and feelings slowly, and gently. Avoid simply repeating what I say. Respond in Traditional Chinese (zh-TW).
Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.
Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const FUNC_TEMPLATE = `Extract the desired information from the following passage.
Only extract the properties mentioned in the {functionName} function.
Passage:`;

let chatModel: string = 'gpt-4.1-mini';

export async function POST(
  req: Request,
  { params }: { params: { intakeId: string } }
  ) {
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(300, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const { 
    chatRuns, 
    companion, 
    threadId, 
    existingThread, 
    chatSummaryList,
    useReflectionThreshold,
    persona, 
    messages, 
    function_call 
  } = await req.json();
  const user = await currentUser()
  const userId = user?.id
  //const userId = (await currentUser())?.id
  const path = `(root)/(routes)/intake/${params.intakeId}/${threadId}`

  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }
  if (!companion || !persona) {
    return new NextResponse("Companion or Persona not found", { status: 404 });
  }

  let systemPrompt;
  let tools: Tool[];
  let toolChoice = "none"
  let refreshMessages
  let chatSummaryHistory: string | string[]
  const personaAge = persona.age
  console.log("isVisited@route/intake: ", persona.isVisited)

  if (chatSummaryList && chatSummaryList.length > 0) {
    chatSummaryHistory = chatSummaryList
  } else {
    chatSummaryHistory = ''
  }
  
  if (!existingThread && (chatRuns <= 1 || !chatRuns)) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns

    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫助嗎？`

    return new NextResponse(greetingMessage);

  } else if (
    persona.age >=10 &&
    persona.isVisited === true &&
    persona.currentRole !== null &&
    persona.currentRole.length > 0 &&
    persona.gender !== null &&
    persona.gender.length > 0
    ) {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE_Phase2);
    systemPrompt = await systemPromptTemplate.format({
      companionName: companion.name,
      userName: persona?.nickName ?? user.firstName,
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      userStatus: persona?.status ?? "",
      chatSummaryHistory: `${chatSummaryHistory}`,
      //companionSeed: companion.seed, // here we use the seed for the 2nd phase's system prompt
    })
    chatModel = 'gpt-4.1-mini' // 'gpt-4.1'
    tools = allFunctionsTools
    refreshMessages = 2
  } else {
    const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
    systemPrompt = await systemPromptTemplate.format({
      companionName: companion.name,
      userName: persona?.nickName ?? "",
      userAge: persona?.age ?? "",
      userGender: persona?.gender ?? "",
      userCurrentRole: persona?.currentRole ?? "",
      companionInstructions: ""//companion.instructions,
    })
    chatModel = 'gpt-4.1-mini'
    tools = extractUserInfoTools
    toolChoice = "auto"
    refreshMessages = chatRuns
  }

  
  if (persona.isVisited === true && 
    messages.length >= useReflectionThreshold && 
    chatRuns % (useReflectionThreshold) === 0)
  {
    toolChoice = "auto"
  }

  /*
  // Get the last 8 messages
  let latestMessages: Message[]

  if (chatRuns <= 1 || !chatRuns) {
    latestMessages = messages.slice(-1);
  } else {
    // Get the last 8 messages
    const maxMessages = 17
    let numberOfMessagesToSlice = chatRuns
    numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages, refreshMessages);
    latestMessages = messages.slice(-numberOfMessagesToSlice);
  }
  */

  const latestMessages = messages.slice(-17)
  
  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element

  const userPrompt: string | undefined = prompt?.content
  /*const combinedFuncMessages = [
    { role: 'system', content: systemFuncPrompt },
    ...latestMessages,
  ]*/

  console.log("combinedMessages: ", combinedMessages)
  // check if the conversation requires a function call to be made

  const initialResponse = await openai.chat.completions.create({
    model: chatModel,
    messages: combinedMessages,
    stream: true,
    tools,
    tool_choice: toolChoice as any,
  })

  const data = new experimental_StreamData();
  const funcStream = OpenAIStream(initialResponse, {
    onStart: async () => {
      if (userPrompt) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.intakeId, userId, userPrompt, "user", threadId
          );
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.intakeId, 
            content: userPrompt,
            role: "user", 
          });
        }
      }
    },
    onFinal: async (completion: string) => {
      await saveCompletionToDatabase(
        params.intakeId, userId, completion, "assistant", threadId
      );
      data.close()
      
      revalidatePath(path, 'page');
    },
    experimental_onToolCall: async (
      call: ToolCallPayload,
      appendToolCallMessage,
    ) => {
      let newMessages: any[] = [];
      for (const toolCall of call.tools) {
        console.log("toolCall: ", toolCall)
        const result = await runFunction(toolCall.func.name, toolCall.func.arguments, personaAge );
        //const result = await runFunction(name, { ...args, personaAge });
        console.log("result: ", result)
        /*const defaultPersonaData = {
          currentRole: persona?.currentRole,
          name: persona?.newame,
          nickName: persona?.nickName,
          gender: persona?.gender,
          age: persona.age,
          traits: persona?.traits,
          status: persona?.status,
          isVisited: persona?.isVisited
        };*/
        if (user?.id && result !== null) {
          if ('userInfo' in result!) {
            const {
              userName,
              userGender,
              userAge,
              currentRole,
            } = (
              result as { userInfo: {
                userName: string
                userGender: string
                userAge: number
                currentRole: string   
              }[]
            }).userInfo[0];
            // Handle userInfo data here
            await savePersonaToDatabase(
              user!.id,
              currentRole,
              userName,
              userGender,
              userAge,
              true
            );
            
            const personaData = {
              ...persona,
              currentRole,
              nickName: userName,
              gender: userGender,
              age: userAge,
              status: "",
              traits: "",
              isVisited: true,
            };

            data.append(personaData)

          } else if ('userChatSummary' in result!) {
            const { chatSummary } = (result as {
              userChatSummary: {
                chatSummary: string
              }[]
            }).userChatSummary[0];
            // Handle userChatSummary data here
            console.log("userChatSummary: ", chatSummary)

            await saveChatSummaryToDatabase(
              chatSummary.substring(0, 100),
              chatSummary,
              params.intakeId,
              companion.name,
              user!.id,
              persona?.nickName ?? user.firstName,
              threadId,
            );
          }
        }

        //const newMessages = createFunctionCallMessages(result);
        newMessages = appendToolCallMessage({
          tool_call_id: toolCall.id,
          function_name: toolCall.func.name, //'aiRecommendation',
          tool_call_result: result,
        });
        console.log("newMessages: ", newMessages)
      }

      return openai.chat.completions.create({
        messages: [...combinedMessages, ...newMessages],
        model: chatModel,
        stream: true,
        tools,
        tool_choice: 'auto',
      });
    },
    experimental_streamData: true,
  });
  return new StreamingTextResponse(funcStream, {}, data);
  
}