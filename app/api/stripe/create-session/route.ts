import { NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server'
import { stripe } from "@/lib/stripe";
import { absoluteUrl } from "@/lib/utils"
import { UNIT_AMOUNT } from '@/constants'

const settingsUrl = absoluteUrl("/settings/credits");

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    const user = await currentUser();
    if (!userId || !user) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }
    const { creditsCount } = await req.json();

    if (!creditsCount || typeof creditsCount !== 'number') {
      throw new Error('Invalid credits count');
    }

    const session = await stripe.checkout.sessions.create({
      //success_url: process.env.STRIPE_SUCCESS_TEST_URL, //For Test
      success_url: settingsUrl,
      cancel_url: settingsUrl,
      payment_method_types: ['card'],
      mode: 'payment',
      billing_address_collection: "auto",
      customer_email: user?.emailAddresses[0].emailAddress,
      line_items: [
        {
          price_data: {
            product_data: {
              name: 'ComfyMindsLab Pro',
            },
            currency: 'USD',
            unit_amount: UNIT_AMOUNT * 100,
          },
          quantity: creditsCount,
        },
      ],
      metadata: {
        userId,
        creditsCount,
      },
    })

    return NextResponse.json({ sessionId: session.id });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return new NextResponse('Internal Server Error', {
      status: 500
    });
  }
}