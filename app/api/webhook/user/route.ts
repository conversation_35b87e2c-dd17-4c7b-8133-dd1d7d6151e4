import prismadb from "@/lib/prismadb";
import { IncomingHttpHeaders } from "http";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { Webhook, WebhookRequiredHeaders } from "svix";
import { AIModel } from '@prisma/client'
import { MAX_FREE_COUNTS, API_MULTIPLIER, UNIT_AMOUNT } from "@/constants";

const webhookSecret = process.env.WEBHOOK_SECRET || "";

async function handler(request: Request) {
  const payload = await request.json();
  const headersList = await headers();
  const heads = {
    "svix-id": headersList.get("svix-id"),
    "svix-timestamp": headersList.get("svix-timestamp"),
    "svix-signature": headersList.get("svix-signature"),
  };
  const wh = new Webhook(webhookSecret);
  let evt: Event | null = null;

  try {
    evt = wh.verify(
      JSON.stringify(payload),
      heads as IncomingHttpHeaders & WebhookRequiredHeaders
    ) as Event;
  } catch (err) {
    console.error((err as Error).message);
    return NextResponse.json({}, { status: 400 });
  }

  const eventType: EventType = evt.type;
  if (eventType === "user.created" || eventType === "user.updated") {
    const { id, ...attributes } = evt.data;
    const emailAddresses = attributes.email_addresses;

    let targetEmail = null;
    if (emailAddresses && Array.isArray(emailAddresses) && emailAddresses.length > 0) {
      targetEmail = emailAddresses[0].email_address;
    }

    // Extract firstName and lastName from attributes
    const firstName = attributes.first_name || '';
    const lastName = attributes.last_name || '';

    // Create or update the Profile record
    await prismadb.profile.upsert({
      where: { externalId: id as string },
      create: {
        externalId: id as string,
        attributes: {
          email: targetEmail || '',
          firstName,
          lastName,
        },
      },
      update: {
        attributes: {
          email: targetEmail || '',
          firstName,
          lastName,
        },
      },
    });

    // Create or update the Persona record with the same userId
    await prismadb.persona.upsert({
      where: { userId: id as string },
      create: {
        userId: id as string,
        name: `${attributes.first_name}`,
        email: targetEmail as string,
        image: `${attributes.profile_image_url}`,
        status: '' as string,
      },
      update: {
        name: `${attributes.first_name}`,
        email: targetEmail as unknown as string,
        image: `${attributes.profile_image_url}`,
      },
    });

    // Create free credits for new user
    const freeCreditCost: number = (MAX_FREE_COUNTS / API_MULTIPLIER) * UNIT_AMOUNT
    await prismadb.creditBalance.create({
      data: {
        balance: freeCreditCost,
        apiBalance: MAX_FREE_COUNTS,
        userId: id as string,
        Transactions: {
          create: {
            count: MAX_FREE_COUNTS,
            amount: freeCreditCost,
            userId: id as string,
            model: 'gpt_4o',
            notes: "Free credits",
          },
        },
      },
    });
    return new NextResponse("Handled user event successfully", { status: 200 });
  }
  return new NextResponse("Unhandled event", { status: 200 });
}


type EventType = "user.created" | "user.updated" | "*";

type Event = {
  data: Record<string, string | number>;
  object: "event";
  type: EventType;
};

export const GET = handler;
export const POST = handler;
export const PUT = handler;
