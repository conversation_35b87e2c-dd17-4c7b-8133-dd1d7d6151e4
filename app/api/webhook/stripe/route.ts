/* eslint-disable camelcase */
import <PERSON><PERSON> from "stripe"
import { headers } from "next/headers"
import { NextResponse } from "next/server"
import { revalidatePath } from 'next/cache'

import prismadb from "@/lib/prismadb"
import { stripe } from "@/lib/stripe"
import { 
  schemaPayment, 
  schemaPaymentId 
} from '@/components/credits/forms/schemas'
import { API_MULTIPLIER, UNIT_AMOUNT } from '@/constants'

export async function POST(req: Request) {
  const body = await req.text()
  const signature = (await headers()).get("Stripe-Signature") as string
  //const signature = req.headers.get("stripe-signature") as string

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error: any) {
    return new NextResponse(`Webhook Error: ${error.message}`, { status: 400 })
  }

  const session = event.data.object as Stripe.Checkout.Session

  if (event.type === "checkout.session.completed") {
    if (!session?.metadata?.userId) {
      return new NextResponse("User id is required", { status: 400 });
    }

    if (session?.mode === "subscription") {
      const subscription = await stripe.subscriptions.retrieve(
        session.subscription as string
      )
      await prismadb.userSubscription.create({
        data: {
          userId: session?.metadata?.userId,
          stripeSubscriptionId: subscription.id,
          stripeCustomerId: subscription.customer as string,
          stripePriceId: subscription.items.data[0].price.id,
          stripeCurrentPeriodEnd: new Date(
            subscription.current_period_end * 1000
          ),
        },
      })
    } else if (session?.mode === "payment" && session?.payment_intent) {
      const oneTime = await stripe.checkout.sessions.retrieve(session.id);
      const parsedCreditsInfo = schemaPayment.safeParse(oneTime.metadata);
      const parsedPaymentId = schemaPaymentId.safeParse(oneTime.payment_intent);
      
      if (!parsedCreditsInfo.success || !parsedPaymentId.success) {
        throw new Error('Payload is missing.');
      }
  
      const { creditsCount, userId } = parsedCreditsInfo.data;
      const paymentIntent = parsedPaymentId.data;

      try {
        await prismadb.$transaction(async (tx) => {
           // Create a user payment
           await tx.userPayment.create({
             data: {
               userId,
               stripePaymentIntent: paymentIntent,
               stripeCreditsCount: creditsCount,
             },
           });
       
           // Upsert credit balance and create a transaction
           const creditBalance = await tx.creditBalance.upsert({
             where: { userId },
             create: {
               userId,
               balance: creditsCount * UNIT_AMOUNT,
               apiBalance: creditsCount * API_MULTIPLIER,
               // Assuming Transactions is a relation field, ensure it's correctly defined in your schema
               Transactions: {
                 create: {
                   count: creditsCount * API_MULTIPLIER,
                   amount: creditsCount * UNIT_AMOUNT,
                   userId,
                   notes: 'Credits purchased.',
                 },
               },
             },
             update: {
               balance: { increment: creditsCount * UNIT_AMOUNT },
               apiBalance: { increment: creditsCount * API_MULTIPLIER },
               balEquivExpense: 0, // reseet equiv bal expense of the cash flow to 0 
               apiEquivExpense:0, // reseet equiv api expense of the cash flow to 0 
               // Ensure this operation is necessary and correctly defined in your schema
               Transactions: {
                 create: {
                   notes: 'Credits purchased.',
                   count: creditsCount * API_MULTIPLIER,
                   amount: creditsCount * UNIT_AMOUNT,
                   userId,
                 },
               },
             },
           });
        });
       } catch (error) {
        console.error('Transaction failed:', error);
       }
      revalidatePath("/settings/credits")
    }
  }

  if (event.type === "invoice.payment_succeeded") {
    const subscription = await stripe.subscriptions.retrieve(
      session.subscription as string
    )

    await prismadb.userSubscription.update({
      where: {
        stripeSubscriptionId: subscription.id,
      },
      data: {
        stripePriceId: subscription.items.data[0].price.id,
        stripeCurrentPeriodEnd: new Date(
          subscription.current_period_end * 1000
        ),
      },
    })
  }

  return new NextResponse(null, { status: 200 })
};