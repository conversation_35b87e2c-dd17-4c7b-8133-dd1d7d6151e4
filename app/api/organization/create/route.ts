import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { Admin, Profile, Organization, AdminRole } from '@prisma/client';
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { nanoid } from "@/lib/utils"

import prismadb from "@/lib/prismadb";

export const runtime = "edge";
export const preferredRegion = 'auto'

export async function POST(req: Request) {
  try {
    const { userId } = await auth();  
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { 
      name, 
      description, 
      email, 
      website, 
      address, 
      phoneNumber, 
      status, 
      members,
      path 
    } = await req.json();
    // Find the Profile record for the admin user
    const adminProfile = await prismadb.profile.findUnique({
      where: { externalId: userId },
      include: {
        admin: true
      }
    });

    if (!adminProfile) {
      throw new Error('Admin user profile not found');
    }
    
    // Assuming the user's profile is associated with an admin
    const admin = adminProfile ? (adminProfile as Profile & { admin: Admin }).admin : null;

    if (!admin || admin.role !== AdminRole.ADMIN) {
      throw new Error('Admin user not found');
    }

    // Find or create Profile records for the members
    const memberProfiles = await Promise.all(
      members.map(async (member: {label: string, value: string}) => {
        const existingProfile = await prismadb.profile.findUnique({
          where: { externalId: member.value },
        });
  
        if (existingProfile) {
          return existingProfile;
        } else {
          throw new Error('User profile not found');
        }

      })
    );

  
    const passcode =  nanoid(32)
    // Create a new organization and connect the admin to it
    const organization = await prismadb.organization.create({
      data: {
        name,
        description,
        email,
        website,
        address, 
        phoneNumber,
        status: status.value,
        organizationPasscode: passcode,
        members: {
          create: memberProfiles.map((profile: Profile) => ({
            user: {
              connect: {
                externalId: profile.externalId,
              },
            },
          })),
        },
      },
      include: {
        members: true,
      },
    });
    
    // Create AdminOrganization records to link the admin to the organization
    await prismadb.adminOrganization.create({
      data: {
        adminId: admin.id,
        orgId: organization.id,
      },
    });
    revalidatePath(path);
    return NextResponse.json(organization);
  
  } catch (error) {
    console.error('Failed to create organization', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}