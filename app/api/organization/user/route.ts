import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { Admin, Profile } from '@prisma/client'

import prismadb from "@/lib/prismadb";

export const runtime = "edge";
export const preferredRegion = ['hnd1']

export async function PATCH(req: Request){
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { 
      orgId,
      usersToRemoveIds,
      path
    } = await req.json();
    // Find the Profile record for the admin user
    const adminProfile = await prismadb.profile.findUnique({
      where: { externalId: userId },
      include: {
        admin: true
      }
    });

    if (!adminProfile) {
      throw new Error('Admin user profile not found');
    }

    // Assuming the user's profile is associated with an admin
    const admin: Admin | null = (adminProfile as Profile & { admin: Admin }).admin;
    if (!admin) {
      throw new Error('Admin user profile not found');
    }

    const organization = await prismadb.organization.update({
      where: { id: orgId },
      data: {
        members: {
          deleteMany: { id: { in: usersToRemoveIds } },
        },
      },
      include: {
        members: true,
      },
    });
    
    revalidatePath(path);
    return NextResponse.json(organization);
  } catch (error) {
    console.error('Failed to create organization', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}