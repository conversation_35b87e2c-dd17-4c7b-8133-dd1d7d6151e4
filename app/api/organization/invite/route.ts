import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { Admin, Profile } from '@prisma/client'

import prismadb from "@/lib/prismadb";

export const runtime = "edge";
export const preferredRegion = ['hnd1']

export async function PATCH(req: Request){
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { 
      orgId,
      usersToInvite,
      path
    } = await req.json();
    // Find the Profile record for the admin user
    const adminProfile = await prismadb.profile.findUnique({
      where: { externalId: userId },
      include: {
        admin: true
      }
    });

    if (!adminProfile) {
      throw new Error('Admin user profile not found');
    }

    // Assuming the user's profile is associated with an admin
    const admin: Admin | null = (adminProfile as Profile & { admin: Admin }).admin;
    if (!admin) {
      throw new Error('Admin user profile not found');
    }

    console.log("usersToInvite", usersToInvite)

    // Get the existing members of the organization
    const existingMembers = await prismadb.member.findMany({
      select: {
        id: true,
        user: { select: { externalId: true } },
      },
    });

    // Assuming usersToInvite is an array of user IDs or objects with a 'value' property
    const upsertPromises = usersToInvite.map(async (user: {label: string, value: string}) => {
      // Assuming 'user.value' is the user ID
      const existingMember = existingMembers.find(member => member.user.externalId === user.value);
    
      console.log(existingMembers)
      console.log(existingMember)
      if (existingMember) {
        // If the user is already a member, you might want to update their status or role
        // This is just an example, adjust according to your needs
        return prismadb.member.update({
          where: { id: existingMember.id },
          data: { 
            user: { connect: { externalId: user.value } }, 
            organization: {
              connect: {
                id: orgId
              }
            }
          },
        });
      } else {
        // If the user is not a member, create a new member
        return prismadb.member.create({
          data: {
            user: { connect: { externalId: user.value } },
            organization: {
              connect: {
                id: orgId
              }
            }
          },
        });
      }
    });
    
    // Wait for all upsert operations to complete
    await Promise.all(upsertPromises);
    
    // Then, fetch the updated organization
    const organization = await prismadb.organization.findUnique({
      where: { id: orgId },
      include: {
        members: true,
        admins: true,
      },
    });
    revalidatePath(path);
    return NextResponse.json(organization);
  } catch (error) {
    console.error('Failed to create organization', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}