import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { Admin, Profile } from '@prisma/client'

import prismadb from "@/lib/prismadb";

export const runtime = "edge";
export const preferredRegion = ['hnd1']

export async function PUT(req: Request) {
  try {
    const { userId } = await auth();  
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const data = await req.json();

    const {
      id,
      name,
      description,
      email,
      website,
      address,
      phoneNumber,
      status,
      path
    } = data;

    // Find the Profile record for the admin user
    const adminProfile = await prismadb.profile.findUnique({
      where: { externalId: userId },
      include: {
        admin: true
      }
    });

    if (!adminProfile) {
      throw new Error('Admin user profile not found');
    }

    // Assuming the user's profile is associated with an admin
    const admin: Admin | null = (adminProfile as Profile & { admin: Admin }).admin;
    if (!admin) {
      throw new Error('Admin user profile not found');
    }

    // Update the organization
    const updatedOrganization = await prismadb.organization.update({
      where: { id },
      data: {
        name,
        description,
        email,
        website,
        address,
        phoneNumber,
        status,
      },
      include: {
        members: true,
        admins: true,
      },
    });

    revalidatePath(path);
    return NextResponse.json(updatedOrganization);
  } catch (error) {
    console.error('Failed to create organization', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}