import { auth } from "@clerk/nextjs/server";
import { NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache'
import { Admin, Profile } from '@prisma/client'

import prismadb from "@/lib/prismadb";

export const runtime = "edge";
export const preferredRegion = ['hnd1']

export async function DELETE(req: Request) {
  try {
    const { userId } = await auth();  
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { 
      orgId,
      path
    } = await req.json();

    // Find the Profile record for the admin user
    const adminProfile = await prismadb.profile.findUnique({
      where: { externalId: userId },
      include: {
        admin: true
      }
    });

    if (!adminProfile) {
      throw new Error('Admin user profile not found');
    }

    // Assuming the user's profile is associated with an admin
    const admin: Admin | null = (adminProfile as Profile & { admin: Admin }).admin;
    if (!admin) {
      throw new Error('Admin user profile not found');
    }

    await prismadb.adminOrganization.deleteMany({
      where: {
         orgId: orgId,
      },
     });

    await prismadb.member.deleteMany({
      where: {
         orgId: orgId,
      },
     });

    // Delete the organization
    const deletedOrganization = await prismadb.organization.delete({
      where: { id: orgId as string },
      //include: { members: true, admins: true },
    });

    revalidatePath(path);
    // Return the deleted organization as response
    return NextResponse.json(deletedOrganization);
  } catch (error) {
    console.error('Failed to delete organization:', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
