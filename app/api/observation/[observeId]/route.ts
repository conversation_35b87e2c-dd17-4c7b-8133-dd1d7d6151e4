import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { 
  ObservationAgent 
} from '@/components/observation/ObservationAgent'

export const runtime = "edge";
export const preferredRegion = ['hnd1']


export async function POST(
  req: Request,
  { params }: { params: Promise<{  observeId: string }> }
  ) {

  const {
    prompt,
    companion,
    threadId,
    observationList,
    useReflectionThreshold,
    persona,
    path,
  } = await req.json();

  const user = await currentUser();
  const userId = user?.id!
  const companionId = companion?.id!

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  console.log("observationList", observationList)

  if (observationList.length > 0) {
    const observationSummary = await ObservationAgent({
      userId,
      sessionId: threadId,
      companionId,
      prompt,
      messages: observationList,
      name: persona?.name || user?.firstName!,
      age: persona?.age || 25,
      traits: persona?.traits || "",
      status: persona?.status || "",
      reflectionThreshold: useReflectionThreshold,
      path,
    })

    return new NextResponse(observationSummary);
  }
}