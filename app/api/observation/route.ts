import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import prismadb from "@/lib/prismadb";
import { Observations } from "@prisma/client";


export async function POST(req: Request) {
  try {
    const body = await req.json();
    const user = await currentUser();
    const { companionId, title, message, roomId } = body;

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!title || !message || !roomId || !companionId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };


    const companion = await prismadb.companion.update({
      where: {
        id: companionId,
      },
      data: {
        observations: {
          create: [
            {
              userId: user!.id,
              title: title,
              message: message,
              roomId: roomId,
            },
          ],
        },
      },
    });
  
    if (!companion) {
      throw new Error("Observation not created");
    }

      
    return NextResponse.json(companion);
  } catch (error) {
    console.log("[OBSERVATION_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};