import {encodingForModel} from "js-tiktoken";
import { addTransaction } from '@/app/actions/creditsActions'

export async function POST(req: Request) {
  const {
    messages,
    completion,
    userApiCount,
    modelName,
    userId,
    secret
  } = await req.json();

  if (secret !== process.env.APP_SECRET_KEY || !userId) {
    return new Response("Unauthorized", { status: 401 });
  }

  let totalTokens = 0
  let promptTokens = 0
  let completionTokens = 0
  const enc = encodingForModel(modelName);
  if (messages) {
    promptTokens = messages.reduce(
      (total: number, msg: {
        role: string;
        content: string;
      }) => total + enc.encode(msg.content ?? '').length,
      0,
    );
    console.log("promptTokens", promptTokens)
  } 
  if (completion) {
    completionTokens = enc.encode(completion).length
    totalTokens = promptTokens + completionTokens
    console.log("totalTokens", totalTokens)
  }

  await addTransaction({
    userApiCount,
    inputTokens,
    outputTokens,
    model: "gpt_4o",
    userId,
    notes: `Token count`,
  })

  return new Response(totalTokens.toString());
}