import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { AIbitat } from '@/aibitat'
import { cli, fileHistory } from '@/aibitat/plugins'

interface MessageResponse {
  from?: string;
  content?: string;
}

let onMessageResponse: MessageResponse = {}

export async function POST(req: Request) {  
  try {
    const body = await req.json()
    const { 
      newMessageText,
     } = body
    
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const aibitat = new AIbitat({
        model: 'gpt-4.1-mini',
        interrupt: 'NEVER',
        maxRounds: 2,
    })
      //.use(fileHistory())
      .agent('Client', {
        interrupt: 'NEVER',
        role: 'You are a human assistant. Reply "TERMINATE" when there is a correct answer or there`s no answer to the question.',
      })
      .agent('Mathematician', {
        role: `You are a Mathematician and only solve math problems from @client`,
      })
      .agent('Reviewer', {
        role: `You are a Peer-Reviewer and you do not solve math problems.
        Check the result from @mathematician and then confirm. Just confirm, no talk.`,
      })
      .channel('management', ['Mathematician', 'Reviewer', 'Client'])

      aibitat.onMessage(async ({
        from, content
      }) => {
        onMessageResponse = { from: `${from}`, content: `${content}` }
        console.log(onMessageResponse)
      })
    
    await aibitat.start({
        from: 'client',
        to: 'management',
        content: `${newMessageText}`,
    })

    
    //console.log(aibitat.chats);
    const testData = [
      {
        from: 'client',
        to: 'management',
        content: 'How much is 2 + 2?',
        state: 'success'
      },
      {
        from: 'mathematician',
        to: 'management',
        content: '2 + 2 is equal to 4.',
        state: 'success'
      },
      {
        from: 'reviewer',
        to: 'management',
        content: 'Confirmed.',
        state: 'success'
      },
      {
        from: 'mathematician',
        to: 'management',
        content: 'Is there anything else I can help you with?',
        state: 'success'
      },
      { from: 'client', to: 'management', state: 'interrupt' }
    ]
    const mathematicianMessage = aibitat.chats.find(message => message.from === 'Mathematician');
 
    return NextResponse.json(mathematicianMessage);
  } catch (error) {
    console.log('[AUTOGEN_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};