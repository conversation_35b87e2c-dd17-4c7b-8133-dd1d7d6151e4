import Groq from "groq-sdk";

export const runtime = "edge";

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY
});

export async function POST(req: Request) {
  const formData = await req.formData();
  const file = formData.get("file") as File;
  const token = formData.get("token") as string;
  const lang = formData.get("lang") as string;

  //if ((!token || token === "null") && !process.env.OPENAI_API_KEY) {
    if (!process.env.GROQ_API_KEY) {  
  
    return Response.json({
      error: "No API key provided.",
    });
  }

  /*const getModels = async ()=>{
    return await groq.models.list();
  };
  getModels().then((models)=>{
   console.log(models);
  });*/

  const transcription = await groq.audio.transcriptions.create({
    file,
    model: "whisper-large-v3",
    language: lang || "zh" //undefined,
  });
  console.log(transcription.text);

  return Response.json(transcription);
}
