import { NextResponse } from 'next/server';

export const runtime = 'edge'

export async function POST(req: Request) {
  const { text, voice_id, params } = await req.json();
  const apiKey = process.env.NEETSAI_API_KEY;
  if (!apiKey) {  
  
    return Response.json({
      error: "No API key provided.",
    });
  }
  try {
    const response = await fetch('https://api.neets.ai/v1/tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
      },
      body: JSON.stringify({
        text,
        voice_id,
        params,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch data from Neets API');
    }

    const buffer = await response.arrayBuffer();
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mp3',
      },
    });
  } catch (error) {
    console.error(error);
    return new NextResponse(JSON.stringify({ message: 'An error occurred.' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
