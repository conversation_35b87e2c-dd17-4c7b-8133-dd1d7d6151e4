import { NextRequest, NextResponse } from 'next/server';
import { generateSpeech } from '@/utils/lobe-tts';

export const runtime = 'nodejs'; // Add this if you need Node.js runtime

export async function POST(request: NextRequest) {
  try {
    const { text, locale, voice } = await request.json();
    const speech = await generateSpeech(text, locale, voice);
    const buffer = Buffer.from(await speech.arrayBuffer());
    
    const headers = new Headers();
    headers.set('Content-Type', 'audio/mpeg');
    headers.set('Content-Length', buffer.length.toString());

    return new NextResponse(buffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: 'Error generating speech' },
      { status: 500 }
    );
  }
}