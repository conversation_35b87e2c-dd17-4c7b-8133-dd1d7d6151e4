import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { GoogleGenerativeAI } from '@google/generative-ai';
import { HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { 
  Message, 
  GoogleGenerativeAIStream, 
  StreamingTextResponse 
} from 'ai';
 
import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'

import { nanoid } from '@/lib/utils'
import { Langfuse } from 'langfuse'
import { MemoryManager } from '@/lib/memory'
import { ObservationAgent } from '@/components/observation-agent'
import { saveObservationToDatabase } from '@/lib/databaseUtils'
import { 
    buildGoogleGenAIPrompt,
    buildGoogleGenAIHistory,
} from '@/lib/buildGoogleGenAIPrompt'

export const runtime = 'edge'
export const preferredRegion = ['hnd1']

const TEMPLATE = `

My name is {userName}
{companionInstructions}

Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}
`

const genAI = new GoogleGenerativeAI(
  process.env.GOOGLE_API_KEY || ''
);

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY ?? '',
  publicKey: process.env.NEXT_PUBLIC_LANGFUSE_PUBLIC_KEY ?? '',
  baseUrl: process.env.NEXT_PUBLIC_LANGFUSE_BASE_URL
})


const generationConfig = {
  stopSequences: ["red"],
  temperature: 0.7,
  topP: 0.1,
  topK: 16,
};

const safetySettings = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
];

type OpenAiMessage = Omit<Message, 'id'>

export async function POST(req: Request) {
  const json = await req.json()
  const {
    messages,
    chatRuns,
    previewToken,
    useReflectionThreshold,
    Persona,
    companionInstruct,
   } = json
  const user = await currentUser()
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress
  const companionId = companionInstruct?.id!
  const chatId = json.id ?? nanoid()
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new Response('Unauthorized', {
        status: 401
      })
  }

  // Exclude additional fields from being sent to OpenAI
  const openAiMessages: OpenAiMessage[] = (messages as OpenAiMessage[]).map(({ content, role }) => ({
    content,
    role: role
  }))

  console.log("openAiMessages@chat: ", openAiMessages)

  const userMessage = openAiMessages.slice(-1)
    .filter((message: { role: string }) => message.role === 'user')
  console.log("userMessage@chat: ", userMessage)

  const promptMessage = userMessage[0]?.content
  console.log("promptMessage@chat: ", promptMessage)

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: companionInstruct.name,
    userName: Persona?.name ?? user.firstName,
    companionInstructions: companionInstruct.instructions,
    companionSeed: "", //companionInstruct.seed,
  })

  
  // Get the last 8 messages without the latest one
  const latestMessages = messages.slice(-19, -1);

  const historyMessages = [
    { role: 'user', parts: [{ text: systemPrompt }] },
    { role: 'model', parts: [{ text: 'okay' }] }
  ]

  console.log("historyMessages@chat/route: ", historyMessages)
  //console.log("buildGoogleGenAIPrompt(latestMessages)", buildGoogleGenAIPrompt(openAiMessages.slice(-18)))

  const trace = langfuse.trace({
    name: 'chat',
    id: `lf-ai-chat:${chatId}`,
    metadata: {
      userEmail
    },
    userId: `user:${userId}`
  })

  const lfGeneration = trace.generation({
    name: 'chat',
    model: 'gemini-pro',
    modelParameters: {
      temperature: 0.7
    },
    input: openAiMessages as any,
  })

  // Use the langfuse generation ID as the message ID
  // Alternatively, trace.generation also accepts id as an argument if you want to use your own message id
  const messageId = lfGeneration.id

  const model = genAI.getGenerativeModel({ 
    model: "gemini-pro",
    generationConfig,
    safetySettings,
  });

  const chat = model.startChat({
    history: historyMessages,
    generationConfig: {
      maxOutputTokens: 500,
    },
  });

  const res = await chat.sendMessageStream(promptMessage)

  console.log("res@chat/gemini: ", res)

  const stream = GoogleGenerativeAIStream(res, {
    async onStart() {
      lfGeneration.update({
        completionStartTime: new Date()
      })
    },
    async onCompletion(completion) {
      lfGeneration.end({
        output: completion
      })

      const title = json.messages[0].content.substring(0, 100)
      const createdAt = Date.now()
      const path = `/chat/${companionInstruct.id}/${chatId}`
      const payload = {
        id: chatId,
        title,
        companionId,
        userId,
        createdAt,
        path,
        messages: [
          ...messages,
          {
            content: completion,
            role: 'assistant',
            id: messageId
          }
        ]
      }
      await kv.hmset(`chat:${chatId}`, payload)
      lfGeneration.event({
        //startTime: new Date(),
        name: 'kv-hmset',
        level: 'DEBUG',
        input: {
          key: `chat:${chatId}`,
          ...payload
        }
      })
      await kv.zadd(`user:chat:${userId}${companionId}`, {
        score: createdAt,
        member: `chat:${chatId}`
      })

      lfGeneration.event({
        startTime: new Date(),
        name: 'kv-zadd',
        level: 'DEBUG',
        input: {
          key: `user:chat:${userId}${companionId}`,
          score: createdAt,
          member: `chat:${chatId}`
        }
      })

      const latestUserMessages = openAiMessages
        .filter((message: { role: string }) => message.role === 'user')
      console.log("latestUserMessages@cheers: ", latestUserMessages)
  
      if (chatRuns % (useReflectionThreshold ) === 0) {
        const observationSummary = await ObservationAgent({
          userId,
          companionId,
          messages: latestUserMessages,
          name: Persona?.name || user.firstName,
          age: Persona?.age || 25,
          traits: Persona?.traits || "",
          status: Persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
        })

        /*if (observationSummary) {
          console.log("observationSummary@chat: ", observationSummary)
          await saveObservationToDatabase(
            companionId, userId, observationSummary, observationSummary, 'user_status_sum'
          )
        }*/
      }

      //revalidatePath(`/chat/${companionId}`, 'layout');
      revalidatePath("/(chat)/(routes)/chat/[chatId]", 'layout');

      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, completion);
      //await kv.expire(key, 60 * 60);

      try {
        await langfuse.shutdownAsync()
      } catch (e) {
        console.error(JSON.stringify(e))
      }
    }
  })

  return new StreamingTextResponse(stream, {
    headers: {
      'X-Message-Id': messageId
    }
  })
}