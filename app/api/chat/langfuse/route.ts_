import OpenAI from 'openai'
import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions'
import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { Message, OpenAIStream, StreamingTextResponse } from 'ai'
import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'

import { nanoid } from '@/lib/utils'
import { Langfuse } from 'langfuse'
import { MemoryManager } from '@/lib/memory'
import { ObservationAgent } from '@/components/observation-agent'
import { saveObservationToDatabase } from '@/lib/databaseUtils'

export const runtime = 'edge'
export const preferredRegion = ['sfo1']

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}
`

type OpenAiMessage = Omit<Message, 'id'>

const defaultOpenAI  = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});


const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY ?? '',
  publicKey: process.env.NEXT_PUBLIC_LANGFUSE_PUBLIC_KEY ?? '',
  baseUrl: process.env.NEXT_PUBLIC_LANGFUSE_BASE_URL
})

export async function POST(req: Request) {
  const json = await req.json()
  const { 
    messages,
    chatRuns,
    previewToken,
    useReflectionThreshold,
    Persona,
    companionInstruct,
   } = json
  const user = await currentUser()
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress
  const companionId = companionInstruct?.id!
  //const userId = (await currentUser())?.id
  const chatId = json.id ?? nanoid()

  if (!userId) {
    return new Response('Unauthorized', {
        status: 401
      })
  }

  const openai = previewToken
  ? new OpenAI({
    apiKey: previewToken,
  })
  : defaultOpenAI;

  //const key = JSON.stringify(messages); // come up with a key based on the request
  // Check if we have a cached response
  //const cached = await kv.get(key);
  //if (cached) {
  //  return new Response(cached);
  //}

  // Exclude additional fields from being sent to OpenAI
  const openAiMessages: OpenAiMessage[] = (messages as OpenAiMessage[]).map(({ content, role }) => ({
    content,
    role: role
  }))

  console.log("openAiMessages@api/langfuse: ", openAiMessages)

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: "", //companionInstruct.name,
    userName: Persona?.name ?? user.firstName,
    companionInstructions: "", //companionInstruct.instructions,
    companionSeed: "", //companionInstruct.seed
  })

  // Get the last 8 messages
  const latestMessages = openAiMessages.slice(-2);

  const combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]
  console.log("combinedMessages@api/langfuse: ", combinedMessages)

    const trace = langfuse.trace({
    name: 'chat',
    id: `lf-ai-chat:${chatId}`,
    metadata: {
      userEmail
    },
    userId: `user:${userId}`
  })

  const lfGeneration = trace.generation({
    name: 'chat',
    model: 'gpt-4.1-mini',
    modelParameters: {
      temperature: 0.7
    },
    input: openAiMessages as any,
  })

  // Use the langfuse generation ID as the message ID
  // Alternatively, trace.generation also accepts id as an argument if you want to use your own message id
  const messageId = lfGeneration.id

  const res = await openai.chat.completions.create({
    model: "gpt-4.1-mini", //"gpt-4.1",
    messages: combinedMessages as any,
    temperature: 0.7,
    stream: true
  })
  
 
  const stream = OpenAIStream(res, {
    async onStart() {
      lfGeneration.update({
        completionStartTime: new Date()
      })
    },
    async onCompletion(completion) {
      lfGeneration.end({
        output: completion
      })

      const title = json.messages[0].content.substring(0, 100)      
      const createdAt = Date.now()
      const path = `/chat/${companionInstruct.id}/${chatId}`
      const payload = {
        id: chatId,
        title,
        companionId,
        userId,
        createdAt,
        path,
        messages: [
          ...messages,
          {
            content: completion,
            role: 'assistant',
            id: messageId
          }
        ]
      }
      await kv.hmset(`chat:${chatId}`, payload)
      lfGeneration.event({
        startTime: new Date(),
        name: 'kv-hmset',
        level: 'DEBUG',
        input: {
          key: `chat:${chatId}`,
          ...payload
        }
      })
      await kv.zadd(`user:chat:${userId}${companionId}`, {
        score: createdAt,
        member: `chat:${chatId}`
      })

      lfGeneration.event({
        startTime: new Date(),
        name: 'kv-zadd',
        level: 'DEBUG',
        input: {
          key: `user:chat:${userId}${companionId}`,
          score: createdAt,
          member: `chat:${chatId}`
        }
      })

      const latestUserMessages = openAiMessages
        .filter((message: { role: string }) => message.role === 'user')
      console.log("latestUserMessages@chat: ", latestUserMessages)

      if (chatRuns % (useReflectionThreshold * 3 ) === 100) {
        const observationSummary = await ObservationAgent({
          userId,
          companionId,
          messages: latestUserMessages,
          name: Persona?.name || user.firstName,
          age: Persona?.age || 25,
          traits: Persona?.traits || "",
          status: Persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
        })

        /*if (observationSummary) {
          console.log("observationSummary@chat: ", observationSummary)
          await saveObservationToDatabase(
            companionId, userId, observationSummary, observationSummary, 'user_status_sum'
          )
        }*/
      }

      revalidatePath(path);

      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, completion);
      //await kv.expire(key, 60 * 60);
      try {
        await langfuse.shutdownAsync()
      } catch (e) {
        console.error(JSON.stringify(e))
      }
      
    }
  })

  return new StreamingTextResponse(stream, {
    headers: {
      'X-Message-Id': messageId
    }
  })
}