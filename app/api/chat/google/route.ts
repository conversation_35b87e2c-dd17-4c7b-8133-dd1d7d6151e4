import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { 
  streamText,
  convertToModelMessages
} from 'ai';
 
import { PromptTemplate } from '@langchain/core/prompts'
import { currentUser } from '@clerk/nextjs/server'

import { nanoid } from '@/lib/utils'



export const runtime = 'edge'
export const preferredRegion = ['hnd1']

const TEMPLATE = `

My name is {userName}
{companionInstructions}

Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}
`
export async function POST(req: Request) {
  const json = await req.json()
  const {
    messages,
    chatRuns,
    previewToken,
    useReflectionThreshold,
    Persona,
    companionInstruct,
   } = json
  const user = await currentUser()
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress
  const companionId = companionInstruct?.id!
  const chatId = json.id ?? nanoid()
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new Response('Unauthorized', {
        status: 401
      })
  }

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: companionInstruct.name,
    userName: Persona?.name ?? user.firstName,
    companionInstructions: companionInstruct.instructions,
    companionSeed: "", //companionInstruct.seed,
  })

  const google = createGoogleGenerativeAI({
    baseURL: "https://generativelanguage.googleapis.com/v1beta",
    apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
  });

  const model = google('gemini-1.5-pro');

  const res = streamText({
    model,
    temperature: 0.5,
    maxOutputTokens: 2000,
    system: systemPrompt,
    messages: convertToModelMessages(messages.slice(-17)),
    async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
      const title = json.messages[0].content.substring(0, 100)
      const createdAt = Date.now()
      const path = `/chat/${companionInstruct.id}/${chatId}`
      const payload = {
        id: chatId,
        title,
        companionId,
        userId,
        createdAt,
        path,
        messages: [
          ...messages,
          {
            content: text,
            role: 'assistant'
          }
        ]
      }
      await kv.hmset(`chat:${chatId}`, payload)
      await kv.zadd(`user:chat:${userId}${companionId}`, {
        score: createdAt,
        member: `chat:${chatId}`
      })

      revalidatePath("/(chat)/(routes)/chat/[chatId]", 'layout');

      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, text);
      //await kv.expire(key, 60 * 60);

    }
  })

  return res.toUIMessageStreamResponse();
}