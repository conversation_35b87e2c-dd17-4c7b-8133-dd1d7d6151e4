import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { 
  Message,
  LangChainStream,
  OpenAIStream,
  StreamingTextResponse 
} from 'ai';
 
import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'

import { nanoid } from '@/lib/utils'
import { MemoryManager } from '@/lib/memory'


import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { ToolExecutor } from "@langchain/langgraph/prebuilt";
import { ChatOpenAI } from "@langchain/openai";
import { convertToOpenAIFunction } from "@langchain/core/utils/function_calling";
import { BaseMessage } from "@langchain/core/messages";
import { HumanMessage, AIMessage, SystemMessage } from "@langchain/core/messages";
import { FunctionMessage } from "@langchain/core/messages";
import { StateGraph, END } from "@langchain/langgraph";
import { RunnableLambda } from "@langchain/core/runnables";
import { AgentStep, AgentAction, AgentFinish } from "@langchain/core/agents";
import type { RunnableConfig } from "@langchain/core/runnables";

export const runtime = 'edge'
export const preferredRegion = ['hnd1']

const TEMPLATE = `

**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}
`


// Set up the tools //
const tools = [new TavilySearchResults({ maxResults: 1 })];
// @ts-ignore
const toolExecutor = new ToolExecutor({ tools });

// Set up the model //
const model = new ChatOpenAI({
  modelName: "gpt-4.1-mini",
  temperature: 0,
  streaming: true
});

const toolsAsOpenAIFunctions = tools.map((tool) =>
  convertToOpenAIFunction(tool)
);
const newModel = model.bind({
  functions: toolsAsOpenAIFunctions,
});

// Define the agent state //
const agentState = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  }
}

// Define the nodes //

// Define the function that determines whether to continue or not
const shouldContinue = (state: { messages: Array<BaseMessage> }) => {
  const { messages } = state;
  const lastMessage = messages[messages.length - 1];
  // If there is no function call, then we finish
  if (
    !("function_call" in lastMessage.additional_kwargs) ||
    !lastMessage.additional_kwargs.function_call
  ) {
    return "end";
  }
  // Otherwise if there is, we continue
  return "continue";
};

// Define the function to execute tools
const _getAction = (state: { messages: Array<BaseMessage> }): AgentAction => {
  const { messages } = state;
  // Based on the continue condition
  // we know the last message involves a function call
  const lastMessage = messages[messages.length - 1];
  if (!lastMessage) {
    throw new Error("No messages found.");
  }
  if (!lastMessage.additional_kwargs.function_call) {
    throw new Error("No function call found in message.");
  }
  // We construct an AgentAction from the function_call
  return {
    tool: lastMessage.additional_kwargs.function_call.name,
    toolInput: JSON.stringify(
      lastMessage.additional_kwargs.function_call.arguments
    ),
    log: "",
  };
};

// Define the function that calls the model
const callModel = async (state: { messages: Array<BaseMessage> }) => {
  const { messages } = state;
  const response = await newModel.invoke(messages);
  // We return a list, because this will get added to the existing list
  return {
    messages: [response],
  };
};

const callTool = async (state: { messages: Array<BaseMessage> }) => {
  const action = _getAction(state);
  // We call the tool_executor and get back a response
  const response = await toolExecutor.invoke(action);
  // We use the response to create a FunctionMessage
  const functionMessage = new FunctionMessage({
    content: response,
    name: action.tool,
  });
  // We return a list, because this will get added to the existing list
  return { messages: [functionMessage] };
};

// Define the graph //

// Define a new graph
const workflow = new StateGraph({
  channels: agentState,
});

// Define the two nodes we will cycle between
workflow.addNode("agent", new RunnableLambda({ func: callModel }));
workflow.addNode("action", new RunnableLambda({ func: callTool }));

// Set the entrypoint as `agent`
// This means that this node is the first one called
workflow.setEntryPoint("agent");

// We now add a conditional edge
workflow.addConditionalEdges(
  // First, we define the start node. We use `agent`.
  // This means these are the edges taken after the `agent` node is called.
  "agent",
  // Next, we pass in the function that will determine which node is called next.
  shouldContinue,
  // Finally we pass in a mapping.
  // The keys are strings, and the values are other nodes.
  // END is a special node marking that the graph should finish.
  // What will happen is we will call `should_continue`, and then the output of that
  // will be matched against the keys in this mapping.
  // Based on which one it matches, that node will then be called.
  {
    // If `tools`, then we call the tool node.
    continue: "action",
    // Otherwise we finish.
    end: END
  }
);

// We now add a normal edge from `tools` to `agent`.
// This means that after `tools` is called, `agent` node is called next.
workflow.addEdge("action", "agent");

// Finally, we compile it!
// This compiles it into a LangChain Runnable,
// meaning you can use it as you would any other runnable
const app = workflow.compile();


export async function POST(req: Request) {
  const json = await req.json()
  const {
    messages,
    chatRuns,
    previewToken,
    useReflectionThreshold,
    Persona,
    companionInstruct,
   } = json
  const user = await currentUser()
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress
  const companionId = companionInstruct?.id!
  const chatId = json.id ?? nanoid()
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new Response('Unauthorized', {
        status: 401
      })
  }

  const userMessage = messages.slice(-1)
    .filter((message: { role: string }) => message.role === 'user')
  console.log("userMessage@chat: ", userMessage)

  const promptMessage = userMessage[0]?.content
  console.log("promptMessage@chat: ", promptMessage)

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: companionInstruct.name,
    userName: Persona?.name ?? user.firstName,
    companionInstructions: '', //companionInstruct.instructions,
    companionSeed: '', //companionInstruct.seed,
  })

  // Get the last 8 messages without the latest one
  const latestMessages = messages.slice(-19, -1);

  const combinedMessages = {
    messages: [
      new SystemMessage(systemPrompt), 
      ...latestMessages.map((message: { role: string; content: string }) =>
        message.role === 'user'
          ? new HumanMessage(message.content)
          : new AIMessage(message.content)
      ),
      new HumanMessage(promptMessage)
    ]
  };

  console.log("combinedMessages@chat/route: ", combinedMessages)

  let finalGeneration: string | undefined = undefined;
  for await (const output of await app.stream(combinedMessages)) {
    if (!output?.__end__) {
      console.log("output_intermediate", output);
      console.log("-----\n");
    } else {
      console.log("output_end", output);
      console.log("-----\n");

      // Extract the messages array from the end object
      const messages = output.__end__.messages;
      finalGeneration = messages[1].lc_kwargs?.content
    }
  }

  const chunkSize = 2;
  const chunks: string[] = [];

  if (finalGeneration !== undefined) {
    for (let i = 0; i < finalGeneration.length; i += chunkSize) {
      chunks.push(finalGeneration.slice(i, i + chunkSize));
    }

    const title = json.messages[0].content.substring(0, 100)
    const createdAt = Date.now()
    const path = `/chat/${companionInstruct.id}/${chatId}`
    const payload = {
      id: chatId,
      title,
      companionId,
      userId,
      createdAt,
      path,
      messages: [
        ...messages,
        {
          content: finalGeneration,
          role: 'assistant'
        }
      ]
    }
  
    await kv.hmset(`chat:${chatId}`, payload)
    await kv.zadd(`user:chat:${userId}${companionId}`, {
      score: createdAt,
      member: `chat:${chatId}`
    })
  }

  const stream = new ReadableStream({
    async start(controller) {
      if (chunks.length === 0) {
        controller.close();
        return;
      }
  
      for (const chunk of chunks) {
        const bytes = new TextEncoder().encode(chunk);
        controller.enqueue(bytes);
        await new Promise((resolve) =>
          setTimeout(resolve, Math.floor(Math.random() * 40) + 10)
        );
      }

      controller.close();
    },
  });

  return new StreamingTextResponse(stream)
}