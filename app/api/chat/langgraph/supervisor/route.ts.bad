import { kv } from '@vercel/kv'
import { revalidatePath } from 'next/cache'
import { 
  Tool,
  Message,
  LangChainStream,
  OpenAIStream,
  StreamingTextResponse 
} from 'ai';

import { PromptTemplate } from '@langchain/core/prompts'
import { auth, currentUser } from '@clerk/nextjs/server'
import { nanoid } from '@/lib/utils'
import { MemoryManager } from '@/lib/memory'

// Create tools
import { TavilySearchResults } from "@langchain/community/tools/tavily_search"
import { DynamicStructuredTool } from "@langchain/core/tools";
import * as d3 from "d3";
import { z } from "zod";

// Helper Utilites
import { AgentExecutor, createOpenAIToolsAgent } from "langchain/agents";
import { HumanMessage } from "@langchain/core/messages";
import { ChatPromptTemplate, MessagesPlaceholder } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { Runnable, type RunnableConfig } from "@langchain/core/runnables";

// Create Agent Supervisor
import { JsonOutputToolsParser } from "langchain/output_parsers";

// Construct Graph
import { BaseMessage } from "@langchain/core/messages";
import { StateGraph, END } from "@langchain/langgraph";

// Invoke the team

export const runtime = 'edge'
export const preferredRegion = ['hnd1']

const TEMPLATE = `

**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
{companionSeed}
`


// Set up the tools //
const chartTool = new DynamicStructuredTool({
  name: "generate_bar_chart",
  description:
    "Generates a bar chart from an array of data points using D3.js and displays it for the user.",
  schema: z.object({
    data: z
      .object({
        label: z.string(),
        value: z.number(),
      })
      .array(),
  }),
  
  func: async ({ data }: { data: { label: string; value?: number }[] }) => {
    const width = 500;
    const height = 500;
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };

    const { createCanvas } = require('canvas');
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext("2d");

    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.label))
      .range([margin.left, width - margin.right])
      .padding(0.1);

    const y = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d) => (d.value !== undefined ? d.value : 0)) as number])
      .nice()
      .range([height - margin.bottom, margin.top]);

    const colorPalette = [
      "#e6194B",
      "#3cb44b",
      "#ffe119",
      "#4363d8",
      "#f58231",
      "#911eb4",
      "#42d4f4",
      "#f032e6",
      "#bfef45",
      "#fabebe",
    ];

    data.forEach((d, idx) => {
      ctx.fillStyle = colorPalette[idx % colorPalette.length];
      ctx.fillRect(
        x(d.label),
         y(d.value !== undefined ? d.value : 0), // Handle undefined values
        x.bandwidth(),
        height - margin.bottom - y(d.value !== undefined ? d.value : 0), // Handle undefined values
      );
    });

    ctx.beginPath();
    ctx.strokeStyle = "black";
    ctx.moveTo(margin.left, height - margin.bottom);
    ctx.lineTo(width - margin.right, height - margin.bottom);
    ctx.stroke();

    ctx.textAlign = "center";
    ctx.textBaseline = "top";
    x.domain().forEach((d) => {
      const xCoord = x(d);
      if (xCoord !== undefined) { // Add a check for undefined
        ctx.fillText(d, xCoord + x.bandwidth() / 2, height - margin.bottom + 6);
      }
    });

    ctx.beginPath();
    ctx.moveTo(margin.left, height - margin.top);
    ctx.lineTo(margin.left, height - margin.bottom);
    ctx.stroke();

    ctx.textAlign = "right";
    ctx.textBaseline = "middle";
    const ticks = y.ticks();
    ticks.forEach((d) => {
      const yCoord = y(d); // height - margin.bottom - y(d);
      ctx.moveTo(margin.left, yCoord);
      ctx.lineTo(margin.left - 6, yCoord);
      ctx.stroke();
      ctx.fillText(d, margin.left - 8, yCoord);
    });

    //await Deno.jupyter.display(canvas);
    return "Chart has been generated and displayed to the user!";
  },
});

const tavilyTool = new TavilySearchResults();

// Helper Utilities //
async function createAgent(
  llm: ChatOpenAI, 
  tools: any[], 
  systemPrompt: string
): Promise<Runnable<any, any, RunnableConfig>> {
  // Each worker node will be given a name and some tools.
  const prompt = await ChatPromptTemplate.fromMessages([
   ["system", systemPrompt],
    new MessagesPlaceholder("messages"),
    new MessagesPlaceholder("agent_scratchpad"),
  ]);
  const agent = await createOpenAIToolsAgent({ llm, tools, prompt });
  return new AgentExecutor({agent, tools});
}


async function agentNode({ 
  state, agent, name 
}:{ 
  state: { messages: BaseMessage[] }
  agent: any;
  name: string;
  }, 
  config?: RunnableConfig)
{
  const result = await agent.invoke(state, config);
  return {
    messages: [
      new HumanMessage({ content: result.output, name })
    ]
  };
}

// Create Agent Supervisor //
const members = ["Researcher", "ChartGenerator"];

const systemPrompt = (
  "You are a supervisor tasked with managing a conversation between the" +
  " following workers: {members}. Given the following user request," +
  " respond with the worker to act next. Each worker will perform a" +
  " task and respond with their results and status. When finished," +
  " respond with FINISH."
);
const options = ["FINISH", ...members];

// Define the routing function
const functionDef = {
  name: "route",
  description: "Select the next role.",
  parameters: {
    title: "routeSchema",
    type: "object",
    properties: {
      next: {
        title: "Next",
        anyOf: [
          { enum: options },
        ],
      },
    },
    required: ["next"],
  },
};
const toolDef: Tool = {
  type: "function",
  function: functionDef,
}

const prompt = await ChatPromptTemplate.fromMessages([
  ["system", systemPrompt],
  new MessagesPlaceholder("messages"),
  [
    "system",
    "Given the conversation above, who should act next?"
      +" Or should we FINISH? Select one of: {options}",
  ],
]).partial({ options: options.join(", "), members: members.join(", ") });

const supervisorModel = new ChatOpenAI({ modelName: "gpt-4.1-mini", temperature: 0, });

const supervisorChain = prompt
  .pipe(supervisorModel.bind({
    tools: [toolDef], 
    tool_choice: {"type": "function" as any, "function": {"name": "route"}}}
    ))
  .pipe(new JsonOutputToolsParser())
   // select the first one
  .pipe((x ) => (x[0].args));

await supervisorChain.invoke({
  messages: [
    new HumanMessage({
      content:"write a report on birds."
    })
  ]
});


// Construct Graph //
// Define the state the graph will track

interface AgentStateChannels {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => BaseMessage[];
    default: () => BaseMessage[];
  };
  next: string;
}

// This defines the agent state
const agentStateChannels: AgentStateChannels = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  next: 'initialValueForNext', // Replace 'initialValueForNext' with your initial value if needed
};

// Create the agents to add to the graph. //
const llm = new ChatOpenAI({ modelName: "gpt-4.1-mini", temperature: 0, });

const researcherAgent = await createAgent(
  llm, 
  [tavilyTool], 
  "You are a web researcher. You may use the Tavily search engine to search the web for"
    +" important information, so the Chart Generator in your team can make useful plots."
);

const researcherNode = async (state: { messages: BaseMessage[] }, config: RunnableConfig) => await agentNode({
  state, 
  agent: researcherAgent, 
  name: "Researcher",
}, config);

const chartGenAgent = await createAgent(
  llm, 
  [chartTool], 
  "You excel at generating bar charts. Use the researcher's information to generate the charts."
);

const chartGenNode = async (state: { messages: BaseMessage[] }, config: RunnableConfig) => await agentNode({
  state, agent: chartGenAgent, 
  name: "ChartGenerator",
}, config);


// Now we can create the graph itself! Add the nodes, and add edges to define how how work will be performed in the graph. //

// 1. Create the graph
const workflow = new StateGraph({
  channels: agentStateChannels,
});

// 2. Add the nodes; these will do the work
workflow.addNode("Researcher", researcherNode);
workflow.addNode("ChartGenerator", chartGenNode);
workflow.addNode("supervisor", supervisorChain);
// 3. Define the edges. We will define both regular and conditional ones
// After a worker completes, report to supervisor
members.forEach(member => {
  workflow.addEdge(member, "supervisor");
});

// When the supervisor returns, route to the agent identified in the supervisor's output
const conditionalMap: { [key: string]: string } = members.reduce((acc, member) => {
    acc[member] = member;
    return acc;
}, {});
// Or end work if done
conditionalMap["FINISH"] = END;

workflow.addConditionalEdges(
    "supervisor", 
    (x: AgentStateChannels) => x.next,
    conditionalMap,
);

workflow.setEntryPoint("supervisor");

const graph = workflow.compile();



export async function POST(req: Request) {
  const json = await req.json()
  const {
    messages,
    chatRuns,
    previewToken,
    useReflectionThreshold,
    Persona,
    companionInstruct,
   } = json
  const user = await currentUser()
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress
  const companionId = companionInstruct?.id!
  const chatId = json.id ?? nanoid()

  if (!userId) {
    return new Response('Unauthorized', {
        status: 401
      })
  }

  const userMessage = messages.slice(-1)
    .filter((message: { role: string }) => message.role === 'user')
  console.log("userMessage@chat: ", userMessage)

  const promptMessage = userMessage[0]?.content
  console.log("promptMessage@chat: ", promptMessage)
  //"What were the 3 most popular tv shows in 2023?"

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: companionInstruct.name,
    userName: Persona?.name ?? user.firstName,
    companionInstructions: companionInstruct.instructions,
    companionSeed: companionInstruct.seed,
  })

  
  // Get the last 8 messages without the latest one
  const latestMessages = messages.slice(-19, -1);

  // Invoke the team //
  // With the graph created, we can now invoke it and see how it performs! //

  const inputs = {
    messages: [new HumanMessage(promptMessage)]
  };

  const streamResults = graph.stream(
    inputs,
    {recursionLimit: 100},
  );
  
  let finalGeneration: string | undefined = undefined;
  for await (const output of await streamResults) {
    if (!output?.__end__){
      console.log(output);
      console.log('----');
    } if (output?.Researcher) {
      console.log("output_Researcher", output.Researcher);
      console.log("-----\n");

      // Extract the messages array from the end object
      const researcherMessages = output.Researcher.messages;

      // Iterate over the messages array and log the content of each message
      researcherMessages.forEach(message => {
        const content = message.lc_kwargs?.content;
        console.log("Researcher Message Content:", content);
      });
    } if (output?.ChartGenerator) {
        console.log("output_ChartGenerator", output.ChartGenerator);
        console.log("-----\n");

      // Extract the messages array from the end object
      const chartGeneratorMessages = output.ChartGenerator.messages;

      // Iterate over the messages array and log the content of each message
      chartGeneratorMessages.forEach(message => {
        const content = message.lc_kwargs?.content;
        console.log("Researcher Message Content:", content);
      });
      
    } else {
      // Extract the messages array from the supervisor object
      const supervisorMessage = output?.supervisor;
      console.log("Supervisor Message Content:", supervisorMessage);
    }
  }


  /*const chunkSize = 2;
  const chunks: string[] = [];

  if (finalGeneration !== undefined) {
    for (let i = 0; i < finalGeneration.length; i += chunkSize) {
      chunks.push(finalGeneration.slice(i, i + chunkSize));
    }

    const title = json.messages[0].content.substring(0, 100)
    const createdAt = Date.now()
    const path = `/chat/${companionInstruct.id}/${chatId}`
    const payload = {
      id: chatId,
      title,
      companionId,
      userId,
      createdAt,
      path,
      messages: [
        ...messages,
        {
          content: finalGeneration,
          role: 'assistant'
        }
      ]
    }
  
    await kv.hmset(`chat:${chatId}`, payload)
    await kv.zadd(`user:chat:${userId}${companionId}`, {
      score: createdAt,
      member: `chat:${chatId}`
    })
  }

  const stream = new ReadableStream({
    async start(controller) {
      if (chunks.length === 0) {
        controller.close();
        return;
      }
  
      for (const chunk of chunks) {
        const bytes = new TextEncoder().encode(chunk);
        controller.enqueue(bytes);
        await new Promise((resolve) =>
          setTimeout(resolve, Math.floor(Math.random() * 40) + 10)
        );
      }

      controller.close();
    },
  });

  return new StreamingTextResponse(stream)*/
}