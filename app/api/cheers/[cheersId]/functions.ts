

export const detectChatStateTool = [
  {
    type: 'function',
    function: {
      name: 'detectConversationStep',
      description:
        'Given the assistant response message, determine which "conversation progress" we are in',
      inputSchema: {
        type: 'object',
        properties: {
          conversationStep: {
            type: 'string',
            enum: ['IntermediateStep', 'FinalStep'],
            description: 'Given the assistant response message, determine which "conversation progress" we are in.'
          },
        },
        required: ['conversationStep'],
      },
    },
  }
]

export const userChatSummaryTool = [
  {
    type: 'function',
    function: {
      name: "extractUserChatSummary",
      description:
        'Summarize and get the user background info from all conversations.',
      inputSchema: {
        type: 'object',
        properties: {
          chatSummary: {
            type: 'string',
            description: 'Summarize and get the user background info from all conversations.',
          },
        },
        required: ['chatSummary'],
      },
    },
  }
]

export const extractAiObservationTool = [
  {
    type: 'function',
    function: {
      name: "extractAiObservation",
      description:
        'Given all my conversations, observe my capabilities, personalities, values, motivation, and interest.',
      inputSchema: {
        type: 'object',
        properties: {
          observation: {
            type: 'string',
            description: 'Given all my conversations, observe my capabilities, personalities, values, motivation, and interest.',
          },
        },
        required: ['observation'],
      },
    },
  },
];

async function extractAiObservation(observation: string) {
  return {
    aiObservation: [
      {
        observation,
      }
    ]
  };
}

async function detectConversationStep(conversationStep: string) {
  return {
    conversationState: [
      {
        conversationStep,
      }
    ]
  };
}

async function extractUserChatSummary(chatSummary: string) {
  console.log("chatSummary@/api/cheers/fuctions: ", chatSummary)
  return {
    userChatSummary: [
      {
        chatSummary,
      }
    ]
  };
}


export async function runFunction(name: string, argumentsName: any) {
  const args = JSON.parse(argumentsName);
  if (Object.keys(args).length === 0) {
    console.log('Arguments object is empty, skipping runFunction');
    return null;
  }  
  console.log("argumentsName: ", argumentsName)
  console.log("args: ",args)

  switch (name) {
    case "detectConversationStep":
      return await detectConversationStep(args.conversationStep);
    case "extractAiObservation":
      return await extractAiObservation(args.observation);
    case "extractUserChatSummary":
      return await extractUserChatSummary(args.chatSummary);
    default:
      return null;
  }
}