import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { checkCreditBalance } from "@/lib/credit-balance";
import { tokenCounter } from "@/lib/token-counter";
import { createUIMessageStreamResponse, UIMessage } from 'ai';
import { toUIMessageStream } from '@ai-sdk/langchain';
import { StreamMode } from "@/components/langgraph/Settings";
import { ThreadState } from "@/components/langgraph/Schema";
import { createGraph } from "@/ai/ragAgent/ragGraph";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { SYSTEM_PROMPT } from "@/ai/memory/prompts";
import { SYSTEM_PROMPT as TOOL_PROMPT } from "@/ai/memory_service/tools";
import { currentUser } from '@clerk/nextjs/server';
import { NextResponse } from "next/server";
import { MemoryManager } from "@/lib/memory";
import { revalidatePath } from 'next/cache';
import { 
  PromptTemplate 
} from '@langchain/core/prompts'
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['sfo1']
export const maxDuration = 30;

const TEMPLATE = `
{instructions}
{seed}

Previous chat
History: {relevantHistory} \n
Fact: {chat_fact} \n
Summary: {chat_summary}
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`


export async function POST(req: Request, props: { params: Promise<{ cheersId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const {
    companion,
    threadId,
    existingThread,
    chatRuns,
    messages,
    useReflectionThreshold,
    persona,
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = companion?.id!

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  const permitAccess = await checkCreditBalance();
  if (!permitAccess) {
    return new NextResponse("Your credit is empty. Please pruchase more credits.", { status: 403 });
  }


  const latestUserMessage = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element
  const userPrompt: string | undefined = latestUserMessage?.content

  let input: Record<string, any> | null = null;
  if (userPrompt !== null) {
    input = {
      messages: 
        {
          role: "user",
          content: userPrompt,
        },
    };
  }

  const memorableTools = [...userProfileTool, ...threadSummaryTool];
  const memorySchemas = createMemorableEventConfig(
    memorableTools,
    TOOL_PROMPT
  );
  const config = {
    recursionLimit: 8,
    configurable: {
      user_id: userId,
      assistant_id: companionId,
      thread_id: threadId,
      system_prompt: SYSTEM_PROMPT,
      schemas: memorySchemas,
      model: "groq/llama-3.3-70b-versatile",
      delay: 0,
    },
    version: "v2" as "v2",
    encoding: undefined, //"text/event-stream",
  };

  const graph = await createGraph()
  //console.log("graph: ", graph)
  //const state = await graph.getState(config);
  //console.log("state: ", state)
  const stream = await graph.streamEvents(input, config);
  
  /*for await (const { event, tags, data } of stream) {
    console.log({
    event: event,
    data: data,
    })
  }*/
  return createUIMessageStreamResponse({
    stream: toUIMessageStream(stream),
  });

}