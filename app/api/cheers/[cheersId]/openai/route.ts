import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { checkCreditBalance } from "@/lib/credit-balance";
import { tokenCounter } from "@/lib/token-counter";
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { MemoryManager } from "@/lib/memory";
import { revalidatePath } from 'next/cache'
import { 
  PromptTemplate 
} from '@langchain/core/prompts'
import zepClient, { zepMemory, formatChatSummaries, formatChatFacts } from "@/lib/zepClient"
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils";


export const runtime = "edge";
export const preferredRegion = ['sfo1']
export const maxDuration = 30;

const TEMPLATE = `
{instructions}
{seed}

Previous chat
History: {relevantHistory} \n
Fact: {chat_fact} \n
Summary: {chat_summary}
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`


export async function POST(req: Request, props: { params: Promise<{ cheersId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const {
    companion,
    threadId,
    existingThread,
    chatRuns,
    messages,
    useReflectionThreshold,
    persona,
    path
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = companion?.id!

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  const permitAccess = await checkCreditBalance();
  if (!permitAccess) {
    return new NextResponse("Your credit is empty. Please pruchase more credits.", { status: 403 });
  }

  let systemPrompt: string;
  let refreshMessages: number;
  let relevantHistory = "";
  const modelName = 'gpt-4-turbo-preview'

  /*if (chatRuns <= 1 || !chatRuns) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns
    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫忙嗎？`

    return new NextResponse(greetingMessage)
  }*/

  // Get the last 8 messages
  const numOfMessages = 16
  const latestMessages = messages.slice(-numOfMessages);

  //const maxMessages = 17
  //let numberOfMessagesToSlice = chatRuns
  //numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages);
  //const latestMessages = messages.slice(-numberOfMessagesToSlice);

  const prompt = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

  const userPrompt: string | undefined = prompt?.content

  let prevMemoryFacts: any[] = [];
  let prevMemorySummaries: any[] = [];
  if (latestMessages?.length > numOfMessages) {
    const memoryData = (await zepMemory(
      companionId!,
      threadId!,
      userId!,
      userPrompt!,
    )) ?? { prevMemoryFacts: [], prevMemorySummaries: [] };
    prevMemoryFacts = memoryData.prevMemoryFacts;
    prevMemorySummaries = memoryData.prevMemorySummaries;
  }

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  systemPrompt = await systemPromptTemplate.format({
    companionName: companion.name,
    userName: persona?.name ?? user.firstName,
    instructions: companion.instructions,
    seed: companion.seed,
    relevantHistory: relevantHistory,
    chat_fact: formatChatFacts(prevMemoryFacts) ?? '',
    chat_summary: formatChatSummaries(prevMemorySummaries) ?? ''
  })

  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]
  //console.log("combinedMessages: ", combinedMessages)

  const res = streamText({
    model: openai('gpt-4.1'),
    system: systemPrompt,
    messages: latestMessages,
    async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
      if (userPrompt && text) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.cheersId, 
            userId, 
            userPrompt, 
            "user", 
            threadId
          )
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.cheersId, 
            content: userPrompt,
            role: "user"
          })
        }

        const promises: Promise<any>[] = [];
        promises.push(
          saveCompletionToDatabase(
            companionId, 
            userId, 
            text, 
            "assistant", 
            threadId
          )
        );

        promises.push(
          zepClient.memory.add(threadId, {
            messages: [
              { role: "assistant", roleType: "assistant", content: text }
            ]
          })
        )
      
        promises.push(
          tokenCounter(
            combinedMessages, 
            text, 
            1,
            modelName, 
            userId,
            process.env.APP_SECRET_KEY!
          )
        );
      
        await Promise.all(promises);
      }
    
      revalidatePath(path)

    },
  });
  return res.toUIMessageStreamResponse();
}