import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { checkCreditBalance } from "@/lib/credit-balance";
import { tokenCounter } from "@/lib/token-counter";
import { traceable } from "langsmith/traceable";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache';
import {
  OpenAIStream,
  StreamingTextResponse,
  Tool,
  ToolCallPayload,
  StreamData,
} from "ai";
import prismadb from "@/lib/prismadb";
import DOMPurify from 'dompurify'
import { PromptTemplate } from '@langchain/core/prompts'
import { 
  detectChatStateTool, 
  userChatSummaryTool,
  extractAiObservationTool,
  runFunction } from "./functions";
import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { addTodoAction } from "@/app/actions/todoActions"
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";
import { markdownToList } from '@/lib/markdownToList-parser'
//import { ObservationAgent } from '@/components/observation-agent'


export const runtime = "edge";
export const preferredRegion = ['hnd1']
//export const dynamic = 'force-dynamic'; // no caching

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

My recent chat summary:
{chatSummaryHistory}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}

Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const OBSERVATION_TEMPLATE: string = `
You act as a psychologist to summarize your observation of my capabilities, personalities, values, motivation, and interest.

You can adopt my discourse particles and use them when responding to me. 
Do not provide suggestions. In each response, use less than 100 words. Be warm and positive. Not repeat what I've said. 

Respond in Traditional Chinese.
Use emojis only when my mood is positive.
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words in zh-tw, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`

const CHAT_SUMMARIZE_TEMPLATE = `
Summarize user key background info in 50 words from previous conversations. 
Do not repeat what I've said. Be concise, response in zh-TW.
`

export async function POST(req: Request, props: { params: Promise<{ cheersId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const {
    companion,
    threadId,
    existingThread,
    chatSummaryList,
    totalWordCount,
    useReflectionThreshold,
    chatRuns,
    messages,
    persona,
    path
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = companion?.id!
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  const permitAccess = await checkCreditBalance();
  if (!permitAccess) {
    return new NextResponse("Your credit is empty. Please pruchase more credits.", { status: 403 });
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  let systemPrompt: string;
  let refreshMessages: number;
  let tools: Tool[];
  let toolChoice = "none"
  let chatSummaryHistory: string | string[]
  let combinedMessages: any[]
  let keyActions = false
  const modelName = 'gpt-4-turbo-preview'

  if (chatSummaryList && chatSummaryList.length > 0) {
    chatSummaryHistory = chatSummaryList
  } else {
    chatSummaryHistory = ''
  }

  /*if (chatRuns <= 1 || !chatRuns) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns

    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫忙嗎？`
    //async () => {
    //  if (greetingMessage) {
    //    await saveCompletionToDatabase(
    //      params.cheersId, userId, greetingMessage, "assistant"
    //    );
    //  }
    //}

    return new NextResponse(greetingMessage)
  }*/

  // Get the last 8 messages
  //const latestMessages = messages.slice(-17);

  const maxMessages = 17
  let numberOfMessagesToSlice = useReflectionThreshold
  numberOfMessagesToSlice = Math.max(numberOfMessagesToSlice, maxMessages);
  const latestMessages = messages.slice(-numberOfMessagesToSlice);

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  systemPrompt = await systemPromptTemplate.format({
    companionName: companion.name,
    userName: persona?.nickName ?? user.firstName,
    companionInstructions: companion.instructions,
    companionSeed: companion.seed,
    chatSummaryHistory: `${chatSummaryHistory}`,
  })

  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

  console.log("combinedMessages: ", combinedMessages)
  const userPrompt: string | undefined = prompt?.content

  /*if (//messages.length >= maxMessages && 
    chatRuns % (useReflectionThreshold + 1) === 0) {
    tools = userChatSummaryTool
    toolChoice = "auto"

    combinedMessages = [
      { role: 'system', content: CHAT_SUMMARIZE_TEMPLATE },
      ...latestMessages,
    ]
    console.log("userChatSummaryTool")

  } else if (totalWordCount >= 30 &&
    //messages.length >= maxMessages &&     
    chatRuns % (useReflectionThreshold * 1) === 0) {
    tools = extractAiObservationTool
    toolChoice = "auto"

    combinedMessages = [
      { role: 'system', content: OBSERVATION_TEMPLATE },
      ...messages.slice(-maxMessages),
    ]

    console.log("extractAiObservationTool")
  } else*/ if (messages.length >= useReflectionThreshold * 1 &&
      companionId === "ded2ca7f-7f17-4223-92bd-1a35c6c82337") {
      tools = detectChatStateTool
      toolChoice = "auto"
      console.log("detectChatStateTool")
    } else {
      tools = detectChatStateTool
      toolChoice = "none"
    }

  //const openAIChatStream = traceable(openai.chatStream.bind(openai), {
  //  name: "OPENAI Stream",
  //  run_type: "llm",
  //});
  // check if the conversation requires a function call to be made
  const initialResponse = await openai.chat.completions.create({
  //const initialResponse = await openAIChatStream({
    model: "gpt-4.1-mini", //"gpt-4.1-mini" "gpt-4.1"
    messages: combinedMessages,
    stream: true,
    tools,
    tool_choice: toolChoice as any,
  });

  const funcStream = OpenAIStream(initialResponse as any, {
    experimental_onToolCall: async (
      call: ToolCallPayload,
      appendToolCallMessage,
    ) => {
      //let newMessages: any[] = [];
      for (const toolCall of call.tools) {
        console.log("toolCall: ", toolCall)
        const result = await runFunction(toolCall.func.name, toolCall.func.arguments );
        console.log("result: ", result)

        if (user?.id && result !== null) {
          if ('conversationState' in result!) {
            const { conversationStep } = (result as {
              conversationState: {
                conversationStep: string
              }[]
            }).conversationState[0];
            console.log("conversationState: ", conversationStep)

            if (conversationStep === 'FinalStep') {            
              const systemPromptTemplate = PromptTemplate.fromTemplate(SUMMARIZE_END_TEMPLATE);
              systemPrompt = await systemPromptTemplate.format({
              })

              combinedMessages = [
                { role: 'system', content: systemPrompt },
                ...latestMessages,
              ]

              const newMessages = [
                { role: 'user', content: "Summarize Key Actions for me" }
              ]

              keyActions = true

              return openai.chat.completions.create({
                messages: [...combinedMessages, ...newMessages],
                model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
                stream: true,
              });
            } else {
              return openai.chat.completions.create({
                messages: [...combinedMessages],
                model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
                stream: true,
              });

            }
          } else if ('aiObservation' in result!) {
            const { observation } = (result as {
              aiObservation: {
                observation: string
              }[]
            }).aiObservation[0];
            // Handle aiObservation data here
            console.log("aiObservation: ", observation)

            await saveObservationToDatabase(
              params.cheersId, 
              userId, 
              observation, 
              observation, 
              'user_status_sum'
            )

            revalidatePath(path)

            const newMessages = appendToolCallMessage({
              tool_call_id: toolCall.id,
              function_name: toolCall.func.name,
              tool_call_result: result,
            });
            console.log("newMessages: ", newMessages)

            return openai.chat.completions.create({
              messages: [...combinedMessages, ...newMessages],
              model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
              stream: true,
              tools,
              tool_choice: 'auto',
            });
          } else if ('userChatSummary' in result!) {
            const { chatSummary } = (result as {
              userChatSummary: {
                chatSummary: string
              }[]
            }).userChatSummary[0];
            // Handle userChatSummary data here
            console.log("userChatSummary: ", chatSummary)

            await saveChatSummaryToDatabase(
              chatSummary.substring(0, 100),
              chatSummary,
              params.cheersId,
              companion.name,
              userId,
              persona?.nickName ?? user.firstName,
              threadId,
            );

            const newMessages = appendToolCallMessage({
              tool_call_id: toolCall.id,
              function_name: toolCall.func.name,
              tool_call_result: result,
            });
            console.log("newMessages: ", newMessages)
            

            return openai.chat.completions.create({
              messages: [...combinedMessages, ...newMessages],
              model: "gpt-4.1-mini", //"gpt-4.1-mini", "gpt-4.1"
              stream: true,
              tools,
              tool_choice: 'auto',
            });
          }
        }
      }
    },
    //onStart: async () => {
    //},
    //onToken: (token: string) => {
    //  completionTokens += token.length;
    //},
    onFinal: async (completion: string) => {      
      if (userPrompt) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.cheersId, userId, userPrompt, "user", threadId
          );
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.cheersId, 
            content: userPrompt,
            role: "user", 
          });
        }
        await saveCompletionToDatabase(
          companionId, userId, completion, "assistant", threadId
        )
      }
      await tokenCounter(
        combinedMessages, 
        completion, 
        1,
        modelName, 
        userId,
        process.env.APP_SECRET_KEY!
      )

      /*if (keyActions) {
        const user_todos = markdownToList(completion);
        console.log("user_todos@cheers: ", user_todos);
      
        if (user_todos && user_todos.length > 0 &&
          companionId === "ded2ca7f-7f17-4223-92bd-1a35c6c82337") {
          try {
            // Loop through the user_todos array
            for (const todo of user_todos) {
              const taskText = todo.action;
      
              // Insert each task into the todos table
              await addTodoAction({
                companionId: companion.id,
                task: taskText,
                path
              });
            }
          } catch (error) {
            console.log("addTodoAction error.");
          }
        }
      }*/

      revalidatePath(path)
      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, completion);
      //await kv.expire(key, 60 * 60);


      /*if (chatRuns % (useReflectionThreshold * 3 ) === 100) {
        const latestUserMessages = latestMessages
          .filter((message: { role: string }) => message.role === 'user')
        console.log("latestUserMessages@cheers: ", latestUserMessages)

        const observationSummary = await ObservationAgent({
          userId,
          companionId,
          messages: latestUserMessages,
          name: persona?.name || user.firstName,
          age: persona?.age || 25,
          traits: persona?.traits || "",
          status: persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
        })

        revalidatePath(path)
      }*/
    },
  });
  return new StreamingTextResponse(funcStream);
}