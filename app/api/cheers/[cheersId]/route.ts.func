import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import {
  OpenAIStream,
  StreamingTextResponse,
} from "ai";
import prismadb from "@/lib/prismadb";
import { PromptTemplate } from '@langchain/core/prompts'
import { 
  DetectChatStateFunction, 
  UserChatSummaryFunction, 
  runFunction } from "./functions";
import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';
import { 
  getThread,
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";
import { ObservationAgent } from '@/components/observation-agent'


export const runtime = "edge";
export const preferredRegion = ['hnd1']
//export const dynamic = 'force-dynamic'; // no caching

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

My recent chat summary:
{chatSummaryHistory}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}

Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`


export async function POST(
  req: Request,
  { params }: { params: { cheersId: string } }
  ) {
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const {
    companion,
    threadId,
    existingThread,
    chatSummaryList,
    useReflectionThreshold,
    chatRuns,
    messages,
    persona
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = companion?.id!
  const path = `/cheers/${companionId}/${threadId}`
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  let systemPrompt;
  let refreshMessages
  let functions: ChatCompletionCreateParams.Function[] = DetectChatStateFunction;
  let functionChoice = "none"
  let chatSummaryHistory: string | string[]

  if (chatSummaryList && chatSummaryList.length > 0) {
    chatSummaryHistory = chatSummaryList
  } else {
    chatSummaryHistory = ''
  }
  
  /*if (chatRuns <= 1 || !chatRuns) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns

    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫忙嗎？`
    //async () => {
    //  if (greetingMessage) {
    //    await saveCompletionToDatabase(
    //      params.cheersId, userId, greetingMessage, "assistant"
    //    );
    //  }
    //}

    return new NextResponse(greetingMessage)
  }*/

  
  if (messages.length >= useReflectionThreshold && 
    chatRuns % (useReflectionThreshold + 1) === 0)
  {
    functions = UserChatSummaryFunction
    functionChoice = "auto"
  } else if (messages.length >= useReflectionThreshold * 0.5) {
    functions = DetectChatStateFunction
    functionChoice = "auto"
  }

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  systemPrompt = await systemPromptTemplate.format({
    companionName: companion.name,
    userName: persona?.nickName ?? user.firstName,
    companionInstructions: companion.instructions,
    companionSeed: companion.seed,
    chatSummaryHistory: `${chatSummaryHistory}`,
  })

  // Get the last 8 messages
  const latestMessages = messages.slice(-17);

  //const maxMessages = 17
  //let numberOfMessagesToSlice = chatRuns
  //numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages);
  //const latestMessages = messages.slice(-numberOfMessagesToSlice);

  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

  console.log("combinedMessages: ", combinedMessages)
  const userPrompt: string | undefined = prompt?.content

  // check if the conversation requires a function call to be made
  const initialResponse = await openai.chat.completions.create({
    model: "gpt-4.1", //"gpt-4.1-mini"
    messages: combinedMessages,
    stream: true,
    functions,
    function_call: functionChoice as any,
  });

  
  const funcStream = OpenAIStream(initialResponse, {
    onStart: async () => {
      if (userPrompt) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.cheersId, userId, userPrompt, "user", threadId
          );
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.cheersId, 
            content: userPrompt,
            role: "user", 
          });
        }
      }
    },
    onFinal: async (completion: string) => {
      await saveCompletionToDatabase(
        companionId, userId, completion, "assistant", threadId
      )

      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, completion);
      //await kv.expire(key, 60 * 60);


      /*if (chatRuns % (useReflectionThreshold * 3 ) === 100) {
        const latestUserMessages = latestMessages
          .filter((message: { role: string }) => message.role === 'user')
        console.log("latestUserMessages@cheers: ", latestUserMessages)

        const observationSummary = await ObservationAgent({
          userId,
          companionId,
          messages: latestUserMessages,
          name: persona?.name || user.firstName,
          age: persona?.age || 25,
          traits: persona?.traits || "",
          status: persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
        })

        revalidatePath(path)
      }*/
    },
    experimental_onFunctionCall: async (
      { name, arguments: args },
      createFunctionCallMessages,
    ) => {
      const result = await runFunction(name, args);
      console.log("result: ", result)

      if (user?.id && result !== null) {
        if ('conversationState' in result!) {
          const { conversationStep } = (result as {
            conversationState: {
              conversationStep: string
            }[]
          }).conversationState[0];
          console.log("conversationState: ", conversationStep)


          const systemPromptTemplate = PromptTemplate.fromTemplate(SUMMARIZE_END_TEMPLATE);
          systemPrompt = await systemPromptTemplate.format({
          })

          combinedMessages = [
            { role: 'system', content: systemPrompt },
            ...latestMessages,
          ]
        } else if ('userChatSummary' in result!) {
          const { chatSummary } = (result as {
            userChatSummary: {
              chatSummary: string
            }[]
          }).userChatSummary[0];
          // Handle userChatSummary data here
          console.log("userChatSummary: ", chatSummary)

          await saveChatSummaryToDatabase(
            chatSummary.substring(0, 100),
            chatSummary,
            params.cheersId,
            companion.name,
            userId,
            persona?.nickName ?? user.firstName,
            threadId,
          );
        }
      }
      
      const newMessages = createFunctionCallMessages(result);
      console.log("newMessages: ", newMessages)

      return openai.chat.completions.create({
        model: "gpt-4.1-mini",
        stream: true,
        messages: [...combinedMessages, ...newMessages ],
      });
    }
  });
  return new StreamingTextResponse(funcStream);

}