import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { checkCreditBalance } from "@/lib/credit-balance";
import { tokenCounter } from "@/lib/token-counter";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { MemoryManager } from "@/lib/memory";
import { revalidatePath } from 'next/cache'
import { UIMessage, streamText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { 
  PromptTemplate 
} from '@langchain/core/prompts'
import { 
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  //saveObservationToDatabase
} from "@/lib/databaseUtils";
//import { 
//  ObservationAgent 
//} from '@/components/observation-agent'


export const runtime = "edge";
export const preferredRegion = ['hnd1']
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}

Below are relevant details about the conversation we are in.
{relevantHistory}

Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`


export async function POST(req: Request, props: { params: Promise<{ cheersId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  const {
    companion,
    threadId,
    existingThread,
    chatRuns,
    messages,
    useReflectionThreshold,
    persona,
    path
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = companion?.id!
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  const permitAccess = await checkCreditBalance();
  if (!permitAccess) {
    return new NextResponse("Your credit is empty. Please pruchase more credits.", { status: 403 });
  }

  let systemPrompt: string;
  let refreshMessages: number;
  let relevantHistory = "";
  const modelName = 'gpt-4-turbo-preview'

  /*if (chatRuns <= 1 || !chatRuns) {
    //const systemPromptTemplate = PromptTemplate.fromTemplate(GREETING_TEMPLATE);
    //systemPrompt = await systemPromptTemplate.format({})
    refreshMessages = chatRuns
    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫忙嗎？`

    return new NextResponse(greetingMessage)
  }*/

  // Get the last 8 messages
  const numOfMessages = 16
  const latestMessages = messages.slice(-numOfMessages);

  //const maxMessages = 17
  //let numberOfMessagesToSlice = chatRuns
  //numberOfMessagesToSlice = Math.min(numberOfMessagesToSlice, maxMessages);
  //const latestMessages = messages.slice(-numberOfMessagesToSlice);

  const prompt = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

  const userPrompt: string | undefined = prompt?.content

  /*const name = companionId;
  const companion_file_name = name + ".txt";

  const companionKey = {
    companionName: name!,
    userId: userId,
    modelName: threadId,
  };
  const memoryManager = await MemoryManager.getInstance();

  //const records = await memoryManager.readLatestHistory(companionKey);
  //if (records.length === 0) {
  //  await memoryManager.seedChatHistory(companion.seed, "\n\n", companionKey);
  //}
  await memoryManager.writeToHistory(user.firstName + ": " + userPrompt + "\n", companionKey);

  
  if (messages.length >= numOfMessages) {
    // Query Pinecone
    const recentChatHistory = await memoryManager.readLatestHistory(companionKey);
    console.log("RecentChatHistory: ", recentChatHistory)
    // Right now the preamble is included in the similarity search, but that
    // shouldn't be an issue

    const similarDocs = await memoryManager.vectorSearch(
      userPrompt,
      companion_file_name
    );
    
    if (!!similarDocs && similarDocs.length !== 0) {
      relevantHistory = similarDocs.map((doc) => doc.pageContent).join("\n");      
    }
    console.log("Relevant History: ", relevantHistory)
  }*/
  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  systemPrompt = await systemPromptTemplate.format({
    companionName: companion.name,
    userName: persona?.name ?? user.firstName,
    companionInstructions: companion.instructions,
    companionSeed: companion.seed,
    relevantHistory: relevantHistory
  })

  let combinedMessages: any[]
  combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]
  //console.log("combinedMessages: ", combinedMessages)

  const openai = createOpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: process.env.OPENROUTER_API_KEY,
    //defaultHeaders: {
    //  "HTTP-Referer": process.env.NEXT_PUBLIC_APP_URL, // Optional, for including your app on openrouter.ai rankings.
    //  "X-Title": "ComfymindsLab", // Optional. Shows in rankings on openrouter.ai.
    //},
  })

  let completionTokens = 0;
  const res = streamText({
    model: openai('gryphe/mythomist-7b'),
    system: systemPrompt,
    messages: latestMessages,
    temperature: 0.7,
    maxOutputTokens: 800,
    async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
      if (userPrompt) {
        if (existingThread) {
          await saveCompletionToDatabase(
            params.cheersId, userId, userPrompt, "user", threadId
          );
        } else {
          const name = userPrompt.substring(0, 30);
          await newThread({
            threadId, 
            name, 
            companionId: params.cheersId, 
            content: userPrompt,
            role: "user", 
          });
        }
        await saveCompletionToDatabase(
          companionId, userId, text, "assistant", threadId
        )
      }
      await tokenCounter(
        combinedMessages, 
        text, 
        1,
        modelName, 
        userId,
        process.env.APP_SECRET_KEY!
      )

      revalidatePath(path)
      // Cache the response. Note that this will also cache function calls.
      //await kv.set(key, text);
      //await kv.expire(key, 60 * 60);


      //if (chatRuns % (useReflectionThreshold * 3 ) === 100) {
      /*if (chatRuns % (useReflectionThreshold * 3 ) === 100) {
        const latestUserMessages = latestMessages
          .filter((message: { role: string }) => message.role === 'user')
        console.log("latestUserMessages@cheers: ", latestUserMessages)

        const observationSummary = await ObservationAgent({
          userId,
          companionId,
          messages: latestUserMessages,
          name: persona?.name || user.firstName,
          age: persona?.age || 25,
          traits: persona?.traits || "",
          status: persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
        })

        revalidatePath(path)

      }*/
    },
  });
  return res.toUIMessageStreamResponse();
}