import type { ChatCompletionCreateParams } from 'openai/resources/chat/completions';

export const DetectChatStateFunction: ChatCompletionCreateParams.Function[] = [
  {
    name: 'detectConversationStep',
    description:
      'Detect if the conversation goes to the final step from the body of the input text',
    parameters: {
      type: 'object',
      properties: {
        conversationStep: {
          type: 'string',
          enum: ['FinalStep'],
          description: 'If the conversation goes to the final step.'
        },
      },
      required: ['conversationStep'],
    },
  },
]


export const UserChatSummaryFunction: ChatCompletionCreateParams.Function[] = [
  {
    name: "extractUserChatSummary",
    description:
      'Summarize and get the user background information from all our conversations.',
    parameters: {
      type: 'object',
      properties: {
        chatSummary: {
          type: 'string',
          description: 'Summarize and get the user background info from all our conversations.',
        },
      },
      required: ['chatSummary'],
    },
  },
]

async function detectConversationStep(conversationStep: string) {
  return {
    conversationState: [
      {
        conversationStep,
      }
    ]
  };
}

async function extractUserChatSummary(chatSummary: string) {
  return {
    userChatSummary: [
      {
        chatSummary,
      }
    ]
  };
}


export async function runFunction(name: string, args: any) {
  switch (name) {
    case "detectConversationStep":
      return await detectConversationStep(args.conversationStep);
    case "extractUserChatSummary":
      return await extractUserChatSummary(args.chatSummary);
    default:
      return null;
  }
}