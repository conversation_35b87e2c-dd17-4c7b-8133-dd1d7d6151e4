import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server'
import { checkSubscription } from '@/lib/subscription';
//import { getApiLimitCount } from '@/lib/api-limit';
import { getCreditBalance } from '@/lib/credit-balance';

export async function GET(req: NextRequest) {
  try {
    const user = await currentUser()
    const userId = user?.id

    if (!userId) {
      //return NextResponse.redirect('/sign-in');
      return NextResponse.json({ isPro: false, creditBalance: {} }, { status: 200 });
    }
    /*const [isPro, apiLimitCount, creditBalance] = await Promise.all([
      checkSubscription(),
      getApiLimitCount(),
      getCreditBalance(),
    ]);*/

    const [isPro, creditBalance] = await Promise.all([
      checkSubscription(),
      getCreditBalance(),
    ]);

    if (!creditBalance) {
      return NextResponse.json({ isPro, creditBalance: {} }, { status: 200 });
    }

    return NextResponse.json({ isPro, creditBalance }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch subscription data' }, { status: 500 });
  }
}