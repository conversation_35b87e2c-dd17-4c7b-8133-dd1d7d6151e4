
import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'
import prismadb from '@/lib/prismadb';

export async function GET(request: Request) {
  const {userId} = await auth();
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  const { searchParams } = new URL(request.url);
  const uId = searchParams.get("uId");
  let currentUserId: string = ''
  if (uId) {
    currentUserId = uId
  } else {
    currentUserId = userId
  }

  const periods = await getHistoryPeriods(currentUserId);
  return Response.json(periods);
}

export type GetHistoryPeriodsResponseType = Awaited<
  ReturnType<typeof getHistoryPeriods>
>;

async function getHistoryPeriods(currentUserId: string) {
  const transactions = await prismadb.transaction.findMany({
    where: {
      userId: currentUserId,
    },
    select: {
      createdAt: true,
    },
    distinct: ["createdAt"],
    orderBy: [
      {
        createdAt: "asc",
      },
    ],
  });

  // Extract years from createdAt dates and filter out duplicates
  const years = [...new Set(transactions.map(transaction => new Date(transaction.createdAt).getFullYear()))];

  return years;
}