import { NextResponse } from 'next/server';
import prismadb from '@/lib/prismadb';
import { auth } from '@clerk/nextjs/server'
import { OverviewQuerySchema } from "@/components/credits/forms/schemas";

export async function GET(request: Request) {
  try {
    const {userId} = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }
    const { searchParams } = new URL(request.url);
    const uId = searchParams.get("uId");
    const from = searchParams.get("from");
    const to = searchParams.get("to");
  
    const queryParams = OverviewQuerySchema.safeParse({
      from,
      to,
    });
  
    if (!queryParams.success) {
      return Response.json(queryParams.error.message, {
        status: 400,
      });
    };

    let currentUserId: string = ''
    if (uId) {
      currentUserId = uId
    } else {
      currentUserId = userId
    }

    const transactions = await getTransactionsHistory(
      currentUserId,
      queryParams.data.from,
      queryParams.data.to
    );
  
    return NextResponse.json(transactions);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' });
  }
}

export type GetTransactionHistoryResponseType = Awaited<
  ReturnType<typeof getTransactionsHistory>
  >;

async function getTransactionsHistory(currentUserId: string, from: Date, to: Date) {
  //console.log("from: ", from)
  //console.log("to: ", to)

  const transactions = await prismadb.transaction.findMany({
    where: { 
      userId: currentUserId,
      createdAt: {
        gte: from,
        lt: to
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  return transactions;

}
