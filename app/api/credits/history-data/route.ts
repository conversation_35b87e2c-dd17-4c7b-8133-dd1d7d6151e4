import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'
import prismadb from '@/lib/prismadb';
import { z } from 'zod/v3';

const getHistoryDataSchema = z.object({
  timeframe: z.enum(["month", "year"]),
  month: z.coerce.number().min(0).max(11).default(0),
  year: z.coerce.number().min(2000).max(3000),
});

// Defining the type for the accumulated counts
export type Transaction = {
  apiPurchase: number;
  apiExpense: number;
  amountPurchase: number;
  amountExpense: number;
  promptTokens: number;
  completionTokens: number;
}
export type Total = {
  totalApiPurchase: number;
  totalApiExpense: number;
  totalAmountPurchase: number;
  totalAmountExpense: number;
}

export async function GET(request: Request) {
  try {
    const {userId} = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const uId = searchParams.get('uId');
    const timeframe = searchParams.get('timeframe');
    const year = searchParams.get('year');
    const month = searchParams.get('month');

    if (!timeframe ||!year ||!month) {
      return new NextResponse('Missing required parameters', {
        status: 400
      });
    }

    const queryParams = getHistoryDataSchema.safeParse({
      timeframe,
      month,
      year,
    });
  
    if (!queryParams.success) {
      return Response.json(queryParams.error.message, {
        status: 400,
      });
    }

    let currentUserId: string = ''
    if (uId) {
      currentUserId = uId
    } else {
      currentUserId = userId
    }

    if (action === 'transactions') {
      // Construct start date string in ISO 8601 format
      const thisYear = Number(year);
      const thisMonth = Number(month) + 1;
      const startDate = `${thisYear}-${thisMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`;

      //console.log("startDate: ", startDate); // Check the constructed start date

      // Calculate the end date of the month
      const endDate = new Date(thisYear, thisMonth, 0); // Setting day to 0 gives the last day of the previous month
      endDate.setUTCHours(23, 59, 59, 999); // Set time to end of day

      //console.log("endDate: ", endDate); // Check the calculated end date

      // Defining the type for the accumulatedCountsByDate object
      interface AccumulatedCountsByDate {
        [key: string]: Transaction;
      }

      const transactions = await prismadb.transaction.findMany({
        where: { 
          userId: currentUserId,
          createdAt: {
            gte: new Date(startDate),
            lte: endDate,
          },
        },
        orderBy: { createdAt: 'asc' },
      })

      // Initialize an empty object to store accumulated counts by date
      const accumulatedCountsByDate: AccumulatedCountsByDate = {};

      // Initialize variables for accumulated totals
      let totalApiPurchase = 0;
      let totalApiExpense = 0;
      let totalAmountPurchase = 0;
      let totalAmountExpense = 0;
 
      // Iterate through transactions to calculate accumulated counts by date
      transactions.forEach((transaction) => {
        const { count, amount, inputTokens, outputTokens } = transaction;
        const transactionDate = transaction.createdAt.toLocaleDateString(undefined, { month: 'short', day: '2-digit', });

        // Initialize the accumulators for the current date if not already present
        if (!accumulatedCountsByDate[transactionDate]) {
          accumulatedCountsByDate[transactionDate] = {
            apiPurchase: 0,
            apiExpense: 0,
            amountPurchase: 0,
            amountExpense: 0,
            inputTokens: 0,
            outputTokens: 0,
          };
        }

        // Accumulate apiExpense
        if (count > 0) {
          accumulatedCountsByDate[transactionDate].apiPurchase += count;
          totalApiPurchase += count; // Accumulate the total apiPurchase
        } else {
          accumulatedCountsByDate[transactionDate].apiExpense += Math.abs(count);
          totalApiExpense += Math.abs(count); // Accumulate the total apiExpense
        }

        // Accumulate amount
        if (amount > 0) {
          accumulatedCountsByDate[transactionDate].amountPurchase += amount;
          totalAmountPurchase += Math.abs(amount); // Accumulate the total amountPurchase
        } else {
          accumulatedCountsByDate[transactionDate].amountExpense += Math.abs(amount);
          totalAmountExpense += Math.abs(amount); // Accumulate the total amountExpense
        }

        // Accumulate promptTokens and completionTokens
        accumulatedCountsByDate[transactionDate].inputTokens += promptTokens;
        accumulatedCountsByDate[transactionDate].outputTokens += completionTokens;

        // Accumulate promptTokens and completionTokens
        accumulatedCountsByDate[transactionDate].inputTokens += promptTokens;
        accumulatedCountsByDate[transactionDate].outputTokens += completionTokens;
      });

      // Convert the accumulated counts object to an array of objects with date and accumulated counts
      const data = Object.entries(accumulatedCountsByDate).map(([date, counts]) => ({
        date,
        ...counts,
      }));

      // Create an object with the total values
      const totals = {
        totalApiPurchase,
        totalApiExpense,
        totalAmountPurchase,
        totalAmountExpense,
      };

      //console.log("totals: ", totals);
      return NextResponse.json({ transactions: data, totals });
    }

    return NextResponse.json({ error: 'Invalid action' });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' });
  }
}