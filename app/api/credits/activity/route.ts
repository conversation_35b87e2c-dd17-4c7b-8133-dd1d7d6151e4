import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'
import prismadb from '@/lib/prismadb';
import { OverviewQuerySchema } from "@/components/credits/forms/schemas";
import { Transaction, Total } from '@/app/api/credits/history-data/route'

export async function GET(request: Request) {
  const {userId} = await auth();
  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  const { searchParams } = new URL(request.url);
  const uId = searchParams.get("uId");
  const from = searchParams.get("from");
  const to = searchParams.get("to");

  const queryParams = OverviewQuerySchema.safeParse({ from, to });

  if (!queryParams.success) {
    return Response.json(queryParams.error.message, {
      status: 400,
    });
  }

  let currentUserId: string = ''
  if (uId) {
    currentUserId = uId
  } else {
    currentUserId = userId
  }

  const activity = await getActivityStats(
    currentUserId,
    queryParams.data.from,
    queryParams.data.to
  );

  return NextResponse.json(activity);
}

export type GetActivityResponseType = Awaited<
  ReturnType<typeof getActivityStats>
>;

async function getActivityStats(currentUserId: string, from: Date, to: Date) {

  //console.log("from: ", from); // Check the calculated end date

  // Defining the type for the accumulatedCountsByDate object
  interface AccumulatedCountsByDate {
    [key: string]: Transaction;
  }

  const transactions = await prismadb.transaction.findMany({
    where: { 
      userId: currentUserId,
      createdAt: {
        gte: from,
        lte: to,
      },
    },
    orderBy: { createdAt: 'asc' },
  });
 
  // Initialize an empty object to store accumulated counts by date
  const accumulatedCountsByDate: AccumulatedCountsByDate = {};

  // Initialize variables for accumulated totals
  let totalApiPurchase = 0;
  let totalApiExpense = 0;
  let totalAmountPurchase = 0;
  let totalAmountExpense = 0;

  // Iterate through transactions to calculate accumulated counts by date
  transactions.forEach((transaction) => {
    const { count, amount, inputTokens, outputTokens } = transaction;
    const transactionDate = transaction.createdAt.toLocaleDateString(undefined, { month: 'short', day: '2-digit', });

    // Initialize the accumulators for the current date if not already present
    if (!accumulatedCountsByDate[transactionDate]) {
      accumulatedCountsByDate[transactionDate] = {
        apiPurchase: 0,
        apiExpense: 0,
        amountPurchase: 0,
        amountExpense: 0,
        inputTokens: 0,
        outputTokens: 0,
      };
    }

    // Accumulate apiExpense
    if (count > 0) {
      accumulatedCountsByDate[transactionDate].apiPurchase += count;
      totalApiPurchase += count; // Accumulate the total apiPurchase
    } else {
      accumulatedCountsByDate[transactionDate].apiExpense += Math.abs(count);
      totalApiExpense += Math.abs(count); // Accumulate the total apiExpense
    }

    // Accumulate amount
    if (amount > 0) {
      accumulatedCountsByDate[transactionDate].amountPurchase += amount;
      totalAmountPurchase += Math.abs(amount); // Accumulate the total amountPurchase
    } else {
      accumulatedCountsByDate[transactionDate].amountExpense += Math.abs(amount);
      totalAmountExpense += Math.abs(amount); // Accumulate the total amountExpense
    }

    // Accumulate promptTokens and completionTokens
    accumulatedCountsByDate[transactionDate].inputTokens += promptTokens;
    accumulatedCountsByDate[transactionDate].outputTokens += completionTokens;

    // Accumulate promptTokens and completionTokens
    accumulatedCountsByDate[transactionDate].inputTokens += promptTokens;
    accumulatedCountsByDate[transactionDate].outputTokens += completionTokens;
  });

  // Convert the accumulated counts object to an array of objects with date and accumulated counts
  const data = Object.entries(accumulatedCountsByDate).map(([date, counts]) => ({
    date,
    ...counts,
  }));

  // Create an object with the total values
  const totals = {
    totalApiPurchase,
    totalApiExpense,
    totalAmountPurchase,
    totalAmountExpense,
  };

  return { transactions: data, totals };
}