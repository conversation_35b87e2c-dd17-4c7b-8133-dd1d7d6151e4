import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server'
import prismadb from '@/lib/prismadb';

export async function GET(request: Request) {
  try {
    const {userId} = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const uId = searchParams.get('uId');

    let currentUserId: string = ''
    if (uId) {
      currentUserId = uId
    } else {
      currentUserId = userId
    }

    if (action === 'balance') {
      const creditBalance = await prismadb.creditBalance.findUnique({
        where: { userId: currentUserId },
      });

      return NextResponse.json(creditBalance);
    } else if (action === 'transactions') {
      const transactions = await prismadb.transaction.findMany({
        where: { userId: currentUserId },
        orderBy: { createdAt: 'desc' },
      });

      return NextResponse.json(transactions);
    }

    return NextResponse.json({ error: 'Invalid action' });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' });
  }
}