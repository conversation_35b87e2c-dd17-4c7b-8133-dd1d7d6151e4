import { NextRequest } from "next/server";
import OpenAI from "openai";

import {
  VectorStoreDeleted,
} from "openai/resources/beta/vector-stores/vector-stores";

export async function POST(request: NextRequest) {
  const { vectorStoreId } = await request.json();

  if (!vectorStoreId)
    return Response.json({ error: "No vector store id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const deletedVectorStore: VectorStoreDeleted = await openai.beta.vectorStores.del(
      vectorStoreId
    );

    console.log(deletedVectorStore);

    return Response.json({ vectorStore: deletedVectorStore });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
