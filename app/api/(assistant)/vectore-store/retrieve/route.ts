import { NextRequest } from "next/server";
import OpenAI from "openai";

import {
  VectorStore,
} from "openai/resources/beta/vector-stores/vector-stores";

export async function POST(request: NextRequest) {
  const { vectorStoreId } = await request.json();

  if (!vectorStoreId)
    return Response.json({ error: "No id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const vectorStore: VectorStore = await openai.beta.vectorStores.retrieve(
      vectorStoreId
    );

    console.log(vectorStore);

    return Response.json({ vectorStore: vectorStore });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
