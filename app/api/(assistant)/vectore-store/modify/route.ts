import { NextRequest } from "next/server";
import OpenAI from "openai";
import {
  VectorStore,
} from "openai/resources/beta/vector-stores/vector-stores";

export async function POST(request: NextRequest) {
  const { vectorStoreId, name } = await request.json();

  if (!name || vectorStoreId)
    return Response.json({ error: "No vector store id or name provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const vectorStore: VectorStore = await openai.beta.vectorStores.update(
      vectorStoreId,
      { name }
    );

    console.log(vectorStore);

    return Response.json({ vectorStore: vectorStore });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
