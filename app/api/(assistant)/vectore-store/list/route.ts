import { NextRequest } from "next/server";
import OpenAI from "openai";
import {
  VectorStore,
} from "openai/resources/beta/vector-stores/vector-stores";

export async function POST(request: NextRequest) {
  const openai = new OpenAI();

  try {
    const vectorStoresResponse = await openai.beta.vectorStores.list();
    const vectorStores: VectorStore[] = vectorStoresResponse.data;

    console.log(vectorStores);

    return Response.json({ vectorStores: vectorStores });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
