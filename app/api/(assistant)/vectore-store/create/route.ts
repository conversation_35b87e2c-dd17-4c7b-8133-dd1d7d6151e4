import { NextRequest } from "next/server";
import OpenAI from "openai";

import {
  VectorStore,
} from "openai/resources/beta/vector-stores/vector-stores";

export async function POST(request: NextRequest) {
  const { name, fileIds } = await request.json();

  if (!name)
    return Response.json(
      { error: "No vector store name provided" },
      { status: 400 }
    );
  if (!fileIds)
    return Response.json({ error: "No file id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const vectorStore: VectorStore = await openai.beta.vectorStores.create({
      name,
      file_ids: fileIds ?? [],
    });

    console.log(vectorStore);

    return Response.json({ vectorStore: vectorStore });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
