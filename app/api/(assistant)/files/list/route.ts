import { NextRequest } from "next/server";
import OpenAI from "openai";
import { FileObject } from "openai/resources/files";

  
export async function POST(request: NextRequest) {
  const openai = new OpenAI();
  try {
    const filesResponse = await openai.files.list();
    const files: FileObject[] = filesResponse.data;

    console.log(files);

    return Response.json({ files: files });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}