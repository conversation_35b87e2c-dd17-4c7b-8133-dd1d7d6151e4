import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(request: NextRequest) {
  const { fileId } = await request.json();

  if (!fileId)
    return Response.json({ error: "No file id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const retrieveFileContent = await openai.files.retrieveContent(
      fileId
    );

    console.log(retrieveFileContent);

    return Response.json(retrieveFileContent);
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}