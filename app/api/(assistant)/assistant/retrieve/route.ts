import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(request: NextRequest) {
  const { assistantId } = await request.json();

  if (!assistantId)
    return Response.json(
      { error: "No assistant id provided" },
      { status: 400 }
    );

  const openai = new OpenAI();

  try {
    const myAssistant = await openai.beta.assistants.retrieve(assistantId);

    return Response.json({ assistant: myAssistant });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}