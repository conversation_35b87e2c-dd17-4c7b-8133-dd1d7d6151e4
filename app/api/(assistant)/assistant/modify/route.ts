import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(request: NextRequest) {
  const { assistantId, vectorStoreIds, filesMetadata } = await request.json();

  if (!assistantId)
    return Response.json(
      { error: "No assistant id provided" },
      { status: 400 }
    );
  if (!vectorStoreIds)
    return Response.json({ error: "No vectorStoreId provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const updatedAssistant = await openai.beta.assistants.update(
      assistantId, {
      tools: [{ type: "file_search" }],
      //file_ids: fileIds,
      //metadata: filesMetadata ?? {}
      tool_resources: {
        file_search: {
          vector_store_ids: vectorStoreIds
        }
      }
    });

    console.log(updatedAssistant);

    return Response.json({ assistant: updatedAssistant });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}