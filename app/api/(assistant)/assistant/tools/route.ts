import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(request: NextRequest) {
  const { assistantId, tools } = await request.json();

  if (!assistantId)
    return Response.json(
      { error: "No assistant id provided" },
      { status: 400 }
    );

  const openai = new OpenAI();

  try {
    const updatedAssistant = await openai.beta.assistants.update(assistantId, {
      tools: tools ?? [], //: [{ type: "retrieval" }],
    });

    console.log(updatedAssistant);

    return Response.json({ assistant: updatedAssistant });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}