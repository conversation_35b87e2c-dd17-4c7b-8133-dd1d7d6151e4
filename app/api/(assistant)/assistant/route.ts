import prismadb from "@/lib/prismadb";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const assistants = await prismadb.assistant.findMany();

    console.log(assistants);

    if (assistants.length === 0) {
      return NextResponse.json(
        { error: "No assistants found", success: false },
        { status: 404 }  // 404 is more appropriate for not found
      );
    }

    return NextResponse.json(
      { assistant: assistants[0], success: true },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching assistants:", error);
    return NextResponse.json(
      { error: "Internal Server Error", success: false },
      { status: 500 }
    );
  }
}
