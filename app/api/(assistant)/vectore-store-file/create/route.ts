import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(request: NextRequest) {
  const { vectorStoreId, fileId } = await request.json();

  if (!vectorStoreId)
    return Response.json(
      { error: "No vector store id provided" },
      { status: 400 }
    );
  if (!fileId)
    return Response.json({ error: "No file id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const vectorStoreFile = await openai.beta.vectorStores.files.create(
      vectorStoreId,
      {
        file_id: fileId,
      }
    );

    console.log(vectorStoreFile);

    return Response.json({ vectorStoreFile: vectorStoreFile });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
