import { NextRequest } from "next/server";
import OpenAI from "openai";
import {
  VectorStoreFile,
  VectorStoreFilesPage,
} from "openai/resources/beta/vector-stores/files";

export async function POST(request: NextRequest) {
  const { vectorStoreId } = await request.json();

  if (!vectorStoreId)
    return Response.json({ error: "No id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const filesResponse = await openai.beta.vectorStores.files.list(vectorStoreId);
    const vectorStoreFiles: VectorStoreFile[] = filesResponse.data;

    console.log(vectorStoreFiles );

    return Response.json({ vectorStoreFiles: vectorStoreFiles });
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
