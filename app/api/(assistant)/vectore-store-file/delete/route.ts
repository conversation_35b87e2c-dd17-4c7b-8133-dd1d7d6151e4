import { NextRequest } from "next/server";
import OpenAI from "openai";

import {
  VectorStoreFileDeleted,
} from "openai/resources/beta/vector-stores/files";

export async function POST(request: NextRequest) {
  const { vectorStoreId, fileId } = await request.json();

  if (!vectorStoreId)
    return Response.json(
      { error: "No vector store id provided" },
      { status: 400 }
    );
  if (!fileId)
    return Response.json({ error: "No file id provided" }, { status: 400 });

  const openai = new OpenAI();

  try {
    const deletedFile = await openai.beta.vectorStores.files.del(
      vectorStoreId,
      fileId
    );

    console.log(deletedFile);

    return Response.json(deletedFile);
  } catch (e) {
    console.log(e);
    return Response.json({ error: e });
  }
}
