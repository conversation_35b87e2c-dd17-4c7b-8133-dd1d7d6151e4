import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

import { z } from 'zod'; // Changed: Remove /v3
import { zodToJsonSchema } from "zod-to-json-schema";

import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { JsonOutputFunctionsParser } from "langchain/output_parsers";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { checkSubscription } from "@/lib/subscription";

export const runtime = "edge";
export const preferredRegion = ['sfo1']
//PrismaClient is unable to run in Vercel Edge Functions.

const TEMPLATE = `Extract the desired information from the following passage.

Only extract the properties mentioned in the 'information_extraction' function

Passage:
{input}`;

/**
 * This handler initializes and calls an OpenAI Functions powered
 * structured output chain. See the docs for more information:
 *
 * https://js.langchain.com/docs/modules/chains/popular/structured_output
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId } = await auth();
    const userMessages = body.userMessages || [];
    const { useReflectionThreshold } = body 

    if (!userId) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
  
    if (!userMessages) {
      return new NextResponse("userMessages is required", { status: 400 });
    }

    /*const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }*/

    // Get the last 3 messages
    const latestMessages = userMessages.slice(-3);
    
    // Process the latest messages as needed
    const userMessagesContent = latestMessages.map(
        (message: { content: string }) => message.content
    );

    const prompt = PromptTemplate.fromTemplate(TEMPLATE);
    
    /**
     * Function calling is currently only supported with ChatOpenAI models
     */
    const model = new ChatOpenAI({
      temperature: 0,
      modelName: "gpt-4o-mini",
    });

    /**
     * We use Zod (https://zod.dev) to define our schema for convenience,
     * but you can pass JSON Schema directly if desired.
     */
    const schema = z.object({
      emotions: z
        .array(
            z.enum(["Excited", "Delighted", "Happy", "Content", "Relaxed", "Calm", "Tired", "Bored", "Depressed", "Frustrated", "Angry", "Tense"])
        )
        .describe("Extract emotions from the input"),
      stars: z
         .array(z.number().int().min(1).max(10))
        .describe("Describe the number of stars for the degree of each extracted emotion"),
    });

    /**
     * Bind the function and schema to the OpenAI model.
     * Future invocations of the returned model will always use these arguments.
     *
     * Specifying "function_call" ensures that the provided function will always
     * be called by the model.
     */
    const functionCallingModel = model.bind({
      functions: [
        {
          name: "information_extraction",
          description: "Should always be used to properly format output",
          parameters: zodToJsonSchema(schema),
        },
      ],
      function_call: { name: "information_extraction" },
    });

    /**
     * Returns a chain with the function calling model.
     */
    const chain = prompt
      .pipe(functionCallingModel)
      .pipe(new JsonOutputFunctionsParser());

    const result = await chain.invoke({
      input: userMessagesContent,
    });

    return NextResponse.json(result, { status: 200 });
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}