import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { auth, currentUser } from '@clerk/nextjs/server'
import { apolloServerClient } from '@/lib/graphql/apolloClient'
import { gql } from '@apollo/client'

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
}

export async function POST(req: Request) {
  const { query, variables } = await req.json();
  const user = await currentUser();
  const userId = user?.id;

  if (!userId) {
    return new Response('Unauthorized', {
      status: 401
    });
  }

  try {
    let result;
    if (query.trim().startsWith("mutation")) {
      // Handle mutations
      result = await apolloServerClient.mutate({
        mutation: gql`${query}`,
        variables,
      });
    } else {
      // Handle queries
      result = await apolloServerClient.query({
        query: gql`${query}`,
        variables,
      });
    }

    const data = result.data;
    console.log("DATA >>>", data);

    return NextResponse.json({
      data,
    }, {
      headers: corsHeaders
    });
  } catch (error: any) {
    console.error(error);
    return NextResponse.json({
      message: error.message,
      stack: error.stack,
    }, {
      status: 500,
      headers: corsHeaders
    });
  }
}
