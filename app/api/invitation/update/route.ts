import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import prismadb from "@/lib/prismadb";
import { ServiceInvitation } from "@prisma/client";


export async function POST(req: Request) {
  try {
    const body = await req.json();
    const user = await currentUser();
    const { companionId, serviceInvitationId, newStartTime } = body;

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!newStartTime || !serviceInvitationId || !companionId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };

    const updateServiceInvitation = await prismadb.companion.update({
        where: {
            id: companionId, userId: user.id,
          },
          data: {
            serviceInvitations: {
              updateMany: {
                where: {
                  id: serviceInvitationId,
                },
                data: {
                  invitationStartAt: newStartTime, 
                },
              },
            },
          },
        });
    
      if (!updateServiceInvitation) {
        throw new Error("ServiceInvitation not found");
      }

      
    return NextResponse.json(updateServiceInvitation);
  } catch (error) {
    console.log("[MESSAGE_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};