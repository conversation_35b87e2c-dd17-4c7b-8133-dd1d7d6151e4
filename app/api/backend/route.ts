import axios from "axios";
import { SERVER_ADDRESS } from '@/constants';
import { NextRequest, NextResponse } from 'next/server';

import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { checkSubscription } from "@/lib/subscription";


export async function POST(req: Request) {
    try {
      const body = await req.json();
      const { 
        route, 
        userId, 
        approach, 
        prompt, 
        overrides,
        persona 
      } = body;
  
      // const freeTrial = await checkApiLimit();
      const isPro = await checkSubscription();
  
      /* if (!freeTrial && !isPro) {      
        return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
      } */
  
      const answerResponse = await axios.post(
        `${SERVER_ADDRESS}/${route}`,
        {
          userId,
          approach,
          prompt,
          overrides,
          persona,
        },
        { timeout: 365000 } // (here: 365 seconds)
      );
  
      if (answerResponse.status === 200) {
        const text = answerResponse.data;
        return new Response(JSON.stringify({ text }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        });
  
        if (!isPro) {
          await incrementApiLimit();
        }
  
        console.log('answerResponse.data.text from chatgpt_api5: ', text);
      } else {
        const errorMessage = answerResponse.data.error || 'Something went wrong';
        console.error(errorMessage);
        return new Response(errorMessage, {
          status: answerResponse.status,
        });
      }
    } catch (error) {
      console.error(error);
      return new Response('Something went wrong', {
        status: 500,
      });
    }
  }