import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

import prismadb from "@/lib/prismadb";
import { Message } from "@prisma/client";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { checkSubscription } from "@/lib/subscription";


export async function POST(req: Request) {
  try {
    const body = await req.json();
    const user = await currentUser();
    const { companionId, content, role } = body;

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!content ||!role || !companionId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    const companion = await prismadb.companion.update({
        where: {
          id: companionId,
        },
        data: {
          messages: {
            create: [
              {
                content: content,
                role: role,
                userId: user!.id,
              },
            ],
          },
        },
      });
    
      if (!companion) {
        throw new Error("Companion not found");
      }

      
    return NextResponse.json(companion);
  } catch (error) {
    console.log("[MESSAGE_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};