import prismadb from "@/lib/prismadb";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { v2 as cloudinary } from "cloudinary";
import { Media } from "@prisma/client";


// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

enum MediaType {
    image = 'image',
    video = 'video',
  }

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    const body = await req.json();
    const { companionId, imageUrl } = body;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const customDirectory = userId.toString();

    const result = await cloudinary.uploader.upload(
      imageUrl, {
      asset_folder: customDirectory,
      detection: "captioning",
      categorization: "aws_rek_tagging:zh-TW",  
      //"aws_rek_tagging:zh-TW", "imagga_tagging:zh_cht", "imagga_tagging:google:zh-TW"
      auto_tagging: 0.7
    });

    const uploadedImageUrl = result.secure_url;
    const imageCaptionContent = result.info.detection.captioning.data.caption
    const tags = result.tags;
    console.log("CloudinaryImageCaption: ", imageCaptionContent)
    console.log("result: ", result)
    console.log("tags: ", tags)

    await prismadb.companion.update({
      where: {
          id: companionId,
      },
      data: {
        medias: {
          create: [
            {
              userId,
              url: uploadedImageUrl,
              mediaType: MediaType.image,
              desc: "",
            },
          ],
        },
      },        
    });

    /*
    await prismadb.companion.update({
      where: {
        id: companionId,
      },
      data: {
        messages: {
          create: [
            {
              content: imageCaptionContent,
              role: "assistant",
              userId: userId,
            },
          ],
        },
      },
    });
    */

    console.log("CloudinaryResponse: ", result)
    return NextResponse.json({ result });
  } catch (error) {
    console.error('[IMAGE_UPLOAD_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}