import prismadb from "@/lib/prismadb";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { v2 as cloudinary } from "cloudinary";
import { uploadMultipleImages } from "@/lib/cloudinary"
import { Media, Role } from "@prisma/client";

////////// TO be modified for /app/api/vision/route.ts ///////
// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

enum MediaType {
    image = 'image',
    video = 'video',
  }

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { companionId, userId, threadId, imageUrls, secret } = body;

    if (secret !== process.env.APP_SECRET_KEY) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    console.log("imageUrls@visionUpload", imageUrls)
    if (!companionId || !userId || !threadId || !imageUrls) {
      return new NextResponse("Missing required Images", { status: 400 });
    };

    const uploadedImageUrls  = await uploadMultipleImages(imageUrls)
    console.log("uploadedImageUrls@visionUpload", uploadedImageUrls)

    for (const uploadedImageUrl of uploadedImageUrls) {
      // Update Message with imageUrl
      await prismadb.companion.update({
        where: {
          id: companionId,
        },
        data: {
          messages: {
            create: [
              {
                threadId,
                role: Role.assistant,
                userId,
                content: '',
                image: uploadedImageUrl,
              },
            ],
          },
          medias: {
            create: [
              {
                userId,
                url: uploadedImageUrl,
                mediaType: MediaType.image,
                desc: "Dalle.E",
              },
            ],
          },
        },
      });
    }

    return NextResponse.json({ uploadedImageUrls });
  } catch (error) {
    console.error('[IMAGE_UPLOAD_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}