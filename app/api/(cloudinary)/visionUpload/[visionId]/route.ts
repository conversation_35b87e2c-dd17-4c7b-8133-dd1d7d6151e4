import prismadb from "@/lib/prismadb";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { v2 as cloudinary } from "cloudinary";
import { uploadMultipleImages } from "@/lib/cloudinary"
import { Media } from "@prisma/client";


// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

interface ImageInfo {
    url: string;
    content: string;
  }

enum MediaType {
    image = 'image',
    video = 'video',
  }

export async function POST(req: Request, props: { params: Promise<{ visionId: string }> }) {
  const params = await props.params;
  try {
    const { userId } = await auth();
    const body = await req.json();
    const { imageToUpload, visionMessageId }: { imageToUpload: ImageInfo[]; visionMessageId: string } = body;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    console.log("imageToUpload@visionId/route", imageToUpload)
    console.log("visionMessageId@visionId/route", visionMessageId)

    // Extract URLs from imageToUpload
    const imageURLs: string[] = imageToUpload.map(image => image.url);
    // Extract Ids from imageToUpload (store in the content of the imageToUpload)
    const messageIds: string[] = imageToUpload.map(image => image.content);

    //const responseUrls  = await uploadMultipleImages(imageURLs)
    const uniqueImageURLs = Array.from(new Set(imageURLs));

    const responseUrls = await uploadMultipleImages(uniqueImageURLs);
    console.log("responseUrls@visionUpload", responseUrls)

    const updateDataToPrisma = messageIds.map(
      (content: string, index: number) => ({ content, imageUrl: responseUrls[index] })
    );


    // Fetch the existing message content
    const existingMessage = await prismadb.message.findUnique({
        where: { id: visionMessageId, userId },
    });

    if (existingMessage) {
      // Check if the message exists and retrieve its content
      const existingContent = existingMessage?.content || '';

      // Construct the new content
      const imageWithMessage = responseUrls.map(url => `![url](${url})`).join('\n') + '\n' + existingContent;
      //const imageWithMessage = responseUrls.map(url => `[![Image](${url})](${url})`).join('\n') + '\n' + existingContent;


      // Update Message content with imageUrl
      //await prismadb.message.update({
      //  where: { id: visionMessageId, userId, },
      //  data: { content: imageWithMessage },
      //});

      for (const updateData of updateDataToPrisma) {
        const { content, imageUrl } = updateData;
    
        const message = await prismadb.message.findUnique({
          where: { id: content },
          include: { companion: true }, // Fetch associated Companion
        });
    
        if (message && message.companion) {
          const { companion } = message;
    
          // Delete the message with the specified ID
          //await prismadb.message.delete({
          //  where: { id: content },
          //});

          // Update Message image with imageUrl to cloudinary
          await prismadb.message.update({
            where: { id: content, userId, },
            data: { image: imageUrl },
          });
    
          await prismadb.media.create({
            data: {
              userId,
              companionId: companion.id,
              url: imageUrl,
              mediaType: MediaType.image,
              desc: "Dalle.E",
              //companion: { connect: { id: companion.id } },
            },
          });
        }
      }
    }

    const path = `/vision/${params.visionId}`
    revalidatePath(path);


    return NextResponse.json({ responseUrls });
  } catch (error) {
    console.error('[IMAGE_UPLOAD_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}