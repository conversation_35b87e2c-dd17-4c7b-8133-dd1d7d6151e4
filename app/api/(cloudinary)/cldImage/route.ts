import prismadb from "@/lib/prismadb";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { v2 as cloudinary } from "cloudinary";
import { Media } from "@prisma/client";


// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true,
});

enum MediaType {
    image = 'image',
    video = 'video',
  }

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    const body = await req.json()
    const { companionId, imageUrl, rotateValue } = body;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const customDirectory = userId.toString();

    const result = await cloudinary.image(
      imageUrl, {transformation: [
        {width: 512, crop: "scale"},
        {angle: rotateValue}
        ]})

    console.log("CloudinaryResponse: ", result)
    return NextResponse.json({ result });
  } catch (error) {
    console.error('[IMAGE_TRANSFORM_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}