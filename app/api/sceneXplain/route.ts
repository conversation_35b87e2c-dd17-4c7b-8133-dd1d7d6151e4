import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { checkSubscription } from "@/lib/subscription";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";



export async function POST(
    req: NextRequest
  ) {
    try {
      const { userId } = await auth();
      const body = await req.json();
      const {
        uploadedImageUrl } = body
  
      if (!userId) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
  
    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
        return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    const apiKey = process.env.SCENEX_API_KEY;

    if (!apiKey) {
    return new NextResponse("API key not configured.", { status: 500 });
    }

    console.log("uploadedImageUrl: ", uploadedImageUrl)

    const response = await fetch('https://api.scenex.jina.ai/v1/describe', {
        headers: {
            'x-api-key': `token ${apiKey}`,
            'content-type': 'application/json'
        },
        body: JSON.stringify({
            data: [
                {
                    image: uploadedImageUrl, 
                    features: [],
                    algorithm: "Hearth",
                    languages: ["zh-TW"] //require extra 2 points
                },
            ]
        }),
        method: 'POST'
    });

    if (!isPro) {
        await incrementApiLimit();
      }

    if (!response.ok) {
    throw new Error(`Request failed with status: ${response.status}`);
    }

    const data = await response.json();

    // Extract the text from the response
    const img = data.result?.[0] || {};
    const text = img.i18n['zh-TW'] || ''; // if use zh-tw
    //const text = img.text || '';

    console.log("img: ", img)
    //console.log('Text from image:', text);

    return new NextResponse(text, { status: 200 });

  } catch (error) {
    console.log('[IMAGE2TEXT_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}