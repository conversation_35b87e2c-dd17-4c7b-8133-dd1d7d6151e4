export async function GET(request: any) {
    const url = new URL(request.url);
    const symbol = url.searchParams.get('symbol');
    const EXTERNAL_API_URL = `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}?&interval=1d`;
  
    try {
      const apiRes = await fetch(EXTERNAL_API_URL, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36',
          // Add other headers if needed
        },
      });
  
      if (!apiRes.ok) throw new Error(`Failed to fetch`);
      const data = await apiRes.json();
      console.log("Fetch Stock Price: ", data)
  
      return Response.json(
        { price: data.chart.result[0].meta.regularMarketPrice }, // Update the path to the price data as needed
        { status: 200 },
      );
    } catch (error) {
      return Response.json({ error: error }, { status: 500 });
    }
  }
  