import { NextResponse } from "next/server";
import { currentUser } from '@clerk/nextjs/server'

export async function POST(
  req: Request
) {
  try {    
    const user = await currentUser();
    const emailAddress = user?.emailAddresses[0]?.emailAddress
    if (!emailAddress) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    return NextResponse.json(emailAddress);
  } catch (error) {
    console.log('[CLERK_ERROR]', error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};