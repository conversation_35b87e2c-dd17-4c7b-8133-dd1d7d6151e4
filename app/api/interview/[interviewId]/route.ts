import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache';
import { checkCreditBalance } from "@/lib/credit-balance";
import { 
  InterviewAgent
} from '@/components/observation/ObservationAgent' 
import { 
    newThread, 
  } from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils"; 
import { tokenCounter } from "@/lib/token-counter";

export const runtime = "edge";
export const preferredRegion = ['hnd1']

export async function POST(req: Request, props: { params: Promise<{ interviewId: string }> }) {
  const params = await props.params;
  const {
    companion,
    threadId,
    existingThread,
    messages,
    chatRuns,
    observationList,
    useReflectionThreshold,
    persona,
    aiLanguage,
    path,
    targetThreadId,
    targetUserId
  } = await req.json();

  const user = await currentUser();
  const userId = user?.id!
  const companionId = companion?.id!
  console.log("targetThreadId: ", targetThreadId)
  console.log("targetUserId: ", targetUserId)

  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  //const freeTrial = await checkApiLimit();
  const creditAccess = await checkCreditBalance();
  //if (!freeTrial && !creditAccess) {
  if (!creditAccess) {
    return new NextResponse(
      JSON.stringify({ 
        error: "Your credit is empty. Please purchase more credits." 
      }), 
      { 
        status: 403,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }

  console.log("observationList", observationList)

  try {
    const maxMessages = 17
    let numberOfMessagesToSlice = useReflectionThreshold
    numberOfMessagesToSlice = Math.max(numberOfMessagesToSlice, maxMessages);
    const latestMessages = messages.slice(-numberOfMessagesToSlice);
  
    const latestUserMessage = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

    const userPrompt = latestUserMessage?.content
    console.log("latestUserMessage", latestUserMessage)
    const stream = new ReadableStream({
      async start(controller) {
        const interviewSummary = await InterviewAgent({
          companionId,
          message: userPrompt!,
          name: persona?.name || "",
          age: persona?.age || 25,
          traits: persona?.traits || "",
          status: persona?.status || "",
          reflectionThreshold: useReflectionThreshold,
          path,
          targetSessionId: targetThreadId,
          targetUserId
        });

        if (userPrompt && interviewSummary) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.interviewId, userId, userPrompt, "user", threadId
            );
          } else {
            const name = userPrompt.substring(0, 30);
            await newThread({
              threadId, 
              name, 
              companionId: params.interviewId, 
              content: userPrompt,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            companionId, userId, interviewSummary, "assistant", threadId
          )
          /*const combinedMessages = [
            { role: 'system', content: systemPrompt },
            ...latestMessages,
          ]
          await tokenCounter(
            combinedMessages, 
            interviewSummary, 
            1,
            modelName: 'gpt-4-turbo-preview', 
            userId,
            process.env.APP_SECRET_KEY!
          )*/
          revalidatePath(path)
        }

        if (!interviewSummary) {
          controller.enqueue('No summary available');
          controller.close();
          return;
        }

        // Simulate streaming by sending the text chunk by chunk
        const chunkSize = 5; // Adjust this value to control the chunk size
        for (let i = 0; i < interviewSummary.length; i += chunkSize) {
          const chunk = interviewSummary.slice(i, i + chunkSize);
          controller.enqueue(chunk);
          // Add a small delay to simulate network latency
          await new Promise(resolve => setTimeout(resolve, 10));
        }

        controller.close();
      }
    });

    return new NextResponse(stream);

  } catch (error: any) {
    console.error("Error fetching observations:", error);
    return new NextResponse(`Error: ${error.message}`, { status: 500 });
  }
}