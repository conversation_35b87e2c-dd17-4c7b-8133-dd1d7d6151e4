import { NextResponse } from 'next/server'
import { SupabaseHybridSearch } from "@langchain/community/retrievers/supabase"
import { CohereEmbeddings } from "@langchain/cohere"
import { createClient } from '@supabase/supabase-js';
import { formatDocumentsAsString } from "langchain/util/document"
import { auth, clerkClient } from '@clerk/nextjs/server'

export const runtime = "edge";

export async function POST(request: Request) {
  const { query } = await request.json()
  const sessionId = "sess_2jJcNEFqn54YAYVmtq4RpjRGgyo"
  const client = await clerkClient()
  const token = await client.sessions.getToken(sessionId, "supabase")
  console.log("token: ", token)
  const authToken = token ? { Authorization: `Bearer ${token}` } : null
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bear<PERSON> ${token}`
        },
      },
    }
  );

  const retriever = new SupabaseHybridSearch(
    new CohereEmbeddings({
      apiKey: process.env.COHERE_API_KEY,
      //@ts-ignore
      model: "embed-multilingual-v3.0",
    }),
    {
      client: supabase,
      similarityK: 4,
      keywordK: 0,
      tableName: "document_sections_1024",
      similarityQueryName: "match_document_sections_1024",
      keywordQueryName: "kw_match_document_sections",
    }
  )

  try {
    const docs = await retriever.getRelevantDocuments(query)
    const results = formatDocumentsAsString(docs)
    console.log("results: ", results)

    return NextResponse.json({ results })
  } catch (error) {
    console.error('Retriever error:', error)
    return NextResponse.json({ error: `An error occurred while searching with "${query}".` }, { status: 500 })
  }
}