import { NextRequest, NextResponse } from "next/server";
import { clerkClient } from '@clerk/nextjs/server'

export async function POST(req: NextRequest) {
  try {
    const { sessionId } = await req.json()
    const client = await clerkClient()
    const authToken = await client.sessions.getToken(sessionId, "supabase")
    console.log("authToken: ", authToken)

    if (!authToken) {
      return new Response('Unauthorized', { status: 401 });
    }
    return NextResponse.json({ authToken });
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}