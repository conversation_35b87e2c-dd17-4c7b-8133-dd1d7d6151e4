import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { traceable } from "langsmith/traceable";
import { notesIndex } from "@/lib/pinecone";
import prismadb from "@/lib/prismadb";
//import openai, { getEmbedding } from "@/lib/openai";
import cohere, { getEmbedding } from "@/lib/cohere";
import { auth } from "@clerk/nextjs/server";
import { createOpenAI } from '@ai-sdk/openai';
import { 
  UIMessage,
  streamText,
  convertToModelMessages,
} from 'ai';
import { newThread } from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";

export const runtime = 'edge'
export const preferredRegion = ['sfo1']

const groq = createOpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY,
});

const model = groq('llama-3.3-70b-versatile');

export async function POST(req: Request, props: { params: Promise<{ notesId: string }> }) {
  const params = await props.params;
  try {
    const {
      companion,
      threadId,
      existingThread,
      messages,
      path
    } = await req.json();
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const prompt = messages
      .filter((message: { role: string }) => message.role === 'user')
      .pop() //retrieve the last element

    const userPrompt: string | undefined = prompt?.content

    const messagesTruncated = messages.slice(-6);
    const embedding = await getEmbedding(
      messagesTruncated.map((message: any) => message.content).join("\n"),
    );

    const vectorQueryResponse = await notesIndex.query({
      vector: embedding as number[],
      topK: 4,
      filter: { userId },
    });

    const relevantNotes = await prismadb.note.findMany({
      where: {
        id: {
          in: vectorQueryResponse.matches.map((match) => match.id),
        },
      },
    });

    console.log("Relevant notes found: ", relevantNotes);

    const systemMessage = "You are an intelligent note-taking app. You answer the user's question based on their existing notes. " +
    "The relevant notes for this query are:\n" +
    relevantNotes
      .map((note) => `Title: ${note.title}\n\nContent:\n${note.content}`)
      .join("\n\n");

    const res = streamText({
      model,
      temperature: 0.2,
      maxOutputTokens: 2000,
      system: systemMessage,
      messages: convertToModelMessages(messagesTruncated),
      async onFinish({ text, toolCalls, toolResults, finishReason, usage }) {
        if (userPrompt && text) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.notesId, userId, userPrompt, "user", threadId
            );
          } else {
            const name = userPrompt.substring(0, 30);
            await newThread({
              threadId, 
              name, 
              companionId: params.notesId, 
              content: userPrompt,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            params.notesId, userId, text, "assistant", threadId
          )
          revalidatePath(path)
        }
      }
    })  

    return res.toUIMessageStreamResponse();
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}