import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { notesIndex } from "@/lib/pinecone";
import prismadb from "@/lib/prismadb";
import cohere, { getEmbedding } from "@/lib/cohere";
import { Client } from "@octoai/client";
import { auth } from "@clerk/nextjs/server";
import { StreamingTextResponse, Message } from "ai";
import { newThread } from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";

export const runtime = 'edge'
export const preferredRegion = ['sfo1']

const OCTOAI_TOKEN = process.env.OCTOAI_TOKEN;
const client = new Client(OCTOAI_TOKEN);

export async function POST(req: Request, props: { params: Promise<{ notesId: string }> }) {
  const params = await props.params;
  try {
    const {
      companion,
      threadId,
      existingThread,
      messages,
      path
    } = await req.json();
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse('Unauthorized', {
        status: 401
      })
    }

    const prompt = messages
      .filter((message: { role: string }) => message.role === 'user')
      .pop() //retrieve the last element

    const userPrompt: string | undefined = prompt?.content
    const messagesTruncated = messages.slice(-6);

    const embedding = await getEmbedding(
      messagesTruncated.map((message: Pick<Message, "role" | "content">) => message.content).join("\n"),
    );

    const vectorQueryResponse = await notesIndex.query({
      vector: embedding as number[],
      topK: 4,
      filter: { userId },
    });

    const relevantNotes = await prismadb.note.findMany({
      where: {
        id: {
          in: vectorQueryResponse.matches.map((match) => match.id),
        },
      },
    });

    console.log("Relevant notes found: ", relevantNotes);

    const systemMessage = {
      role: "system",
      content:
        "You are an intelligent note-taking app. You answer the user's question based on their existing notes. Response in Traditional Chinese (zh-TW)." +
        "The relevant notes for this query are:\n" +
        relevantNotes
          .map((note) => `Title: ${note.title}\n\nContent:\n${note.content}`)
          .join("\n\n"),
    };

    const stream = await client.chat.completions.create({
      model: "mixtral-8x7b-instruct",
      stream: true,
      messages: [systemMessage, ...messagesTruncated],
    });

    let completion = '';
    //for await (const chunk of stream) {
    //  completion += chunk.choices[0].delta.content;
    //}

    const textEncoder = new TextEncoder();
    const clientStream = new ReadableStream({
      async start(controller) {
        for await (const chunk of stream) {
          if (chunk.choices[0].delta.content != null) {
            controller.enqueue(
              textEncoder.encode(
                chunk.choices[0].delta.content
              ),
            );
          completion += chunk.choices[0].delta.content;
          }
        }
        
        if (userPrompt && completion) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.notesId, userId, userPrompt, "user", threadId
            );
          } else {
            const name = userPrompt.substring(0, 30);
            await newThread({
              threadId, 
              name, 
              companionId: params.notesId, 
              content: userPrompt,
              role: "user", 
            });
          }
          await saveCompletionToDatabase(
            params.notesId, userId, completion, "assistant", threadId
          )
        }
  
        controller.close();
      },
    });
    
    revalidatePath(path)

    return new StreamingTextResponse(clientStream)
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}