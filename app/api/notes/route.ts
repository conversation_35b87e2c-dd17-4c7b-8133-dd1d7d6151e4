import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { notesIndex } from "@/lib/pinecone";
import prismadb from "@/lib/prismadb";
//import { getEmbedding } from "@/lib/openai";
import { getEmbedding } from "@/lib/cohere";
import {
  createNoteSchema,
  deleteNoteSchema,
  updateNoteSchema,
} from "@/lib/validation/note";
import { auth } from "@clerk/nextjs/server";

export const runtime = 'edge'
export const preferredRegion = ['sfo1']

export async function POST(req: Request) {
  try {
    const body = await req.json();

    const parseResult = createNoteSchema.safeParse(body);

    if (!parseResult.success) {
      console.error(parseResult.error);
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }

    const { title, content, path } = parseResult.data;

    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!title || !content) {
      return new NextResponse("Companion or Persona not found", { status: 404 });
    }

    const embedding = await getEmbeddingForNote(title, content);

    const note = await prismadb.$transaction(async (tx) => {
      const note = await tx.note.create({
        data: {
          title,
          content,
          companionName: "Note assistant",
          userId,
        },
      });

      await notesIndex.upsert([
        {
          id: note.id,
          values: embedding as number[],
          metadata: { userId },
        },
      ]);

      return note;
    });

    if (path) {
      revalidatePath(path)
    }

    return NextResponse.json({ note }, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();

    const parseResult = updateNoteSchema.safeParse(body);

    if (!parseResult.success) {
      console.error(parseResult.error);
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }

    const { id, title, content, path } = parseResult.data;

    const note = await prismadb.note.findUnique({ where: { id } });

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 });
    }

    const { userId } = await auth();

    if (!userId || userId !== note.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const embedding = await getEmbeddingForNote(title, content);

    const updatedNote = await prismadb.$transaction(async (tx) => {
      const updatedNote = await tx.note.update({
        where: { id },
        data: {
          title,
          content,
        },
      });

      await notesIndex.upsert([
        {
          id,
          values: embedding as number[],
          metadata: { userId },
        },
      ]);

      return updatedNote;
    });

    if (path) {
      revalidatePath(path)
    }

    return NextResponse.json({ updatedNote }, { status: 200 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const body = await req.json();

    const parseResult = deleteNoteSchema.safeParse(body);

    if (!parseResult.success) {
      console.error(parseResult.error);
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }

    const { id, path } = parseResult.data;

    const note = await prismadb.note.findUnique({ where: { id } });

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 });
    }

    const { userId } = await auth();

    if (!userId || userId !== note.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await prismadb.$transaction(async (tx) => {
      await tx.note.delete({ where: { id } });
      await notesIndex.deleteOne(id);
    });

    if (path) {
      revalidatePath(path)
    }
    return NextResponse.json({ message: "Note deleted" }, { status: 200 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

async function getEmbeddingForNote(title: string, content: string | undefined) {
  return getEmbedding(title + "\n\n" + (content ?? ""));
}
