import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'

import prismadb from "@/lib/prismadb";
import { checkSubscription } from "@/lib/subscription";
import { v4 as uuidv4 } from 'uuid'

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const user = await currentUser();
    const { src, name, description, minAge, maxAge, introduction, instructions, seed, categoryId, assistantId } = body;

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!src || !name || !description || !minAge || !maxAge || !introduction || !instructions || !seed || !categoryId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };

    const isPro = await checkSubscription();

    if (!isPro) {
      return new NextResponse("Pro subscription required", { status: 403 });
    }

    let newAssistantId: string = "" 
    if (assistantId?.includes("asst_")) {
      newAssistantId = assistantId
    } else {
      newAssistantId = uuidv4()
    }

    const companion = await prismadb.companion.create({
      data: {
        assistantId: newAssistantId,
        categoryId,
        userId: user.id,
        userName: user.firstName,
        src,
        name,
        introduction,
        minAge,
        maxAge,
        description,
        instructions,
        seed,
      }
    });
    revalidatePath("/")
    return NextResponse.json(companion);
  } catch (error) {
    console.log("[COMPANION_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};
