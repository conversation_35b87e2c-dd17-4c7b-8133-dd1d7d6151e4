import { auth, currentUser } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'

import prismadb from "@/lib/prismadb";
import { checkSubscription } from "@/lib/subscription";

export async function PATCH(req: Request, props: { params: Promise<{ companionId: string }> }) {
  const params = await props.params;
  try {
    const body = await req.json();
    const user = await currentUser();
    const { src, name, description, minAge, maxAge, introduction, instructions, seed, categoryId, assistantId } = body;

    if (!params.companionId) {
      return new NextResponse("Companion ID required", { status: 400 });
    }

    if (!user || !user.id || !user.firstName) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!src || !name || !description || !minAge || !maxAge || !introduction || !instructions || !seed || !categoryId) {
      return new NextResponse("Missing required fields", { status: 400 });
    };

    const isPro = await checkSubscription();

    if (!isPro) {
      return new NextResponse("Pro subscription required", { status: 403 });
    }

    let newAssistantId: string = ''
    if (assistantId?.includes("asst_")) {
      newAssistantId = assistantId
    } else {
      newAssistantId = params.companionId
    }

    const companion = await prismadb.companion.update({
      where: {
        id: params.companionId,
        userId: user.id,
      },
      data: {
        assistantId: newAssistantId,
        categoryId,
        userId: user.id,
        userName: user.firstName,
        src,
        name,
        introduction,
        minAge,
        maxAge,
        description,
        instructions,
        seed,
      }
    });

    revalidatePath("/")
    return NextResponse.json(companion);
  } catch (error) {
    console.log("[COMPANION_PATCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};

export async function DELETE(request: Request, props: { params: Promise<{ companionId: string }> }) {
  const params = await props.params;
  try {
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const companion = await prismadb.companion.delete({
      where: {
        userId,
        id: params.companionId
      }
    });
    revalidatePath("/")
    
    return NextResponse.json(companion);
  } catch (error) {
    console.log("[COMPANION_DELETE]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
};
