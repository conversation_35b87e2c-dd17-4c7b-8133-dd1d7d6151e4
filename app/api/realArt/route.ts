import { NextRequest, NextResponse } from "next/server";
import Replicate from "replicate";

import { auth, currentUser } from "@clerk/nextjs/server";
import { incrementApiLimit, checkApiLimit } from "@/lib/api-limit";
import { checkSubscription } from "@/lib/subscription";

const NEGATIVE_PROMPT =
  "(deformed iris, deformed pupils, semi-realistic, cgi, 3d, render, sketch, cartoon, drawing, anime, mutated hands and fingers:1.4), (deformed, distorted, disfigured:1.3), poorly drawn, bad anatomy, wrong anatomy, extra limb, missing limb, floating limbs, disconnected limbs, mutation, mutated, ugly, disgusting, amputation, human anatomy";
const STRENGTH = 0.71;

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

export async function POST(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user || !user.id || !user.firstName) {
        return new NextResponse("Unauthorized", { status: 401 });
      }

    const formData = await req.formData();
    const file = formData.get("image") as Blob;
    const prompt = formData.get("prompt") as string;

    if (!file) {
      return new NextResponse("No file found", { status: 400 });
    }

    if (!prompt) {
      return new NextResponse("No prompt found", { status: 400 });
    }

    const freeTrial = await checkApiLimit();
    const isPro = await checkSubscription();

    if (!freeTrial && !isPro) {
      return new NextResponse("Free trial has expired. Please upgrade to pro.", { status: 403 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const dataURI = `data:${file.type};base64,${buffer.toString("base64")}`;

    const modelURL =
      "15a3689ee13b0d2616e98820eca31d4c3abcd36672df6afce5cb6feb1d66087d";

    const output = await replicate.predictions.create({
      version: modelURL,
      input: {
        image: dataURI,
        prompt: prompt,
        negative_prompt: NEGATIVE_PROMPT,
        strength: STRENGTH,
      },
    });

    const resp_data = {
      generation_id: output.id,
      status: output.status,
    };

    if (!isPro) {
        await incrementApiLimit();
      }

    return NextResponse.json(resp_data);
  } catch (err: any) {
    console.log("[REAL_ART_API_ERROR]");
    console.log(err);
    return new NextResponse(err, { status: 500 });
  }
}