import { NextRequest, NextResponse } from "next/server";
import Replicate from "replicate";

import { auth, currentUser } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { uploadImage } from "@/lib/cloudinary";

enum MediaType {
  image = 'image',
  video = 'video',
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

export async function GET(
  req: NextRequest,
  props: { params: Promise<{ id: string }>, query: { companionId: string } }
) {
  const params = await props.params;

  const {
    query
  } = props;

  const user = await currentUser();
  if (!user || !user.id || !user.firstName) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { companionId } = query;
  const prediction = await replicate.predictions.get(params.id);

  if (prediction?.status !== "succeeded" && prediction?.status !== "failed") {
    return NextResponse.json({ status: prediction.status, outputURL: "" });
  } else {
    if (prediction?.status === "succeeded") {
      const cloudinary_resp = await uploadImage(prediction.output as string);

      await prismadb.companion.update({
        where: {
            id: companionId,
        },
        data: {
          medias: {
            create: [
              {
                userId: user.id,
                url: cloudinary_resp.secure_url,
                mediaType: MediaType.image,
                desc: "realArt",
              },
            ],
          },
        },        
      });


      return NextResponse.json(
        {
          status: prediction.status,
          outputURL: cloudinary_resp.secure_url,
        },
        { status: 200 },
      );
    } else
      return NextResponse.json({ status: prediction.status, outputURL: "" });
  }
}