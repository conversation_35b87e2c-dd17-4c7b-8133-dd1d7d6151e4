import '@/app/scrollbar-hidden.css'
import { auth } from '@clerk/nextjs/server'
const Layout = async (
  {
    children
  }: {
    children: React.ReactNode;
  }
) => {
  await auth.protect()
  //const { sessionId } = auth()
  //console.log("sessionId: ", sessionId) 
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      
      {children}
    </div>
  );
}

export default Layout;