"use client";
import MobileNav from "@/components/todovex/nav/mobile-nav";
import SideBar from "@/components/todovex/nav/side-bar";
import Todos from "@/components/todovex/todos/todos";
import { api } from "@/convex/_generated/api";
import { useAction } from "convex/react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Search() {
  const params = useParams<{ searchQuery: string }>();
  const searchQuery = params?.searchQuery;
  
  const [searchResults, setSearchResults] = useState<any>([]);
  const [searchInProgress, setSearchInProgress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const vectorSearch = useAction(api.search.searchTasks);

  useEffect(() => {
    const handleSearch = async () => {
      if (!searchQuery) {
        setError("Search query is missing");
        return;
      }

      setSearchResults([]);
      setSearchInProgress(true);
      setError(null);

      try {
        const results = await vectorSearch({
          query: searchQuery,
        });
        console.log("searchQueryResults", results)
        setSearchResults(results || []);
      } catch (err) {
        console.error("Error during search:", err);
        setError("An error occurred during the search");
      } finally {
        setSearchInProgress(false);
      }
    };

    if (searchQuery) {
      console.log("searchQuery", searchQuery)
      handleSearch();
    }
  }, [searchQuery, vectorSearch]);

  if (!searchQuery) {
    return <div>Search query not found</div>;
  }

  return (
    <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
      <SideBar />
      <div className="flex flex-col">
        <MobileNav />
        <main className="flex flex-1 flex-col gap-4 p-4 lg:px-8">
          <div className="xl:px-40">
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-semibold md:text-2xl">
                Search Results for{" "}
                <span>
                  {`"`}
                  {decodeURI(searchQuery)}
                  {`"`}
                </span>
              </h1>
            </div>
            {error && <div className="text-red-500">{error}</div>}
            {searchInProgress ? (
              <div>Searching...</div>
            ) : (
              <div className="flex flex-col gap-1 py-4">
                <Todos
                  items={searchResults?.filter(
                    (item: any) => item.isCompleted === false
                  )}
                />
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}