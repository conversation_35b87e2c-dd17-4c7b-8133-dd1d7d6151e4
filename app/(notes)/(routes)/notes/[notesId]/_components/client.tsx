'use client';

import { useEffect, useRef, useState } from 'react';

import NavBar from "@/components/note/NavBar";
import { Notes } from '@/components/note/Notes';

import { useChat, type Message } from '@ai-sdk/react'
import { 
  Companion, 
  Message as chatMessage, 
  Observations, 
  ChatSummary,
  Todos,
  Note,
  Persona } from '@prisma/client'
import { ChatFixedHeader } from '@/components/chat-fixed-header'

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { GradientPicker } from "@/components/GradientPicker.tsx"
import { defaultBackground } from '@/constants'
import { ObservationModal } from '@/components/modals/ObservationModal'
import ObservationNew from '@/components/observation/observation-new'
import TodoList from '@/components/suggestion/TodoList'
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
import ThemeChanger from "@/components/nextly/DarkSwitch";

type ThreadInfo = { id: string; name: string } | null;

export interface NotesProps {
  companion: Companion & {
    messages: chatMessage[]
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: ThreadInfo
  notes: Note[]
}

export function NotesClient({ 
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  }: NotesProps) {
  const [isMounted, setIsMounted] = useState(false);
  const isLocalStorageAvailable = typeof localStorage !== 'undefined';
  const initialBackground = isLocalStorageAvailable ? localStorage.getItem('background') : defaultBackground;
  const [background, setBackground] = useState(initialBackground || defaultBackground);
  const path = `/notes/${companion.id}`

  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  useEffect(() => {
    localStorage.setItem('background', background);
  }, [background]);
  

  if (!isMounted) {
    return null;
  }

  return (    
    <div className="flex flex-col h-[100vh] w-full items-center lg:px-4 pb-2 space-y-2" style={{ background }}>
      {companion ? <ChatFixedHeader companion={companion} countMessages={notes?.length} /> : null}
      <NavBar companion={companion} threadId={threadId} existingThread={existingThread} path={path} />
      <div className="grid gap-3 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-6 2xl:grid-cols-7 max-h-[86vh] overflow-y-auto">
        {notes.map((note) => (
          <Notes note={note} key={note.id} path={path} />
        ))}
        {notes.length === 0 && (
            <div className="col-span-full text-center">
            {"You don't have any notes yet. Why don't you create one?"}
            </div>
        )}
        </div>
    </div>    
  );
}
