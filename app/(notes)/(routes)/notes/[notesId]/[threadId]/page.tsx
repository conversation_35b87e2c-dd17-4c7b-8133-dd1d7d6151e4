import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { getCompanionById, getThread } from "@/app/actions/threadActions"
import { getNotes } from "@/app/actions/noteActions"
import { Metadata } from "next";
import { NotesClient } from "../_components/client";

//export const runtime = 'edge'
export const revalidate = 0
export const preferredRegion = ['sfo1']

export interface NotesPageProps {
  params: Promise<{
    threadId: string
    notesId: string
  }>
}

type ThreadInfo = { id: string; name: string } | null;

export const metadata: Metadata = {
  title: "Notes",
};

export default async function NotesPage(props: NotesPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const existingThread: ThreadInfo = await getThread(
    userId, params.notesId, params.threadId
  );

  const companion = await getCompanionById(userId, params.notesId, params.threadId)

  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  return <NotesClient companion={companion} foundPersona={foundPersona} threadId={params.threadId} existingThread={existingThread} notes={notes} />
}