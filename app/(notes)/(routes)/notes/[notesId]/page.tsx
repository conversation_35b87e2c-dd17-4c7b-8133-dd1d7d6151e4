import { redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { v4 as uuidv4 } from 'uuid'

//export const runtime = 'edge'
// Do not cache this page
export const revalidate = 0


export interface NotesIdPageProps {
  params: Promise<{
    notesId: string
  }>
}

export default async function NotesIdPage(props: NotesIdPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
      return redirectToSignIn();
    }

  const threadId: string = uuidv4()

  if (threadId) {
    redirect(`/notes/${params.notesId}/${threadId}`)
  } else {
    redirect(`/`)
  }
}