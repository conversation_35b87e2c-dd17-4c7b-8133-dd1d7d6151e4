import { SidebarHeader } from '@/components/talk/sidebar-header'
import '@/app/scrollbar-hidden.css'

interface NotesLayoutProps {
  params: Promise<{ 
    notesId: string
  }>
}

const NotesLayout = async (
  props: NotesLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/notes/${params.notesId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.notesId} path={path} />
      {children}
    </div>
  );
}

export default NotesLayout;