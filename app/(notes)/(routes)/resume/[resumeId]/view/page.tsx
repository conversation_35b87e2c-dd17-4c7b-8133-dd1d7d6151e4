'use client'

import Header from '@/components/resume/Header'
import Loading from '@/app/loading'
import { ResumeInfo } from '@/components/resume/types';
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { ResumeInfoContext } from '@/components/providers/ResumeInfoContext'
import ResumePreview from '@/components/resume/ResumeTemplate'
import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation';
import { RWebShare } from 'react-web-share'
import { useQuery } from '@tanstack/react-query'
import { getResumeById, ResumeData } from '@/app/actions/resumeActions'

function ViewResume() {
  const params = useParams();
  const resumeId = params?.resumeId! as string;
  const [isPrinting, setIsPrinting] = useState(false);
  const [resumeInfo, setResumeInfo] = useState<ResumeInfo | null>(null);

  const { isLoading, isError, error, refetch } = useQuery({
    queryKey: ['resumeById', resumeId],
    queryFn: async () => {
      if (!resumeId) {
        toast.error('Resume ID is required');
        return null;
      }
      const response = await getResumeById(resumeId);
      if (!response.success) {
        toast.error(response.error || 'Failed to fetch resume');
        return null;
      }
      const resumeData: ResumeInfo = {
        ...response.data,
        educations: response.data?.educations ?? [],
        experiences: response.data?.experiences ?? [],
        skills: response.data?.skills ?? [],
      } as ResumeInfo;
      
      setResumeInfo(resumeData);
      return resumeData;
    },
    enabled: !!resumeId,
  });

  useEffect(() => {
    if (isPrinting) {
      setTimeout(() => {
        window.print();
        setIsPrinting(false);
      }, 500);
    }
  }, [isPrinting]);

  if (isLoading) return <Loading />;
  if (error) return toast.error(`Error fetching resume: ${error.message}`);

  const HandleDownload = () => {
    setIsPrinting(true);
  };

  return (
    <ResumeInfoContext.Provider value={{ resumeInfo, setResumeInfo }}>
      <div className={isPrinting ? 'hidden' : ''}>
        <div className='my-4 mx-10 md:mx-20 lg:mx-36'>
          <h2 className='text-center text-2xl font-medium'>
            Congratulations! Your generates Resume is ready!
          </h2>
          <p className='text-center text-gray-400'>
            Now you are ready to download your resume and you can share a unique
            resume URL with your friends and family.
          </p>
          <div className='flex justify-between px-44 my-2'>
            <Button size="xs" onClick={HandleDownload}>Download</Button>

            <RWebShare
              data={{
                text: "Hello Everyone, This is my resume please open the URL to see it",
                url: `${process.env.NEXT_PUBLIC_BASE_URL}/resume/${resumeId}/view`,
                title: `${resumeInfo?.firstName || ''} ${resumeInfo?.lastName || ''} resume`,
              }}
              onClick={() => console.log("shared successfully!")}
            >
              <Button size="xs">Share</Button>
            </RWebShare>
          </div>
        </div>

        <div className='my-2 mx-10 md:mx-20 lg:mx-36'>
          <ResumePreview />
        </div>
      </div>
      {isPrinting && (
        <div className='print-only'>
          <ResumePreview />
        </div>
      )}
    </ResumeInfoContext.Provider>
  );
}

export default ViewResume;
