'use client'

import React, { useState} from 'react';
import Loading from '@/app/loading'
import { ResumeInfo } from '@/components/resume/types';
import { toast } from 'sonner'
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { getResumeById } from '@/app/actions/resumeActions';
import FormSection from '@/components/resume/FormSection';
import ResumePreview from '@/components/resume/ResumeTemplate';
import { ResumeInfoContext } from '@/components/providers/ResumeInfoContext';
import { ResumeData } from '@/app/actions/resumeActions';

function EditResume() {
  const params = useParams();
  const resumeId = params?.resumeId! as string;
  const [resumeInfo, setResumeInfo] = useState<ResumeInfo | null>(null);

  const { isLoading, isError, error, refetch } = useQuery({
    queryKey: ['resumeById', resumeId],
    queryFn: async () => {
      if (!resumeId) {
        toast.error('Resume ID is required');
        return null;
      }
      const response = await getResumeById(resumeId);
      if (!response.success) {
        toast.error(response.error || 'Failed to fetch resume');
        return null;
      }
      const resumeData: ResumeInfo = {
        ...response.data,
        educations: response.data?.educations ?? [],
        experiences: response.data?.experiences ?? [],
        skills: response.data?.skills ?? [],
      } as ResumeInfo;
      
      setResumeInfo(resumeData);
      return resumeData;
    },
    enabled: !!resumeId,
  });

  if (isLoading) return <Loading />;
  if (error) return toast.error(`Error fetching resume: ${error.message}`);

  return (
    <ResumeInfoContext.Provider value={{ resumeInfo, setResumeInfo }}>
      <div className='grid grid-cols-1 md:grid-cols-2 px-4 pt-2 gap-8 overflow-auto'>
        {/* Form Section  */}
        <FormSection />
        {/* Preview Section  */}
        <ResumePreview />
      </div>
    </ResumeInfoContext.Provider>
  );
}

export default EditResume;
