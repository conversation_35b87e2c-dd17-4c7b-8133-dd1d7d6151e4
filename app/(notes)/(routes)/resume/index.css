@tailwind base;
  @tailwind components;
  @tailwind utilities;


  @media print{
    #no-print{
      display: none;
    }

    #print-area{
      display: block;
      background-color: white !important;
    }

    body{
      margin: 0;
      padding: 0;
      box-shadow: none;
    }
  }

  @page{
    size: auto;
    margin: 0mm;
  }

  

  .rsw-ce ul {
    list-style: disc;
    padding-left: 2em;
  }
  
  .rsw-ce ol {
    list-style: decimal;
    padding-left: 2em;
  }

  ol, ul, menu {
    list-style: disc;
    margin: 0;
    padding: 0;
}
  @layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 0 0% 3.9%;

      --card: 0 0% 100%;
      --card-foreground: 0 0% 3.9%;

      --popover: 0 0% 100%;
      --popover-foreground: 0 0% 3.9%;

      --primary: 0 0% 9%;
      --primary-foreground: 0 0% 98%;

      --secondary: 0 0% 96.1%;
      --secondary-foreground: 0 0% 9%;

      --muted: 0 0% 96.1%;
      --muted-foreground: 0 0% 45.1%;

      --accent: 0 0% 96.1%;
      --accent-foreground: 0 0% 9%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;

      --border: 0 0% 89.8%;
      --input: 0 0% 89.8%;
      --ring: 0 0% 3.9%;

      --radius: 0.5rem;
    }

  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
  }