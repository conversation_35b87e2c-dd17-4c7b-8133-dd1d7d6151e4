import { notFound } from 'next/navigation'
import { Chat } from '../../_components/chat'
import { getSharedChat } from '@/ai/morphic/actions/chat'
import { convertToUIMessages } from '@/ai/morphic/utils'
import { getModels } from '@/ai/morphic/config/models'

export interface SharePageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata(props: SharePageProps) {
  const { id } = await props.params;
  const chat = await getSharedChat(id)

  if (!chat || !chat.sharePath) {
    return notFound()
  }

  return {
    title: chat?.title.toString().slice(0, 50) || 'Search'
  }
}

export default async function SharePage(props: SharePageProps) {
  const {id } = await props.params;
  const chat = await getSharedChat(id)

  if (!chat || !chat.sharePath) {
    notFound()
  }

  const models = await getModels()
  return (
    <Chat 
      id={id} 
      savedMessages={convertToUIMessages(chat.messages)}
      models={models}
    />
  )
}
