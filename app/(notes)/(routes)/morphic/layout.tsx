import Header from '@/components/morphic/header'
import { Sidebar } from '@/components/morphic/sidebar'
import { AppStateProvider } from '@/components/providers/AppStateProvider'
const Layout = ({
  children
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <AppStateProvider>
      <Header />
        {children}
      <Sidebar />
      </AppStateProvider>
    </div>
  );
}

export default Layout;