import { notFound, redirect } from 'next/navigation'
import { auth } from '@clerk/nextjs/server'
import { Chat } from '../../_components/chat'
import { getChat } from '@/ai/morphic/actions/chat'
import { convertToUIMessages } from '@/ai/morphic/utils'
import { getModels } from '@/ai/morphic/config/models'

export const maxDuration = 60

export interface SearchPageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata(props: SearchPageProps) {
  const { id } = await props.params
  const chat = await getChat(id, 'anonymous')
  return {
    title: chat?.title.toString().slice(0, 50) || 'Search'
  }
}

export default async function SearchPage(props: SearchPageProps) {
  const { id } = await props.params;
  const { userId } = await auth();
  if (!userId) {
    return redirect('/sign-in');
  }
  const chat = await getChat(id, userId)
  // convertToUIMessages for useChat hook
  const messages = convertToUIMessages(chat?.messages || [])

  if (!chat) {
    redirect('/morphic')
  }

  if (chat?.userId !== userId) {
    notFound()
  }

  const models = await getModels()
  return <Chat id={id} savedMessages={messages} models={models} />
}
