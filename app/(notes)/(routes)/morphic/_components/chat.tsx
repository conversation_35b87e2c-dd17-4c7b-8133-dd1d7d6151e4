'use client'

import { CHAT_ID } from '@/constants'
import { Model } from '@/ai/morphic/types/models'
import { Message, useChat, DefaultChatTransport } from '@ai-sdk/react';
//import { useChat } from '@ai-sdk/react'
import { useEffect, useState } from 'react';
import { toast } from 'sonner'
import { ChatMessages } from '@/components/morphic/chat-messages'
import { ChatPanel } from '@/components/morphic/chat-panel'

export function Chat({
  id,
  savedMessages = [],
  query,
  models
}: {
  id: string
  savedMessages?: Message[]
  query?: string
  models?: Model[]
}) {
  const [input, setInput] = useState('');
  const {
    messages,
    handleSubmit,
    isLoading,
    setMessages,
    stop,
    append,
    data,
    setData
  } = useChat({
    id: CHAT_ID,
    initialMessages: savedMessages,

    body: {
      id
    },

    onFinish: () => {
      window.history.replaceState({}, '', `/morphic/search/${id}`)
    },

    onError: error => {
      toast.error(`Error in chat: ${error.message}`)
    },

    transport: new DefaultChatTransport({
      api: `/api/morphic`
    })
  })

  useEffect(() => {
    setMessages(savedMessages)
  }, [id])

  const onQuerySelect = (query: string) => {
    append({
      role: 'user',
      content: query
    })
  }

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setData(undefined) // reset data to clear tool call
    handleSubmit(e)
  }

  return (
    <div className="flex flex-col h-screen w-full max-w-3xl pt-4 pb-20 mx-auto stretch overflow-auto">
      <ChatMessages
        messages={messages}
        data={data}
        onQuerySelect={onQuerySelect}
        isLoading={isLoading}
        chatId={id}
      />
      <ChatPanel
        input={input}
        e => setInput(e.target.value)={e => setInput(e.target.value)}
        handleSubmit={onSubmit}
        isLoading={isLoading}
        messages={messages}
        setMessages={setMessages}
        stop={stop}
        query={query}
        append={append}
        models={models}
      />
    </div>
  );
}