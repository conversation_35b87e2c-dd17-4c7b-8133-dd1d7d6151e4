import '@/app/scrollbar-hidden.css'
import { redirect } from 'next/navigation'
import { auth } from "@clerk/nextjs/server";
import { isAdminAllowed } from '@/lib/authUtils';
import { Suspense } from 'react'
import Loading from '@/app/loading'
import { Navbar } from "@/components/navbar";
import { Sidebar } from "@/components/sidebar";
import { checkSubscription } from "@/lib/subscription";
//import { getApiLimitCount } from "@/lib/api-limit";
//import { getCreditBalanceCount } from "@/lib/credit-balance"
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { SupabaseFileProvider } from '@/components/providers/SupabaseFileProvider';



async function getSupabaseFiles() {
  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  const { data, error } = await client.from("documents_1024").select();
  return data ?? [];
}

const RootLayout = async ({
  children
}: {
  children: React.ReactNode;
}) => {
  //const creditBalanceCount = await getCreditBalanceCount();
  //const apiLimitCount = await getApiLimitCount();
  const isPro = await checkSubscription();
  // Fetch files at the layout level
  const { userId } = await auth()
  if (!userId) {
    return redirect("/sign-in");
  }
  if (userId && typeof userId === 'string' && !(await isAdminAllowed(userId))) {
    return redirect('/sign-in');
  }
  const supabaseFiles = await getSupabaseFiles();

  return (
    <div className="h-screen overflow-hidden">
      <Navbar isPro={isPro} />
      <div className="hidden md:flex mt-16 h-full w-20 flex-col fixed inset-y-0">
        <Sidebar isPro={isPro} />
      </div>
      <main className="md:pl-20 pt-16 h-full">
        <SupabaseFileProvider userId={userId} initialFiles={supabaseFiles}>{children}</SupabaseFileProvider>
      </main>        
    </div>       
   );
}
 
export default RootLayout;