import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server"

import prismadb from "@/lib/prismadb";
import { checkSubscription } from "@/lib/subscription";

import { CompanionForm } from "./_components/companion-form";

//export const runtime = 'edge'
export const preferredRegion = ['hnd1']

interface CompanionIdPageProps {
  params: Promise<{
    companionId: string;
  }>;
};

const CompanionIdPage = async (props: CompanionIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const validSubscription = await checkSubscription();

  if (!validSubscription) {
    return redirect("/");
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.companionId,
      userId,
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  });

  let categoryIncludeFilter = {};
  if (userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow") {
    categoryIncludeFilter = {
      where: {
        name: {
          in: ["ChatAgent", "Enneagram"], // List of categories to include
        },
      },
    };
  }

  const categories = await prismadb.category.findMany({
    ...categoryIncludeFilter, // Apply the filter conditionally
  });

  return ( 
    <div className="h-full overflow-hidden">
       <CompanionForm initialData={companion} categories={categories} />
    </div>
  );
}
 
export default CompanionIdPage;
