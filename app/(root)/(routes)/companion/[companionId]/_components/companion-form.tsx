"use client";

import * as z from 'zod/v3';
import axios from "axios";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { Wand2 } from "lucide-react";
import { Category, Companion } from "@prisma/client";

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { ImageUpload } from "@/components/image-upload";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectValue, SelectTrigger } from "@/components/ui/select";
import { Slider } from '@/components/ui/slider'
import { useMutation } from '@apollo/client';
import { CREATE_CATEGORY } from '@/lib/graphql/mutations/mutations';

const PREAMBLE = `
`;

const SEED_CHAT = `
`;

const formSchema = z.object({
  name: z.string().min(1, {
    message: "Name is required.",
  }),
  description: z.string().min(1, {
    message: "Description is required.",
  }),
  minAge: z.number().min(0, {
    message: "Mini Age is required.",
  }),
  maxAge: z.number().min(17, {
    message: "Max Age is required.",
  }),
  introduction: z.string().min(1, {
    message: "Introduction is required."
  }),
  instructions: z.string().min(200, {
    message: "Instructions require at least 200 characters."
  }),
  seed: z.string().min(1, {
    message: "Seed is required."
  }),
  src: z.string().min(1, {
    message: "Image is required."
  }),
  categoryId: z.string().min(1, {
    message: "Category is required",
  }),
  assistantId: z.string().min(0, {
    message: "Assistant ID is required.",
  }),
});

interface CompanionFormProps {
  categories: Category[];
  initialData: Companion | null;
};

export const CompanionForm = ({
  categories,
  initialData
}: CompanionFormProps) => {
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData ? {
      ...initialData,
      assistantId: initialData.assistantId !== null ? initialData.assistantId : undefined,
    } : {
      name: "",
      description: "",
      minAge: 10,
      maxAge: 17,
      introduction: "",
      instructions: "",
      seed: "",
      src: "",
      categoryId: "",
      assistantId: "",
    }
  });

  const isLoading = form.formState.isSubmitting;

  const [insertCategory, { data, loading, error }] = useMutation(CREATE_CATEGORY)
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      if (initialData) {
        await axios.patch(`/api/companion/${initialData.id}`, values);
      } else {
        await axios.post("/api/companion", values);
      }
      /*const data = insertCategory(
        {
          variables: {
            name: "Todoss",
            routes: "todoss"
          }
        })*/

      toast({
        description: "Success.",
        duration: 3000,
      });

      router.refresh();
      router.push("/");
    } catch (error) {
      toast({
        variant: "destructive",
        description: "Something went wrong.",
        duration: 3000,
      });
    }
  };

  return ( 
    <div className="h-full p-4 space-y-2 max-w-3xl mx-auto overflow-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 pb-10">
          <div className="space-y-2 w-full col-span-2">
            <div>
              <h3 className="text-lg font-medium">General Information</h3>
              <p className="text-sm text-muted-foreground">
                General information about your Companion
              </p>
            </div>
            <Separator className="bg-primary/10" />
          </div>
          <FormField
            name="src"
            render={({ field }) => (
              <FormItem className="flex flex-col items-center justify-center space-y-4 col-span-2">
                <FormControl>
                  <ImageUpload disabled={isLoading} onChange={field.onChange} value={field.value} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              name="name"
              control={form.control}
              render={({ field }) => (
                <FormItem className="col-span-2 md:col-span-1">
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input disabled={isLoading} placeholder="" {...field} />
                  </FormControl>
                  <FormDescription>
                    This is how your AI Companion will be named.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="description"
              control={form.control}
              render={({ field }) => (
                <FormItem className="col-span-2 md:col-span-1">
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input disabled={isLoading} placeholder="" {...field} />
                  </FormControl>
                  <FormDescription>
                    Short description for your AI Companion
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="minAge"
              control={form.control}
              render={({ field }) => (
                <FormItem className="col-span-2 md:col-span-1">
                  <FormLabel>Mini Age: {[field.value as number]}</FormLabel>
                  <FormControl>
                    <Slider
                      min={10}
                      max={39}
                      value={[field.value as number]}
                      defaultValue={[field.value as number]}
                      disabled={isLoading}
                      id="minAge"
                      onValueChange={(value: number[]) => field.onChange(value[0])}
                      className="bg-background resize-none"
                    />
                  </FormControl>
                  <FormDescription>
                    The minimum age for your target users.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="maxAge"
              control={form.control}
              render={({ field }) => (
                <FormItem className="col-span-2 md:col-span-1">
                  <FormLabel>Max Age: {[field.value as number]}</FormLabel>
                  <FormControl>
                    <Slider
                      min={17}
                      max={101}
                      value={[field.value as number]}
                      defaultValue={[field.value as number]}
                      disabled={isLoading}
                      id="maxAge"
                      onValueChange={(value: number[]) => field.onChange(value[0])}
                      className="bg-background resize-none"
                    />
                  </FormControl>
                  <FormDescription>
                    The maximum age for your target users.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="introduction"
              control={form.control}
              render={({ field }) => (
                <FormItem className="col-span-2 md:col-span-1">
                  <FormLabel>Introduction</FormLabel>
                  <FormControl>
                    <Textarea disabled={isLoading} rows={5} className="bg-background resize-none" placeholder={PREAMBLE} {...field} />
                  </FormControl>
                  <FormDescription>
                    Describe in detail your companion&apos;s backstory and relevant details.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-0 w-full">
              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem className="col-span-2 md:col-span-1">
                    <FormLabel>Category</FormLabel>
                    <Select disabled={isLoading} onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-background">
                          <SelectValue defaultValue={field.value} placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select a category for your AI Companion
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {form.watch('categoryId') &&
                categories.find(category => category.id === form.watch('categoryId'))?.name === 'Assistant' && (
                <FormField
                  name="assistantId"
                  control={form.control}
                  rules={form.watch('categoryId') === 'Assistant' ? { required: 'Assistant ID is required' } : {}}
                  render={({ field }) => (
                    <FormItem className="col-span-2 md:col-span-1">
                      <FormLabel>Assistant ID</FormLabel>
                      <FormControl>
                        <Input disabled={isLoading} placeholder="" {...field} />
                      </FormControl>
                      <FormDescription>
                        Assistant Agent ID from OpenAI
                      </FormDescription>
                      {(form.formState.errors && form.formState.errors.assistantId && form.formState.errors.assistantId.message) && (
                        <FormMessage>{form.formState.errors.assistantId.message}</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              )}
            </div>
          </div>
          <div className="space-y-2 w-full">
            <div>
              <h3 className="text-lg font-medium">Configuration</h3>
              <p className="text-sm text-muted-foreground">
                Detailed instructions for AI Behaviour
              </p>
            </div>
            <Separator className="bg-primary/10" />
          </div>
          <FormField
            name="instructions"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Instructions</FormLabel>
                <FormControl>
                  <Textarea disabled={isLoading} rows={7} className="bg-background resize-none" placeholder={PREAMBLE} {...field} />
                </FormControl>
                <FormDescription>
                  Describe in detail your companion&apos;s instructions.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="seed"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Actions or Example Conversation</FormLabel>
                <FormControl>
                  <Textarea disabled={isLoading} rows={7} className="bg-background resize-none" placeholder={SEED_CHAT} {...field} />
                </FormControl>
                <FormDescription>
                  Write couple of examples of a human chatting with your AI companion, write expected answers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="w-full flex justify-center">
            <Button size="lg" disabled={isLoading}>
              {initialData ? "Update your companion" : "Create your companion"}
              <Wand2 className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
   );
};
