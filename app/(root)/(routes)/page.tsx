import { Suspense } from 'react'
import Loading from '@/app/loading'
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server"
import prismadb from "@/lib/prismadb"
import { Persona } from "@prisma/client";
import { Categories } from "@/components/categories"
import { Companions } from "@/components/companions"
import { SearchInput } from "@/components/search-input"

import { isUserAllowed } from '@/lib/authUtils';

//export const runtime = 'edge'
export const preferredRegion = ['sfo1']
//export const experimental_ppr = true

interface RootPageProps {
  searchParams: Promise<{
    categoryId: string;
    name: string;
  }>;
};

const RootPage = async (props: RootPageProps) => {
  const searchParams = await props.searchParams;
  const { userId } = await auth();

  if (!userId) {
    return redirect('/sign-in');
  }

  if (userId && typeof userId === 'string' && !(await isUserAllowed(userId))) {
    return redirect('/activation');
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  });

  const userAge = foundPersona?.age ?? 0;

  let categoryIncludeFilter = {};
  if (userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow") {
    categoryIncludeFilter = {
      name: {
        in: ["ChatAgent", "Enneagram"],
      },
    };
  }

  let categoryExcludeFilter = {}
  if (userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow") {
    categoryExcludeFilter = {
      name: {
        notIn: ["Test", "Musicians", "Vision", "Painting", "Sketches", "GroupTalk", "VideoChat", "Interactive", "ChatLife"],
      },
    };
  }
  const data = await prismadb.companion.findMany({
    where: {
      categoryId: searchParams.categoryId,
      name: {
        search: searchParams.name,
      },
      category: categoryIncludeFilter
    },
    cacheStrategy: {
      swr: 60,
      ttl: 30
    },
    orderBy: {
      maxAge: "desc"
    },
    include: {
      _count: {
        select: {
          messages: true,
        }
      },
      category: true,
    },
  });

  const filteredCompanions = data.filter((companion) => {
    if (userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow" && companion.minAge && companion.maxAge) {
      return userAge >= companion.minAge && userAge <= companion.maxAge;
    }
    // If minAge and maxAge are not defined for a companion, include it in the result
    return true;
  });

  // Extract the category from the data (if there is at least one item)
  const category = data.length > 0 ? data[0].category : null;

  const categories = await prismadb.category.findMany({
    where: {
      ...categoryIncludeFilter, // Apply the filter conditionally
    }
  });

  //if (foundPersona && foundPersona.isVisited === false || null)
  //  return redirect("/intake/c98b5fe0-e1c9-4c02-8d84-01cb1d0092e3");

  return (
    <div className="h-full p-4 space-y-2 overflow-hidden">
      <SearchInput />
      <Categories data={categories} />
      <div className="h-full overflow-auto">
        <Suspense fallback={<Loading />}>
          <Companions data={filteredCompanions} />
        </Suspense>
      </div>         
    </div>
  )
}

export default RootPage
