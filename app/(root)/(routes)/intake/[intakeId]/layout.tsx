import { SidebarHeader } from '@/components/talk/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface IntakeLayoutProps {
  params: Promise<{ 
    intakeId: string
  }>
}

const IntakeLayout = async (
  props: IntakeLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/intake/${params.intakeId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.intakeId} path={path} />
      {children}
    </div>
  );
}

export default IntakeLayout;