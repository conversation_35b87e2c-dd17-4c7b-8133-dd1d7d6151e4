import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { IntakeClient } from "../_components/client";
import { getCompanionForIntake, getThread } from "@/app/actions/threadActions"
import { getNotes } from "@/app/actions/noteActions"

//export const runtime = 'edge'

export const preferredRegion = ['sfo1']

type ThreadInfo = { id: string; name: string } | null;

export interface IntakePageProps {
  params: Promise<{
    threadId: string
    intakeId: string
  }>
}


export default async function IntakePage(props: IntakePageProps) {
  const params = await props.params;
  const { userId } = await auth()

  if (!userId) {
    redirect(`/sign-in`)
  }

  const existingThread: ThreadInfo = await getThread(
    userId, params.intakeId, params.threadId
  );

  const companion = await getCompanionForIntake(userId, params.intakeId, params.threadId)
  console.log("CompanionForIntake@app/Intake: ", companion)

  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  return <IntakeClient threadId={params.threadId} companion={companion} foundPersona={foundPersona} existingThread={existingThread} notes={notes} />
}