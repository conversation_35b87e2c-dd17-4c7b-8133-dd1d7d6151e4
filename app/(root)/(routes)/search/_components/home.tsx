"use client";

import { Message } from './client'
import React, { useContext, useEffect, useState } from "react";
import { useMediaQuery } from "@/hooks/use-media-query"
import { SidebarHeader } from '@/components/search/layout/sidebar-header'
import { SearchClient } from "./client";
import { Note } from '@prisma/client'
import { LoadingPage } from "@/components/ui/loading";


interface HomeProps {
  threadId: string
  existingThreadId: string
  threads: {id: string, name: string, user_id: string, created: string}[]
  notes: Note[]
  searchHistories?: Message[]
}
interface ScreenProps extends HomeProps {
  path: string;
}

const useHasHydrated = () => {
  const [hasHydrated, setHasHydrated] = useState<boolean>(false);

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  return hasHydrated;
};

const SidebarContext = React.createContext<{
  showSidebar: boolean;
  setShowSidebar: (show: boolean) => void;
} | null>(null);

function SidebarContextProvider(props: { children: React.ReactNode }) {
  const [showSidebar, setShowSidebar] = useState(false);
  return (
    <SidebarContext.Provider value={{ showSidebar, setShowSidebar }}>
      {props.children}
    </SidebarContext.Provider>
  );
}

export const useSidebarContext = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error(
      "useSidebarContext must be used within an SidebarContextProvider",
    );
  }
  return context;
};

function Screen({ 
  threadId, 
  existingThreadId, 
  threads,
  notes, 
  searchHistories,
  path,
}: ScreenProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const { showSidebar, setShowSidebar } = useSidebarContext();

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const showSidebarOnMobile = showSidebar || isDesktop && showSidebar;

  return (
    <div className="flex overflow-hidden h-[90vh] w-full px-2">
      <>
        {showSidebarOnMobile && <SidebarHeader threads={threads} path={path} />}
        <button 
          onClick={toggleSidebar}
          className="inset-y-0 z-10 my-auto *:transition-transform group flex h-[85vh] w-2 flex-col items-center justify-start -space-y-1 outline-none *:h-3 *:w-1 *:rounded-full *:hover:bg-gray-600 max-md:hidden dark:*:hover:bg-gray-300 *:bg-gray-800 dark:*:bg-gray-200">
          {isDesktop && (showSidebar ? (
            <>
              <div className="group-hover:rotate-[20deg]"></div> 
              <div className="group-hover:-rotate-[20deg]"></div>
            </>
          ) : (
            <>
              <div className="group-hover:-rotate-[20deg]"></div>
              <div className="group-hover:rotate-[20deg]"></div>
            </>
          ))}
        </button>
        <div className="flex w-full justify-center overflow-hidden">
          <SearchClient 
            threadId={threadId} 
            existingThreadId={existingThreadId} 
            notes={notes} 
            searchHistories={searchHistories} 
          />
        </div>
      </>
    </div>
  );
}


export function Home({
  threadId, 
  existingThreadId,
  threads, 
  notes, 
  searchHistories,
}: HomeProps) {
  const path = `/search`
  return (
    <SidebarContextProvider>
      <Screen 
        threadId={threadId} 
        existingThreadId={existingThreadId} 
        threads={threads}
        notes={notes} 
        searchHistories={searchHistories}
        path={path}
      />
    </SidebarContextProvider>
  );
}
