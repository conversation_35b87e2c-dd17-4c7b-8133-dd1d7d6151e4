import { redirect } from 'next/navigation'
import { auth, currentUser } from '@clerk/nextjs/server'
import { Suspense } from 'react'
import Loading from '@/app/loading'
import prismadb from "@/lib/prismadb";
import { createClerkSupabaseServerClient } from '@/lib/clerkSupabaseServer';
import { getThreads } from '@/app/actions/supabaseActions'
import { getNotes } from "@/app/actions/noteActions"
import { Home } from "../_components/home";
import { SearchClient } from "../_components/client";
import { Message } from "../_components/client";

export const revalidate = 0
export const preferredRegion = ['sfo1']

export interface SearchIdPageProps {
  params: Promise<{
    searchId: string
  }>
}

interface Thread {
  id: string;
  name: string;
  user_id: string;
  created_at: string;
}



export default async function SearchIdPage(props: SearchIdPageProps) {
  const params = await props.params;
  const user = await currentUser();
  const userId = user?.id!

  if (!userId) {
    redirect(`/sign-in`)
  }

  const { getToken } = await auth();
  const token = await getToken({ template: "supabase" });
  const authToken = token ? { Authorization: `Bearer ${token}` } : null;
  const client = await createClerkSupabaseServerClient(authToken);
  let searchHistories: Message[] = []
  let existingThreadId: string = ""
  try {
    const { data: threadData, error: threadError } = await client
      .from("thread")
      .select("*")
      .eq('user_id', userId)
      .eq('id', params.searchId)
      //.order('created_at', { ascending: false })
      //.limit(1);
  
      //console.log('threadData:', threadData);
    if (threadError) {
      console.error('Error fetching thread data:', threadError);
      return null;
    }
  
    if (threadData && threadData.length > 0) {
      existingThreadId = threadData[0].id;
  
      // Fetch messages for the existingThreadId
      const { data: messagesData, error: messagesError } = await client
        .from("messages")
        .select(
          `
          *,
          images:images(*),
          videos:videos(*),
          searchResults:search_results(*)
          `
        )
        .eq('owner_id', userId)
        .eq('thread_id', existingThreadId);
  
      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        throw new Error('Failed to fetch messages');
      }
  
      searchHistories = messagesData ?? null;
      //console.log("searchHistories: ", searchHistories);
  
    }
  } catch (error) {
    console.error('Error:', error);
  }
  const threads = (await getThreads(authToken)) ?? []
  const notes =  (await getNotes(userId)) ?? []

  return (
    <Suspense fallback={<Loading />}>
      <Home 
        threadId={params.searchId} 
        existingThreadId={existingThreadId} 
        threads={threads} 
        notes={notes}
      />
    </Suspense>
  )
}