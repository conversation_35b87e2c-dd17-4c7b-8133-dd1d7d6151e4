import { auth } from "@clerk/nextjs/server";
import { Suspense } from 'react'
import prismadb from '@/lib/prismadb';
import Loading from '@/app/loading';
import getOrganizations from "@/app/actions/getOrganizations";
//import { getApiLimitCount } from "@/lib/api-limit";
//import { getCreditBalanceCount } from "@/lib/credit-balance";
import { CreditCounter } from "@/components/credits/CreditCounter";
import { CreditBalance } from '@/components/credits/CreditBalance';
//import { checkSubscription } from "@/lib/subscription";

export type Transaction = {
  apiPurchase: number;
  apiExpense: number;
  amountPurchase: number;
  amountExpense: number;
  promptTokens: number;
  completionTokens: number;
}
export type Total = {
  totalApiPurchase: number;
  totalApiExpense: number;
  totalAmountPurchase: number;
  totalAmountExpense: number;
}

export interface InitialTransactions {
  transactions: Transaction[];
  totals: Total;
}

export default async function Page() {
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  try {
    /*// Get the current date and time
    const currentDate = new Date();

    // Extract the year and month from the current date
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // Month is zero-based

    // Construct the start date (first day of the current month)
    const startDate = new Date(currentYear, currentMonth - 1, 1);

    // Calculate the end date (last day of the current month)
    const endDate = new Date(currentYear, currentMonth, 0);

    // Defining the type for the accumulatedCountsByDate object
    interface AccumulatedCountsByDate {
      [key: string]: Transaction;
    }

    // Fetch transactions from the database within the current month range
    const transactions = await prismadb.transaction.findMany({
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Initialize an empty object to store accumulated counts by date
    const accumulatedCountsByDate: AccumulatedCountsByDate = {};

    // Initialize variables for accumulated totals
    let totalApiPurchase = 0;
    let totalApiExpense = 0;
    let totalAmountPurchase = 0;
    let totalAmountExpense = 0;

    // Iterate through transactions to calculate accumulated counts by date
    transactions.forEach((transaction) => {
      const { count, amount, promptTokens, completionTokens } = transaction;
      const transactionDate = transaction.createdAt.toLocaleDateString(undefined, { month: 'short', day: '2-digit', });

      // Initialize the accumulators for the current date if not already present
      if (!accumulatedCountsByDate[transactionDate]) {
        accumulatedCountsByDate[transactionDate] = {
          apiPurchase: 0,
          apiExpense: 0,
          amountPurchase: 0,
          amountExpense: 0,
          promptTokens: 0,
          completionTokens: 0,
        };
      }

      // Accumulate apiExpense
      if (count > 0) {
        accumulatedCountsByDate[transactionDate].apiPurchase += count;
        totalApiPurchase += count; // Accumulate the total apiPurchase
      } else {
        accumulatedCountsByDate[transactionDate].apiExpense += Math.abs(count);
        totalApiExpense += Math.abs(count); // Accumulate the total apiExpense
      }

      // Accumulate amount
      if (amount > 0) {
        accumulatedCountsByDate[transactionDate].amountPurchase += amount;
        totalAmountPurchase += Math.abs(amount); // Accumulate the total amountPurchase
      } else {
        accumulatedCountsByDate[transactionDate].amountExpense += Math.abs(amount);
        totalAmountExpense += Math.abs(amount); // Accumulate the total amountExpense
      }

      // Accumulate promptTokens and completionTokens
      accumulatedCountsByDate[transactionDate].promptTokens += promptTokens;
      accumulatedCountsByDate[transactionDate].completionTokens += completionTokens;

      // Accumulate promptTokens and completionTokens
      accumulatedCountsByDate[transactionDate].promptTokens += promptTokens;
      accumulatedCountsByDate[transactionDate].completionTokens += completionTokens;
    });

    // Convert the accumulated counts object to an array of objects with date and accumulated counts
    const data = Object.entries(accumulatedCountsByDate).map(([date, counts]) => ({
      date,
      ...counts,
    }));

    // Create an object with the total values
    const totals = {
      totalApiPurchase,
      totalApiExpense,
      totalAmountPurchase,
      totalAmountExpense,
    };

    const initialTransactions = {
      transactions: data,
      totals,
    }*/

    const admin = await prismadb.admin.findUnique({
      where: {
        id: userId,
      },
      cacheStrategy: {
        swr: 60,
        ttl: 30
      },
      include: {
       adminOrganizations: true, // Include the organization in the result
      },
    });

    if (admin) {
      // Render for admin users
      const organizations = await getOrganizations({ adminId: admin.id });
      return (
        <div className="flex justify-center h-full items-start gap-x-3 pt-1 px-4 pb-4 2xl:p-8">
          <Suspense fallback={<Loading />}>
            <CreditBalance 
              organizations={organizations}
            />
          </Suspense>
        </div>
      );
    } else {
      //const apiLimitCount = await getApiLimitCount();
      //const creditBalanceCount = await getCreditBalanceCount();
      //const isPro = await checkSubscription();
      // Render for non-admin users
      return (
        <div className="flex justify-center h-full items-start gap-x-3 pt-1 px-4 pb-4 2xl:p-8">
          <Suspense fallback={<Loading />}>
            <CreditCounter />
          </Suspense>
        </div>
      );
    }
  } catch (error) {
    console.error('Error fetching members:', error);
    return <div>Error occurred while fetching members.</div>;
  }
}