import prismadb from '@/lib/prismadb';
import { auth } from "@clerk/nextjs/server";
import { CreditBalance } from '@/components/credits/CreditBalance'
import { TransferBalance } from '@/components/credits/TransferBalance'

export default async function Page() {
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  try {
    const admin = await prismadb.admin.findFirst({
      where: {
        user: {
          externalId: userId,
        },
        organization: {
          id: {
            not: undefined, // Check if the id is defined
          },
        },
      },
      select: {
        organization: {
          select: {
            id: true,
            members: true,
          },
        },
      },
    });

    if (!admin) {
      // Handle case where the user is not an admin
      return <div>Access denied. You must be an admin to access this page.</div>;
    }

    const members = admin.organization.members;

    return (
      <main>
        <CreditBalance />
        <TransferBalance users={members ?? []} />
      </main>
    )
  } catch (error) {
    console.error('Error fetching members:', error);
    return <div>Error occurred while fetching members.</div>;
  }
}