import { Suspense } from 'react'
import Loading from '@/app/loading'
import getAdmin from "@/app/actions/getAdmin";
import getCurrentUser from "@/app/actions/getCurrentUser";
import getCurrentProfile from "@/app/actions/getCurrentProfile";
import getOrganizations from "@/app/actions/getOrganizations";
import getUsers from "@/app/actions/getUsers";
//import getProfiles from "@/app/actions/getProfiles";
import { CreateOrganization } from "@/components/organization/create-org";
import { ProfileButton } from "@/components/profile-button";
//import { SubscriptionButton } from "@/components/subscription-button";
//import { checkSubscription } from "@/lib/subscription";
import { Admin, Profile } from '@prisma/client';
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { Link } from '@/components/credits/CustomLink'
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button";
import prismadb from "@/lib/prismadb";
import { <PERSON><PERSON><PERSON> } from "lucide-react"
import { RAGFileUploader } from '@/components/storage/RAGFileUploader'
import { SupabaseDocument } from '@/components/storage/SupabaseAtomDocument'

export const experimental_ppr = true

const SettingsPage = async () => {
  const currentUser = await getCurrentUser();
  const currentProfile = await getCurrentProfile();
  const users = await getUsers();
  //const profiles = await getProfiles();
  //const isPro = await checkSubscription();
  const path = "/settings"

  // Fetch the organization associated with the current user
  let admin: Admin | null = null;
  let organizations: OrganizationWithMembersAndUsers[] | null = null;
  /*if (currentProfile && 'admin' in currentProfile) {
    // Assuming the user's profile is associated with an admin
    admin = (currentProfile as Profile & { admin: Admin }).admin;
    if (admin) {
      organization = await getOrganization({ orgId: admin.orgId });
    }
  }*/

  if (currentProfile) {
    // Fetch the Admin record associated with the current user's profile
    admin = await getAdmin({ id: currentProfile.externalId });
   
    if (admin) {
       // If an Admin record is found, the user is an Admin
       // The organization associated with this admin is already included in the admin record
       organizations = await getOrganizations({ adminId: admin.id });
       //console.log("organizations", organizations)
    }
  }

  return (
    <div className="w-auto min-h-screen mx-2">
      <div className="grid lg:grid-cols-2 gap-y-4 p-4 md:gap-y-8">
        <div>
          {admin && (
            <>
              <h2 className="font-semibold text-xl">Organization</h2>
              <p className="text-muted-foreground text-sm">
                Manage your organization
              </p>
              <div className="flex flex-row text-muted-foreground text-sm space-x-2 py-4">
                <Suspense fallback={<Loading />}>
                  <CreateOrganization admin={admin} users={users} organizations={organizations || null} path={path} />
                </Suspense>
              </div>
              <Separator className="my-4" />            
            </>
          )}
          <h2 className="font-semibold text-xl">Profile</h2>
          <p className="text-muted-foreground text-sm">
            Update your personal information
          </p>
          <div className="flex flex-row text-muted-foreground text-sm space-x-2 py-4">
            <Suspense fallback={<Loading />}>
              <ProfileButton currentUser={currentUser!} path={path} />
            </Suspense>
          </div>
          <Separator className="my-4" />
          <h2 className="font-semibold text-xl">Usage</h2>
          <p className="text-muted-foreground text-sm">
            Mangage your credit usage
          </p>
          {/*
          <div className="text-muted-foreground text-sm">
            <Suspense fallback={<Loading />}>
              {isPro ? "You are currently on a Pro plan." : "You are currently on a free plan."}
            </Suspense>
          </div>
          */}
          <div className="flex flex-row space-x-2 py-4">
            <Suspense fallback={<Loading />}>
              {/*<SubscriptionButton isPro={isPro} />*/}
              <Link href="/settings/credits">
                <Button size="sm" variant={"premium"}>
                <PieChart className="mr-2 h-4 w-4" />
                  Credit Usage
                </Button>
              </Link>
            </Suspense>
          </div>
          
        </div>
        <div>
          <h2 className="font-semibold text-xl">Knowledge Store</h2>
          <p className="text-muted-foreground text-sm">
            Mangage your external knowledge
          </p>
          <div className="flex relative w-fit h-fit rounded-full bg-gradient-to-r from-sky-500 via-blue-500 to-cyan-500 text-white border-0 hover:bg-gradient-to-r from-sky-500 via-blue-500 to-cyan-500/90 drop-shadow-lg shadow-inner shadow-lg p-4 my-4 mx-auto">
            <RAGFileUploader method={["all"]} path={path} mode={"full"} /> 
          </div>
          <SupabaseDocument path={path} />
        </div>
      </div>
    </div> 

  );
}
 
export default SettingsPage;
