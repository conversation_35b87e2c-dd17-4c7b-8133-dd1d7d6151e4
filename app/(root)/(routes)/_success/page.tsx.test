import { AlertBox } from '@/components/credits/AlertBox'
import { redirect } from 'next/navigation'
import { addCreditBalance } from '@/app/actions/creditsActions'

export default async function Page({
  searchParams,
}: {
  searchParams?: { [key: string]: string | string[] | undefined }
}) {
  const sessionId = searchParams?.session_id as string

  if (!sessionId) {
    return <AlertBox>Session id missing.</AlertBox>
  }

  const path = '/settings/credits'
  const creditBalance = await addCreditBalance({
    sessionId, 
    secret: process.env.APP_SECRET_KEY!,
    path
  })

  if (creditBalance.id) {
    redirect(path)
  }

  return null
}