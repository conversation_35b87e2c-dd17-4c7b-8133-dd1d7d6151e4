import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { nanoid } from '@/lib/utils'

//export const runtime = 'edge'

const id = nanoid()

export const preferredRegion = ['sfo1']

export interface ChatPageProps {
  params: Promise<{
    chatId: string
  }>
}

export default async function ChatPage(props: ChatPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
      return redirectToSignIn();
    }

  if (id) {
    redirect(`/chat/${params.chatId}/${id}`)
  } else {
    redirect(`/`)
  }
}
