import '@/app/scrollbar-hidden.css'
import { SidebarHeader } from '@/components/chat/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface ChatLayoutProps {
  params:  Promise<{
    chatId: string
  }>
}

const ChatLayout = async (
  props: ChatLayoutProps & {
    children: React.ReactNode;
  }
) => {
  const params = await props.params;

  const {
    children
  } = props;

  const path = `/chat/${params.chatId}`

  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.chatId} path={path} />
      {children}
    </div>
  );
}

export default ChatLayout;