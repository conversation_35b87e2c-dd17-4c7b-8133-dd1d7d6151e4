import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { nanoid } from '@/lib/utils'
import { getChat } from '@/app/actions'
import { Chat } from '@/components/chat'

//export const runtime = 'edge'

export const preferredRegion = ['sfo1']

export interface ChatPageProps {
  params: Promise<{
    id: string
    chatId: string
  }>
}

export async function generateMetadata(props: ChatPageProps): Promise<Metadata> {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
    return {}
  }

  const chat = await getChat(params.id, userId, params.chatId)

  return {
    title: chat?.title.toString().slice(0, 50) ?? 'Chat'
  }
}

export default async function ChatPage(props: ChatPageProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth()

  if (!userId) {
      return redirectToSignIn();
      // redirect(`/sign-in?next=/chat/${params.chatId}/${params.id}`)
    }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.chatId
    },
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      todos: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

  if (!companion) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  })

  const chat = await getChat(params.id, userId, params.chatId)

  /*if (!chat || chat.id !== params.id || chat.userId !== userId) {
    notFound()
  } else {
    return <Chat id={chat.id} initialMessages={chat.messages} companion={companion} foundPersona={foundPersona} />
  }*/

  return <Chat id={params.id} initialMessages={chat?.messages!} companion={companion} foundPersona={foundPersona} />
}