import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server"
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { nanoid } from '@/lib/utils'
import { getChat } from '@/app/actions'
import { Chat } from '@/components/chat'

//export const runtime = 'edge'

const id = nanoid()

export const preferredRegion = ['sfo1']

export interface ChatPageProps {
  params: {
    chatId: string
  }
}

export async function generateMetadata({
  params
}: ChatPageProps): Promise<Metadata> {
  const { userId } = await auth()  

  if (!userId) {
    return {}
  }

  const chat = await getChat(id, params.chatId, userId)

  return {
    title: chat?.title.toString().slice(0, 50) ?? 'Chat'
  }
}

export default async function ChatPage({ params }: ChatPageProps) {
    const { userId, redirectToSignIn } = await auth()

    if (!userId) {
        return redirectToSignIn();
      }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.chatId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      observations: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
        take: 2,
      },
      todos: {
        orderBy: {
          createdAt: "desc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });

  if (!companion) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  })

  const chat = await getChat(id, params.chatId, userId)
  console.log("chat@app/chat: ", chat)

  if (!chat || chat.companionId !== params.chatId || chat.userId !== userId) {
    return <Chat id={id} companion={companion} foundPersona ={foundPersona} />
  }

  return <Chat id={chat.id} initialMessages={chat.messages} companion={companion} foundPersona ={foundPersona} />
}
