import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { Analytics } from '@vercel/analytics/react';
import { Suspense } from 'react'
import Loading from '@/app/loading'

import { cn } from '@/lib/utils';
import { ClerkProvider } from "@clerk/nextjs";
import { ConvexClientProvider } from '@/components/providers/convex-client-provider'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { Toaster } from '@/components/ui/toaster';
import { SoToaster } from "@/components/ui/sonner";
import { ProModal } from '@/components/pro-modal';
import { PayPerUseModal } from '@/components/credits/PayPerUseModal';
import { SubscriptionProvider } from '@/components/providers/SubscriptionContext';
import { ApolloProviderWrapper } from '@/components/providers/ApolloProvider'
import { BackgroundProvider } from "@/components/providers/BackgroundContext";
//import { DeepgramContextProvider } from "@/components/providers/DeepgramContextProvider";
//import { MicrophoneContextProvider } from '@/components/providers/MicrophoneContextProvider'

import './globals.css'
const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!
const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ComfyMinds Lab',
  description: 'Your customized companion.',
  manifest: '/manifest.webmanifest',
  icons: { apple: '/icon-192x192.png' },
}

export const experimental_ppr = true

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-tw" suppressHydrationWarning>
      <link rel="manifest" href="/manifest.webmanifest" crossOrigin="use-credentials" />
      <head>
        <script src="/serviceWorkerRegister.js" defer></script>
      </head>
      <body className={cn("bg-secondary", inter.className)} style={{ background: 'var(--gradient)' }}>
        <Suspense fallback={<Loading />}>
          <ClerkProvider publishableKey={PUBLISHABLE_KEY} dynamic>
            <ConvexClientProvider>
              <ApolloProviderWrapper>
                <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
                  <BackgroundProvider>
                    <SubscriptionProvider>
                      <ProModal />
                      <PayPerUseModal />
                        {children}
                      <Analytics />
                      <Toaster />
                      <SoToaster richColors position="bottom-right" />
                    </SubscriptionProvider>
                  </BackgroundProvider>
                </ThemeProvider>
              </ApolloProviderWrapper>
            </ConvexClientProvider>
           </ClerkProvider>
        </Suspense>
      </body>
    </html>
  )
}
