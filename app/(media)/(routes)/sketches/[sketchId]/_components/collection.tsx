"use client";

import Image from "next/image";
import ImageModal from "@/components/modals/ImageModal";
import React, { useState } from 'react';
import { api } from "@/convex/_generated/api";
import { useQuery } from "convex/react";

export function Collection() {
  const sketches = useQuery(api.sketches.getSketches);  
  const sortedSketches = (sketches ?? []).sort((a, b) => {
    return b._creationTime - a._creationTime;
  });

  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  return (
    <div className="flex min-h-screen flex-col items-center p-4 pb-24">
      <h2></h2>
      <div className="grid grid-cols-4 gap-2">       
        {sortedSketches.map((sketch, index) => (
          <div key={sketch._id} className="relative aspect-square rounded-sm">
            <ImageModal src={sketch.result} isOpen={selectedImageIndex === index} onClose={() => setSelectedImageIndex(null)} />
              <Image
                onClick={() => setSelectedImageIndex(index)}
                src={sketch.result || ''}
                alt="image"
                className="
                  object-cover 
                  cursor-pointer 
                  hover:scale-110 
                  transition 
                  translate
                "
              />
          </div>
        ))}
      </div>
    </div>
  );
}