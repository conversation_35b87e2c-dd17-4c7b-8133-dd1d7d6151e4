"use client";

import { Companion, Message, Media } from "@prisma/client";
import { api } from "@/convex/_generated/api";
import { useMutation, useQuery } from "convex/react";
import { useForm } from "react-hook-form";
import React, { RefObject, useState } from 'react';
import Image from "next/image";
import ImageModal from "@/components/modals/ImageModal";
import { ReactSketchCanvas, ReactSketchCanvasRef } from "react-sketch-canvas";
import { useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  <PERSON>do,
  <PERSON>o,
  <PERSON>ser,
  <PERSON>L<PERSON>,
  Send,
  <PERSON>fresh<PERSON><PERSON><PERSON>,
  RotateCw,
  RotateCcw,
} from "lucide-react";
import { Slider } from '@/components/ui/slider'
import { imagesType } from '@/lib/types'
import { IconSidebar } from '@/components/ui/icons'
import { Collection } from './collection'

interface SketchClientProps {
  companion: Companion & {
    messages: Message[];
    medias: Media[];
    _count: {
      messages: number;
    }
  };
};

export function SketchClient({
  companion,
}: SketchClientProps) {
  const saveSketchMutation = useMutation(api.sketches.saveSketch);
  const sketchesQuery = useQuery(api.sketches.getSketches);
  const [strokeSize, setStrokeSize] = useState<number>(4);
  const [eraserSize, setEraserSize] = useState<number>(5);
  const [size, setSize] = useState<number>(4)
  const [isEraserMode, setIsEraserMode] = useState<boolean>(false)
  const [prompt, setPrompt] = useState<string>('')


  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<{
    prompt: string;
  }>();  
  const canvasRef = useRef<ReactSketchCanvasRef | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const sortedSketches = (sketchesQuery ?? []).sort((a, b) => {
    return b._creationTime - a._creationTime;
  });

  const handleEraserClick = (
    isEraser: boolean,
    canvasRef: React.RefObject<ReactSketchCanvasRef | null>
  ) => () => {
    if (canvasRef.current) {
      canvasRef.current.eraseMode(isEraser);
      setIsEraserMode(isEraser);
      setSize(isEraser ? eraserSize : strokeSize);
    }
  };

  const handleSizeChange = (values: number[]) => {
    const numValue = values[0];
    setSize(numValue);
    if (isEraserMode) {
      setEraserSize(numValue);
    } else {
      setStrokeSize(numValue);
    }
  };
  
  return (
    <div className="flex min-h-screen flex-col items-center justify-between pt-4">
      <div className="container mx-auto flex gap-5">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" className="fixed h-9 w-9 right-2 px-0 py-2">
              <IconSidebar className="h-6 w-6" />
              <span className="sr-only">Toggle Sidebar</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="inset-y-0 flex h-auto w-[450px] flex-col p-0">
            <SheetHeader className="p-4">
              <SheetTitle className="text-sm">Collections</SheetTitle>
            </SheetHeader>
             <Collection />
          </SheetContent>
        </Sheet>
        <form
          className="flex flex-col gap-2 w-[40%] items-center"
          onSubmit={handleSubmit(async (formData) => {
            if (!canvasRef.current) return;
            const image = await canvasRef.current.exportImage("jpeg");
            await saveSketchMutation({ ...formData, image });
          })}
        >
          <Label htmlFor="prompt"></Label>
          <div className="flex items-center w-full">
            <Input
              id="prompt"
              className="h-[2.8rem]"
              placeholder="請在塗鴉板上繪製草圖，接著描述您希望它呈現的結果。"
              {...register("prompt", { required: true })}
            />
            <Button
              type="submit"
              className="btn btn-success text-white bg-primary/10 hover:bg-blue-800 font-medium rounded-lg text-sm p-3 px-5 focus:outline-none"
              disabled={!prompt}
            >
                <Send color="cyan" className="w-7 h-7" />
              </Button>
          </div>
          {errors.prompt && <span>This field is required</span>}

          <ReactSketchCanvas
            ref={canvasRef}
            className="w-full aspect-square border-none cursor-crosshair"
            style={{ width: 512, height: 512, borderRadius: "4px", borderColor: "#80DEEA", }}
            strokeWidth={strokeSize}
            strokeColor="black"
            eraserWidth={eraserSize}
            backgroundImage={
              "https://res.cloudinary.com/dyuei3zjr/image/upload/v1699592240/colorful-2174045_512_512_pf9ppc_djvv7t.png"              
            }            
          />
          <div className="flex justify-center mb-2">
            <div className='mt-0'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex cursor-pointer items-center text-white text-sm px-2 py-1"
                    type="button"
                    variant={"ghost"}
                    onClick={() => {
                      canvasRef.current?.undo();
                    }}
                  >
                    <Undo color="cyan" className="w-6 h-6" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Undo</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-0'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex cursor-pointer items-center text-white text-sm px-2 py-1"
                    type="button"
                    variant={"ghost"}
                    onClick={() => {
                      canvasRef.current?.redo();
                    }}
                  >
                    <Redo color="cyan" className="w-6 h-6" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Redo</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-0'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex cursor-pointer items-center text-white text-sm px-2 py-1"
                    type="button"
                    variant={"ghost"}
                    onClick={() => {
                      canvasRef.current?.clearCanvas();
                    }}
                  >
                    <RefreshCcw color="cyan" className="w-6 h-6" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Clear All</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-0'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex cursor-pointer items-center text-white text-sm px-2 py-1"
                    type="button"
                    variant={"ghost"}
                    onClick={handleEraserClick(true, canvasRef)}
                  >
                    <Eraser color="cyan" className="w-6 h-6" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Eraser</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-0'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="flex cursor-pointer items-center text-white text-sm px-2 py-1"
                    type="button"
                    variant={"ghost"}
                    onClick={handleEraserClick(false, canvasRef)}
                  >
                    <PenLine color="cyan" className="w-6 h-6" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Pen</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1 w-[6rem] flex items-center'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Slider
                    min={1}
                    max={30}
                    defaultValue={[size]}
                    id="slider"
                    onValueChange={(value) => handleSizeChange(value)}
                    className="h-[90%]"
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Adjust Size</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        </form>

        <section>
          <h2></h2>        
          <div className="grid md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-3 mx-[1rem]">            
             {sortedSketches.map((sketch, index) => (
                <div key={sketch._id} className="relative aspect-square rounded-sm">
                  <ImageModal src={sketch.result} isOpen={selectedImageIndex === index} onClose={() => setSelectedImageIndex(null)} />
                  <Image
                    onClick={() => setSelectedImageIndex(index)}
                    src={sketch.result || ''}
                    alt="image"
                    className="
                      object-cover 
                      cursor-pointer 
                      hover:scale-110 
                      transition 
                      translate
                      rounded-sm
                    "
                  />
                </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}