import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";

import prismadb from "@/lib/prismadb";
import { SketchClient } from "./_components/client";

//export const runtime = 'edge'

interface PhotoIdPageProps {
  params: Promise<{
    sketchId: string;
  }>
}

// Do not cache this page
export const revalidate = 0

const SketchIdPage = async (props: PhotoIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.sketchId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      medias: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });


  if (!companion) {
    return redirect("/");
  }


  return (
    <SketchClient companion={companion} />
  );
}
 
export default SketchIdPage;