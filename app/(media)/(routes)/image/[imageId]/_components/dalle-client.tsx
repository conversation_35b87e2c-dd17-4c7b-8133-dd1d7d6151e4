"use client";

import { useState } from "react";
import { Companion, Message, Media } from "@prisma/client";

import { ChatHeader } from "@/components/chat-header";
import Alert from '@/components/Alert';
import MaskImage from '@/components/MaskImage';
import Gallery from '@/components/gallery/index';

import { amountOptions, formSchema, resolutionOptions } from "./constants";


interface DalleClientProps {
    companion: Companion & {
      messages: Message[];
      medias: Media[];
      _count: {
        messages: number;
      }
    };
  };

export const DalleClient = ({
  companion,
}: DalleClientProps) => {
  const medias: Media[] = companion ? companion.medias : [];
  const [selectedMediaUrl, setSelectedMediaUrl] = useState<string | null>(null);
  const [alert, setAlert] = useState<{ isSet: boolean; message: string | null }>({ isSet: false, message: null });

  const handleSelectMedia = (mediaUrl: string) => {
    setSelectedMediaUrl(mediaUrl);
  };

  const showAlert = (isSet: boolean, message: string) => {
    setAlert({ isSet, message });
  
    setTimeout(() => {
      setAlert({ isSet: false, message: null });
    }, 8000);
  };


  return ( 
    <div className="flex max-w-screen max-h-screen overflow-hidden">
      {/* Left Panel */}
      <div className="flex flex-col h-[98vh] 2xl:h-screen w-full pt-3 pl-2">
        <ChatHeader companion={companion} />
        <div className="flex-grow overflow-y-hidden">
          <Alert alert={alert} />
          <MaskImage companion={companion} showAlert={showAlert} selectedMediaUrl={selectedMediaUrl} />
        </div>
      </div>

      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[98vh] 2xl:h-[98vh] w-[20%] md:w-[20%] lg:w-[25%] pt-3 2xl:pt-16 px-1 overflow-hidden">
        <div className="flex flex-col pr-4 justify-center">
          <Gallery companionId={companion.id} medias={medias ?? [] } onSelect={handleSelectMedia} />
        </div>
      </div>
    </div>
   );
}