'use client'

import { useState } from 'react';
import { HashLoader } from 'react-spinners'

interface PromptIdeaProps {
  onGeneratePrompts: (prompts: string[]) => void;
}

export function PromptIdea({ onGeneratePrompts }: PromptIdeaProps) {
  const [generatedPrompts, setGeneratedPrompts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleGeneratePrompts = async () => {
    setIsLoading(true);
    console.log("I have been clicked!");
    try {
      const promptsArray = await generatePrompts("pr2.txt", 20, 2);
      console.log("promptArray: ", promptsArray)
      setGeneratedPrompts(promptsArray);
      const textToCopy = promptsArray.join(', ');
      
      if (navigator.clipboard) {
        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            console.log('Text copied to clipboard successfully');
          })
          .catch((error) => {
            console.error('Failed to copy text to clipboard:', error);
          });
      } else {
        console.warn('Clipboard API not available');
      }
      
      onGeneratePrompts(promptsArray);
    } catch (error) {
      console.error('Error generating prompts:', error);
      // Handle error state if needed
    } finally {
      setIsLoading(false);
    }
  };  

  async function generatePrompts(prompts: string, num: number, artists: number): Promise<string[]> {
    return new Promise<string[]>((resolve, reject) => {
      let prompt: string[] = [];
      if (prompts === "pr1.txt" || prompts === "pr2.txt") {
        const filePath = `/${prompts}`;
        fetch(filePath)
          .then((response) => response.text())
          .then((text) => {
            prompt = text.split("\n");
            const generatedPrompts = generate(prompt, num, artists);
            resolve(generatedPrompts);
          })
          .catch(reject);
      } else {
        reject("Invalid prompt file");
      }
  
      function generate(prompt: string[], num_word: number, artists: number): string[] {
        let vocab = prompt.length;
        let generated: string[] = [];
        let artists_num = 0;
  
        while (generated.length < num_word && generated.length < vocab) {
          let rand = Math.floor(Math.random() * vocab);
          if (
            prompt[rand].startsWith("art by") &&
            artists_num < artists &&
            !generated.includes(prompt[rand])
          ) {
            artists_num += 1;
            generated.push(prompt[rand]);
          } else if (
            !prompt[rand].startsWith("art by") &&
            !generated.includes(prompt[rand])
          ) {
            generated.push(prompt[rand]);
          }
        }
  
        return generated;
      }
    });
  }

  return (
    <div className="text-center">
      <div className="flex flex-row justify-center items-center">
          <button 
            onClick={handleGeneratePrompts} 
            className="mt-4 px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white text-xs rounded-lg cursor-pointer"
            disabled={isLoading}
          >
            Suprise me
          </button>
          {isLoading ? (
            <HashLoader color={"#36d7b7"} size={20} className="ml-2"/>
          ) : null}
      </div>
    </div>
  );
}
