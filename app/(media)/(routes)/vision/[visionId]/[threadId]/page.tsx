import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { getThread } from "@/app/actions/threadActions"

import { VisionClient } from "../_components/client";

//export const runtime = 'edge'

interface VisionIdPageProps {
  params: Promise<{
    threadId: string
    visionId: string;
  }>
}

type ThreadInfo = { id: string; name: string } | null;

const VisionIdPage = async (props: VisionIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  if (userId !== "user_2XZNZhACVouAS4fBv7OmRqeetTc" && userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow") {
    return redirect("/")
  }

  const existingThread: ThreadInfo = await getThread(
    userId, params.visionId, params.threadId
  );

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.visionId
    },
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
          threadId: params.threadId,
        },
      },
      medias: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });


  if (!companion) {
    return redirect("/");
  }

  return (
    <VisionClient companion={companion} threadId={params.threadId} existingThread={existingThread} />
  );
}
 
export default VisionIdPage;