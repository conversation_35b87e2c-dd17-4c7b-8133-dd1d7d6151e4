import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";

import prismadb from "@/lib/prismadb";

import { RealArtClient } from "./_components/client";

//export const runtime = 'edge'

interface RealArtIdPageProps {
  params: Promise<{
    imageId: string;
  }>
}


const RealArtIdPage = async (props: RealArtIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.imageId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
    include: {
      messages: {
        orderBy: {
          createdAt: "asc"
        },
        where: {
          userId,
        },
      },
      _count: {
        select: {
          messages: true,
        }
      }
    }
  });


  if (!companion) {
    return redirect("/");
  }


  return (
     <RealArtClient companion={companion} />
  );
}
 
export default RealArtIdPage;