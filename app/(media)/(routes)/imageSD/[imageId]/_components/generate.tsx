'use client'

import Image from 'next/image'
import { Empty } from "@/components/ui/empty";
import { imagesType, GenerateType } from '@/lib/types'


const Generate = ({ isLoading, images, setModalData, setModalOpen }: GenerateType) => {

  const openModal = (data: imagesType) => {
    setModalData(data)
    setModalOpen(true)
  }

  return (
    <>
      <div className="border-b-2 border-blue-100 mb-4 font-bold text-lg">Generation</div>
      {!images && !isLoading || images?.length === 0 && !isLoading && (
            <Empty label="No images generated." />
      )}
      {isLoading ? (
        <div className="flex items-center justify-center h-full">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-blue-700 border-t-transparent" />
        </div>
      ) : images ? (
        <div className="grid grid-cols-2 gap-1">
          {images.map((data, index) => (
            <div key={index} className="cursor-pointer" onClick={() => openModal(data)}>
              <Image
                src={data.imageSrc}
                className="object-cover rounded-lg"
                alt="image"
                width={740}
                height={740}
              />
            </div>
          ))}
        </div>
      ) : (
        <></>
      )}
    </>
  )
}

export default Generate