"use client";

import { useState } from "react";
import { DownloadCloud, ImageIcon } from "lucide-react";
import { Companion, Message } from "@prisma/client";

import { ChatHeader } from "@/components/chat-header";
import { imagesType } from '@/lib/types'
import Create from './create'
import Generate from './generate'
import Modal from '@/components/modal'


const testImages: imagesType[] = [
    {
      imageSrc: 'https://placehold.jp/512x512.png',
      prompt: 'test prompt1',
      negative: 'test negative prompt1',
      width: 512,
      height: 512,
      ratio: '1:1',
      steps: 30,
      seed: 1,
    },
    {
      imageSrc: 'https://placehold.jp/512x512.png',
      prompt: 'test prompt2',
      negative: 'test negative prompt2',
      width: 512,
      height: 512,
      ratio: '1:1',
      steps: 30,
      seed: 1,
    },
  ]

interface DiffusionClientProps {
    companion: Companion & {
      messages: Message[];
      _count: {
        messages: number;
      }
    };
  };

export const DiffusionClient = ({
  companion,
}: DiffusionClientProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const [images, setImages] = useState<imagesType[] | null>(null)
  const [modalOpen, setModalOpen] = useState(false)
  const [modalData, setModalData] = useState<imagesType | null>(null)

  const closeModal = () => {
    setModalOpen(false)
  }


  return ( 
    <div className="flex flex-col h-auto w-[100%] px-4 py-2 space-y-1">
      <ChatHeader companion={companion} />
      <div className="grid grid-cols-5 gap-4">
        {/* Model */}
        {modalData && <Modal isOpen={modalOpen} closeModal={closeModal} modalData={modalData} />}

        <div className="col-span-12 lg:col-span-3">
          {/* generated image */}
          <Generate
            isLoading={isLoading}
            images={images}
            setModalData={setModalData}
            setModalOpen={setModalOpen}
          />
        </div>

        <div className="col-span-12 lg:col-span-2">
          {/* generation form */}
          <Create isLoading={isLoading} setIsLoading={setIsLoading} setImages={setImages} />
        </div>
      </div>
    </div>
  )
}