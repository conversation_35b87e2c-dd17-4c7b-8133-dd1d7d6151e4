'use client'

import axios from "axios";
import { useRef, useState } from 'react'
import { useProModal } from "@/hooks/use-pro-modal";
import { useToast } from "@/components/ui/use-toast";
import { useForm } from "react-hook-form";
import { CreateType, imagesType } from '@/lib/types'

//import JSZip from 'jszip'
import Slider from 'rc-slider'
import 'rc-slider/assets/index.css'

const SIZE_OPTIONS = [
  { ratio: '7:4', width: 896, height: 512 },
  { ratio: '3:2', width: 768, height: 512 },
  { ratio: '5:4', width: 640, height: 512 },
  { ratio: '1:1', width: 512, height: 512 },
  { ratio: '4:5', width: 512, height: 640 },
  { ratio: '2:3', width: 512, height: 768 },
  { ratio: '4:7', width: 512, height: 896 },
]

const negativeInitialData = `nsfw, weapon, blood, guro, lowres, bad anatomy, text, error, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name`;

const MAX_IMAGE_COUNT = 4

const Create = ({ isLoading, setIsLoading, setImages }: CreateType) => {
  const proModal = useProModal()
  const { toast } = useToast()
  const promptRef = useRef<HTMLTextAreaElement>(null)
  const negativeRef = useRef<HTMLTextAreaElement>(null)
  const seedRef = useRef<HTMLInputElement>(null)
  const [selectedSize, setSelectedSize] = useState(SIZE_OPTIONS[3])
  const [size, setSize] = useState(3)
  const [count, setCount] = useState(1)
  const [guidance, setGuidance] = useState(7.5)
  const [inference, setInference] = useState(30)

  const [error, setError] = useState<string | null>(null)

  const countHandleChange = (value: number | number[]) => {
    const numValue = value as number
    setCount(numValue)
  }

  const sizeHandleChange = (value: number | number[]) => {
    const numValue = value as number
    setSize(numValue)
    setSelectedSize(SIZE_OPTIONS[numValue])
  }

  const guidanceHandleChange = (value: number | number[]) => {
    const numValue = value as number
    setGuidance(numValue)
  }  

  const inferenceHandleChange = (value: number | number[]) => {
    const numValue = value as number
    setInference(numValue)
  }  

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    // Clear error message
    setError('')

    // Get form data
    const prompt = promptRef.current!.value
    const negative = negativeRef.current!.value
    const width = selectedSize.width
    const height = selectedSize.height
    const ratio = selectedSize.ratio
    const seed = parseInt(seedRef.current!.value, 10)

    // Seed generation
    const seedList: number[] = []
    for (let i = 0; i < count; i++) {
      if (!seed) {
        // If no seed is specified, set a random seed
        seedList.push(Math.floor(Math.random() * 1000000000))
      } else {
        // If a seed is specified, set the specified seed
        seedList.push(seed)
      }
    }


    try {
      /*
      const body = {
        prompt,
        negative,
        count,
        height,
        width,
        scale: guidance,
        steps: inference,
        seed: seedList,
        genImage_API: 'stable_diffusion'
      }

      // Image generation api call
      const response = await fetch('/api/gen_image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })
      */

      const response = await axios.post('/api/image/diffuser', {
        prompt,
        negative,
        count,
        height,
        width,
        scale: guidance,
        steps: inference,
        seed: seedList,
        genImage_API: 'stable_diffusion'
      })

      /*
      if (!response.ok) {
        const errorData = await response.json()
        setError(`Image could not be generated: ${errorData.detail}`)
        setIsLoading(false)
        return
      }

      const responseData = await response.json();
      */


      const responseData = response.data;
      //console.log("ImageURL: ", responseData)
      const imageDataList: imagesType[] = [];

      // Check if there's an "image" array and handle it
      if (responseData.image && Array.isArray(responseData.image)) {
        responseData.image.forEach((imageUrl: string, index: number) => {
          // Create image data for each image URL and push it to imageDataList
          imageDataList.push({
            imageSrc: imageUrl,
            prompt,
            negative,
            ratio,
            width,
            height,
            seed: seedList[index],
            steps: inference,
          });
        });
      }

      // Set image data
      setImages(imageDataList)

      console.log("imageDataList: ", imageDataList)      

      /*
      // Image generation API successful
      const zipBlob = await response.blob()
      // Unzip the zip file
      const zipArrayBuffer = await zipBlob.arrayBuffer()
      // Load zip file
      const zip = await JSZip.loadAsync(zipArrayBuffer)

      // Create image data
      const imageDataList = []
      // Get images in zip file
      for (const [index, fileName] of Object.entries(Object.keys(zip.files))) {
        // Get image file
        const imageFile = zip.file(fileName)
        // Convert image file to blob
        const imageData = await imageFile!.async('blob')
        // convert blob to URL
        const imageObjectURL = URL.createObjectURL(imageData)

        // Create image data
        imageDataList.push({
          imageSrc: imageObjectURL,
          prompt,
          negative,
          ratio,
          width,
          height,
          seed: seedList[parseInt(index, 10)],
          steps,
        })
      }

      // Set image data
      setImages(imageDataList)
      */
    } catch (error: any) {
      //alert(error)
      console.error('An error occurred:', error);
      if (error?.response?.status === 403) {
        proModal.onOpen();
      } else {
        toast({
          variant: "destructive",
          description: "Something went wrong.",
          duration: 3000,
        });
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <div className="border-b-2 border-blue-100 mb-4 font-bold text-lg">Create</div>

      <form onSubmit={onSubmit}>
        <div className="p-4 rounded-lg shadow">
          {/* Prompt */}
          <div className="mb-5">
            <div className="font-bold mb-2 text-sm">Prompt</div>
            <textarea
              className="w-full border-0 rounded-lg p-2 focus-visible:ring-0 focus-visible:ring-transparent"
              rows={3}
              ref={promptRef}
              id="prompt"
              required
            />
          </div>

          {/* Negative prompt */}
          <div className="mb-5">
            <div className="font-bold mb-2 text-sm">Negative Prompt</div>
            <textarea
              className="w-full border-0 rounded-lg p-2 focus-visible:ring-0 focus-visible:ring-transparent"
              rows={3}
              ref={negativeRef}
              id="negative"
            />
          </div>

          {/* Image generation size, Image Count, and Displayed Size */}
          <div className="mb-5 flex justify-between gap-4">
            {/* Image generation size */}
            <div className="flex-auto">
              <div className="font-bold mb-2 text-sm">
                Size   {SIZE_OPTIONS[size].width} x {SIZE_OPTIONS[size].height}
              </div>
              <div className="px-2">
                <Slider
                  min={0}
                  max={SIZE_OPTIONS.length - 1}
                  value={size}
                  onChange={sizeHandleChange}
                  trackStyle={{ backgroundColor: 'rgba(29, 78, 216)', height: 4 }}
                  handleStyle={{
                    borderColor: 'rgba(29, 78, 216)',
                    borderWidth: 2,
                    backgroundColor: 'rgba(29, 78, 216)',
                    width: 16,
                    height: 16,
                  }}
                  railStyle={{ backgroundColor: 'rgba(219, 234, 254)', height: 4 }}
                />
              </div>
              {/* <div className="flex justify-between mt-2 text-sm">
                {SIZE_OPTIONS.map((data, index) => (
                  <div key={index}>{data.ratio}</div>
                ))}
              </div> */}
            </div>

            {/* Number of inference steps */}
            <div className="flex-auto">
              <div className="mb-5">
                <div className="font-bold mb-2 text-sm">Inference steps  {inference}</div>
                <div className="px-2">
                  <Slider
                    min={20}
                    max={50}
                    value={inference}
                    onChange={inferenceHandleChange}
                    trackStyle={{ backgroundColor: 'rgba(29, 78, 216)', height: 4 }}
                    handleStyle={{
                      borderColor: 'rgba(29, 78, 216)',
                      borderWidth: 2,
                      backgroundColor: 'rgba(29, 78, 216)',
                      width: 16,
                      height: 16,
                    }}
                    railStyle={{ backgroundColor: 'rgba(219, 234, 254)', height: 4 }}
                  />
                </div>
              </div>
            </div>            
          </div>

          {/* Three divisions in the same row */}
          <div className="mb-5 flex justify-between gap-1">
            {/* Image Count */}
            <div className="flex-auto">
              <div className="font-bold mb-2 text-sm">
                Image Count   {count}
              </div>
              <div className="px-2">
                <Slider
                  min={1}
                  max={MAX_IMAGE_COUNT}
                  value={count}
                  onChange={countHandleChange}
                  trackStyle={{ backgroundColor: 'rgba(29, 78, 216)', height: 4 }}
                  handleStyle={{
                    borderColor: 'rgba(29, 78, 216)',
                    borderWidth: 2,
                    backgroundColor: 'rgba(29, 78, 216)',
                    width: 16,
                    height: 16,
                  }}
                  railStyle={{ backgroundColor: 'rgba(219, 234, 254)', height: 4 }}
                />
              </div>
              {/* <div className="flex justify-between mt-2 text-sm">
                {Array.from({ length: MAX_IMAGE_COUNT }, (_, i) => i + 1).map((data, index) => (
                  <div key={index}>{data}</div>
                ))}
              </div> */}
            </div>

            {/* Guidance Scale */}
            <div className="flex-auto">
              <div className="font-bold mb-2 text-sm">Guidance Scale  {guidance}</div>
                <div className="px-2">
                  <Slider
                    min={0}
                    max={20}
                    value={guidance}
                    onChange={guidanceHandleChange}
                    trackStyle={{ backgroundColor: 'rgba(29, 78, 216)', height: 4 }}
                    handleStyle={{
                      borderColor: 'rgba(29, 78, 216)',
                      borderWidth: 2,
                      backgroundColor: 'rgba(29, 78, 216)',
                      width: 16,
                      height: 16,
                    }}
                    railStyle={{ backgroundColor: 'rgba(219, 234, 254)', height: 4 }}
                  />
                </div>
            </div>

            {/* Seed */}
            <div className="width[10rem]">
              <div className="font-bold mb-2 text-sm">Seed</div>
              <input
                className="w-full border-0 rounded-lg p-2 focus-visible:ring-0 focus-visible:ring-transparent"
                type="number"
                ref={seedRef}
                id="seed"
              />
            </div>
          </div>

          {/* Error Message */}
          {error && <div className="text-red-500 text-center mb-5">{error}</div>}

          {/* Button */}
          <div>
            <button
              type="submit"
              className="w-full text-white bg-blue-700 hover:bg-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none"
              disabled={isLoading}
            >
              <div className="flex items-center justify-center space-x-3">
                {isLoading && (
                  <div className="h-4 w-4 animate-spin rounded-full border-0 border-white border-t-transparent" />
                )}
                <div>Generate</div>
              </div>
            </button>
          </div>
        </div>
      </form>
    </>

  )
}

export default Create