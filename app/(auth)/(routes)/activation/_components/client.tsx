'use client'

import React, { useState } from "react";
import { useClerk } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'

import { useForm } from 'react-hook-form'; 
import { z } from 'zod/v3';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON>ist, 
  TabsTrigger 
} from "@/components/ui/tabs"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { toast } from "sonner";
import { spinner } from '@/components/uiCard/stocks/spinner';
import { activateUser } from '@/app/actions/activateUser'

const activationSchema = z.object({
  otp: z.string().min(5, 'Please enter a otp number of at least 5 characters long')
});



export function Activation() {
  const router = useRouter()
  const { signOut } = useClerk();
  const [attemptCount, setAttemptCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const form = useForm({
    resolver: zodResolver(activationSchema),
    defaultValues: {
      otp: '',
    },
  });

  const { handleSubmit, control, formState } = form;

  const onSubmit = async (data: { otp: string }) => {
    console.log("send actions", data.otp);
    setIsLoading(true);
  
    const response = await activateUser(data.otp);
  
    if (response && response?.status === 200) {
      toast.success(response.body.message);
      router.push("/")
    } else {
      setAttemptCount(prevCount => prevCount + 1);
      
      if (attemptCount >= 2) { // This will be the 3rd attempt
        toast.error("Too many failed attempts. Signing out...");
        setTimeout(() => {
          signOut();
          router.push("/sign-in"); // Adjust this route as needed
        }, 2000); // Delay for 2 seconds to show the toast message
      } else {
        toast.error(response?.body?.message || 'An unknown error occurred');
      }
    }
  
    setIsLoading(false);
  };


  return (
    <div className="flex flex-rows w-full h-full justify-center items-center overflow-y-hidden">
      <Tabs defaultValue="activation" className="flex flex-col w-full items-center gap-x-2 mx-auto bg-transparent">
        <TabsList className="h-8 bg-background/20">
          <TabsTrigger value="activation" className="text-sm">Account Activation</TabsTrigger>
        </TabsList>
        <TabsContent value="activation" className="flex flex-col items-center w-full mt-1 overflow-y-auto">
          <div className="w-full font-bold text-lg text-center text-orange-600 dark:text-green-300 px-2 lg:px-0 my-2">
            User Account Activation
          </div>
          <div className='w-fit flex flex-col items-center justify-between text-pink-900 dark:text-green-300 gap-2 mb-4'>
            Please enter the OTP code to activate your account.
          </div>
          <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-1 flex-col items-center gap-8 mt-4">
              {isLoading && (
                <div className="flex items-center font-bold text-orange-600">
                  {spinner}
                  <span className="ml-2">Working on it ...</span>
                </div>
              )}
              <FormField
                   control={control}
                name='otp'
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="flex justify-center font-semibold">OTP code</FormLabel>
                    <div className='flex flex-rows items-center gap-x-2'>
                      <FormControl>
                      <Input
                        className="border-0 text-center rounded-md text-gray-900 bg-violet-200"
                          placeholder=""
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                          }}
                        />
                      </FormControl>
                    </div>
                    <FormDescription>Please enter OTP code to activate an account.</FormDescription>
                    <FormMessage>{fieldState.error?.message}</FormMessage> {/* Display error message */}
                  </FormItem>
                )}
                />
              <div className="flex flex-row items-center justify-center gap-2 my-2">
                <Button
                  disabled={isLoading}
                  type="submit"
                  variant={"default"} size={"sm"} 
                  className="rounded"
                >
                  Submit
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </div>
  );
}

