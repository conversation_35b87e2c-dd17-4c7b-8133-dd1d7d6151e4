'use client';

import React from 'react';
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { MessageCircleIcon } from "lucide-react";
import Chat from "./chat";

interface AIChatButtonProps {
  contractId?: string;
  filePath?: string;
  children?: React.ReactNode;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  dialogTitle?: string;
}

export function AIChatButton({
  contractId,
  filePath,
  children,
  buttonVariant = "outline",
  buttonSize = "icon",
  dialogTitle = "Ask AI About Your Contract",
}: AIChatButtonProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        {children || (
          <Button className="rounded-full" variant={buttonVariant} size={buttonSize}>
            <MessageCircleIcon className="h-4 w-4" />
            <span className="sr-only">{dialogTitle}</span>
          </Button>
        )}
      </SheetTrigger>
      <SheetContent
        side="right"
        className="sm:max-w-[420px] h-full"
        onInteractOutside={(e: any) => e.preventDefault()}
      >
        <SheetHeader>
          <SheetTitle className='underline'>{dialogTitle}</SheetTitle>
        </SheetHeader>
        <div className="">
          <Chat contractId={contractId} filePath={filePath} />
        </div>
      </SheetContent>
    </Sheet>
  );
}
