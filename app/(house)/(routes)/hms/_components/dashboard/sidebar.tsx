"use client";

import { cn } from "@/lib/utils";
import { FileText, Home, LayoutDashboard, Menu, Settings } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ElementType, useState } from "react";

export const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <aside
      className={cn(
        "bg-white text-black border-r border-gray-200 min-h-screen hidden lg:flex flex-col transition-all",
        isCollapsed ? "w-11" : "w-[280px]"
      )}
    >
      <div className="flex items-center justify-between p-1">
        {!isCollapsed && <span className="font-semibold text-lg">Dashboard</span>}
        <button
          onClick={() => setIsCollapsed((prev) => !prev)}
          className="p-2 text-gray-500 hover:bg-gray-100 rounded"
        >
          <Menu className="size-5" />
        </button>
      </div>
      {SidebarContent({ isCollapsed })}
    </aside>
  );
};

const SidebarContent = ({ isCollapsed }: { isCollapsed: boolean }) => {
  const pathname = usePathname();

  const sidebarItems = [
    {
      icon: Home,
      label: "Home",
      href: "/",
    },
    {
      icon: LayoutDashboard,
      label: "Contracts",
      href: "/hms",
    },
    {
      icon: FileText,
      label: "Results",
      href: "/hms/results",
    },
  ];

  return (
    <div className="flex flex-col flex-grow">
      <nav className="flex-grow pt-4">
        <ul role="list" className="space-y-2">
          {sidebarItems.map((item) => (
            <Navlink key={item.label} path={pathname} link={item} isCollapsed={isCollapsed} />
          ))}
        </ul>
      </nav>
    </div>
  );
};

const Navlink = ({
  path,
  link,
  isCollapsed,
}: {
  path: string;
  link: {
    icon: ElementType;
    label: string;
    href: string;
    target?: string;
  };
  isCollapsed: boolean;
}) => {
  return (
    <li>
      <Link
        href={link.href}
        target={link.target}
        className={cn(
          "group flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-semibold leading-5 text-black",
          path === link.href ? "bg-gray-200" : "hover:bg-gray-200"
        )}
      >
        <link.icon className="size-5 shrink-0" />
        {!isCollapsed && <span>{link.label}</span>}
      </Link>
    </li>
  );
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">{children}</div>
    </div>
  );
}
