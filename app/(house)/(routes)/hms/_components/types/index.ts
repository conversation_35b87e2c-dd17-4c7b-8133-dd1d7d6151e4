type Risk = {
  risk: string;
  explanation: string;
  severity: "low" | "medium" | "high";
  id: string;
}

type Opportunity = {
  opportunity: string;
  explanation: string;
  impact: "low" | "medium" | "high";
  id: string;
}

type CompensationStructure = {
  baseSalary: string;
  bonuses: string;
  equity: string;
  otherBenefits: string;
  id: string;
}

export type ContractAnalysis = {
  id: string;
  createdAt: string;  
  userId: string;
  contractText: string;
  risks: Risk[];
  opportunities: Opportunity[];
  summary: string;
  recommendations: string[];
  keyClauses: string[];
  legalCompliance: string;
  negotiationPoints: string[];
  contractDuration: string;
  terminationConditions: string;
  overallScore: number;
  compensationStructure: CompensationStructure;
  performanceMetrics: string[];
  contractType: string;
  intellectualPropertyClauses: string;
  version: number;
  expirationDate: string | null;
  language: string;
  filePath: string;
}