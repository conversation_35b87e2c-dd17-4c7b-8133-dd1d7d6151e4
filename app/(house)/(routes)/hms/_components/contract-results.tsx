"use client";

import axios from 'axios';
import ContractAnalysisResults from "./analysis/contract-analysis-results";
import { useUser } from "@clerk/nextjs";
import { ContractAnalysis } from "./types";
import { notFound } from "next/navigation";
import { useEffect, useState } from "react";

interface IContractResultsProps {
  contractId: string;
}

export default function ContractResults({ contractId }: IContractResultsProps) {
  const { user } = useUser();
  const userId = user?.id
  const [analysisResults, setAnalysisResults] = useState<ContractAnalysis>();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    const fetchResults = async () => {
      if (userId && contractId) {
        try {
          setLoading(true);
          const response = await axios.get(`/api/contracts/${contractId}`);
          setAnalysisResults(response.data);
          console.log(response.data);
          setError(false);
        } catch (error) {
          console.error(error);
          setError(true);
        } finally {
          setLoading(false);
        }
      }
    };
  
    fetchResults();
  }, [userId, contractId]); 
  
  // Component Render
  if (error) {
    return notFound();
  }
  
  if (!analysisResults) {
    return <div>Loading...</div>;
  }

  console.log("analysisResults", analysisResults)

  return (
    <ContractAnalysisResults
      contractId={contractId}
      analysisResults={analysisResults}
      isActive={true}
      onUpgrade={function (): void {
        throw new Error("Function not implemented.");
      }}
    />
  );
}