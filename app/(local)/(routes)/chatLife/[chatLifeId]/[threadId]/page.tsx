import { auth } from "@clerk/nextjs/server";
import prismadb from "@/lib/prismadb";
import { Companion, Persona } from "@prisma/client";
import { getCompanionById, getThread } from "@/app/actions/threadActions"
import { getNotes } from "@/app/actions/noteActions"
import { Home } from "@/components/llamaindex/home";

export interface ChatLifeProps {
  params: Promise<{
    threadId: string
    chatLifeId: string
  }>
}

type ThreadInfo = { id: string; name: string } | null;

export default async function ChatLife(props: ChatLifeProps) {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  const existingThread: ThreadInfo = await getThread(
    userId, params.chatLifeId, params.threadId
  );

  const companion = await getCompanionById(userId, params.chatLifeId, params.threadId)

  if (!companion || Array.isArray(companion) || Object.keys(companion).length === 0) {
    return <div>Companion not found.</div>;
  }

  const foundPersona : Persona | null = await prismadb.persona.findUnique({
    where: {
      userId
    },
  })

  const notes =  await getNotes(userId)

  return (
    <>
      <Home companion={companion} foundPersona={foundPersona} threadId={params.threadId} existingThread={existingThread} notes={notes} />
    </>
  );
}
