import { SidebarHeader } from '@/components/llamaindex/layout/sidebar-header'

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export interface ChatLifeLayoutProps {
  params: Promise<{ 
    chatLifeId: string
  }>
}

const ChatLifeLayout = ({
  children,
  params,
}: ChatLifeLayoutProps & {
  children: React.ReactNode;
}) => {
  const path = `/chatLife/${params.chatLifeId}`
  return ( 
    <div className="mx-auto max-w-screen max-h-screen w-full overflow-hidden">
      <SidebarHeader companionId={params.chatLifeId} path={path} />
      {children}
    </div>
  );
}

export default ChatLifeLayout;