import { Home } from "@/components/llamaindex/home";
import { Bo<PERSON> } from "@/store/bot";
import { kv } from "@vercel/kv";
import { v4 as uuidv4 } from 'uuid'

export default async function App(props: { params: Promise<{ botId: string }> }) {
  const params = await props.params;
  console.log(`[Share] try loading bot with key ${params.botId}`);
  let bot: Bot | null = null;
  try {
    const res: { bot: Bot } | null = await kv.get(params.botId);
    bot = res?.bot || null;
  } catch (e) {
    console.error(`[Share] failed to load bot with key ${params.botId}`, e);
  }

  if (!bot) {
    console.log(`[Share] requested unknown bot with id ${params.botId}`);
    return (
      <>
        Sorry, there is no bot at this URL. Try&nbsp;
        <a href="/">creating your own bot</a>.
      </>
    );
  }

  console.debug("[Share] bot loaded", bot);
  const threadId: string = uuidv4()
  return (
    <>
      {/*<Home bot={bot} threadId={threadId} />*/}
    </>
  );
}
