import "@/app/client/styles/markdown.css";
import "@/app/client/styles/highlight.css";
import '@/app/scrollbar-hidden.css'

import Locale from "@/locales";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: Locale.Welcome.Title,
  description: Locale.Welcome.SubTitle,
};

export default function LlamaindexLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="mx-auto h-screen max-w-screen max-h-screen w-full overflow-hidden">
      {/*<head>
        <link rel="manifest" href="/site.webmanifest"></link>
        <script src="/serviceWorkerRegister.js" defer></script>
      </head>*/}
        {children}
    </div>
  );
}
