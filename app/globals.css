@tailwind base;
@tailwind components;
@tailwind utilities;

/*@import '@liveblocks/react-ui/styles.css';
@import '@liveblocks/react-lexical/styles.css';
@import '@/components/live-editor/styles/light-theme.css';*/

html,
body,
:root {
  height: 100%;
}

@layer base {
  :root {
    --gradient: linear-gradient(to top left,#43C6AC,#F8FFAE);
    --background: 252.5 94.7% 85.1%;
    --foreground: 20 14.3% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
 
    --primary: 243.7 54.5% 41.4%;
    --primary-foreground: 60 9.1% 97.8%;
 
    --secondary: 500 45.8% 91.9%;
    --secondary-foreground: 24 9.8% 10%;
 
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
 
    --accent: 60 4.8% 85.9%;
    --accent-foreground: 24 9.8% 10%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;

    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 20 14.3% 4.1%;
    --chart-1: 173 58% 39%;
    --chart-2: 12 76% 61%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    --primary-text: #111827;
    --secondary-text: rgb(0, 0, 0);
    --bubbles-background: rgb(238, 228, 228);
    --bubbles-mine-background: #3b71ca;
  }

  .dark {
    /* CSS: .bg-gradient { background: var(--gradient) } */
    /* --gradient: linear-gradient(to top left,#141517,#024b11,#226847,#425709,#1b626e,#013529); */
    --gradient: linear-gradient(to top left,#3f51b1,#5a55ae,#7b5fac,#8f6aae,#a86aa4,#cc6b8e,#f18271,#f3a469,#cfa65e);

    --background: 243.7 54.5% 41.4%;
    --foreground: 237 3.5% 97.8%;

    --muted: 243.7 54.5% 41.4%;
    --muted-foreground: 237 3.5% 80.6%;

    --popover: 237 58% 7.280000000000001%;
    --popover-foreground: 237 3.5% 97.8%;

    --card: 237 58% 7.280000000000001%;
    --card-foreground: 237 3.5% 97.8%;

    --border: 242.2 47.4% 34.3%;
    --input: 243.7 54.5% 41.4%;

    --primary: 237 35% 56%;
    --primary-foreground: 237 3.5% 5.6000000000000005%;

    --secondary: 243.7 54.5% 41.4%;
    --secondary-foreground: 237 3.5% 97.8%;

    --accent: 243.7 54.5% 41.4%;
    --accent-foreground: 237 3.5% 97.8%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 237 3.5% 97.8%;

    --ring: 237 35% 56%;
    --chart-1: 220 70% 50%;
    --chart-2: 340 75% 55%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 160 60% 45%;
    --radius: 0.75rem;

    --primary-text: #98aedd;
    --secondary-text: rgb(240, 233, 233);
    --bubbles-background: rgb(59, 57, 57);
    --bubbles-mine-background: #3b71ca;

  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    border-radius: 50px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #09090a;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #2e3d5b;
    border-radius: 50px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #7878a3;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.no-visible-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}

.keyboard-visible {
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
}

.leaflet-popup-content p {
  margin: 0 !important;
}

/* leaflet made me do it.. */
.bring-to-front {
  z-index: 99999 !important;
}

.bring-to-front-modal {
  z-index: 99999999 !important;
}

.rate-limit-modal {
  z-index: 999999999999 !important;
}

/* tradingview mobile */
.my-5.tradingview-widget-container {
  height: 150px !important;
}

/* Markdown styles */
.markdown-container {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.markdown-container h1,
.markdown-container h2,
.markdown-container h3,
.markdown-container h4,
.markdown-container h5,
.markdown-container h6 {
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.3em;
}

.markdown-container h1 {
  font-size: 1.5em;
}

.markdown-container h2 {
  font-size: 1.4em;
}

.markdown-container h3 {
  font-size: 1.3em;
}

.markdown-container h4 {
  font-size: 1.2em;
}

.markdown-container h5 {
  font-size: 1.1em;
}

.markdown-container h6 {
  font-size: 1em;
}

.markdown-container p {
  margin-bottom: 1em;
}

.markdown-container strong {
  font-weight: bold;
}

.markdown-container em {
  font-style: italic;
}

.markdown-container a {
  color: #000;
  text-decoration: underline;
}

.markdown-container code {
  font-family: monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-container pre {
  font-family: monospace;
  font-size: 0.9em;
  padding: 1em;
  overflow: auto;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-container pre code {
  padding: 0;
  background-color: transparent;
}

.markdown-container ul,
.markdown-container ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

.markdown-container ul li,
.markdown-container ol li {
  margin-bottom: 0.5em;
}

.markdown-container ul li {
  list-style-type: disc;
}

.markdown-container ol li {
  list-style-type: decimal;
}

.markdown-container blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.markdown-container hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1.5em 0;
}

/* CopilotKit styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  /* ======================== Live-blocks overrides */
  .text-28-semibold {
    @apply text-[28px] font-semibold;
  }
  .text-10-regular {
    @apply text-[10px] font-normal;
  }

  .gradient-blue {
    @apply bg-gradient-to-t from-blue-500 to-blue-400;
  }
  .gradient-red {
    @apply bg-gradient-to-t from-red-500 to-red-400;
  }

  .shad-dialog {
    @apply w-full max-w-[400px] rounded-xl border-none bg-cover px-5 py-7 shadow-xl sm:min-w-[500px] !important;
  }

  .shad-dialog button {
    @apply focus:ring-0 focus:ring-offset-0 focus-visible:border-none focus-visible:outline-none focus-visible:ring-transparent focus-visible:ring-offset-0 !important;
  }

  .shad-select {
    @apply w-fit border-none bg-transparent text-blue-100 !important;
  }

  .shad-select svg {
    @apply ml-1 mt-1;
  }

  .shad-select-item {
    @apply cursor-pointer bg-dark-200 text-blue-100 focus:bg-dark-300 hover:bg-dark-300 focus:text-blue-100 !important;
  }

  .shad-popover {
    @apply w-[460px] border-none bg-dark-200 shadow-lg !important;
  }

  .floating-toolbar {
    @apply flex w-full min-w-max items-center justify-center gap-2 rounded-lg bg-dark-350 p-1.5 shadow-xl;
  }

  .floating-toolbar-btn {
    @apply relative inline-flex size-8 items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50;
  }

  .toolbar-wrapper {
    @apply z-50 custom-scrollbar w-screen overflow-auto border-y border-dark-300 bg-dark-100 pl-3 pr-4 shadow-sm;
  }

  .editor-wrapper {
    @apply custom-scrollbar h-[calc(100vh-140px)] gap-5 overflow-auto px-5 pt-5 lg:flex-row lg:items-start lg:justify-center xl:gap-10 xl:pt-10;
  }

  .header {
    @apply min-h-[42px] min-w-full flex-nowrap bg-transparent flex w-full items-center justify-between gap-2 px-4;
  }

  .document-list-container {
    @apply flex flex-col items-center mb-10 w-full gap-10 px-5;
  }

  .document-list-title {
    @apply max-w-[730px] items-end flex w-full justify-between;
  }

  .document-list-item {
    @apply flex items-center justify-between gap-4 rounded-lg bg-cover p-5 shadow-xl;
  }

  .document-list-empty {
    @apply flex w-full max-w-[730px] flex-col items-center justify-center gap-5 rounded-lg bg-dark-200 px-10 py-8;
  }

  .document-title-input {
    @apply min-w-[78px] flex-1 border-none bg-transparent px-0 text-left text-base font-semibold leading-[24px] focus-visible:ring-0 focus-visible:ring-offset-0 disabled:text-black sm:text-xl md:text-center !important;
  }

  .document-title {
    @apply line-clamp-1 border-dark-400 text-base font-semibold leading-[24px] sm:pl-0 sm:text-xl;
  }

  .view-only-tag {
    @apply rounded-md bg-dark-400/50 px-2 py-0.5 text-xs text-blue-100/50;
  }

  .collaborators-list {
    @apply hidden items-center justify-end -space-x-3 overflow-hidden sm:flex;
  }

  .share-input {
    @apply h-11 flex-1 border-none bg-dark-400 focus-visible:ring-0 focus-visible:ring-offset-0 !important;
  }

  .remove-btn {
    @apply rounded-lg bg-transparent px-0 text-red-500 hover:bg-transparent;
  }

  .comments-container {
    @apply mb-10 space-y-4 lg:w-fit flex w-full flex-col items-center justify-center;
  }

  .comment-composer {
    @apply w-full max-w-[800px] border border-dark-300 bg-dark-200 shadow-sm lg:w-[350px];
  }

  .comment-thread {
    @apply w-full max-w-[800px] border border-dark-300 bg-dark-200 shadow-sm lg:w-[350px] transition-all;
  }

  .loader {
    @apply flex size-full h-screen items-center justify-center gap-3 text-white;
  }

  /* ======================== Auth Pages */
  .auth-page {
    @apply flex h-screen w-full flex-col items-center justify-center gap-10;
  }

  /* ======================== Home Page */
  .home-container {
    @apply relative flex min-h-screen w-full flex-col items-center gap-5 sm:gap-10;
  }

  .document-ul {
    @apply flex w-full max-w-[730px] flex-col gap-5;
  }

  /* ======================== CollaborativeRoom */
  .collaborative-room {
    @apply flex size-full max-h-screen flex-1 flex-col items-center overflow-hidden;
  }
}

:root {
  --copilot-kit-primary-color: #222222 !important;
}

.copilotKitHeader {
  height: 60px;
  border-left: 1px solid #555;
}

.copilotKitWindow.open {
  box-shadow: none;
}

.copilotKitMessage.copilotKitUserMessage {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 0.5rem 0.5rem 0 0.5rem;
}

.copilotKitMessage.copilotKitAssistantMessage {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 0.5rem 0.5rem 0.5rem 0;
}

.copilotKitMessage svg {
  color: white !important;
}

.copilotKitChat {
  background-color: #e0e9fd;
}

.copilotKitResponseButton {
  background-color: transparent;
  color: var(--copilot-kit-secondary-color);
  border: 0px;
}

.copilotKitInput > .copilotKitInputControls > button:not([disabled]) {
  color: var(--copilot-kit-secondary-color);
}

.aspect-ratio-box {
  width: 100%;
  max-width: calc(100% - 5rem); /* Account for margin */
  position: relative;
}
.aspect-ratio-box::before {
  content: "";
  display: block;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}
.aspect-ratio-box > * {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}


/* ======================== Liveblocks Override */
.lb-root {
  --lb-accent-subtle: #0b1527;
  --lb-radius: 0px;
  --lb-dynamic-background: #1b2840;
}

.lb-comment,
.lb-thread-comments,
.lb-composer,
.lb-comment-reaction {
  background-color: #0f1c34;
  color: #fff;
}

.lb-button {
  --lb-foreground-moderate: #fff;
}

.lb-button:where([data-variant='primary']) {
  background-color: #161e30;
  color: #b4c6ee;
  padding: 8px;
}

.lb-button:where(
    [data-variant='default']:not(
        :is(
            :enabled:hover,
            :enabled:focus-visible,
            [aria-expanded='true'],
            [aria-selected='true']
          )
      )
  ) {
  color: #b4c6ee;
}

.lb-button:where(
    :enabled:hover,
    :enabled:focus-visible,
    [aria-expanded='true'],
    [aria-selected='true']
  ) {
  --lb-button-background: #161e30;

  color: #b4c6ee;
}

.lb-inbox-notification-list-item:where(:not(:last-of-type)) {
  border-bottom: none;
}

.lb-comment-body,
.lb-dropdown-item,
.lb-dropdown-item-icon,
.lb-composer-editor {
  color: #fff;
}

.lb-composer-action {
  padding: 8px;
}

.lb-comment-content {
  background: #0b1527;
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
}

.lb-comment-date,
.lb-lexical-mention-suggestion-user,
.lb-composer-suggestions-list-item,
.lb-inbox-notification-date,
.lb-comment-author,
.lb-emoji-picker-search-icon,
.lb-emoji-picker-category-header-title,
.lb-emoji-picker-search::placeholder {
  color: #b4c6ee;
}

.data-liveblocks-portal {
  color: #b4c6ee !important;
}

.lb-root:where(:not(.lb-root .lb-root)) {
  --lb-dynamic-background: #1b2840;
  color: #fff;
}

.lb-composer-editor :where([data-placeholder]) {
  color: #b4c6ee;
  font-size: 14px;
}

.lb-lexical-floating-threads-thread:where([data-resolved]) {
  opacity: 40%;
}

.lb-elevation {
  background: #0f1c34;
}