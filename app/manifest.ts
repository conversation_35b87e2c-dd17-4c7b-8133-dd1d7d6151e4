import { MetadataRoute } from 'next'
 
export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'ComfyMindsLab Companion AI',
    short_name: 'ComfyMindsLab AI',
    description: 'Your customized companion.',
    start_url: '/',
    scope: '/',
    display: 'standalone',
    background_color: '#fff',
    theme_color: '#fff',
    icons: [
	  {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png'
	  },
	  {
        src: '/icon-256x256.png',
        sizes: '256x256',
        type: 'image/png'
	  },
	  {
        src: '/icon-384x384.png',
        sizes: '384x384',
        type: 'image/png'
	  },
	  {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png'
	  }
    ],
  }
}