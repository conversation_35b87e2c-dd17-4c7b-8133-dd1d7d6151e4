import { redirect } from "next/navigation";
import { auth, currentUser } from "@clerk/nextjs/server";

import prismadb from "@/lib/prismadb";

import { GroupTalkClient } from "./_components/client";

//export const runtime = 'edge'

interface GroupTalkIdPageProps {
  params: Promise<{
    gId: string;
  }>
}


const GroupTalkIdPage = async (props: GroupTalkIdPageProps) => {
  const params = await props.params;
  const { userId, redirectToSignIn } = await auth();
  const user = await currentUser()
  /*const client await clerkClient()
  const session = await client.users.getUserList({
    orderBy: '-updated_at',
    limit: 50,
  });*/

  if (!userId || !user || !user.id || !user.firstName) {
    return redirectToSignIn();
  }

  const companion = await prismadb.companion.findUnique({
    where: {
      id: params.gId
    },
    //cacheStrategy: {
    //  swr: 60,
    //  ttl: 30
    //},
  });


  if (!companion) {
    return redirect("/");
  }

  //const userList: (string | null)[] = session.map(user => user.firstName);

  return (
     <GroupTalkClient companion={companion} userName={user.firstName} />
  );
}
 
export default GroupTalkIdPage;