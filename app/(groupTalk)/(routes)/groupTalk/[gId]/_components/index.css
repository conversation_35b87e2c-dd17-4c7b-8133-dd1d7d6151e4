  
    /*
    
    :root {
    --primary: #252052;
    --primary-text: #111827;
    --secondary-text: #374151;
    --tertiary-text: #4b5563;
    --secondary-background: #f3f4f6;
    --bubbles-background: white;
    --bubbles-mine-background: #3b71ca;
    --focus-ring: #3b82f680;
  
    color-scheme: light dark;
  }
  
  @media (prefers-color-scheme: dark) {
    :root {
      --primary: #252052;
      --primary-text: #f9fafb;
      --secondary-text: #f3f4f6;
      --tertiary-text: #e5e7eb;
      --secondary-background: #0f172a;
      --bubbles-background: #374151;
      --bubbles-mine-background: #195489;
    }
  }
  

  html,
  body {
    margin: 0;
    padding: 0;
  }
  
  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    -webkit-font-smoothing: antialiased;
    background-color: var(--secondary-background);
  
    padding-top: 2px;
    padding-bottom: 2px;
  }
  */

  h1 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 1.8em;
    font-weight: 500;
  }

  .main-box {
    height: 100vh;
    padding-top: 0.5em;
    padding-bottom: 1em;
    margin: 0 auto;
    overflow: hidden;
  }

  .badge span {
    background-color: #212529;
    color: #ffffff;
    border-radius: 6px;
    font-weight: bold;
    padding: 4px 8px 4px 8px;
    font-size: 0.75em;
  }
  
  .main-content {
    display: flex;
    height: 100vh;
    overflow: hidden;

  }
  
  .channel-box {
    flex-grow: 0;
    height: 100%;
    margin-top: 2em;
    margin-bottom: 1px;
    margin-left: 1em;
    margin-right: 1em;
    overflow: hidden;
    z-index: 42;
  }
  
  .chat-box {
    flex-grow: 1;
    height: 100vh;
    overflow: hidden;
  }
  
  .channel-box ul {
    height: 80vh;
    margin: 8px;
    padding-left: 4px;
    padding-right: 4px;
    border-radius: 16px;
    border: solid 0px lightgray;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    overflow-y: auto;
  }
  
  .channel-box ul:empty {
    display: none;
  }
  
  .channel-box li {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    padding: 8px 8px 8px 8px;
    border-bottom: solid 0px lightgray;
    font-size: 16px;
    border-radius: 16px;
  }


  .channel-box li:hover {
    background: #accef02f;
    border-radius: 16px;
    cursor: pointer;
  }
  
  .channel-box li:last-child {
    border: 0;
  }
  
  .channel-box li span:nth-child(1) {    
    font-weight: bold;
    margin-right: 4px;
    white-space: nowrap;
    color:rgb(33, 37, 41)
  }
  .channel-box li span:nth-child(2) {
    margin-right: 4px;
    word-break: break-word;
  }
  .channel-box li span:nth-child(3) {
    color: #6c757d;
    margin-left: auto;
    white-space: nowrap;
  }
  
  .channel-box form {
    display: flex;
    justify-content: center;
    padding-top: 20px;
  }
  
  .channel-box input:not([type]) {
    width: 100%;
    padding: 6px 12px 6px 12px;
    color: rgb(141, 145, 150);
    border: solid 0px rgb(73, 39, 194);
    border-radius: 8px;
    font-size: 16px;
    outline: none
  }
  
  .channel-box input[type="submit"],
  .channel-box button {
    display: flex;
    margin-left: 4px;
    background: lightblue;
    color: white;
    padding: 6px 12px 6px 12px;
    border-radius: 8px;
    font-size: 16px;
    background-color: rgb(49, 108, 244);
  }
  
  .channel-box input[type="submit"]:hover,
  .channel-box button:hover {
    background-color: rgb(41, 93, 207);
  }
  
  .channel-box input[type="submit"]:disabled,
  .channel-box button:disabled {
    background-color: rgb(122, 160, 248);
  }
  

  .chat headerX {
    position: fixed;
    top: 0;
    right: 1rem;
    width: 100%;
    z-index: 40;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    flex-direction: column;
    background: var(--secondary);
    color: white;
    text-align: center;
    height: 70px;
  }
  
  
  .chat header h1 {
    font-size: 1.1rem;
    font-weight: 500;
    letter-spacing: -0.02em;
    margin: 0;
  }
  
  .chat header p {
    margin: 0;
    position: relative;
    padding-left: 1.2em;
    font-weight: 300;
  }
  
  .chat header p::before,
  .chat header p::after {
    position: absolute;
    top: 20%;
    left: 0;
    display: inline-block;
  
    content: "";
    width: 0.7em;
    height: 0.7em;
    margin-right: 0.5em;
    background-color: #81e18c;
    border-radius: 50%;
    margin-bottom: 1px;
  
    animation: pulse 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  }
  
  .chat header p::after {
    animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  }
  
  @media (prefers-reduced-motion) {
    .chat header p::after {
      display: none;
    }
  }
  
  @keyframes ping {
    75%,
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
  
  .chat header p strong {
    font-weight: 500;
  }
  .chat header p input {
    font-weight: 500;
    margin: 0;
    padding: 0;
    width: 100px;
    border-radius: 0;
  }
  
  .chat article {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin: 14px auto;

    max-width: 680px;
    padding-left: 16px;
    padding-right: calc(16px + 10vw);
    animation: 0.15s ease-in-out message;
    box-sizing: content-box;
  }
  
  @media (prefers-reduced-motion) {
    .chat article {
      animation-name: fade;
    }
  }
  
  @keyframes message {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
  }
  
  @keyframes fade {
    from {
      opacity: 0;
    }
  }
  
  .chat article div {
    font-weight: 300;
    grid-column: 1 / 3;
    color: var(--secondary-text);
  }
  
  .chat article p {
    color: var(--secondary-text);
    background-color: var(--bubbles-background);
    padding: 9px;
    margin: 0 0;
    border-radius: 16px;
    border-top-left-radius: 0;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    text-overflow: ellipsis;
    overflow-x: hidden;
    line-height: 1.4;
    justify-self: start;
    white-space: pre-line;
  }
  
  .chat article.message-mine {
    padding-left: calc(16px + 10vw);
    padding-right: 26px;
  }
  
  .chat article.message-mine div {
    text-align: right;
    justify-self: end;
    grid-column: 1 / 3;
  }
  
  .chat article.message-mine p {
    border-radius: 16px;
    border-top-right-radius: 0;
    background: var(--bubbles-mine-background);
    color: white;
    justify-self: end;
  }
  
  .chat form {
    position: fixed;
    left: 25%;
    bottom: 0px;
    width: calc(100% - 50%);
    height: 72px;
    align-items: center;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
      0 8px 10px -6px rgb(0 0 0 / 0.1);
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border-radius: 16px;
    display: flex;
  }
  
  @media (prefers-color-scheme: dark) {
    .chat form {
      background-color: rgba(55, 65, 81, 0.8);
    }
  }

  .chat select {
    border-radius: 16px;
    background: transparent;
  }

  .chat input {
    color: #111827;
    width: 100%;
    background: transparent;
    font-size: 18px;
    padding-left: 4px;
    padding-right: 72px;
    font-family: inherit;
    border: 0px solid transparent;
    border-radius: 16px;
    outline: none;
  }
  
  .chat input::placeholder {
    color: #6b7280;
  }
  
  @media (prefers-color-scheme: dark) {
    .chat input {
      color: white;
    }
  
    .chat input::placeholder {
      color: #9ca3af;
    }
  }
  
  .chat input:focus {
    outline: 0;
    border-color: #3b82f680;
  }
  
  .chatButton {
    appearance: none;
    width: 48px;
    height: 48px;
    border: 0;
    border-radius: 50%;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 0;
    transition: 0.15s ease-in-out opacity;
  
    background-color: var(--bubbles-mine-background);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='white' %3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5' /%3E%3C/svg%3E%0A");
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  .chat button:disabled {
    opacity: 0.7;
  }
  
  .facepile {
    position: fixed;
    left: 25%;
    bottom: 58px;
    isolation: isolate;
    overflow: auto;
    width: 60%;
  }
  
  .face {
    display: inline-block;
    position: relative;
    background-color: transparent;
    font-size: 1.25rem;
    line-height: 1.75rem;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
  }