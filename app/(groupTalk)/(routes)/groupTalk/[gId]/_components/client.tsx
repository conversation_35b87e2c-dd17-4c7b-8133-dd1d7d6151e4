"use client";

import { FormEvent, useEffect, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Companion } from "@prisma/client";
import { ChatBox } from "./ChatBox";
import { Id } from "@/convex/_generated/dataModel";
import { HiMiniSparkles, HiSquaresPlus, HiMiniPencil,
  HiOutlineTrash, HiOutlineCheck, HiOutlineXMark } from 'react-icons/hi2';
import './index.css'

//const NAME = faker.person.firstName();

interface GroupTalkClientProps {
  companion: Companion
  userName: string
};

export function GroupTalkClient({
  companion, userName
}: GroupTalkClientProps) {
  const channels = useQuery(api.channels.list) || [];
  const [initializing, setInitializing] = useState(true);
  const [newChannelName, setNewChannelName] = useState("");
  const [isSelectedView, setIsSelectedView] = useState(false);
  const [isSelectedEdit, setIsSelectedEdit] = useState(false);
  const [editedChannelName, setEditedChannelName] = useState("");
  const addChannel = useMutation(api.channels.add);
  const renameChannel = useMutation(api.channels.renameChannel);
  const deleteChannel = useMutation(api.channels.deleteChannel);
  const NAME = userName //faker.person.firstName()
  const [isLoading, setIsLoading] = useState(false);
  const [channelId, setChannelId] = useState<Id<"channels"> | null>(null);
  
  useEffect(() => {
    if (initializing && channels.length > 0) {
      const initialChannelId = channels.length > 0 ? channels[0]._id : null;
      setChannelId(initialChannelId);
      setInitializing(false);
    }
  }, [initializing, channels]);


  async function handleAddChannel(e: FormEvent) {
    e.preventDefault();
    const id = await addChannel({ name: newChannelName });
    setNewChannelName("");
    setChannelId(id);
  }

  async function handleSubmitEdit(channelId: Id<"channels">) {
    if (editedChannelName) {
      await renameChannel({ channelId, name: editedChannelName });
      setEditedChannelName("");
      setIsSelectedEdit(false);
      setIsSelectedView(true)
    }
  }

  async function handleDelete(channelId: Id<"channels">) {
    if (channelId && window.confirm("Are you sure you want to delete this channel?")) {
      // Call the deleteChannel mutation to delete the channel
      await deleteChannel({ channelId });
      setIsSelectedView(false)
      setIsSelectedEdit(false);
    }
  }

  function handleCancelEdit(channelName: string) {
    // Exit edit mode
    setIsSelectedEdit(false);
    setIsSelectedView(true)
    setEditedChannelName(channelName);
  }

  function handleEdit(channelName: string) {
    setEditedChannelName(channelName);
    setIsSelectedView(false)
    setIsSelectedEdit(true);
  }

  return (
    <div className="h-[100vh] w-full pt-2 pb-4 overflow-hidden">
      {channels.length > 0 && (
        <div className="flex">
          <div className="channel-box" style={{ width: '20%' }}>
            <form onSubmit={handleAddChannel}>
              <input
                value={newChannelName}
                onChange={(e) => setNewChannelName(e.target.value)}
                placeholder="Add a channel..."
              />
              <button type="submit" disabled={!newChannelName}>
                <HiSquaresPlus color="blue" size={24} />
              </button>
            </form>
            <ul>
              {channels.slice(1).map((channel) => (
                <li
                  key={channel._id}
                  onClick={() => {
                    setChannelId(channel._id);
                    setIsSelectedView(true);
                  }}
                  style={{
                    fontWeight: channel._id === channelId ? 'bold bg-neutral-100 rounded-2xl' : 'normal bg-transparent',
                  }}
                >
                  {isSelectedEdit && channel._id === channelId ? (
                    <>
                      <HiMiniSparkles color="secondary-text" size={17} className="mr-1" />
                      <input
                        value={editedChannelName}
                        onChange={(e) => setEditedChannelName(e.target.value)}
                        className="h-6 focus:outline-none"
                      />
                      <HiOutlineCheck
                        onClick={() => handleSubmitEdit(channel._id)}
                        color="secondary-text"
                        size={20}
                        className="ml-2"
                      />
                      <HiOutlineXMark
                        onClick={() => handleCancelEdit(channel.name)}
                        color="secondary-text"
                        size={20}
                        className="ml-1"
                      />
                    </>
                  ) : (
                    <div className="flex items-center justify-between w-[100%]">
                      <div className="flex items-center space-x-2">
                        <HiMiniSparkles color="secondary-text" size={20} />
                        <span className="flex-grow">{channel.name}</span>
                      </div>
                    </div>
                  )}
                  {!isSelectedEdit && isSelectedView && channel._id === channelId && (
                    <div className="flex items-center">
                      <div className="flex items-center space-x-2">
                        <HiMiniPencil
                          onClick={() => handleEdit(channel.name)
                          }
                          color="secondary-text"
                          size={16}
                          className="ml-2"
                        />
                        <HiOutlineTrash
                          onClick={() => handleDelete(channel._id)}
                          color="secondary-text"
                          size={16}
                          className="ml-1 mr-2"
                        />
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
          <div className="h-screen w-[100%] overflow-hidden">
            {channelId ? <ChatBox companion={companion} channelId={channelId} NAME={NAME} /> : null}
          </div>
        </div>
      )}
    </div>
  );
}