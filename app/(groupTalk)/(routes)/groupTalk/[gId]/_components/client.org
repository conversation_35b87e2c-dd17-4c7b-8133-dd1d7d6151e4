"use client";

import { ElementRef, useEffect, useRef, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Companion } from "@prisma/client";
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from "@/components/user-avatar";
import { faker } from "@faker-js/faker";
import { PiPersonSimpleRunBold } from "react-icons/pi";
import { LoadingCircle } from '@/components/loaders'
import './index.css'

//const NAME = faker.person.firstName();

interface GroupTalkClientProps {
  companion: Companion
  userName: string
};

export function GroupTalkClient({
  companion, userName
}: GroupTalkClientProps) {
  const messages = useQuery(api.messages.list);
  const sendMessage = useMutation(api.messages.send);
  const [newMessageText, setNewMessageText] = useState("");
  const [groupDiscussion, setGroupDiscussion] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const NAME = userName //faker.person.firstName()
  const scrollRef = useRef<ElementRef<"article">>(null);

  
  useEffect(() => {
    if (groupDiscussion && newMessageText) {
      const fetchData = async () => {
        const body = JSON.stringify({ newMessageText });
        const response = await fetch(`/api/autogen`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body,
        });
        
        if (response.ok) {
          const responseData = await response.json();
          console.log("response_data from autogen:", responseData);
          const sendDiscussionResult = async () => {
            await sendMessage({ body: responseData.content, author: responseData.from });
            setGroupDiscussion(false)
          }
          if (responseData.from !== "Client") {
            await sendDiscussionResult()
          }
                    
        } else {
          console.error("Error in fetch request:", response.statusText);
        }
      };
      fetchData();      
    }    
  }, [groupDiscussion, newMessageText]);
  

  useEffect(() => {
    scrollRef?.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  //console.log("messages: ", messages)

  return (
    <div className="chat">
      <header>
        <h1>Group Chat (You can use @gpt to talk to ChatGPT)</h1>
        <p>
          Connected as <strong>{NAME}</strong>
        </p>
      </header>
      <div className="flex-grow h-screen pb-[6rem] overflow-y-auto">
        {messages?.map((message) => (
          <article
            key={message._id}
            className={message.author === NAME ? "message-mine" : ""}
          >
            <div className="flex items-start">
              {message.author !== NAME && message.author !== "ChatGPT" && (
                <>
                  <PiPersonSimpleRunBold className="h-10 w-10" />
                  <div className="ml-1">
                    <div className="text-sm">{message.author}</div>
                    <p>{message.body}</p>
                  </div>
                  <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                    {new Date(message._creationTime).toLocaleTimeString()}
                  </div>
                </>
              )}
              {message.author === "ChatGPT" && (
                <>
                  <BotAvatar src={companion.src} />
                  <div className="ml-1">
                    <div className="text-sm">{message.author}</div>
                    <p>{message.body}</p>
                  </div>
                  <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                    {new Date(message._creationTime).toLocaleTimeString()}
                  </div>
                </>
              )}
              {message.author === NAME && (
                <>
                  <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                    {new Date(message._creationTime).toLocaleTimeString()}
                  </div>
                  <div className="mr-1">
                    <div className="text-sm">{message.author}</div>
                    <p>{message.body}</p>
                  </div>
                  <UserAvatar />
                </>
              )}
            </div>
          </article>
        ))}
        <article ref={scrollRef} />
      </div>

      <form
        onSubmit={async (e) => {
          e.preventDefault();
          setIsLoading(true)
          if (newMessageText.includes('@opinion')) {
            await setGroupDiscussion(true)
          }
          await sendMessage({ body: newMessageText, author: NAME });
          setNewMessageText("");
          setIsLoading(false)
        }}
      >
        <input
          value={newMessageText}
          onChange={(e) => setNewMessageText(e.target.value)}
          placeholder="Write a message…"
        />
        <button type="submit" disabled={!newMessageText}>
          {isLoading ? (
            <LoadingCircle />
          ) : null}
        </button>
      </form>
    </div>
  );
}