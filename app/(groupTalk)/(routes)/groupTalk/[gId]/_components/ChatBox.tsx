"use client";

import clsx from "clsx";
import { cn } from "@/lib/utils";
import { ElementRef, FormEvent, useEffect, useRef, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import Textarea from "react-textarea-autosize";
import FacePile from "@/components/Facepile";
import usePresence from "@/hooks/usePresence";
import { Companion } from "@prisma/client";
import { ChatFixedHeader } from '@/components/chat-fixed-header'
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from "@/components/user-avatar";
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { faker } from "@faker-js/faker";
import { PiPersonSimpleRunBold } from "react-icons/pi";
import { ArrowUp } from "lucide-react";
import { LoadingCircle } from '@/components/loaders'

//const NAME = faker.person.firstName();

interface ChatBoxProps {
  companion: Companion
  channelId: Id<"channels">
  NAME: string
};

const Emojis =
  "😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 🥰 😘 😗 😙 😚 😋 😛 😝 😜 🤪 😎 🤩 🥳 😏 😳 🤔 🤭 🤫 😶 😮 🤤 😵‍💫 🥴 🤑 🤠".split(
    " "
  );

const initialEmoji = Emojis[Math.floor(Math.random() * Emojis.length)];

export function ChatBox({
  companion, channelId, NAME
}: ChatBoxProps) {
  const messages = useQuery(api.messages.list, { channelId }) || [];
  const sendMessage = useMutation(api.messages.send);
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<ElementRef<"article">>(null);
  const [newMessageText, setNewMessageText] = useState("");
  const [groupDiscussion, setGroupDiscussion] = useState(false);
  const [allowJoin, setAllowJoin] = useState<boolean>(true)
  const [isLoading, setIsLoading] = useState(false); 
  const [myPresence, othersPresence, updateMyPresence] = usePresence(
    channelId,
    NAME,
    {
      name: NAME,
      emoji: initialEmoji,
    }
  );
  
  useEffect(() => {
    if (groupDiscussion && newMessageText) {
      const fetchData = async () => {
        const body = JSON.stringify({ newMessageText });
        const response = await fetch(`/api/autogen`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body,
        });
        
        if (response.ok) {
          const responseData = await response.json();
          console.log("response_data from autogen:", responseData);
          const sendDiscussionResult = async () => {
            await sendMessage({
              allowJoin,
              channel: channelId,
              companionName: companion.name!,
              body: responseData.content,
              author: responseData.from
            });
            setGroupDiscussion(false)
          }
          if (responseData.from !== "Client") {
            await sendDiscussionResult()
          }
                    
        } else {
          console.error("Error in fetch request:", response.statusText);
        }
      };
      fetchData();      
    }    
  }, [groupDiscussion, newMessageText]);
  

  useEffect(() => {
    scrollRef?.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  async function handleSendMessage(e: FormEvent) {
    e.preventDefault();
    setIsLoading(true)
    if (newMessageText.includes('@opinion')) {
      await setGroupDiscussion(true)
    }
    await sendMessage({
      allowJoin,
      channel: channelId,
      companionName: companion.name!,
      body: newMessageText,
      author: NAME,
    });
    setNewMessageText("");
    setIsLoading(false)
  }
  //console.log("messages: ", messages)

  const disabled = isLoading || newMessageText.length === 0;

  return (
    <div className="chat">
      <div className="fixed top-0 z-20 rounded-b-2xl w-[50%]">
        <ChatFixedHeader companion={companion} countMessages={messages?.length} />
      </div>
      <div className="flex flex-col h-screen mt-[1.5rem] overflow-hidden">      
        <header className={cn("absolute top-0 right-0 z-40 flex h-[70px] w-32 justify-end items-center px-4")}>        
          <p>
            <strong>
              <Select
                disabled={isLoading}
                defaultValue={myPresence.emoji}
                onValueChange={(value) => updateMyPresence({ emoji: value })}
              >
                <SelectTrigger className="h-[1rem] w-[4rem] border-0 px-3">
                  <SelectValue placeholder="Emoji" />
                </SelectTrigger>
                  <SelectContent>
                    {Emojis.map((e) => (
                      <SelectItem key={e} value={e}>{e}</SelectItem>
                    ))}
                  </SelectContent>
              </Select>
            </strong>
          </p>
        </header>
        <div className="flex h-[6rem] w-full z-10"></div>
        <div className="flex-grow h-screen pb-[10rem] mt-[2rem] overflow-y-auto">
          {messages?.map((message) => (
            <article
              key={message._id}
              className={message.author === NAME ? "message-mine" : ""}
            >
              <div className="flex items-start">
                {message.author !== NAME && message.author !== companion.name && (
                  <>
                    <PiPersonSimpleRunBold className="h-9 w-9" />
                    <div className="ml-1">
                      <div className="text-sm">{message.author}</div>
                      <p>{message.body}</p>
                    </div>
                    <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                      {new Date(message._creationTime).toLocaleTimeString()}
                    </div>
                  </>
                )}
                {message.author === companion.name && (
                  <>
                    <BotAvatar src={companion.src} />
                    <div className="ml-1">
                      <div className="text-sm">{message.author}</div>
                      <p>{message.body}</p>
                    </div>
                    <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                      {new Date(message._creationTime).toLocaleTimeString()}
                    </div>
                  </>
                )}
                {message.author === NAME && (
                  <>
                    <div className="items-end m-1 text-xs text-antiquewhite self-end whitespace-nowrap">
                      {new Date(message._creationTime).toLocaleTimeString()}
                    </div>
                    <div className="mr-1">
                      <div className="text-sm">{message.author}</div>
                      <p>{message.body}</p>
                    </div>
                    <UserAvatar />
                  </>
                )}
              </div>
            </article>
          ))}
          <article ref={scrollRef} />
        </div>
        <div>
          <FacePile othersPresence={othersPresence ?? []} />
          <form 
            ref={formRef}
            onSubmit={handleSendMessage}
            className="relative w-full max-w-screen-xl rounded-2xl bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg px-4 pb-2 pt-3 shadow-lg sm:pb-3 sm:pt-4"
          >
            <div className='flex items-center mt-1 mr-2 w-[3rem]'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Switch
                    className='flex items-center bg-primary/30 space-x-2'
                    id="allowJoin"
                    checked={allowJoin}
                    onCheckedChange={(e) => setAllowJoin(e)}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>邀請{companion.name!}加入討論</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Textarea
              value={newMessageText}
              onChange={(e) => setNewMessageText(e.target.value)}
              required
              rows={1}
              autoFocus
              placeholder={`You can use @${companion.name} to talk to AI as well…`}
              className="min-h-[30px] w-full pr-[3.2rem] resize-none bg-transparent text-white focus-within:outline-none"
            />
            <button
              className={clsx(
                "absolute inset-y-0 right-3 my-auto flex h-8 w-8 items-center justify-center rounded-md transition-all",
                disabled
                  ? "cursor-not-allowed bg-indigo-600"
                  : "bg-green-600 hover:bg-green-700",
              )}
              disabled={disabled}
            >
              {isLoading ? (
                <LoadingCircle />
              ) : (
                <ArrowUp size={18}
                  className={clsx(
                    "",
                    newMessageText.length === 0 ? "text-gray-300" : "text-white",
                  )}
                />
              )}
            </button>
          </form>
        </div>
      </div>
    </div>

  );
}