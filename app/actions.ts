'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { kv } from '@vercel/kv'

import { auth } from "@clerk/nextjs/server"
import { type Chat } from '@/lib/types'
import { 
  deleteMessages, 
  updatePersona, 
  updateMyPersona, 
  saveObservationToDatabase 
} from '@/lib/databaseUtils'

export async function getChats(userId?: string | null, companionId?: string | null) {  
  if (!userId || !companionId) {
    return []
  }

  try {
    const pipeline = kv.pipeline()
    const chats: string[] = await kv.zrange(`user:chat:${userId}${companionId}`, 0, -1, {
      rev: true
    })

    for (const chat of chats) {
      pipeline.hgetall(chat)
    }

    const results = await pipeline.exec()

    return results as Chat[]
  } catch (error) {
    return []
  }
}

export async function saveChat(chat: Chat) {
  const { userId } = await auth();

  if (userId) {
    const pipeline = kv.pipeline()
    pipeline.hmset(`chat:${chat.id}`, chat)
    pipeline.zadd(`user:chat:${chat.userId}${chat.companionId}`, {
      score: Date.now(),
      member: `chat:${chat.id}`
    })
    await pipeline.exec()
  } else {
    return
  }
}

export async function getChat(id: string, userId: string, companionId: string) {
  const chat = await kv.hgetall<Chat>(`chat:${id}`)

  if (!chat || (userId && chat.userId !== userId || (companionId && chat.companionId !== companionId))) {
    return null
  }

  return chat
}

export async function removeChat({ 
  id, 
  companionId, 
  path 
}: { 
  id: string; 
  companionId: string; 
  path: string 
}) {
  const { userId } = await auth();

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const uid = await kv.hget<string>(`chat:${id}`, 'userId')

  if (uid !== userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const cid = await kv.hget<string>(`chat:${id}`, 'companionId')
  if (cid !== companionId) {
    return {
      error: 'Unauthorized'
    }
  }
  
  await kv.del(`chat:${id}`)
  await kv.zrem(`user:chat:${userId}${companionId}`, `chat:${id}`)

  revalidatePath(path)
  return revalidatePath(path)
}

export async function clearChats({
  companionId, 
  path 
}: { 
  companionId: string; 
  path: string 
}) {
  const { userId } = await auth()

  if (!userId || !companionId) {
    return {
      error: 'Unauthorized'
    }
  }

  const chats: string[] = await kv.zrange(`user:chat:${userId}${companionId}`, 0, -1)
  if (!chats.length) {
  return redirect(path)
  }
  const pipeline = kv.pipeline()

  for (const chat of chats) {
    pipeline.del(chat)
    pipeline.zrem(`user:chat:${userId}${companionId}`, chat)
  }

  await pipeline.exec()

  revalidatePath(path)
  return redirect(path)
}

export async function getSharedChat(id: string) {
  const chat = await kv.hgetall<Chat>(`chat:${id}`)

  if (!chat || !chat.sharePath) {
    return null
  }

  return chat
}

export async function shareChat(id: string) {
  const { userId } = await auth()

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  const chat = await kv.hgetall<Chat>(`chat:${id}`)

  if (!chat || chat.userId !== userId) {
    return {
      error: 'Something went wrong'
    }
  }

  const payload = {
    ...chat,
    sharePath: `/share/${chat.id}`
  }

  await kv.hmset(`chat:${chat.id}`, payload)

  return payload
}

export async function updatePersonaAction(
  update: { 
    name: string;
    nickName?: string,
    age?: number,
    gender?: string,
    traits?: string,
    status?: string,
    currentRole?: string,
    image?: string 
  },
  path: string
) {
  const { userId } = await auth()

  if (!userId) {
    return {
      error: 'Unauthorized'
    }
  }

  if (update?.nickName && update?.age 
    && update?.gender && update?.currentRole) 
  {
    await updateMyPersona(userId, update);
  } else {
    await updatePersona(userId, update);
  }
  
  revalidatePath(path);
}

export async function deleteMessageAction({
  companionId,
  path,
}: {
  companionId: string,
  path: string,
}) {
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  await deleteMessages(userId, companionId);
  revalidatePath(path)

  if (!deleteMessages) {
    throw new Error("Messages not deleted");
  }
}

export async function saveObservationAction({
  companionId,
  title,
  message,
  roomId,
  path,
}: {
  companionId: string;
  title: string;
  message: string;
  roomId: string;
  path: string;
}) {
  const { userId, redirectToSignIn } = await auth();

  if (!userId) {
    return redirectToSignIn();
  }

  await saveObservationToDatabase(
    companionId, userId, title, message, roomId
  )
  revalidatePath(path)

  if (!saveObservationToDatabase) {
    throw new Error("Observation not saved");
  }
}