'use client';

import axios from 'axios';
import React, { Fragment, useMemo, useState, useEffect } from 'react'
import { useRouter } from "next/navigation";
import { FieldValues, SubmitHandler, useForm } from 'react-hook-form';
import Input from "@/components/organization/Input";
import SelectOne from '@/components/organization/SelectOne';
import { Dialog, Transition } from '@headlessui/react';
import { IoClose, IoTrash } from 'react-icons/io5';
import { MdApps, MdOutlineGroupAdd, MdOutlineGroupRemove, MdOutlineDeleteSweep } from 'react-icons/md';
import { toast } from 'react-hot-toast';
import { Persona } from "@prisma/client";
import { OrganizationWithMembersAndUsers } from '@/lib/types'

import { OrganizationSettingModal } from '@/components/organization/org-setting-modal'
import { OrganizationInviteModal } from '@/components/organization/org-invite-modal'
import { OrganizationUserModal } from '@/components/organization/org-user-modal'
import { OrganizationDeleteModal } from '@/components/organization/org-delete-modal';
import { useOrganizations } from '@/components/providers/OrganizationContext';

interface OrganizationDrawerProps {
  data: OrganizationWithMembersAndUsers | null
  users: Persona[]
  path: string
}

const StatusOptions = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'PENDING', label: 'Pending' },
  { value: 'SUSPENDED', label: 'Suspended' },
  { value: 'CLOSED', label: 'Closed' },
  { value: 'BLOCKED', label: 'Blocked' },
]

export const OrganizationDrawer: React.FC<OrganizationDrawerProps> = ({
  data,
  users,
  path,
}) => {
  const router = useRouter();
  const { refreshOrganizations } = useOrganizations()
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [inviteModalOpen, setInviteModalOpen] = useState(false);
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [settingModalOpen, setSettingModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(true);

  const title = useMemo(() => {
    return data ? data.name : '';
  }, [data?.name]);
  
  const statusText = useMemo(() => {
    return data?.members ? `${data.members.length} members` : '0 members';
  }, [data]);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FieldValues>({
    defaultValues: {
      status: data?.status || '',
    },
  });
  
  const watchStatus = watch('status');
  //console.log("watchStatus: ", watchStatus)

  useEffect(() => {
    // Update form values when organization data changes
    //console.log("data: ", data)
    if (data) {
      Object.keys(data).forEach((key) => {
        setValue(key, (data as Record<string, any>)[key]);
      });
      //if (data?.status) {
        // Set the initial value of the Select field to the label
      //  setValue('status', data.status, { shouldValidate: true });
      //}
    }    
  }, [data, setValue]);

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    setIsLoading(true);
    const endpoint = `/api/organization/update`;
    const method = 'PUT';
    
    axios.request({
      method,
      url: endpoint,
      data: { ...data, path },
    })
    .then(() => {
      toast.success('Organization updated successfully!');
      //onClose();
      refreshOrganizations()
      router.push(`/admin/dashboard`);
    })
    .catch(() => toast.error('Something went wrong!'))
    .finally(() => setIsLoading(false));
  };

  return (
    <>
      {data && (
        <OrganizationSettingModal
          isOpen={settingModalOpen}
          onClose={() => setSettingModalOpen(false)}
          organization={data}
          path={path}
        />
      )}
      {data && (
        <OrganizationDeleteModal
          orgId={data.id}
          isOpen={confirmOpen} 
          onClose={() => setConfirmOpen(false)}
          path={path}
        />
      )}
      {data && (
        <OrganizationInviteModal
          isOpen={inviteModalOpen}
          onClose={() => setInviteModalOpen(false)}
          organization={data}
          users={users} 
          path={path}
        />
      )}
      {data && (
        <OrganizationUserModal
          isOpen={userModalOpen}
          onClose={() => setUserModalOpen(false)}
          organization={data}
          users={users}
          path={path}
        />
      )}
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setIsOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-500"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-500"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
          <div className="fixed inset-0 bg-black bg-opacity-40" />
        </Transition.Child>

          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 overflow-hidden">
              <div className="pointer-events-none h-full 2xl:h-[80%] fixed inset-y-0 right-[50%] top-[50%] translate-x-[50%] translate-y-[-50%] flex max-w-full rounded-lg pl-10">
                <Transition.Child
                  as={Fragment}
                  enter="transform transition ease-in-out duration-500"
                  enterFrom="translate-x-full"
                  enterTo="translate-x-0"
                  leave="transform transition ease-in-out duration-500"
                  leaveFrom="translate-x-0"
                  leaveTo="translate-x-full"
                >
                  <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                    <div className="flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl" style={{ background: 'var(--gradient)' }}>
                      <div className="px-4 sm:px-6">
                        <div className="flex items-start justify-end">
                          <div className="ml-3 flex h-7 items-center">
                            <button
                              type="button"
                              className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                              onClick={() => setIsOpen(false)}
                            >
                              <span className="sr-only">Close panel</span>
                              <IoClose size={24} aria-hidden="true" />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="relative mt-4 2xl:mt-6 flex-1 px-4 sm:px-6">
                        <div className="flex flex-col items-center">
                          <div className="text-gray-500">
                            {title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {statusText}
                          </div>
                          <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="mt-4 2xl:mt-6 flex flex-col gap-y-2">
                              {/* Organization fields */}
                              <Input disabled={isLoading} label="Name" id="name" errors={errors} register={register} />
                              <Input disabled={isLoading} label="Description" id="description" errors={errors} register={register} />
                              {/* Add more organization fields here */}
                              <Input disabled={isLoading} label="Email" id="email" errors={errors} register={register} />
                              <Input disabled={isLoading} label="Website" id="website" errors={errors} register={register} />
                              <Input disabled={isLoading} label="Address" id="address" errors={errors} register={register} />
                              <Input disabled={isLoading} label="Phone Number" errors={errors} id="phoneNumber" register={register} />
                              <Input disabled={isLoading} label="Status" errors={errors} id="status" register={register} />
                              {/*<SelectOne
                                disabled={isLoading}
                                label="Status"
                                value={watchStatus}
                                onChange={(newValue, actionMeta) => {
                                  console.log('New Value:', newValue);
                                  console.log('Action Meta:', actionMeta);
                                  setValue('status', newValue, { shouldValidate: true });
                                }}
                                options={StatusOptions}
                              />*/}
                            </div>
                            {/* Submit button */}
                            <div className="mt-6 flex items-center justify-end gap-x-6">
                              {/*<Button
                                disabled={isLoading}
                                onClick={() => setIsOpen(false)}
                                type="button"
                                secondary
                              >
                                Cancel
                              </Button>*/}
                              {/*<Button disabled={isLoading} type="submit">Update</Button>*/}
                            </div>
                          </form>
                          <div className="flex gap-10 my-8">
                            <div onClick={() => {
                              setSettingModalOpen(true)
                              setIsOpen(false);
                            }} className="flex flex-col gap-3 items-center cursor-pointer hover:opacity-75">
                              <div className="w-10 h-10 bg-neutral-100 rounded-full flex items-center justify-center">
                                <MdApps color="blue" size={20} />
                              </div>
                              <div className="text-sm font-light text-neutral-600">
                                Settings
                              </div>
                            </div>
                            <div onClick={() => {
                              setInviteModalOpen(true)
                              setIsOpen(false);
                            }} className="flex flex-col gap-3 items-center cursor-pointer hover:opacity-75">
                              <div className="w-10 h-10 bg-neutral-100 rounded-full flex items-center justify-center">
                                <MdOutlineGroupAdd color="blue" size={20} />
                              </div>
                              <div className="text-sm font-light text-neutral-600">
                                Invite
                              </div>
                            </div>
                            <div onClick={() => {
                              setUserModalOpen(true);
                              setIsOpen(false);
                            }} className="flex flex-col gap-3 items-center cursor-pointer hover:opacity-75">
                              <div className="w-10 h-10 bg-neutral-100 rounded-full flex items-center justify-center">
                                <MdOutlineGroupRemove color="blue" size={20} />
                              </div>
                              <div className="text-sm font-light text-neutral-600">
                                Members
                              </div>
                            </div>
                            <div onClick={() => setConfirmOpen(true)} className="flex flex-col gap-3 items-center cursor-pointer hover:opacity-75">
                              <div className="w-10 h-10 bg-neutral-100 rounded-full flex items-center justify-center">
                                <MdOutlineDeleteSweep color="red" size={20} />
                              </div>
                              <div className="text-sm font-light text-neutral-600">
                                Delete
                              </div>
                            </div>
                          </div>
                        <div className="w-full pb-5 pt-5 sm:px-0 sm:pt-0">
                      </div>
                        </div>
                      </div>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </>
  )
}