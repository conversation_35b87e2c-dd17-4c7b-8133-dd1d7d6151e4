"use client";

import Link from "next/link";
import { User<PERSON><PERSON><PERSON> } from "@clerk/nextjs";

import { Admin } from "@prisma/client";
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import ThemeChanger from "@/components/nextly/DarkSwitch";
import { LocaleSelection } from '@/components/ui/locale-select'
import { cn } from "@/lib/utils";
import { MobileSidebar } from "./mobile-sidebar";
import { Button } from "@/components/ui/button";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { LayoutDashboard } from "lucide-react"


interface NavbarProps {
  isPro: boolean;
  admin?: Admin | null;
  organizations?: OrganizationWithMembersAndUsers[] | null;
}

export const Navbar = ({
  isPro,
  admin,
  organizations
}: NavbarProps) => {
  const isNavbar = true
  const title = `健心坊 | ComfyMinds Lab`
  const words = [
    {
      text: "健心坊",
    },
    {
      text: "|",
    },
    {
      text: "ComfyMinds",
    },
    {
      text: "Lab",
    },
  ];
  return ( 
    <div className="fixed w-full z-50 flex justify-between items-center py-2 px-4 h-16 border-b border-primary/10 bg-secondary"
      style={{ background: 'var(--gradient)' }}
    >
      <div className="flex items-center">
        <MobileSidebar isPro={isPro} admin={admin} organizations={organizations || null}/>
        <Link href="/admin/dashboard">
          <h1 className={cn("hidden md:block text-xl md:text-3xl font-bold text-primary-text")}>
            <TextGenerateEffect words={title} />
          </h1>
        </Link>
      </div>
      <div className="flex items-center gap-x-3">
        <Link href="/admin/dashboard">
          <Button size="sm" variant={"ghost"}>
            <LayoutDashboard className="mr-2 h-4 w-4" /> 
            Dashboard
          </Button>
        </Link>
        <LocaleSelection />
        <ThemeChanger />
        <UserButton afterSignOutUrl="/sign-in" />
      </div>
    </div>
  );
}