"use client";

import React, { useState } from "react"
import { Home, Plus, Settings } from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HiMiniChartBar, HiMiniPencilSquare, HiMiniRectangleGroup } from 'react-icons/hi2';
import { usePathname, useRouter } from "next/navigation";

import { cn } from "@/lib/utils";
import { useProModal } from "@/hooks/use-pro-modal";
import { Admin } from '@prisma/client';
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { ChevronDownIcon } from "@radix-ui/react-icons"
 
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface SidebarProps {
  isPro: boolean;
  admin?: Admin | null;
  organizations?: OrganizationWithMembersAndUsers[] | null;
}

export const Sidebar = ({
  isPro,
  admin,
  organizations = []
}: SidebarProps) => {
  const proModal = useProModal();
  const router = useRouter();
  const pathname = usePathname();
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string | null>(organizations && organizations?.length > 0 ? organizations[0].id : null);

  const onNavigate = (url: string, pro: boolean) => {
    if (pro && !isPro) {
      return proModal.onOpen();
    }

    return router.push(url);
  }

  const routes = [
    {
      icon: Home,
      href: '/',
      label: "Home",
      pro: false,
    },
    {
      icon: HiChartPie,
      href: '/admin/dashboard',
      label: "Overview",
      pro: true,
    },
    {
      icon: HiMiniRectangleGroup,
      href: '/admin/usage',
      label: "Usage",
      pro: true,
    },
    {
      icon: HiMiniChartBar,
      href: '/admin/transaction',
      label: "Transaction",
      pro: true,
    },
    {
      icon: Settings,
      href: '/admin/settings',
      label: "Settings",
      pro: false,
    },
  ];

  const handleCreateOrganization = () => {
    router.push("/admin/organizations/new");
  };

  const handleNavigateOrganization = (organizationId: string) => {
    setSelectedOrganizationId(organizationId)
    router.push(`/admin/organizations/${organizationId}`);
  };

  const selectedOrganization = organizations?.find(org => org.id === selectedOrganizationId);

  return (
    <div className="space-y-4 flex flex-col h-full text-primary z-99">
      <div className="p-3 flex-1 flex justify-center">
        <div className="space-y-2">
          {admin && organizations && (
            <>
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-1 text-sm font-bold overflow-hidden text-ellipsis whitespace-nowrap my-8" style={{ width: '135px' }}>
                  {selectedOrganization ? selectedOrganization.name : 'Organization'}
                  <ChevronDownIcon />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="center">
                  <DropdownMenuItem className="p-0"
                  >
                    <Button
                      disabled={!admin || admin.role != 'ADMIN'}
                      onClick={handleCreateOrganization}
                      size="sm" 
                      type="button"
                      variant={"ghost"}
                    >
                      <Plus className="size-4 mr-2" />
                      Create Organization
                    </Button>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {organizations.map((organization) => (
                    <DropdownMenuItem
                      key={organization.id}
                      onClick={() => handleNavigateOrganization(organization.id)}
                    >
                      {organization.name}
                      <HiMiniPencilSquare className="size-4 ml-2" />
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
          {routes.map((route) => (
            <div
              onClick={() => onNavigate(route.href, route.pro)}
              key={route.href}
              className={cn(
                "text-muted-foreground text-xs group flex p-3 my-6 w-full justify-start font-medium cursor-pointer hover:text-primary hover:bg-primary/10 rounded-lg transition",
                pathname === route.href && "bg-primary/10 text-primary",
              )}
            >
              <div className="flex flex-col gap-y-2 items-center flex-1">
                <route.icon className="h-5 w-5" />
                {route.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};