'use client'

import axios from 'axios';
import React, { useState } from 'react'
import { useRouter } from "next/navigation";
import { 
  FieldValues, 
  SubmitHandler, 
  useForm 
} from 'react-hook-form';
import Modal from '@/components/modals/Modal';
import Input from "@/components/organization/Input";
import Select from '@/components/inputs/Select';
import SelectOne from '@/components/organization/SelectOne';
import Button from '@/components/Button';
import { toast } from 'react-hot-toast';
import { Persona } from "@prisma/client";

interface OrganizationModalProps {
  users: Persona[];
  path: string
}

export const OrganizationCreateModal: React.FC<OrganizationModalProps> = ({ 
  users = [],
  path
}) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: {
      errors,
    }
  } = useForm<FieldValues>({
    defaultValues: {
      name: '',
      description: '',
      email: '',
      website: '',
      address: '',
      phoneNumber: '',
      status: '',
      members: [],
    }
  });

  const members = watch('members');

  const handleOnClose = () => {
    router.push("/admin/dashboard");
  };

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    setIsLoading(true);
    const endpoint = '/api/organization/create';
    const method = 'POST';
    
    axios.request({
      method,
      url: endpoint,
      data: { ...data, path },
    })
    .then(() => {
      toast.success('Organization created successfully!');
    })
    .catch(() => toast.error('Something went wrong!'))
    .finally(async () => {
      setIsLoading(false)
      router.push("/admin/dashboard")
      router.refresh()
    })
  };

  return (
    <Modal isOpen={isOpen} onClose={() => setIsOpen(true)}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="px-4 space-y-12">
          <div className="border-b border-gray-900/10 pb-12">
            <h2 
              className="
                text-base 
                font-semibold 
                leading-7 
                text-gray-900
              "
            >
              {'Create Organization'}
            </h2>
            <div className="mt-10 flex flex-col gap-y-2">
              {/* Organization fields */}
              <Input disabled={isLoading} label="Name" id="name" errors={errors} register={register} />
              <Input disabled={isLoading} label="Description" id="description" errors={errors} register={register} />
              {/* Add more organization fields here */}
              <Input disabled={isLoading} label="Email" id="email" errors={errors} register={register} />
              <Input disabled={isLoading} label="Website" id="website" errors={errors} register={register} />
              <Input disabled={isLoading} label="Address" id="address" errors={errors} register={register} />
              <Input disabled={isLoading} label="Phone Number" errors={errors} id="phoneNumber" register={register} />
              <SelectOne
                disabled={isLoading}
                label="Status"
                value={watch('status')}
                onChange={(newValue, actionMeta) => {
                  console.log('New Value:', newValue);
                  console.log('Action Meta:', actionMeta);
                  setValue('status', newValue, { shouldValidate: true });
                }}
                options={[
                  { value: 'ACTIVE', label: 'Active' },
                  { value: 'INACTIVE', label: 'Inactive' },
                  { value: 'PENDING', label: 'Pending' },
                  { value: 'SUSPENDED', label: 'Suspended' },
                  { value: 'CLOSED', label: 'Closed' },
                  { value: 'BLOCKED', label: 'Blocked' },
                ]}
              />
              {/* Member selection */}
              <Select
                disabled={isLoading}
                label="Members" 
                options={users.map((user) => ({ 
                  value: user.userId, 
                  label: user.email 
                }))} 
                onChange={(value) => setValue('members', value, { 
                  shouldValidate: true 
                })} 
                value={members}
              />
            </div>
          </div>
        </div>
        {/* Submit button */}
        <div className="mt-6 flex items-center justify-end gap-x-6">
        <Button
            disabled={isLoading}
            onClick={handleOnClose} 
            type="button"
            secondary
          >
            Cancel
          </Button>
          <Button disabled={isLoading} type="submit">Create</Button>
        </div>
      </form>
    </Modal>
  );
};