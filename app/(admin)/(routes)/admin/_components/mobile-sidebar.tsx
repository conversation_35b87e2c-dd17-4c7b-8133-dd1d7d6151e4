import { Menu } from "lucide-react";

import { Admin } from "@prisma/client";
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Sidebar } from "./sidebar";

export const MobileSidebar = ({
  isPro,
  admin,
  organizations
}: {
  isPro: boolean;
  admin?: Admin | null;
  organizations?: OrganizationWithMembersAndUsers[] | null;
}) => {
  return (
    <Sheet>
      <SheetTrigger className="md:hidden pr-4">
        <Menu />
      </SheetTrigger>
      <SheetContent side="left" className="p-0 bg-secondary pt-10 w-32">
        <Sidebar isPro={isPro} admin={admin} organizations={organizations || null}/>
      </SheetContent>
    </Sheet>
  );
};
