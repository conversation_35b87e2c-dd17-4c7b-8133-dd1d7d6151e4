import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import { checkRole } from "@/utils/roles";
import { Suspense } from 'react'
import prismadb from '@/lib/prismadb';
import Loading from '@/app/loading';
import getOrganizations from "@/app/actions/getOrganizations";
import { Overview } from '@/components/credits/Overview'

export default async function Page() {
  const { userId } = await auth()

  if (!userId) {
    redirect("/sign-in");
  }
  if (!checkRole("admin")) {
    redirect("/");
  }

  try {
    const admin = await prismadb.admin.findUnique({
      where: {
        id: userId,
      },
      cacheStrategy: {
        swr: 60,
        ttl: 30
      },
      include: {
       adminOrganizations: true, // Include the organization in the result
      },
    });

    if (admin) {
      const organizations = await getOrganizations({ adminId: admin.id });
      return (
        <div className="flex flex-col items-center h-full w-full mt-1 overflow-y-auto">
          <Suspense fallback={<Loading />}>
            <Overview organizations={organizations} />
          </Suspense>
        </div>
      );
    } else {
      redirect("/");
    }
  } catch (error) {
    console.error('Error fetching members:', error);
    return <div className="flex flex-col w-full h-full items-center justify-center">Error occurred while fetching members.</div>;
  }
}