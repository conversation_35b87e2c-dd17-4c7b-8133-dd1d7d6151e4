import '@/app/scrollbar-hidden.css'

import { redirect } from 'next/navigation'
import { Suspense } from 'react'
import Loading from '@/app/loading'
import { OrganizationProvider } from '@/components/providers/OrganizationContext';
import getAdmin from "@/app/actions/getAdmin";
import getCurrentProfile from "@/app/actions/getCurrentProfile";
import getOrganizations from "@/app/actions/getOrganizations";

import { checkRole } from "@/utils/roles";
import { Admin } from '@prisma/client';
import { OrganizationWithMembersAndUsers } from '@/lib/types'
import { Navbar } from "./_components/navbar";
import { Sidebar } from "./_components/sidebar";
import { checkSubscription } from "@/lib/subscription";

const AdminLayout = async ({
  children
}: {
  children: React.ReactNode;
}) => {
  if (!checkRole(["admin"])) {
    redirect("/");
  }
  const currentProfile = await getCurrentProfile();
  const isPro = await checkSubscription();

  // Fetch the organization associated with the current user
  let admin: Admin | null = null;
  let organizations: OrganizationWithMembersAndUsers[] | null = null;

  if (currentProfile) {
    // Fetch the Admin record associated with the current user's profile
    admin = await getAdmin({ id: currentProfile.externalId });
   
    if (admin) {
       // If an Admin record is found, the user is an Admin
       // The organization associated with this admin is already included in the admin record
       organizations = await getOrganizations({ adminId: admin.id });
       //console.log("organizations", organizations)
    }
   }


   return (
    <div className="h-screen overflow-hidden">
      <OrganizationProvider>
        <Suspense fallback={<Loading />}>
          <Navbar isPro={isPro} admin={admin} organizations={organizations || null} />
        </Suspense>
        <Suspense fallback={<Loading />}>
        <div className="hidden md:flex mt-16 h-full w-30 flex-col fixed inset-y-0">
          <Sidebar isPro={isPro} admin={admin} organizations={organizations || null} />
        </div>
        </Suspense>
        <Suspense fallback={<Loading />}>
          <main className="md:pl-30 pt-16 h-full">
            {children}
          </main>        
        </Suspense>
      </OrganizationProvider>
    </div>   
  );
}
 
export default AdminLayout;