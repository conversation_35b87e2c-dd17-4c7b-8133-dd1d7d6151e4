"use client";

import { usePathname, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";

export const SearchUsers = () => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-full max-w-xl mx-auto">
      <form
        onSubmit={async (e) => {
          e.preventDefault();
          const form = e.currentTarget;
          const formData = new FormData(form);
          const queryTerm = formData.get("search") as string;
          router.push(pathname + "?search=" + queryTerm);
        }}
        className="flex flex-col space-y-4"
      >
        <label htmlFor="search" className="text-lg font-medium">
          Search for Users
        </label>
        <input
          id="search"
          name="search"
          type="text"
          className="p-2 border border-gray-300 rounded-md"
        />
        <Button type="submit" className="bg-blue-500 hover:bg-blue-700 text-secondary-text font-bold py-2 px-4 rounded">
          Submit
        </Button>
      </form>
    </div>
  );
};
