import { redirect } from "next/navigation";
import { checkRole } from "@/utils/roles";
import { SearchUsers } from "./_search-users";
import { auth } from "@clerk/nextjs/server"
import { clerkClient } from "@clerk/nextjs/server";
import { setRole } from "./_actions";
import { Button } from "@/components/ui/button";

type SearchParams = Promise<{
  search?: string;
}>;

interface AdminProps {
  searchParams: Promise<SearchParams>;
}

export default async function AdminDashboard(props: AdminProps) {
  const searchParams = await props.searchParams;
  const {redirectToSignIn, userId} = await auth();
  if (!userId) {
    redirectToSignIn()
  }
  
  if (!checkRole(["admin"]) && userId !== "user_2UP0QlBDvTPYf6VdPsjXPgiK3ow") {
    redirect("/");
  }

  const query = searchParams.search;
  const client = await clerkClient();
  const users = query ? (await client.users?.getUserList({ query })).data : [];

  return (
    <div className="min-h-screen flex flex-col items-center p-4 space-y-6">
      <h1 className="text-lg font-bold text-center mb-6">Admin Dashboard</h1>

      <div className="w-full max-w-xl">
        <SearchUsers />
      </div>

      {users.map((user) => (
        <div key={user.id} className="w-full max-w-xl shadow-md rounded-lg p-4 mb-4">
          <div className="font-semibold">{user.firstName} {user.lastName}</div>
          <div className="text-sm text-gray-600">
            {
              user.emailAddresses.find(
                (email) => email.id === user.primaryEmailAddressId
              )?.emailAddress
            }
          </div>
          <div className="text-sm text-gray-600">{user.publicMetadata.role as string}</div>
          
          <div className="flex space-x-4 mt-4">
            <form action={setRole} className="flex-1">
              <input type="hidden" value={user.id} name="id" />
              <input type="hidden" value="admin" name="role" />
              <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-secondary-text font-bold py-2 px-4 rounded">
                Make Admin
              </Button>
            </form>
            <form action={setRole} className="flex-1">
              <input type="hidden" value={user.id} name="id" />
              <input type="hidden" value="moderator" name="role" />
              <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-secondary-text font-bold py-2 px-4 rounded">
                Make Moderator
              </Button>
            </form>
          </div>
        </div>
      ))}
    </div>
  );
}
