import { redirect } from 'next/navigation'

import { auth } from "@clerk/nextjs/server";
import { OrganizationCreateModal } from "../../_components/org-create-modal";
import getUsers from "@/app/actions/getUsers";;


export interface GenUIdPageProps {
  params: Promise<{
    ichatId: string
  }>
}

export default async function NewOrganizationPage(props: GenUIdPageProps) {
  const { userId } = await auth()
  const users = await getUsers();
  const path = '/admin'

  if (!userId) {
    redirect(`/sign-in`)
  }
  const params = await props.params;
  return <OrganizationCreateModal users={users} path={path} /> 
}