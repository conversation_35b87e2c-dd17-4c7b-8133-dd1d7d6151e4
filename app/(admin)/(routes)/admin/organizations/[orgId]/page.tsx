
import { OrganizationDrawer } from "@/app/(admin)/(routes)/admin/_components/org-edit-modal";
import getCurrentProfile from "@/app/actions/getCurrentProfile";
import getOrganization from "@/app/actions/getOrganization";
import getAdmin from "@/app/actions/getAdmin";
import getUsers from "@/app/actions/getUsers";

import { Admin } from '@prisma/client';
import { OrganizationWithMembersAndUsers } from '@/lib/types'


export interface OrgIdPageProps {
  params: Promise<{
    orgId: string
  }>
}

export default async function NewOrganizationPage(props: OrgIdPageProps) {
  const params = await props.params;

  const currentProfile = await getCurrentProfile();
  const users = await getUsers();
  const path = '/admin'

  // Fetch the organization associated with the current user
  let admin: Admin | null = null;
  let organization: OrganizationWithMembersAndUsers | null = null;

  if (currentProfile) {
    // Fetch the Admin record associated with the current user's profile
    admin = await getAdmin({ id: currentProfile.externalId })
    
    if (admin) {
      // If an Admin record is found, the user is an Admin
      // The organization associated with this admin is already included in the admin record
      organization = await getOrganization({ orgId: params.orgId });
      //console.log("organizations", organizations)
    }
  }

  return <OrganizationDrawer data={organization} users={users} path={path} />
}