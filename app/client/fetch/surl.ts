import { DocumentType, ImageType } from "@/constants";


export type UrlDetailType = DocumentType | ImageType;

export type URLDetail = {
  url: string;
  size: number;
  type: UrlDetailType;
};

export type URLDetailContent = URLDetail & {
  content?: string;
};

export const isURL = (text: string) => {
  const isUrlRegex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
  return isUrlRegex.test(text);
};

/*export async function fetchSiteContent(
  site: string,
): Promise<URLDetailContent> {
  const response = await fetch(`/api/upload/fetch?site=${site}`);
  const data = await response.json();
  if (!response.ok) throw new Error(data.error);
  return data as URLDetailContent;
}*/
