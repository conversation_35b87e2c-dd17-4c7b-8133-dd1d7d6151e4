@tailwind base;
@tailwind components;
@tailwind utilities;


html,
body,
:root {
  height: 100%;
}

@layer base {
  :root {
    --gradient: linear-gradient(to top left,#43C6AC,#F8FFAE);
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
 
    --primary: 24 9.8% 10%;
    --primary-foreground: 60 9.1% 97.8%;
 
    --secondary: 500 45.8% 91.9%;
    --secondary-foreground: 24 9.8% 10%;
 
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
 
    --accent: 60 4.8% 85.9%;
    --accent-foreground: 24 9.8% 10%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;

    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 20 14.3% 4.1%;
 
    --radius: 0.5rem;

    --primary-text: #111827;
    --secondary-text: rgb(0, 0, 0);
    --bubbles-background: rgb(238, 228, 228);
    --bubbles-mine-background: #3b71ca;
  }

  .dark {
    /* CSS: .bg-gradient { background: var(--gradient) } */
    /* --gradient: linear-gradient(to top left,#141517,#024b11,#226847,#425709,#1b626e,#013529); */
    --gradient: linear-gradient(to top left,#3f51b1,#5a55ae,#7b5fac,#8f6aae,#a86aa4,#cc6b8e,#f18271,#f3a469,#cfa65e);

    --background: 237 22.75% 4.48%;
    --foreground: 237 3.5% 97.8%;

    --muted: 237 17.5% 16.8%;
    --muted-foreground: 237 3.5% 80.6%;

    --popover: 237 58% 7.280000000000001%;
    --popover-foreground: 237 3.5% 97.8%;

    --card: 237 58% 7.280000000000001%;
    --card-foreground: 237 3.5% 97.8%;

    --border: 237 17.5% 16.8%;
    --input: 237 17.5% 16.8%;

    --primary: 237 35% 56%;
    --primary-foreground: 237 3.5% 5.6000000000000005%;

    --secondary: 237 17.5% 16.8%;
    --secondary-foreground: 237 3.5% 97.8%;

    --accent: 237 17.5% 16.8%;
    --accent-foreground: 237 3.5% 97.8%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 237 3.5% 97.8%;

    --ring: 237 35% 56%;
    
    --radius: 0.75rem;

    --primary-text: #98aedd;
    --secondary-text: rgb(240, 233, 233);
    --bubbles-background: rgb(59, 57, 57);
    --bubbles-mine-background: #3b71ca;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.no-visible-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}

.keyboard-visible {
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
}

.leaflet-popup-content p {
  margin: 0 !important;
}

/* leaflet made me do it.. */
.bring-to-front {
  z-index: 99999 !important;
}

.bring-to-front-modal {
  z-index: 99999999 !important;
}
.rate-limit-modal {
  z-index: 999999999999 !important;
}

/* tradingview mobile */
.my-5.tradingview-widget-container {
  height: 150px !important;
}

/* Markdown styles */
/* Markdown styles */
.markdown-container {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.markdown-container h1,
.markdown-container h2,
.markdown-container h3,
.markdown-container h4,
.markdown-container h5,
.markdown-container h6 {
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.3em;
}

.markdown-container h1 {
  font-size: 1.5em;
}

.markdown-container h2 {
  font-size: 1.4em;
}

.markdown-container h3 {
  font-size: 1.3em;
}

.markdown-container h4 {
  font-size: 1.2em;
}

.markdown-container h5 {
  font-size: 1.1em;
}

.markdown-container h6 {
  font-size: 1em;
}

.markdown-container p {
  margin-bottom: 1em;
}

.markdown-container strong {
  font-weight: bold;
}

.markdown-container em {
  font-style: italic;
}

.markdown-container a {
  color: #000;
  text-decoration: underline;
}

.markdown-container code {
  font-family: monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-container pre {
  font-family: monospace;
  font-size: 0.9em;
  padding: 1em;
  overflow: auto;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-container pre code {
  padding: 0;
  background-color: transparent;
}

.markdown-container ul,
.markdown-container ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

.markdown-container ul li,
.markdown-container ol li {
  margin-bottom: 0.5em;
}

.markdown-container ul li {
  list-style-type: disc;
}

.markdown-container ol li {
  list-style-type: decimal;
}

.markdown-container blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.markdown-container hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1.5em 0;
}

