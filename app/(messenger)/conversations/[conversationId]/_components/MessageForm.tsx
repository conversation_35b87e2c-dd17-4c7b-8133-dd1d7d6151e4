"use client";

import { ChatRequestOptions } from "ai";
import { 
    HiPaperAirplane, 
  } from "react-icons/hi2";
import { ChangeEvent, FormEvent } from "react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { LoadingCircle } from '@/components/loaders'

interface MessageFormProps {
  input: string;
  handleInputChange: (e: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>) => void;
  onSubmit: (e: FormEvent<HTMLFormElement>, chatRequestOptions?: ChatRequestOptions | undefined) => void;
  isLoading: boolean;
}

export const MessageForm = ({
  input,
  handleInputChange,
  onSubmit,
  isLoading,
}: MessageFormProps) => {
  return (
    <form onSubmit={onSubmit} className="flex items-center gap-2 lg:gap-4 w-full">
      <Input
        disabled={isLoading}
        value={input}
        onChange={handleInputChange}
        placeholder="Type a message"
        className="
          text-black
          font-light
          py-2
          px-4
          bg-neutral-100 
          w-full
          border-0
          rounded-full
          outline-none
          focus-outline-none
        "
      />
      <Button
        disabled={isLoading}
        className="
        rounded-full 
        h-10
        w-10
        p-2 
        bg-sky-500 
        cursor-pointer 
        hover:bg-sky-600 
        transition
      "
      >
      {isLoading ? (
        <LoadingCircle />
      ) : null}
        <HiPaperAirplane
        size={22}
        className="text-white"
        />
      </Button>
    </form>
  )
}