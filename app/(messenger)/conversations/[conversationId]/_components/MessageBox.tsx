'use client';

import clsx from "clsx";
import Image from "next/image";
import { useState } from "react";
import { format } from "date-fns";
import { FullMessageType } from "@/lib/types";

import Avatar from "@/components/Avatar";
import ImageModal from "@/components/modals/ImageModal";

interface MessageBoxProps {
  clerkUserEmail: string
  data: FullMessageType;  
  isLast?: boolean;
}

const MessageBox: React.FC<MessageBoxProps> = ({ 
  clerkUserEmail,
  data,  
  isLast
}) => {
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const isOwn = clerkUserEmail === data?.sender?.email
  const seenList = (data.seen || [])
    .filter((user) => user.email !== clerkUserEmail)
    .map((user) => user.name)
    .join(', ');
  console.log("data@MessageBox: ", data)
  console.log("seenList@MessageBox: ", seenList)
  const container = clsx('flex gap-3 p-4', isOwn && 'justify-end');
  const avatar = clsx(isOwn && 'order-2');
  const body = clsx('flex flex-col gap-2', isOwn && 'items-end');
  const message = clsx(
    'text-sm w-fit overflow-hidden', 
    isOwn ? 'bg-sky-500 text-white' : 'bg-white text-black', 
    (data.image && data.image.trim() !== '') ? 'rounded-md p-0' : 'rounded-2xl py-2 px-4'
  );

  return ( 
    <div className={container}>
      <div className={avatar}>
        <Avatar user={data.sender} />
      </div>
      <div className={body}>
        <div className="flex items-center gap-1">
          <div className="text-sm text-gray-500">
            {data.sender.name}
          </div>
          <div className="text-xs text-gray-400">
            {format(new Date(data.createdAt), 'p')}
          </div>
        </div>
        <div className={message}>
          <ImageModal src={data.image} isOpen={imageModalOpen} onClose={() => setImageModalOpen(false)} />
          {data.image ? (
            <Image
              alt="Image"
              height="288"
              width="288"
              onClick={() => setImageModalOpen(true)} 
              src={data.image} 
              className="
                object-cover 
                cursor-pointer 
                hover:scale-110 
                transition 
                translate
              "
            />
          ) : (
            <div>{data.content}</div>
          )}
        </div>
        {isLast && isOwn && seenList.length > 0 && (
          <div 
            className="
            text-xs 
            font-light 
            text-gray-500
            "
          >
            {`Seen by ${seenList}`}
          </div>
        )}
      </div>
    </div>
   );
}
 
export default MessageBox;