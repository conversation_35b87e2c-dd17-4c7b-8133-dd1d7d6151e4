'use client';

import axios from "axios";
import { useEffect, useRef, useState } from "react";

import { pusherClient } from "@/lib/pusher";
import useConversation from "@/hooks/useConversation";
import MessageBox from "./MessageBox";
import { FullMessageType } from "@/lib/types";
import { Persona } from "@prisma/client";
import { find } from "lodash";

interface BodyProps {
  initialMessages: FullMessageType[];
  clerkUserEmail: string
  assistant?: Persona
}

const Body: React.FC<BodyProps> = ({ initialMessages = [], clerkUserEmail, assistant }) => {
  const bottomRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState(initialMessages);
  
  const { conversationId } = useConversation();

  useEffect(() => {
    axios.post(`/api/conversations/${conversationId}/seen`, { companionUerId: assistant?.userId! });
  }, [conversationId]);

  useEffect(() => {
    pusherClient.subscribe(conversationId)
    bottomRef?.current?.scrollIntoView();

    const messageHandler = (message: FullMessageType) => {
      axios.post(`/api/conversations/${conversationId}/seen`, { companionUerId: assistant?.userId! });

      setMessages((current) => {
        const foundMessage = find(current, { id: message.id });
        if (foundMessage) {
          return current;
        }
      
        return [...current, message];
      });

      bottomRef?.current?.scrollIntoView();
    };

    const updateMessageHandler = (newMessage: FullMessageType) => {
      setMessages((current) => current.map((currentMessage) => {
        if (currentMessage.id === newMessage.id) {
          return newMessage;
        }
        console.log("newMessage@Body: ", newMessage)
  
        return currentMessage;
        console.log("currentMessage@Body: ", currentMessage)
      }))
    };
  

    pusherClient.bind('messages:new', messageHandler)
    pusherClient.bind('message:update', updateMessageHandler);

    return () => {
      pusherClient.unsubscribe(conversationId)
      pusherClient.unbind('messages:new', messageHandler)
      pusherClient.unbind('message:update', updateMessageHandler)
    }
  }, [conversationId]);

  console.log("messages@Body: ", messages)

  return ( 
    <div className="flex-1 bg-gray-300 overflow-y-auto">
      {messages.map((message, i) => (
        <MessageBox 
          isLast={i === messages.length - 1} 
          key={message.id} 
          data={message}
          clerkUserEmail={clerkUserEmail}
        />
      ))}
      <div className="pt-24" ref={bottomRef} />
    </div>
  );
}
 
export default Body;