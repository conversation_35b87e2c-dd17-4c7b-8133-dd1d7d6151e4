import getConversationById from "@/app/actions/getConversationById";
import getUsers from "@/app/actions/getUsers";
import getCurrentUser from '@/app/actions/getCurrentUser'
import getMessages from "@/app/actions/getMessages";
import getClerkUserEmail from '@/app/actions/getClerkUserEmail';

import Header from "./_components/Header";
import Body from "./_components/Body";
import Form from "./_components/Form";
import MessagePanel from "./_components/MessagePanel"
import EmptyState from "@/components/EmptyState";

// Do not cache this page
export const revalidate = 0

interface IParams {
  conversationId: string;
}

const ChatId = async (props: { params: Promise<IParams> }) => {
  const params = await props.params;
  const conversation = await getConversationById(params.conversationId);
  const messages = await getMessages(params.conversationId);
  const users = await getUsers();
  const currentUser = await getCurrentUser();
  const clerkUserEmail = await getClerkUserEmail();
  const assistant = conversation?.users.find((user) => user.role === 'assistant');
  console.log("conversation@page: ", conversation)
  console.log("assistant@page: ", assistant)

  if (!conversation) {
    return (
      <div className="lg:pl-80 h-full">
        <div className="h-full flex flex-col">
          <EmptyState />
        </div>
      </div>
    )
  }

  return ( 
    <div className="lg:pl-80 h-full">
      <div className="h-full flex flex-col">
        <Header conversation={conversation} usersList={users} clerkUserEmail={clerkUserEmail!} />
        <Body initialMessages={messages} clerkUserEmail={clerkUserEmail!} assistant={assistant!} />
        <MessagePanel messagesBody={messages} currentUser={currentUser!} assistant={assistant!} />
      </div>
    </div>
  );
}

export default ChatId;