import '@/app/scrollbar.css'
import getConversations from "@/app/actions/getConversations";
import getUsers from "@/app/actions/getUsers";
import getClerkUserEmail from '@/app/actions/getClerkUserEmail';
import Sidebar from "@/components/sidebar/Sidebar";
import ConversationList from "./_components/ConversationList";

// 'auto' | 'force-dynamic' | 'error' | 'force-static'
export const dynamic = 'force-dynamic'

// Do not cache this page, will not work with runtime = 'edge'
export const revalidate = 0

export default async function ConversationsLayout({
  children
}: {
  children: React.ReactNode,
}) {
  const users = await getUsers(); 
  const clerkUserEmail = await getClerkUserEmail();
  const conversations = await getConversations();

  return (
    <Sidebar>
      <div className="h-full">
        <ConversationList 
          users={users}
          clerkUserEmail={clerkUserEmail!}
          title="Messages" 
          initialItems={conversations}
        />
        {children}
      </div>
    </Sidebar>
  );
}