import axios from "axios";
import { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import {  Persona } from "@prisma/client";

import Avatar from "@/components/Avatar";
import LoadingModal from "@/components/modals/LoadingModal";

interface UserBoxProps {
  data: Persona
}

const UserBox: React.FC<UserBoxProps> = ({ 
  data
}) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = useCallback(() => {
    setIsLoading(true);
    console.log("data@UserBox", data)

    axios.post('/api/conversations', { userId: data.userId })
    .then((data) => {
      console.log("data.data.userId@UserBox: ", data)
      console.log("data.data.id@UserBox: ", data.data.id)
      //router push to conversation/groupID
      router.push(`/conversations/${data.data.id}`);
    })
    .finally(() => setIsLoading(false));
  }, [data, router]);

  return (
    <>
      {isLoading && (
        <LoadingModal />
      )}
      <div
        onClick={handleClick}
        className="
          w-full 
          relative 
          flex 
          items-center 
          space-x-3
          bg-gray-200
          p-3
          py-2
          hover:bg-neutral-100
          rounded-lg
          transition
          cursor-pointer
        "
      >
        <Avatar user={data} />
        <div className="min-w-0 flex-1">
          <div className="focus:outline-none focus:bg-neutral-200">
            <span className="absolute inset-0" aria-hidden="true" />
            <div className="flex justify-between items-center mb-1">
              <p className="text-sm font-medium text-gray-900">
                {data.name}
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
 
export default UserBox;