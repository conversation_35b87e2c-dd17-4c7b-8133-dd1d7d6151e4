{"name": "comfyminds-lab", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "postinstall": "prisma generate --no-engine", "type-check": "tsc --noEmit", "format:write": "prettier --write \"{app,lib,components}/**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"{app,lib,components}**/*.{ts,tsx,mdx}\" --cache", "generate": "node ./scripts/generate.mjs"}, "dependencies": {"@agentic/stdlib": "^7.2.0", "@ai-sdk/anthropic": "^2.0.23", "@ai-sdk/deepseek": "^1.0.20", "@ai-sdk/google": "^2.0.17", "@ai-sdk/groq": "^2.0.22", "@ai-sdk/langchain": "^1.0.62", "@ai-sdk/openai": "^2.0.44", "@ai-sdk/react": "^2.0.62", "@ai-sdk/rsc": "^1.0.61", "@ai-sdk/xai": "^2.0.23", "@apollo/client": "^3.10.8", "@chakra-ui/react": "^2.8.1", "@clerk/clerk-react": "^5.25.5", "@clerk/nextjs": "^6.12.12", "@clerk/themes": "^2.2.26", "@cloudinary/url-gen": "^1.16.1", "@copilotkit/react-core": "^1.4.1", "@copilotkit/react-textarea": "^1.4.1", "@copilotkit/react-ui": "^1.4.1", "@copilotkit/runtime": "^1.4.1", "@copilotkit/sdk-js": "^1.4.1", "@copilotkit/shared": "1.3.15", "@deepgram/sdk": "^3.3.5", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/core": "latest", "@faker-js/faker": "^8.4.1", "@fal-ai/serverless-client": "^0.12.0", "@fluentui/react": "^8.109.0", "@fluentui/react-icons": "^2.0.201", "@fortaine/fetch-event-source": "^3.0.6", "@getzep/zep-cloud": "^2.0.0", "@google/generative-ai": "^0.24.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.17", "@hookform/resolvers": "^3.1.1", "@huggingface/inference": "^2.6.4", "@inquirer/prompts": "^3.2.0", "@langchain/anthropic": "^0.3.18", "@langchain/cohere": "^0.3.3", "@langchain/community": "^0.3.41", "@langchain/core": "^0.3.45", "@langchain/deepseek": "^0.0.1", "@langchain/google-gauth": "^0.2.4", "@langchain/google-genai": "^0.2.4", "@langchain/groq": "^0.2.2", "@langchain/langgraph": "^0.2.65", "@langchain/langgraph-checkpoint": "^0.0.16", "@langchain/langgraph-checkpoint-postgres": "^0.0.4", "@langchain/langgraph-swarm": "^0.0.3", "@langchain/nomic": "^0.1.0", "@langchain/ollama": "^0.2.0", "@langchain/openai": "^0.5.6", "@langchain/pinecone": "^0.2.0", "@langchain/weaviate": "^0.0.1", "@lexical/code": "^0.16.1", "@lexical/link": "^0.16.1", "@lexical/list": "^0.16.1", "@lexical/react": "^0.16.1", "@lexical/rich-text": "^0.16.1", "@lexical/selection": "^0.16.1", "@lexical/utils": "^0.16.1", "@liveblocks/client": "^2.3.0", "@liveblocks/node": "^2.3.0", "@liveblocks/react": "^2.3.0", "@liveblocks/react-lexical": "^2.3.0", "@liveblocks/react-ui": "^2.3.0", "@llamaindex/cloud": "^2.0.17", "@llamaindex/edge": "^0.3.8", "@llamaindex/pdf-viewer": "^1.1.1", "@mendable/firecrawl-js": "^0.0.13", "@mui/material": "^6.1.5", "@mui/x-charts": "^7.22.0", "@neondatabase/serverless": "^0.10.4", "@next/bundle-analyzer": "15.0.2", "@octoai/client": "latest", "@phosphor-icons/react": "^2.1.7", "@pinecone-database/pinecone": "^1.1.2", "@prisma/adapter-neon": "^6.6.0", "@prisma/client": "^6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.2", "@smastrom/react-rating": "^1.5.0", "@spotify/web-api-ts-sdk": "^1.2.0", "@stripe/stripe-js": "^3.2.0", "@supabase/auth-helpers-nextjs": "^0.5.6", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^5.59.19", "@tanstack/react-table": "^8.17.0", "@tavily/core": "^0.0.3", "@textea/json-viewer": "^4.0.0", "@types/bun": "^1.1.6", "@types/chart.js": "^2.9.37", "@types/common-tags": "^1.8.4", "@types/d3-scale": "^4.0.8", "@types/dompurify": "^3.0.5", "@types/formidable": "^2.0.6", "@types/js-cookie": "^3.0.6", "@types/jsdom": "^21.1.1", "@types/leaflet": "^1.9.8", "@types/lodash": "^4.14.200", "@types/marked": "^5.0.1", "@types/node": "^22.8.7", "@types/node-forge": "^1.3.11", "@types/pdf-parse": "^1.1.4", "@types/react": "^19.2.2", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.2.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/recharts": "^1.8.29", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "@types/web-push": "^3.6.3", "@types/ws": "^8.5.10", "@upstash/ratelimit": "1.0.3", "@upstash/redis": "^1.34.3", "@upstash/semantic-cache": "^1.0.4", "@upstash/vector": "^1.1.7", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.22.1", "@vercel/edge-config": "latest", "@vercel/kv": "^0.2.4", "@wmik/use-media-recorder": "^1.6.5-beta.0", "ag-grid-community": "^32.0.0", "ag-grid-react": "^32.0.0", "ai": "^5.0.61", "antd": "^5.21.6", "autoprefixer": "^10.4.19", "axios": "^1.4.0", "base64-arraybuffer": "^1.0.2", "bun-types": "latest", "chalk": "^5.3.0", "chart.js": "^4.4.0", "cheerio": "1.0.0-rc.12", "class-variance-authority": "^0.7.0", "cloudinary": "^2.0.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cohere-ai": "^7.7.7", "common-tags": "^1.8.2", "convex": "^1.13.0", "d3": "^7.8.5", "d3-scale": "^4.0.2", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "debug": "^4.3.4", "dompurify": "^3.0.9", "dotenv": "^16.4.5", "embla-carousel-autoplay": "^8.1.6", "embla-carousel-react": "^8.1.6", "emoji-picker-react": "^4.8.0", "emojisplosion": "^2.6.1", "encoding": "^0.1.13", "eslint": "^8.56.0", "eslint-config-next": "15.0.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.14.0", "exa-js": "^1.4.10", "export-to-csv": "^1.2.4", "focus-trap-react": "^10.2.2", "formidable": "^2.1.1", "framer-motion": "^11.14.4", "fs": "^0.0.1-security", "geist": "^1.2.2", "gpt3-tokenizer": "^1.1.5", "graphql": "^16.9.0", "groq-sdk": "^0.5.0", "gsap": "^3.12.1", "highlight.js": "^11.9.0", "install": "^0.13.0", "ip": "^2.0.1", "jotai": "^2.10.1", "js-cookie": "^3.0.5", "js-tiktoken": "latest", "jsdom": "^26.0.0", "jszip": "^3.10.1", "katex": "^0.16.11", "langchain": "^0.3.15", "langchainhub": "^0.0.11", "langfuse": "^2.3.3", "langsmith": "^0.3.4", "leaflet": "^1.9.4", "lexical": "^0.16.1", "linkup-sdk": "^1.0.4", "llamaindex": "^0.8.26", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.468.0", "marked": "^12.0.0", "markmap-common": "^0.15.0", "markmap-lib": "^0.15.2", "markmap-toolbar": "^0.15.0", "markmap-view": "^0.15.0", "merge-images": "^2.0.0", "mermaid": "^10.9.0", "mic-recorder-to-mp3": "^2.2.2", "next": "^15.3.0", "next-cloudinary": "^5.20.0", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "node-forge": "^1.3.1", "node-html-markdown": "^1.3.0", "node-loader": "^2.0.0", "npm": "^10.4.0", "octokit": "^4.0.2", "ollama-ai-provider": "^1.2.0", "openai": "^4.38.2", "pdf-parse": "^1.1.1", "pdfreader": "^3.0.2", "pickleparser": "^0.2.1", "polished": "latest", "portkey-ai": "^1.3.2", "postcss": "^8.4.47", "pptxgenjs": "^3.12.0", "pusher": "^5.1.3", "pusher-js": "^8.3.0", "query-string": "^8.1.0", "rc-drawer": "latest", "rc-slider": "^10.2.1", "react": "^19.2.0", "react-chrono": "^2.4.2", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^19.2.0", "react-dropzone": "^14.3.5", "react-form-stepper": "^2.0.3", "react-hook-form": "^7.45.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.11.0", "react-intersection-observer": "^9.8.2", "react-jsbarcode": "^1.0.1", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-resizable-panels": "^2.0.11", "react-responsive": "latest", "react-router-dom": "^6.22.3", "react-scroll": "latest", "react-select": "^5.7.7", "react-simple-wysiwyg": "^3.1.1", "react-sketch-canvas": "^6.2.0", "react-slick": "latest", "react-spinners": "^0.13.8", "react-spreadsheet": "^0.9.5", "react-stickynode": "latest", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.3", "react-timer-hook": "^3.0.5", "react-use": "^17.4.0", "react-waypoint": "latest", "react-web-share": "^2.0.2", "recharts": "^2.14.1", "redis": "^4.7.0", "rehype-external-links": "^3.0.0", "rehype-highlight": "^7.0.0", "rehype-katex": "^7.0.0", "rehype-parse": "^8.0.4", "rehype-raw": "^7.0.0", "rehype-remark": "^9.1.2", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-stringify": "^10.0.3", "replicate": "^0.29.4", "sharp": "^0.33.5", "silence-aware-recorder": "^1.0.4", "sonner": "^1.5.0", "stream-browserify": "^3.0.0", "stripe": "^14.4.0", "supabase": "^1.46.5", "svix": "^0.84.0", "swr": "^2.2.5", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "tempy": "^3.1.0", "theme-ui": "latest", "turndown": "^7.2.0", "typeorm": "^0.3.20", "typescript": "^5.7.3", "unified": "^10.1.2", "unist-util-remove": "^4.0.0", "unstructured-client": "^0.11.1", "use-debounce": "^10.0.0", "usehooks-ts": "^3.1.0", "uuid": "^11.0.5", "vaul": "^0.9.0", "weaviate-ts-client": "^2.1.0", "web-push": "^3.6.7", "wrap-ansi-cjs": "^8.0.0", "ws": "^8.16.0", "xlsx": "^0.18.5", "zod": "^4.1.12", "zod-to-json-schema": "^3.24.1", "zustand": "^4.3.9"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.49.2", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.5.6", "prisma": "^6.6.0", "tsx": "^4.7.1"}, "browser": {"fs": false, "os": false, "path": false, "child_process": false, "crypto": false}, "trustedDependencies": ["@clerk/shared", "@prisma/client", "@prisma/engines", "core-js", "esbuild", "prisma", "protobufjs", "sharp", "supabase"]}