from fastapi import FastAPI, HTTPException
'''
import os
from dotenv import load_dotenv

from pydantic import BaseModel
from semantic_router.encoders.cohere import CohereEncoder
from semantic_chunkers import StatisticalChunker
from typing import List

# Load environment variables from .env file
load_dotenv()
'''

app = FastAPI()

'''
class DocumentInput(BaseModel):
    documents: str

class DocumentOutput(BaseModel):
    pageContent: str
    metadata: dict
    document_id: str


@app.post("/api/semantic-chunkers", response_model=List[DocumentOutput])
async def semantic_chunkers(document_id: str, input: DocumentInput):
    try:
        cohere_api_key = os.getenv("COHERE_API_KEY")
        if not cohere_api_key:
            raise ValueError("COHERE_API_KEY environment variable is not set")

        encoder = CohereEncoder(name="embed-multilingual-v3.0", cohere_api_key=cohere_api_key)
        chunker = StatisticalChunker(encoder=encoder)
        chunks = chunker(docs=[input.documents])
        
        results = []
        for doc_chunks in chunks:
            for i, chunk in enumerate(doc_chunks):
                # Concatenate text from each split in the chunk
                chunk_text = "\n".join(chunk.splits)
                
                results.append(
                    DocumentOutput(
                        pageContent=chunk_text,
                        metadata={"index": i},
                        document_id=document_id
                    )
                )

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
'''

@app.get("/api/health")
def health_check():
    return {"message": "ok"}
